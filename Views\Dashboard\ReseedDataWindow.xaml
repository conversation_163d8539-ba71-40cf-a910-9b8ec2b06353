<Window x:Class="AqlanCenterProApp.Views.Dashboard.ReseedDataWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعادة تعبئة البيانات التجريبية"
        Height="400"
        Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0"
                Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="DatabaseRefresh"
                                     Width="48"
                    Height="48"
                                     HorizontalAlignment="Center"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="إعادة تعبئة البيانات التجريبية للداشبورد"
                       Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                       HorizontalAlignment="Center"
                       Margin="0,10,0,0"/>
        </StackPanel>

        <!-- المحتوى -->
        <ScrollViewer Grid.Row="1"
                VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- تحذير -->
                <Border Background="{DynamicResource MaterialDesignValidationErrorBrush}"
                        CornerRadius="4"
                        Padding="15"
                        Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Warning"
                                                 Width="24"
                                Height="24"
                                                 Foreground="White"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,10,0"/>
                        <TextBlock Text="تحذير: سيتم حذف جميع البيانات الموجودة (المرضى، الأطباء، الجلسات، المواعيد، المدفوعات) واستبدالها ببيانات تجريبية جديدة."
                                   Foreground="White"
                                   TextWrapping="Wrap"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- معلومات البيانات التي ستتم إضافتها -->
                <TextBlock Text="البيانات التي ستتم إضافتها:"
                           Style="{DynamicResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,0,0,10"/>

                <ItemsControl>
                    <ItemsControl.Items>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="Doctor"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 أطباء (د. عقلان الحكيمي، د. سارة أحمد، د. محمد عبدالله)"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="Account"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 مرضى مع بيانات كاملة"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="Calendar"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 مواعيد (اليوم وغداً)"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="MedicalBag"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 جلسات علاج مكتملة"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="CashMultiple"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 مدفوعات بطرق مختلفة"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                Margin="0,5">
                            <materialDesign:PackIcon Kind="Package"
                                    Width="20"
                                    Height="20"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                     VerticalAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <TextBlock Text="3 عناصر مخزون (متوفر، منخفض، نفد)"/>
                        </StackPanel>
                    </ItemsControl.Items>
                </ItemsControl>

                <!-- شريط التقدم -->
                <ProgressBar x:Name="ProgressBar"
                             Visibility="Collapsed"
                             IsIndeterminate="True"
                             Margin="0,20,0,0"/>

                <!-- رسالة الحالة -->
                <TextBlock x:Name="StatusMessage"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"
                           Style="{DynamicResource MaterialDesignBody2TextBlock}"/>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2"
                Orientation="Horizontal"
                    HorizontalAlignment="Center"
                Margin="0,20,0,0">
            <Button x:Name="ReseedButton"
                    Style="{DynamicResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Margin="0,0,10,0"
                    Padding="20,10"
                    Click="ReseedButton_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="DatabaseRefresh"
                                                 Width="20"
                                Height="20"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,5,0"/>
                        <TextBlock Text="إعادة تعبئة البيانات"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Content="إلغاء"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Margin="10,0,0,0"
                    Padding="20,10"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
