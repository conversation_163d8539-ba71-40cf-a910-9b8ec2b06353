namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// إحصائيات موظف واحد
    /// </summary>
    public class EmployeeStatistics
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string JobTitle { get; set; } = string.Empty;
        public decimal BasicSalary { get; set; } = 0;
        public int TotalWorkDays { get; set; } = 0;
        public int TotalAbsentDays { get; set; } = 0;
        public int TotalLeaveDays { get; set; } = 0;
        public int LateDays { get; set; } = 0;
        public int AnnualLeaveBalance { get; set; } = 0;
        public int EmergencyLeaveBalance { get; set; } = 0;
        public int UnpaidLeaveBalance { get; set; } = 0;
        public decimal TotalEarnings { get; set; } = 0;
        public decimal TotalDeductions { get; set; } = 0;
        public decimal NetSalary { get; set; } = 0;
        public int PendingLeaves { get; set; } = 0;
        public int ApprovedLeaves { get; set; } = 0;
        public int RejectedLeaves { get; set; } = 0;
        public int ExpiringDocuments { get; set; } = 0;
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int InactiveEmployees { get; set; }
        public decimal TotalSalary { get; set; }
        public decimal AverageSalary { get; set; }
    }

    /// <summary>
    /// إحصائيات عامة للموظفين
    /// </summary>
    public class GeneralEmployeeStatistics
    {
        public int TotalEmployees { get; set; } = 0;
        public int ActiveEmployees { get; set; } = 0;
        public int InactiveEmployees { get; set; } = 0;
        public int NewEmployeesThisMonth { get; set; } = 0;
        public int ResignedEmployeesThisMonth { get; set; } = 0;
        public decimal TotalSalaries { get; set; } = 0;
        public decimal AverageSalary { get; set; } = 0;
        public int EmployeesOnLeave { get; set; } = 0;
        public int PendingLeaveRequests { get; set; } = 0;
        public int ExpiringDocuments { get; set; } = 0;
    }

    /// <summary>
    /// إحصائيات الحضور
    /// </summary>
    public class AttendanceStatistics
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalWorkDays { get; set; } = 0;
        public int TotalPresentDays { get; set; } = 0;
        public int TotalAbsentDays { get; set; } = 0;
        public int TotalLateDays { get; set; } = 0;
        public int TotalLeaveDays { get; set; } = 0;
        public decimal AttendanceRate { get; set; } = 0;
        public decimal AverageWorkHours { get; set; } = 0;
        public int TotalOvertimeHours { get; set; } = 0;
    }

    /// <summary>
    /// إحصائيات الرواتب
    /// </summary>
    public class SalaryStatistics
    {
        public int Month { get; set; }
        public int Year { get; set; }
        public int TotalEmployees { get; set; } = 0;
        public decimal TotalBasicSalaries { get; set; } = 0;
        public decimal TotalAllowances { get; set; } = 0;
        public decimal TotalDeductions { get; set; } = 0;
        public decimal TotalBonuses { get; set; } = 0;
        public decimal TotalOvertime { get; set; } = 0;
        public decimal TotalNetSalaries { get; set; } = 0;
        public decimal AverageSalary { get; set; } = 0;
        public int PaidSalaries { get; set; } = 0;
        public int PendingSalaries { get; set; } = 0;
    }
} 