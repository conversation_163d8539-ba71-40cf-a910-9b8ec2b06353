using System.Collections.ObjectModel;
using System.Threading.Tasks;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeSalariesViewModel
    {
        private readonly IEmployeeService _employeeService;
        public ObservableCollection<EmployeeSalary> SalaryRecords { get; set; } = new();
        public Employee Employee { get; }

        public EmployeeSalariesViewModel(IEmployeeService employeeService, Employee employee)
        {
            _employeeService = employeeService;
            Employee = employee;
            _ = LoadSalariesAsync();
        }

        private async Task LoadSalariesAsync()
        {
            var year = System.DateTime.Today.Year;
            var records = await _employeeService.GetEmployeeSalariesAsync(Employee.EmployeeId, year);
            SalaryRecords.Clear();
            foreach (var record in records)
                SalaryRecords.Add(record);
        }
    }
} 