using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IRoleService
    {
        Task<IEnumerable<Role>> GetAllRolesAsync();
        Task<Role?> GetRoleByIdAsync(int id);
        Task<Role> CreateRoleAsync(Role role);
        Task<Role> UpdateRoleAsync(Role role);
        Task<bool> DeleteRoleAsync(int id);
        Task<bool> ActivateRoleAsync(int id);
        Task<bool> DeactivateRoleAsync(int id);
        Task<IEnumerable<Role>> GetActiveRolesAsync();
        Task<Role?> GetRoleByNameAsync(string roleName);
        Task<bool> IsRoleNameUniqueAsync(string roleName, int? excludeRoleId = null);
        Task<IEnumerable<User>> GetUsersInRoleAsync(int roleId);
        Task<bool> AssignRoleToUserAsync(int userId, int roleId);
        Task<bool> RemoveRoleFromUserAsync(int userId);
        Task<bool> HasPermissionAsync(int userId, string permission);
        Task<IEnumerable<string>> GetUserPermissionsAsync(int userId);
        Task<bool> UpdateRolePermissionsAsync(int roleId, Dictionary<string, bool> permissions);
    }
} 