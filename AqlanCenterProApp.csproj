﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <LangVersion>12.0</LangVersion>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>

    <!-- إعدادات لحل مشاكل .NET 8 -->
    <EnableUnsafeBinaryFormatterSerialization>false</EnableUnsafeBinaryFormatterSerialization>
    <SuppressTrimAnalysisWarnings>true</SuppressTrimAnalysisWarnings>
    <TrimmerDefaultAction>link</TrimmerDefaultAction>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework Core for .NET 8 LTS -->
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc2" />
    <PackageReference Include="MaterialDesignColors" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite"
      Version="$(EntityFrameworkVersion)" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools"
      Version="$(EntityFrameworkVersion)">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>

    <!-- MVVM Community Toolkit -->
    <PackageReference Include="CommunityToolkit.Mvvm" Version="$(MVVMToolkitVersion)" />

    <!-- Microsoft Extensions for .NET 8 LTS -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="$(MicrosoftExtensionsVersion)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json"
      Version="$(MicrosoftExtensionsVersion)" />

    <!-- Security & Cryptography -->
    <PackageReference Include="BCrypt.Net-Next" Version="$(BCryptVersion)" />

    <!-- Microsoft Extensions for Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection"
      Version="$(MicrosoftExtensionsVersion)" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Copy configuration files to output directory -->
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <!-- Resources - Logo and Images -->
    <Resource Include="Resources\logo.png" />
  </ItemGroup>

</Project>