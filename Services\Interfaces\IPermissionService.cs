using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IPermissionService
    {
        Task<List<Permission>> GetAllPermissionsAsync();
        Task<List<Permission>> GetPermissionsByModuleAsync(string module);
        Task<Permission?> GetPermissionByIdAsync(int id);
        Task<Permission?> GetPermissionByActionAsync(string action);
        Task<bool> AddPermissionAsync(Permission permission);
        Task<bool> UpdatePermissionAsync(Permission permission);
        Task<bool> DeletePermissionAsync(int id);
        Task<bool> InitializePermissionsAsync();

        // User Permission Management
        Task<List<UserPermission>> GetUserPermissionsAsync(int userId);
        Task<bool> GrantPermissionToUserAsync(int userId, int permissionId, string? notes = null);
        Task<bool> RevokePermissionFromUserAsync(int userId, int permissionId);
        Task<bool> UpdateUserPermissionAsync(int userId, int permissionId, bool isGranted, string? notes = null);
        Task<bool> HasUserPermissionAsync(int userId, string permissionAction);
        Task<bool> GrantMultiplePermissionsToUserAsync(int userId, List<int> permissionIds, string? notes = null);
        Task<bool> RevokeMultiplePermissionsFromUserAsync(int userId, List<int> permissionIds);

        // Bulk Operations
        Task<bool> GrantAllPermissionsToUserAsync(int userId, string? notes = null);
        Task<bool> RevokeAllPermissionsFromUserAsync(int userId);
        Task<bool> GrantPermissionsByModuleToUserAsync(int userId, string module, string? notes = null);
    }
} 