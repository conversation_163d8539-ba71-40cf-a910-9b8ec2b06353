using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface ILabOrderService
    {
        // CRUD Operations
        Task<IEnumerable<LabOrder>> GetAllLabOrdersAsync();
        Task<LabOrder?> GetLabOrderByIdAsync(int id);
        Task<LabOrder> AddLabOrderAsync(LabOrder labOrder);
        Task<LabOrder> UpdateLabOrderAsync(LabOrder labOrder);
        Task<bool> DeleteLabOrderAsync(int id);
        Task<bool> LabOrderExistsAsync(int id);

        // Search and Filter
        Task<IEnumerable<LabOrder>> SearchLabOrdersAsync(string searchTerm);
        Task<IEnumerable<LabOrder>> GetLabOrdersByPatientAsync(int patientId);
        Task<IEnumerable<LabOrder>> GetLabOrdersByDoctorAsync(int doctorId);
        Task<IEnumerable<LabOrder>> GetLabOrdersByLabAsync(int labId);
        Task<IEnumerable<LabOrder>> GetLabOrdersByStatusAsync(string status);
        Task<IEnumerable<LabOrder>> GetLabOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<LabOrder>> GetOverdueLabOrdersAsync();
        Task<IEnumerable<LabOrder>> GetReadyLabOrdersAsync();

        // Special Operations
        Task<string> GenerateOrderNumberAsync();
        Task<bool> UpdateLabOrderStatusAsync(int labOrderId, string status);
        Task<LabOrder> DuplicateLabOrderAsync(int labOrderId);
        Task<bool> RateLabOrderAsync(int labOrderId, int rating, string? notes);
        Task<bool> MarkAsPaidAsync(int labOrderId, string? invoiceNumber);

        // Reports and Analytics
        Task<IEnumerable<LabOrder>> GetLabOrdersReportAsync(DateTime startDate, DateTime endDate, string? status = null, int? labId = null);
        Task<object> GetLabOrdersStatisticsAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetLabPerformanceReportAsync(DateTime startDate, DateTime endDate);

        // Notifications
        Task<IEnumerable<LabOrder>> GetOrdersNeedingNotificationAsync();
        Task<bool> SendWhatsAppNotificationAsync(int labOrderId, string message);
        Task<bool> SendSMSNotificationAsync(int labOrderId, string message);

        // Export
        Task<byte[]> ExportLabOrdersToPdfAsync(IEnumerable<LabOrder> labOrders);
        Task<byte[]> ExportLabOrdersToExcelAsync(IEnumerable<LabOrder> labOrders);
        Task<byte[]> ExportLabOrdersToCsvAsync(IEnumerable<LabOrder> labOrders);

        // Print
        Task<bool> PrintLabOrderAsync(int labOrderId);
        Task<bool> PrintLabOrderReportAsync(IEnumerable<LabOrder> labOrders);
    }
} 