using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.ViewModels.Doctors
{
    /// <summary>
    /// ViewModel لنافذة إحصائيات الطبيب
    /// </summary>
    public partial class DoctorStatisticsViewModel : ObservableObject
    {
        #region Properties

        [ObservableProperty]
        private Doctor _doctor;

        [ObservableProperty]
        private decimal _averageEarningsPerSession;

        [ObservableProperty]
        private decimal _averagePatientsPerMonth;

        [ObservableProperty]
        private List<MonthlyStatistic> _monthlyEarnings = new();

        [ObservableProperty]
        private List<MonthlyStatistic> _monthlySessions = new();

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand CloseCommand { get; }

        #endregion

        #region Events

        public event EventHandler<bool>? CloseRequested;

        #endregion

        #region Constructor

        public DoctorStatisticsViewModel(Doctor doctor)
        {
            _doctor = doctor ?? throw new ArgumentNullException(nameof(doctor));

            // تهيئة الأوامر
            RefreshCommand = new RelayCommand(RefreshStatistics);
            PrintCommand = new RelayCommand(PrintStatistics);
            ExportCommand = new RelayCommand(ExportStatistics);
            CloseCommand = new RelayCommand(Close);

            // تحميل الإحصائيات
            LoadStatistics();
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل الإحصائيات
        /// </summary>
        private void LoadStatistics()
        {
            try
            {
                // حساب متوسط الأرباح لكل جلسة
                if (Doctor.CompletedSessionsCount > 0)
                {
                    AverageEarningsPerSession = Doctor.TotalEarnings / Doctor.CompletedSessionsCount;
                }

                // حساب متوسط المرضى شهرياً (بناءً على تاريخ الانضمام)
                var monthsSinceJoining = (DateTime.Now - Doctor.JoinDate).Days / 30.0;
                if (monthsSinceJoining > 0)
                {
                    AveragePatientsPerMonth = (decimal)(Doctor.TotalPatientsCount / monthsSinceJoining);
                }

                // إنشاء بيانات وهمية للإحصائيات الشهرية
                GenerateMonthlyStatistics();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء الإحصائيات الشهرية
        /// </summary>
        private void GenerateMonthlyStatistics()
        {
            var random = new Random();
            var months = new List<MonthlyStatistic>();
            var sessions = new List<MonthlyStatistic>();

            for (int i = 5; i >= 0; i--)
            {
                var date = DateTime.Now.AddMonths(-i);
                var monthName = date.ToString("yyyy-MM");

                // إنشاء بيانات وهمية للأرباح
                var earnings = (decimal)(random.NextDouble() * (double)Doctor.TotalEarnings * 0.3);
                months.Add(new MonthlyStatistic
                {
                    Month = monthName,
                    Value = earnings
                });

                // إنشاء بيانات وهمية للجلسات
                var sessionCount = random.Next(1, Math.Max(2, Doctor.CompletedSessionsCount / 6));
                sessions.Add(new MonthlyStatistic
                {
                    Month = monthName,
                    Value = sessionCount
                });
            }

            MonthlyEarnings = months;
            MonthlySessions = sessions;
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void RefreshStatistics()
        {
            try
            {
                LoadStatistics();
                System.Windows.MessageBox.Show("تم تحديث الإحصائيات بنجاح", "نجح",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة الإحصائيات
        /// </summary>
        private void PrintStatistics()
        {
            try
            {
                var printContent = GeneratePrintContent();
                System.Windows.MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير الإحصائيات
        /// </summary>
        private void ExportStatistics()
        {
            try
            {
                System.Windows.MessageBox.Show("سيتم تنفيذ وظيفة التصدير قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, false);
        }

        /// <summary>
        /// إنشاء محتوى الطباعة
        /// </summary>
        private string GeneratePrintContent()
        {
            var monthlyEarningsText = string.Join("\n", MonthlyEarnings.Select(m => $"- {m.Month}: {m.Value:N0} ريال"));
            var monthlySessionsText = string.Join("\n", MonthlySessions.Select(m => $"- {m.Month}: {m.Value} جلسة"));

            return $@"
إحصائيات الطبيب: {Doctor.FullName}
=====================================

الإحصائيات العامة:
- إجمالي المرضى: {Doctor.TotalPatientsCount}
- الجلسات المكتملة: {Doctor.CompletedSessionsCount}
- إجمالي الأرباح: {Doctor.TotalEarnings:N0} ريال
- التقييم: {Doctor.Rating:F1}/5 ({Doctor.RatingCount} تقييم)

الإحصائيات المحسوبة:
- متوسط الربح لكل جلسة: {AverageEarningsPerSession:N0} ريال
- متوسط المرضى شهرياً: {AveragePatientsPerMonth:F1}

الأرباح الشهرية:
{monthlyEarningsText}

الجلسات الشهرية:
{monthlySessionsText}

تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}
";
        }

        #endregion
    }

    /// <summary>
    /// نموذج الإحصائيات الشهرية
    /// </summary>
    public class MonthlyStatistic
    {
        public string Month { get; set; } = string.Empty;
        public decimal Value { get; set; }
    }
}
