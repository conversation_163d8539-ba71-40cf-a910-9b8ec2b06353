using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Views.Suppliers;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.Suppliers
{
    public class SuppliersListViewModel : BaseViewModel
    {
        private readonly ISupplierService _supplierService;
        private ObservableCollection<Supplier> _suppliers;
        private Supplier? _selectedSupplier;
        private string _searchTerm = string.Empty;
        private bool _showOnlyActive = true;

        public SuppliersListViewModel(ISupplierService supplierService)
        {
            _supplierService = supplierService;
            _suppliers = new ObservableCollection<Supplier>();

            LoadSuppliersCommand = new RelayCommand(async () => await LoadSuppliersAsync());
            AddSupplierCommand = new RelayCommand(() => AddSupplier());
            EditSupplierCommand = new RelayCommand(() => EditSupplier(), () => SelectedSupplier != null);
            DeleteSupplierCommand = new RelayCommand(async () => await DeleteSupplierAsync(), () => SelectedSupplier != null);
            SearchCommand = new RelayCommand(async () => await SearchSuppliersAsync());
            ExportCommand = new RelayCommand(async () => await ExportSuppliersAsync());
            PrintCommand = new RelayCommand(async () => await PrintSuppliersAsync());

            // تحميل البيانات عند الإنشاء
            _ = LoadSuppliersAsync();
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                SetProperty(ref _selectedSupplier, value);
                OnPropertyChanged(nameof(CanEdit));
                OnPropertyChanged(nameof(CanDelete));
                OnPropertyChanged(nameof(SelectedSupplierBalance));
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                SetProperty(ref _searchTerm, value);
                _ = SearchSuppliersAsync();
            }
        }

        public bool ShowOnlyActive
        {
            get => _showOnlyActive;
            set
            {
                SetProperty(ref _showOnlyActive, value);
                _ = LoadSuppliersAsync();
            }
        }

        public bool CanEdit => SelectedSupplier != null;
        public bool CanDelete => SelectedSupplier != null;

        public decimal SelectedSupplierBalance
        {
            get
            {
                if (SelectedSupplier == null) return 0;
                return _supplierService.GetSupplierBalanceAsync(SelectedSupplier.Id).Result;
            }
        }

        // Commands
        public ICommand LoadSuppliersCommand { get; }
        public ICommand AddSupplierCommand { get; }
        public ICommand EditSupplierCommand { get; }
        public ICommand DeleteSupplierCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }

        public async Task LoadSuppliersAsync()
        {
            try
            {
                IsBusy = true;
                var suppliers = ShowOnlyActive 
                    ? await _supplierService.GetActiveSuppliersAsync()
                    : await _supplierService.GetAllSuppliersAsync();

                Suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    Suppliers.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تحميل الموردين: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddSupplier()
        {
            var vm = new AddEditSupplierViewModel(_supplierService);
            var dialog = new AddEditSupplierView { DataContext = vm, Owner = Application.Current.MainWindow };
            vm.SaveCompleted += async () =>
            {
                dialog.DialogResult = true;
                dialog.Close();
                await LoadSuppliersAsync();
            };
            vm.CancelRequested += () =>
            {
                dialog.DialogResult = false;
                dialog.Close();
            };
            dialog.ShowDialog();
        }

        private void EditSupplier()
        {
            if (SelectedSupplier == null) return;
            var vm = new AddEditSupplierViewModel(_supplierService, SelectedSupplier);
            var dialog = new AddEditSupplierView { DataContext = vm, Owner = Application.Current.MainWindow };
            vm.SaveCompleted += async () =>
            {
                dialog.DialogResult = true;
                dialog.Close();
                await LoadSuppliersAsync();
            };
            vm.CancelRequested += () =>
            {
                dialog.DialogResult = false;
                dialog.Close();
            };
            dialog.ShowDialog();
        }

        private async Task DeleteSupplierAsync()
        {
            if (SelectedSupplier == null) return;

            var result = MessageHelper.ShowConfirmation($"هل أنت متأكد من حذف المورد '{SelectedSupplier.Name}'؟");
            if (!result) return;

            try
            {
                IsBusy = true;
                var success = await _supplierService.DeleteSupplierAsync(SelectedSupplier.Id);
                if (success)
                {
                    Suppliers.Remove(SelectedSupplier);
                    MessageHelper.ShowSuccess("تم حذف المورد بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حذف المورد");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في حذف المورد: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchSuppliersAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadSuppliersAsync();
                return;
            }

            try
            {
                IsBusy = true;
                var suppliers = await _supplierService.SearchSuppliersAsync(SearchTerm);
                
                Suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    if (!ShowOnlyActive || supplier.IsActive)
                    {
                        Suppliers.Add(supplier);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExportSuppliersAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                MessageHelper.ShowSuccess("تم تصدير البيانات بنجاح");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task PrintSuppliersAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                MessageHelper.ShowSuccess("تم إرسال البيانات للطباعة بنجاح");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في الطباعة: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
} 