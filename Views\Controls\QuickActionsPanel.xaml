<UserControl x:Class="AqlanCenterProApp.Views.Controls.QuickActionsPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="300"
             d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <Border Style="{StaticResource ModernCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- رأس الأزرار السريعة -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="⚡" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="الإجراءات السريعة" 
                               Style="{StaticResource SectionHeaderStyle}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="اختصارات سريعة للعمليات الأساسية" 
                               FontSize="12" 
                               Foreground="#7F8C8D" 
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- شبكة الأزرار السريعة -->
            <UniformGrid Grid.Row="1" Columns="4" Rows="2">

                <!-- إضافة مريض جديد -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#3498DB"
                        Command="{Binding AddPatientCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="👤" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="مريض جديد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="إضافة مريض للنظام" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- جدولة موعد جديد -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#2ECC71"
                        Command="{Binding AddAppointmentCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="📅" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="موعد جديد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="جدولة موعد للمريض" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- إنشاء فاتورة جديدة -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#E67E22"
                        Command="{Binding AddInvoiceCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="🧾" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="فاتورة جديدة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="إنشاء فاتورة للمريض" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- إضافة جلسة علاج -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#9B59B6"
                        Command="{Binding AddSessionCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="🦷" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="جلسة علاج" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="تسجيل جلسة علاج" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- عرض التقارير -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#34495E"
                        Command="{Binding ViewReportsCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="📊" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="التقارير" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض التقارير والإحصائيات" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- إدارة المخزون -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#1ABC9C"
                        Command="{Binding ManageInventoryCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="📦" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="المخزون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة المخزون والمواد" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- إدارة الموظفين -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#E74C3C"
                        Command="{Binding ManageEmployeesCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="الموظفين" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة الموظفين والحضور" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

                <!-- الإعدادات -->
                <Button Style="{StaticResource QuickActionButtonStyle}"
                        Background="#95A5A6"
                        Command="{Binding OpenSettingsCommand}"
                        Margin="8">
                    <StackPanel>
                        <TextBlock Text="⚙️" FontSize="24" Margin="0,0,0,8" HorizontalAlignment="Center"/>
                        <TextBlock Text="الإعدادات" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="إعدادات النظام والتخصيص" FontSize="11" Opacity="0.8" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Button>

            </UniformGrid>
        </Grid>
    </Border>
</UserControl>
