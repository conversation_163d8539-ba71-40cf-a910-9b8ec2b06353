using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models.Dashboard;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace AqlanCenterProApp.Services.Implementations
{
    public class ChartService : IChartService
    {
        private readonly AqlanCenterDbContext _context;

        public ChartService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<List<ChartData>> GetAppointmentsChartDataAsync()
        {
            var today = DateTime.Today;
            var startDate = today.AddDays(-29); // Last 30 days

            var data = await _context.Appointments
                .Where(a => a.AppointmentDateTime.Date >= startDate && a.AppointmentDateTime.Date <= today)
                .GroupBy(a => a.AppointmentDateTime.Date)
                .Select(g => new ChartData
                {
                    Date = g.Key,
                    Value = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            // Fill in missing dates with 0 appointments
            var result = new List<ChartData>();
            for (var date = startDate; date <= today; date = date.AddDays(1))
            {
                var dayData = data.FirstOrDefault(d => d.Date == date);
                result.Add(new ChartData
                {
                    Label = date.ToString("MMM dd"),
                    Value = dayData?.Value ?? 0,
                    Date = date
                });
            }

            return result;
        }

        public async Task<List<ChartData>> GetRevenueChartDataAsync()
        {
            var today = DateTime.Today;
            var startDate = new DateTime(today.Year, 1, 1); // Start of the year

            var data = await _context.Receipts
                .Where(r => r.ReceiptDate >= startDate && r.ReceiptDate <= today)
                .GroupBy(r => new { r.ReceiptDate.Year, r.ReceiptDate.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    Total = g.Sum(r => r.Amount)
                })
                .OrderBy(x => x.Year).ThenBy(x => x.Month)
                .ToListAsync();

            var result = data.Select(d => new ChartData
            {
                Label = new DateTime(d.Year, d.Month, 1).ToString("MMM", CultureInfo.InvariantCulture),
                Value = (double)d.Total
            }).ToList();

            return result;
        }
    }
}