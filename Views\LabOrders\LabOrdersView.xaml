<UserControl x:Class="AqlanCenterProApp.Views.LabOrders.LabOrdersView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:conv="clr-namespace:AqlanCenterProApp.Converters"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <UserControl.Resources>
        <conv:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="طلبات المعامل" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة طلبات المعمل والتركيبات" 
                               FontSize="14" 
                               Foreground="#E3F2FD" 
                               VerticalAlignment="Center" 
                               Margin="15,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="إضافة جديد" 
                            Command="{Binding AddCommand}"
                            Style="{StaticResource SuccessButtonStyle}"/>
                    <Button Content="تحديث" 
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="White" Padding="20,15" BorderBrush="#DDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row -->
                <StackPanel Grid.Column="0" Grid.Row="0" Margin="5">
                    <TextBlock Text="المريض" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding Patients}"
                              SelectedItem="{Binding SelectedPatient}"
                              DisplayMemberPath="FullName"
                              Style="{StaticResource ModernComboBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="0" Margin="5">
                    <TextBlock Text="الطبيب" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding Doctors}"
                              SelectedItem="{Binding SelectedDoctor}"
                              DisplayMemberPath="FullName"
                              Style="{StaticResource ModernComboBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Grid.Row="0" Margin="5">
                    <TextBlock Text="المعمل" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding Labs}"
                              SelectedItem="{Binding SelectedLab}"
                              DisplayMemberPath="Name"
                              Style="{StaticResource ModernComboBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Grid.Row="0" Margin="5">
                    <TextBlock Text="الحالة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding StatusFilter, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="4" Grid.Row="0" Margin="5" VerticalAlignment="Bottom">
                    <Button Content="مسح الفلاتر" 
                            Command="{Binding ClearFiltersCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                </StackPanel>

                <!-- Second Row -->
                <StackPanel Grid.Column="0" Grid.Row="1" Margin="5">
                    <TextBlock Text="من تاريخ" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker SelectedDate="{Binding StartDate}" 
                                Padding="10,8" 
                                BorderThickness="1" 
                                BorderBrush="#DDD"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="1" Margin="5">
                    <TextBlock Text="إلى تاريخ" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker SelectedDate="{Binding EndDate}" 
                                Padding="10,8" 
                                BorderThickness="1" 
                                BorderBrush="#DDD"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Grid.Row="1" Margin="5">
                    <TextBlock Text="بحث عام" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Grid.Row="1" Grid.ColumnSpan="2" Margin="5" Orientation="Horizontal" VerticalAlignment="Bottom">
                    <Button Content="تصدير" 
                            Command="{Binding ExportCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                    <Button Content="طباعة" 
                            Command="{Binding PrintCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <DataGrid Grid.Row="2" 
                  ItemsSource="{Binding LabOrders}"
                  SelectedItem="{Binding SelectedLabOrder}"
                  Style="{StaticResource ModernDataGridStyle}"
                  Margin="20">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الطلب" Binding="{Binding OrderNumber}" Width="120"/>
                <DataGridTextColumn Header="المريض" Binding="{Binding Patient.FullName}" Width="150"/>
                <DataGridTextColumn Header="الطبيب" Binding="{Binding Doctor.FullName}" Width="150"/>
                <DataGridTextColumn Header="المعمل" Binding="{Binding Lab.Name}" Width="120"/>
                <DataGridTextColumn Header="نوع العمل" Binding="{Binding WorkType}" Width="120"/>
                <DataGridTextColumn Header="عدد القطع" Binding="{Binding PiecesCount}" Width="80"/>
                <DataGridTextColumn Header="تاريخ الإرسال" Binding="{Binding SendDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                <DataGridTextColumn Header="تاريخ الاستلام المتوقع" Binding="{Binding ExpectedReturnDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                <DataGridTextColumn Header="التكلفة" Binding="{Binding Cost, StringFormat=C}" Width="100"/>
                <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="200"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#FAFAFA" Padding="20,10" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="إجمالي الطلبات: " FontWeight="SemiBold"/>
                    <TextBlock Text="{Binding LabOrders.Count}" Foreground="#2196F3" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="تعديل" 
                            Command="{Binding EditCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                    <Button Content="حذف" 
                            Command="{Binding DeleteCommand}"
                            Style="{StaticResource DangerButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="4" 
                Background="#80000000" 
                Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                           Foreground="White" 
                           FontSize="16" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl> 