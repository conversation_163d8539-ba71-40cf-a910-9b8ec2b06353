using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.PaymentVouchers;

namespace AqlanCenterProApp.Views.PaymentVouchers
{
    /// <summary>
    /// Interaction logic for PaymentVouchersView.xaml
    /// </summary>
    public partial class PaymentVouchersView : UserControl
    {
        public PaymentVouchersView()
        {
            InitializeComponent();
        }

        public PaymentVouchersView(PaymentVouchersListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += PaymentVouchersView_Loaded;
        }

        private void PaymentVouchersView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is PaymentVouchersListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadPaymentVouchersAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadPaymentVouchersAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات سندات الصرف: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في PaymentVouchersView_Loaded: {ex.Message}");
            }
        }
    }
}