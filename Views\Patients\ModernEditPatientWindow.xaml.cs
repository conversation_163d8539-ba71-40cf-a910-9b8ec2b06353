using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Views.Patients
{
    /// <summary>
    /// نافذة حديثة لتعديل بيانات المريض
    /// </summary>
    public partial class ModernEditPatientWindow : Window
    {
        private readonly IPatientService _patientService;
        private readonly Patient _originalPatient;

        public ModernEditPatientWindow(Patient patient)
        {
            InitializeComponent();

            _originalPatient = patient ?? throw new ArgumentNullException(nameof(patient));

            // إنشاء خدمة مؤقتة للاختبار
            _patientService = CreateTempPatientService();

            // تحميل بيانات المريض
            LoadPatientData();
        }

        private IPatientService CreateTempPatientService()
        {
            // إنشاء خدمة مؤقتة باستخدام قاعدة البيانات الموحدة
            return Helpers.DbContextHelper.CreatePatientService();
        }

        private void LoadPatientData()
        {
            try
            {
                // تحميل البيانات الأساسية
                FileNumberTextBox.Text = _originalPatient.FileNumber.ToString();
                FullNameTextBox.Text = _originalPatient.FullName ?? string.Empty;
                PhoneTextBox.Text = _originalPatient.Phone ?? string.Empty;
                AddressTextBox.Text = _originalPatient.Address ?? string.Empty;
                DateOfBirthPicker.SelectedDate = _originalPatient.DateOfBirth;
                ConsultationFeeTextBox.Text = _originalPatient.ConsultationFee?.ToString("F2") ?? "50.00";

                // تحديد الجنس
                if (!string.IsNullOrEmpty(_originalPatient.Gender))
                {
                    var genderItem = GenderComboBox.Items.Cast<ComboBoxItem>()
                        .FirstOrDefault(item => item.Content.ToString() == _originalPatient.Gender);
                    if (genderItem != null)
                        GenderComboBox.SelectedItem = genderItem;
                }

                // تحديد تصنيف المريض
                if (!string.IsNullOrEmpty(_originalPatient.PatientCategory))
                {
                    var categoryItem = CategoryComboBox.Items.Cast<ComboBoxItem>()
                        .FirstOrDefault(item => item.Content.ToString() == _originalPatient.PatientCategory);
                    if (categoryItem != null)
                        CategoryComboBox.SelectedItem = categoryItem;
                }

                // تحديد حالة الملف
                if (!string.IsNullOrEmpty(_originalPatient.FileStatus))
                {
                    var statusItem = FileStatusComboBox.Items.Cast<ComboBoxItem>()
                        .FirstOrDefault(item => item.Content.ToString() == _originalPatient.FileStatus);
                    if (statusItem != null)
                        FileStatusComboBox.SelectedItem = statusItem;
                }

                // تحديث العنوان الفرعي
                PatientInfoSubtitle.Text = $"تعديل بيانات المريض - رقم الملف: {_originalPatient.FileNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المريض: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من البيانات المطلوبة
                if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المريض", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    FullNameTextBox.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء الحفظ
                SaveButton.IsEnabled = false;
                CancelButton.IsEnabled = false;
                SaveButton.Content = "جاري الحفظ...";

                // تحديث بيانات المريض
                var consultationFee = decimal.TryParse(ConsultationFeeTextBox.Text, out var fee) ? fee : 50.00m;

                _originalPatient.FullName = FullNameTextBox.Text.Trim();
                _originalPatient.Gender = ((ComboBoxItem)GenderComboBox.SelectedItem)?.Content?.ToString() ?? "ذكر";
                _originalPatient.DateOfBirth = DateOfBirthPicker.SelectedDate;
                _originalPatient.Phone = PhoneTextBox.Text?.Trim();
                _originalPatient.Address = AddressTextBox.Text?.Trim();
                _originalPatient.PatientCategory = ((ComboBoxItem)CategoryComboBox.SelectedItem)?.Content?.ToString() ?? "جديد";
                _originalPatient.FileStatus = ((ComboBoxItem)FileStatusComboBox.SelectedItem)?.Content?.ToString() ?? "نشط";
                _originalPatient.ConsultationFee = consultationFee;
                _originalPatient.UpdatedAt = DateTime.Now;
                _originalPatient.UpdatedBy = "System";

                // حفظ التعديلات
                await _patientService.UpdatePatientAsync(_originalPatient, "System");

                // إغلاق النافذة بنجاح
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // إعادة تفعيل الأزرار
                SaveButton.IsEnabled = true;
                CancelButton.IsEnabled = true;
                SaveButton.Content = "حفظ التعديلات";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
