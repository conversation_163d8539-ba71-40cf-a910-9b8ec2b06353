using System;
using System.Globalization;
using System.Windows.Data;

namespace AqlanCenterProApp.Converters
{
    /// <summary>
    /// Converter للتحقق من عدم وجود null
    /// </summary>
    public class NotNullConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 