using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// التقرير المالي
    /// </summary>
    public class FinancialReport : ReportBase
    {
        public FinancialReport()
        {
            Type = ReportType.FinancialReport;
        }

        // الإيرادات
        public decimal TotalRevenue { get; set; }
        public decimal CashRevenue { get; set; }
        public decimal CardRevenue { get; set; }
        public decimal InsuranceRevenue { get; set; }
        public decimal OtherRevenue { get; set; }

        // المصروفات
        public decimal TotalExpenses { get; set; }
        public decimal SalariesExpenses { get; set; }
        public decimal RentExpenses { get; set; }
        public decimal UtilitiesExpenses { get; set; }
        public decimal SuppliesExpenses { get; set; }
        public decimal LabExpenses { get; set; }
        public decimal MarketingExpenses { get; set; }
        public decimal OtherExpenses { get; set; }

        // الربح/الخسارة
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public double ProfitMargin { get; set; }

        // الديون
        public decimal TotalReceivables { get; set; }
        public decimal OverdueReceivables { get; set; }
        public int OverdueInvoicesCount { get; set; }

        // الإيرادات حسب الطبيب
        public List<DoctorRevenue> DoctorRevenues { get; set; } = new();

        // الإيرادات حسب الخدمة
        public List<ServiceRevenue> ServiceRevenues { get; set; } = new();

        // الإيرادات حسب الشهر
        public List<MonthlyRevenue> MonthlyRevenues { get; set; } = new();

        // الإيرادات حسب اليوم
        public List<DailyRevenue> DailyRevenues { get; set; } = new();

        // المصروفات حسب الفئة
        public List<ExpenseCategory> ExpenseCategories { get; set; } = new();

        // المقارنة مع الفترات السابقة
        public decimal PreviousPeriodRevenue { get; set; }
        public decimal RevenueGrowth { get; set; }
        public double RevenueGrowthPercentage { get; set; }
    }

    /// <summary>
    /// إيرادات الطبيب
    /// </summary>
    public class DoctorRevenue
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public int PatientsCount { get; set; }
        public int AppointmentsCount { get; set; }
        public decimal AverageRevenuePerPatient { get; set; }
        public decimal AverageRevenuePerAppointment { get; set; }
    }

    /// <summary>
    /// إيرادات الخدمة
    /// </summary>
    public class ServiceRevenue
    {
        public string ServiceName { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public int ServiceCount { get; set; }
        public decimal AveragePrice { get; set; }
        public double RevenuePercentage { get; set; }
    }

    /// <summary>
    /// الإيرادات الشهرية
    /// </summary>
    public class MonthlyRevenue
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal Profit { get; set; }
        public int PatientsCount { get; set; }
    }

    /// <summary>
    /// الإيرادات اليومية
    /// </summary>
    public class DailyRevenue
    {
        public DateTime Date { get; set; }
        public string DayName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal Profit { get; set; }
        public int PatientsCount { get; set; }
        public int AppointmentsCount { get; set; }
    }

    /// <summary>
    /// فئة المصروفات
    /// </summary>
    public class ExpenseCategory
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public double Percentage { get; set; }
        public int TransactionsCount { get; set; }
    }
} 