using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class Shade : BaseEntity
    {
        [Key]
        public int ShadeId { get; set; }

        [Required(ErrorMessage = "اسم درجة اللون مطلوب")]
        [StringLength(50, ErrorMessage = "اسم درجة اللون لا يمكن أن يتجاوز 50 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "الوصف لا يمكن أن يتجاوز 100 حرف")]
        public string? Description { get; set; }

        [StringLength(20, ErrorMessage = "رمز اللون لا يمكن أن يتجاوز 20 حرف")]
        public string? ColorCode { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<LabOrder> LabOrders { get; set; } = new List<LabOrder>();
    }
} 