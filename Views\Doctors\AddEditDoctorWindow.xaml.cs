using System;
using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Doctors;
using AqlanCenterProApp.Services;
using AqlanCenterProApp.Data;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Views.Doctors
{
    /// <summary>
    /// Interaction logic for AddEditDoctorWindow.xaml
    /// </summary>
    public partial class AddEditDoctorWindow : Window
    {
        public AddEditDoctorWindow()
        {
            InitializeComponent();

            try
            {
                // إنشاء ViewModel للإضافة بطريقة آمنة
                var viewModel = CreateViewModel();
                viewModel.Initialize();
                DataContext = viewModel;

                // ربط الأحداث
                if (DataContext is AddEditDoctorViewModel vm)
                {
                    vm.CloseRequested += (sender, result) =>
                    {
                        DialogResult = result;
                        Close();
                    };
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                Close();
            }
        }

        private AddEditDoctorViewModel CreateViewModel()
        {
            // استخدم نفس سلسلة الاتصال الموحدة كما في بقية النظام
            var connectionString = @"Data Source=D:\AqlanCenterProApp\AqlanCenterDatabase.db";
            var optionsBuilder = new Microsoft.EntityFrameworkCore.DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);

            var context = new AqlanCenterDbContext(optionsBuilder.Options);
            var doctorService = new DoctorService(context);
            return new AddEditDoctorViewModel(doctorService);
        }

        public AddEditDoctorWindow(Doctor doctor) : this()
        {
            try
            {
                // إنشاء ViewModel للتعديل
                if (DataContext is AddEditDoctorViewModel viewModel)
                {
                    viewModel.Initialize(doctor);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل بيانات الطبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
