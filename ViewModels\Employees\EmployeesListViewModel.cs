using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Employees
{
    /// <summary>
    /// ViewModel لإدارة قائمة الموظفين
    /// </summary>
    public class EmployeesListViewModel : BaseViewModel
    {
        private readonly IEmployeeService _employeeService = null!;

        #region Properties

        /// <summary>
        /// قائمة الموظفين
        /// </summary>
        public ObservableCollection<Employee> Employees { get; } = new();

        private Employee? _selectedEmployee;
        private string _searchText = string.Empty;
        private bool _isLoading;

        /// <summary>
        /// الموظف المحدد
        /// </summary>
        public Employee? SelectedEmployee
        {
            get => _selectedEmployee;
            set
            {
                if (SetProperty(ref _selectedEmployee, value))
                {
                    // تحديث حالة الأوامر عند تغيير الموظف المحدد
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// حالة التحميل
        /// </summary>
        public new bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحديث القائمة
        /// </summary>
        public ICommand RefreshCommand { get; private set; } = null!;

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; private set; } = null!;

        /// <summary>
        /// أمر إضافة موظف جديد
        /// </summary>
        public ICommand AddEmployeeCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تعديل الموظف
        /// </summary>
        public ICommand EditEmployeeCommand { get; private set; } = null!;

        /// <summary>
        /// أمر حذف الموظف
        /// </summary>
        public ICommand DeleteEmployeeCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض تفاصيل الموظف
        /// </summary>
        public ICommand ViewDetailsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر طباعة قائمة الموظفين
        /// </summary>
        public ICommand PrintCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تصدير البيانات
        /// </summary>
        public ICommand ExportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الحضور
        /// </summary>
        public ICommand ViewAttendanceCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الرواتب
        /// </summary>
        public ICommand ViewSalariesCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الإجازات
        /// </summary>
        public ICommand ViewLeavesCommand { get; private set; } = null!;

        /// <summary>
        /// أمر طلب إجازة
        /// </summary>
        public ICommand RequestLeaveCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض المستندات
        /// </summary>
        public ICommand ViewDocumentsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر نسخ البيانات
        /// </summary>
        public ICommand CopyCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الإحصائيات
        /// </summary>
        public ICommand ShowStatisticsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض التقارير
        /// </summary>
        public ICommand ShowReportsCommand { get; private set; } = null!;

        #endregion

        #region Constructor

        public EmployeesListViewModel(IEmployeeService employeeService)
        {
            try
            {
                _employeeService = employeeService;

                // تهيئة الأوامر
                RefreshCommand = new RelayCommand(async () => await RefreshAsync());
                SearchCommand = new RelayCommand(async () => await SearchAsync());
                AddEmployeeCommand = new RelayCommand(AddEmployee);
                EditEmployeeCommand = new RelayCommand(EditEmployee, () => SelectedEmployee != null);
                DeleteEmployeeCommand = new RelayCommand(DeleteEmployee, () => SelectedEmployee != null);
                ViewDetailsCommand = new RelayCommand(ViewDetails, () => SelectedEmployee != null);
                PrintCommand = new RelayCommand(Print);
                ExportCommand = new RelayCommand(Export);
                ViewAttendanceCommand = new RelayCommand(ViewAttendance, () => SelectedEmployee != null);
                ViewSalariesCommand = new RelayCommand(ViewSalaries, () => SelectedEmployee != null);
                ViewLeavesCommand = new RelayCommand(ViewLeaves, () => SelectedEmployee != null);
                RequestLeaveCommand = new RelayCommand(RequestLeave, () => SelectedEmployee != null);
                ViewDocumentsCommand = new RelayCommand(ViewDocuments, () => SelectedEmployee != null);
                CopyCommand = new RelayCommand(Copy, () => SelectedEmployee != null);
                ShowStatisticsCommand = new RelayCommand(ShowStatistics);
                ShowReportsCommand = new RelayCommand(ShowReports);

                Console.WriteLine("EmployeesListViewModel initialized successfully");

                // تحميل البيانات
                _ = Task.Run(async () => await RefreshAsync());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تهيئة EmployeesListViewModel: {ex.Message}");
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحديث قائمة الموظفين
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                IsLoading = true;
                // تحميل البيانات في الخلفية
                var employees = await _employeeService.GetAllEmployeesAsync();

                // تحديث القائمة في الخيط الرئيسي
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Employees.Clear();
                    foreach (var employee in employees)
                    {
                        Employees.Add(employee);
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// البحث في قائمة الموظفين
        /// </summary>
        private async Task SearchAsync()
        {
            try
            {
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    await Application.Current.Dispatcher.InvokeAsync(async () => await SearchAsync());
                    return;
                }

                IsLoading = true;

                var employees = await Task.Run(async () =>
                    await _employeeService.SearchEmployeesAsync(SearchText));

                Employees.Clear();
                foreach (var employee in employees)
                {
                    Employees.Add(employee);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SearchAsync: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إضافة موظف جديد
        /// </summary>
        private void AddEmployee()
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ إضافة موظف جديد قريباً", "إضافة موظف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في AddEmployee: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل الموظف المحدد
        /// </summary>
        private void EditEmployee()
        {
            if (SelectedEmployee == null) return;

            try
            {
                MessageBox.Show($"سيتم تنفيذ تعديل الموظف '{SelectedEmployee.FullName}' قريباً", "تعديل موظف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في EditEmployee: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف الموظف المحدد
        /// </summary>
        private void DeleteEmployee()
        {
            if (SelectedEmployee == null) return;

            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الموظف '{SelectedEmployee.FullName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("سيتم تنفيذ الحذف قريباً", "حذف موظف", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في DeleteEmployee: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض تفاصيل الموظف
        /// </summary>
        private void ViewDetails()
        {
            if (SelectedEmployee == null) return;

            try
            {
                MessageBox.Show($"تفاصيل الموظف:\n\nالاسم: {SelectedEmployee.FullName}\nالقسم: {SelectedEmployee.Department}\nالمنصب: {SelectedEmployee.Position}\nالحالة: {SelectedEmployee.Status}",
                    "تفاصيل الموظف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ViewDetails: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة قائمة الموظفين
        /// </summary>
        private void Print()
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في Print: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export()
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ التصدير قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في Export: {ex.Message}");
            }
        }

        // باقي الدوال البسيطة
        private void ViewAttendance() => MessageBox.Show("سيتم تنفيذ عرض الحضور قريباً", "الحضور", MessageBoxButton.OK, MessageBoxImage.Information);
        private void ViewSalaries() => MessageBox.Show("سيتم تنفيذ عرض الرواتب قريباً", "الرواتب", MessageBoxButton.OK, MessageBoxImage.Information);
        private void ViewLeaves() => MessageBox.Show("سيتم تنفيذ عرض الإجازات قريباً", "الإجازات", MessageBoxButton.OK, MessageBoxImage.Information);
        private void RequestLeave() => MessageBox.Show("سيتم تنفيذ طلب إجازة قريباً", "طلب إجازة", MessageBoxButton.OK, MessageBoxImage.Information);
        private void ViewDocuments() => MessageBox.Show("سيتم تنفيذ عرض المستندات قريباً", "المستندات", MessageBoxButton.OK, MessageBoxImage.Information);
        private void Copy() => MessageBox.Show("سيتم تنفيذ النسخ قريباً", "نسخ", MessageBoxButton.OK, MessageBoxImage.Information);
        private void ShowStatistics() => MessageBox.Show("سيتم تنفيذ عرض الإحصائيات قريباً", "الإحصائيات", MessageBoxButton.OK, MessageBoxImage.Information);
        private void ShowReports() => MessageBox.Show("سيتم تنفيذ عرض التقارير قريباً", "التقارير", MessageBoxButton.OK, MessageBoxImage.Information);

        #endregion
    }
}
