using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Data
{
    public class AqlanCenterDbContext : DbContext
    {
        public AqlanCenterDbContext(DbContextOptions<AqlanCenterDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Patient> Patients { get; set; }
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<EmployeeAttendance> EmployeeAttendances { get; set; }
        public DbSet<EmployeeSalary> EmployeeSalaries { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeeDocument> EmployeeDocuments { get; set; }
        public DbSet<Session> Sessions { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Receipt> Receipts { get; set; }
        public DbSet<PaymentVoucher> PaymentVouchers { get; set; }
        public DbSet<AccountStatement> AccountStatements { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<OrthodonticPlan> OrthodonticPlans { get; set; }
        public DbSet<Lab> Labs { get; set; }
        public DbSet<LabOrder> LabOrders { get; set; }
        public DbSet<ProsthesisType> ProsthesisTypes { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Purchase> Purchases { get; set; }
        public DbSet<PurchaseItem> PurchaseItems { get; set; }
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }
        public DbSet<PatientFile> PatientFiles { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<Shade> Shades { get; set; }
        public DbSet<BackupInfo> BackupInfos { get; set; }
        public DbSet<BackupSettings> BackupSettings { get; set; }
        
        // Settings DbSets
        public DbSet<ClinicSettings> ClinicSettings { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }
        public DbSet<NotificationSettings> NotificationSettings { get; set; }
        public DbSet<SystemLookup> SystemLookups { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure global query filters
            ConfigureGlobalQueryFilters(modelBuilder);

            // Configure relationships
            ConfigurePatientRelationships(modelBuilder);
            ConfigureDoctorRelationships(modelBuilder);
            ConfigureEmployeeRelationships(modelBuilder);
            ConfigureInvoiceRelationships(modelBuilder);
            ConfigureLabRelationships(modelBuilder);
            ConfigurePurchaseRelationships(modelBuilder);
            ConfigureUserRelationships(modelBuilder);
            ConfigureBillingRelationships(modelBuilder);

            // Configure indexes
            ConfigureIndexes(modelBuilder);
        }

        private void ConfigureGlobalQueryFilters(ModelBuilder modelBuilder)
        {
            // Apply soft delete filter to all entities that inherit from BaseEntity
            modelBuilder.Entity<Patient>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Doctor>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Employee>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Session>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Payment>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Invoice>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<InvoiceItem>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Receipt>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PaymentVoucher>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<AccountStatement>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Appointment>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<OrthodonticPlan>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Lab>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<LabOrder>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ProsthesisType>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Supplier>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Purchase>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PurchaseItem>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<InventoryItem>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Role>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Permission>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<UserPermission>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<PatientFile>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Notification>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BackupInfo>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BackupSettings>().HasQueryFilter(e => !e.IsDeleted);
            
            // Settings query filters
            modelBuilder.Entity<ClinicSettings>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<SystemSettings>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<NotificationSettings>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<SystemLookup>().HasQueryFilter(e => !e.IsDeleted);
        }

        private void ConfigurePatientRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Patient>()
                .HasMany(p => p.Sessions)
                .WithOne(s => s.Patient)
                .HasForeignKey(s => s.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Patient>()
                .HasMany(p => p.Payments)
                .WithOne(p => p.Patient)
                .HasForeignKey(p => p.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Patient>()
                .HasMany(p => p.Appointments)
                .WithOne(a => a.Patient)
                .HasForeignKey(a => a.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Patient>()
                .HasMany(p => p.PatientFiles)
                .WithOne(pf => pf.Patient)
                .HasForeignKey(pf => pf.PatientId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureDoctorRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Doctor>()
                .HasMany(d => d.Appointments)
                .WithOne(a => a.Doctor)
                .HasForeignKey(a => a.DoctorId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureEmployeeRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Employee>()
                .HasMany(e => e.Attendances)
                .WithOne(a => a.Employee)
                .HasForeignKey(a => a.EmployeeId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureInvoiceRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Invoice>()
                .HasMany(i => i.InvoiceItems)
                .WithOne(ii => ii.Invoice)
                .HasForeignKey(ii => ii.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Invoice>()
                .HasMany(i => i.Payments)
                .WithOne(p => p.Invoice)
                .HasForeignKey(p => p.InvoiceId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureBillingRelationships(ModelBuilder modelBuilder)
        {
            // Receipt relationships
            modelBuilder.Entity<Receipt>()
                .HasOne(r => r.Patient)
                .WithMany()
                .HasForeignKey(r => r.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Receipt>()
                .HasOne(r => r.Session)
                .WithMany()
                .HasForeignKey(r => r.SessionId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Receipt>()
                .HasOne(r => r.Invoice)
                .WithMany()
                .HasForeignKey(r => r.InvoiceId)
                .OnDelete(DeleteBehavior.SetNull);

            // PaymentVoucher relationships
            modelBuilder.Entity<PaymentVoucher>()
                .HasOne(pv => pv.Employee)
                .WithMany()
                .HasForeignKey(pv => pv.EmployeeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<PaymentVoucher>()
                .HasOne(pv => pv.Supplier)
                .WithMany()
                .HasForeignKey(pv => pv.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<PaymentVoucher>()
                .HasOne(pv => pv.Lab)
                .WithMany()
                .HasForeignKey(pv => pv.LabId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureLabRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Lab>()
                .HasMany(l => l.LabOrders)
                .WithOne(lo => lo.Lab)
                .HasForeignKey(lo => lo.LabId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProsthesisType>()
                .HasMany(pt => pt.LabOrders)
                .WithOne(lo => lo.ProsthesisType)
                .HasForeignKey(lo => lo.ProsthesisTypeId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigurePurchaseRelationships(ModelBuilder modelBuilder)
        {
            // Purchase relationships
            modelBuilder.Entity<Purchase>()
                .HasMany(p => p.PurchaseItems)
                .WithOne(pi => pi.Purchase)
                .HasForeignKey(pi => pi.PurchaseId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Purchase>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Purchases)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            // Supplier relationships
            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.Purchases)
                .WithOne(p => p.Supplier)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Supplier>()
                .HasMany(s => s.PaymentVouchers)
                .WithOne(pv => pv.Supplier)
                .HasForeignKey(pv => pv.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            // InventoryItem relationships
            modelBuilder.Entity<InventoryItem>()
                .HasMany(ii => ii.PurchaseItems)
                .WithOne(pi => pi.InventoryItem)
                .HasForeignKey(pi => pi.InventoryItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryItem>()
                .HasMany(ii => ii.InvoiceItems)
                .WithOne(ivi => ivi.InventoryItem)
                .HasForeignKey(ivi => ivi.InventoryItemId)
                .OnDelete(DeleteBehavior.Restrict);

            // PurchaseItem relationships
            modelBuilder.Entity<PurchaseItem>()
                .HasOne(pi => pi.Purchase)
                .WithMany(p => p.PurchaseItems)
                .HasForeignKey(pi => pi.PurchaseId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PurchaseItem>()
                .HasOne(pi => pi.InventoryItem)
                .WithMany(ii => ii.PurchaseItems)
                .HasForeignKey(pi => pi.InventoryItemId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureUserRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>()
                .HasOne(u => u.Role)
                .WithMany()
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

            // UserPermission relationships
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.UserPermissions)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany()
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Unique constraint for UserPermission
            modelBuilder.Entity<UserPermission>()
                .HasIndex(up => new { up.UserId, up.PermissionId })
                .IsUnique();
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Patient indexes
            modelBuilder.Entity<Patient>()
                .HasIndex(p => p.FileNumber)
                .IsUnique();

            modelBuilder.Entity<Patient>()
                .HasIndex(p => p.Phone);

            // Invoice indexes
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceDate);

            // Receipt indexes
            modelBuilder.Entity<Receipt>()
                .HasIndex(r => r.ReceiptNumber)
                .IsUnique();

            modelBuilder.Entity<Receipt>()
                .HasIndex(r => r.ReceiptDate);

            // PaymentVoucher indexes
            modelBuilder.Entity<PaymentVoucher>()
                .HasIndex(pv => pv.VoucherNumber)
                .IsUnique();

            modelBuilder.Entity<PaymentVoucher>()
                .HasIndex(pv => pv.VoucherDate);

            // AccountStatement indexes
            modelBuilder.Entity<AccountStatement>()
                .HasIndex(as_ => as_.StatementNumber)
                .IsUnique();

            modelBuilder.Entity<AccountStatement>()
                .HasIndex(as_ => as_.StatementDate);

            // Purchase indexes
            modelBuilder.Entity<Purchase>()
                .HasIndex(p => p.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<Purchase>()
                .HasIndex(p => p.PurchaseDate);

            modelBuilder.Entity<Purchase>()
                .HasIndex(p => p.SupplierId);

            // Supplier indexes
            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.Name);

            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.Phone);

            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.TaxNumber)
                .IsUnique();

            // InventoryItem indexes
            modelBuilder.Entity<InventoryItem>()
                .HasIndex(ii => ii.Code)
                .IsUnique();

            modelBuilder.Entity<InventoryItem>()
                .HasIndex(ii => ii.Barcode)
                .IsUnique();

            modelBuilder.Entity<InventoryItem>()
                .HasIndex(ii => ii.Name);

            modelBuilder.Entity<InventoryItem>()
                .HasIndex(ii => ii.Category);

            modelBuilder.Entity<InventoryItem>()
                .HasIndex(ii => ii.ExpiryDate);
        }

        /// <summary>
        /// إضافة الحقول الجديدة إلى قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        public async Task EnsureCurrencyFieldsExistAsync()
        {
            try
            {
                // التحقق من وجود الحقول الجديدة
                var connection = Database.GetDbConnection();
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Doctors)";

                var reader = await command.ExecuteReaderAsync();
                var columns = new List<string>();

                while (await reader.ReadAsync())
                {
                    columns.Add(reader.GetString(1)); // اسم العمود
                }

                await reader.CloseAsync();

                // إضافة الحقول إذا لم تكن موجودة
                if (!columns.Contains("CommissionCurrency"))
                {
                    command.CommandText = "ALTER TABLE Doctors ADD COLUMN CommissionCurrency TEXT DEFAULT 'ر.ي'";
                    await command.ExecuteNonQueryAsync();
                }

                if (!columns.Contains("SalaryCurrency"))
                {
                    command.CommandText = "ALTER TABLE Doctors ADD COLUMN SalaryCurrency TEXT DEFAULT 'ر.ي'";
                    await command.ExecuteNonQueryAsync();
                }

                // تحديث البيانات الموجودة
                command.CommandText = "UPDATE Doctors SET CommissionCurrency = 'ر.ي' WHERE CommissionCurrency IS NULL";
                await command.ExecuteNonQueryAsync();

                command.CommandText = "UPDATE Doctors SET SalaryCurrency = 'ر.ي' WHERE SalaryCurrency IS NULL";
                await command.ExecuteNonQueryAsync();

                await connection.CloseAsync();
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء إذا كانت الحقول موجودة بالفعل
                Console.WriteLine($"تحذير: {ex.Message}");
            }
        }
    }
}
