<Window x:Class="AqlanCenterProApp.Views.Settings.SettingsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="SettingsTabStyle" TargetType="TabItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="0,0,5,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="Border" Background="{TemplateBinding Background}" 
                                BorderBrush="#DDD" BorderThickness="1" CornerRadius="5,5,0,0">
                            <ContentPresenter ContentSource="Header" HorizontalAlignment="Center" 
                                            VerticalAlignment="Center" Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#007ACC"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#E6F3FF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SettingsGroupStyle" TargetType="GroupBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333"/>
        </Style>
        
        <Style x:Key="SettingsButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="⚙️" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات النظام" FontSize="20" FontWeight="Bold" 
                             Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="💾 حفظ الكل" Style="{StaticResource SettingsButtonStyle}"
                            Command="{Binding SaveAllCommand}" Margin="0,0,10,0"/>
                    <Button Content="❌ إغلاق" Style="{StaticResource SettingsButtonStyle}"
                            Background="#DC3545" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="10" Background="Transparent">
            <TabControl.ItemContainerStyle>
                <StaticResource ResourceKey="SettingsTabStyle"/>
            </TabControl.ItemContainerStyle>
            
            <!-- بيانات العيادة -->
            <TabItem Header="🏥 بيانات العيادة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- معلومات أساسية -->
                        <GroupBox Grid.Row="0" Header="المعلومات الأساسية" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="اسم العيادة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.ClinicName}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="اسم المدير" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.ManagerName}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="رقم الهاتف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.PhoneNumber}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="رقم الهاتف المحمول" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.MobileNumber}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.Email}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="الموقع الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.Website}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="رقم الترخيص" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.LicenseNumber}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="3" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="رقم الضريبي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.TaxNumber}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- العنوان -->
                        <GroupBox Grid.Row="1" Header="العنوان" Style="{StaticResource SettingsGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="العنوان الكامل" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding ClinicSettings.Address}" 
                                       FontSize="14" Padding="10,8" Height="60" 
                                       TextWrapping="Wrap" AcceptsReturn="True"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- ملاحظات الطباعة -->
                        <GroupBox Grid.Row="2" Header="ملاحظات الطباعة" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="ملاحظة الفواتير" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.InvoiceNote}" 
                                           FontSize="14" Padding="10,8" Height="80" 
                                           TextWrapping="Wrap" AcceptsReturn="True"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="ملاحظة الإيصالات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding ClinicSettings.ReceiptNote}" 
                                           FontSize="14" Padding="10,8" Height="80" 
                                           TextWrapping="Wrap" AcceptsReturn="True"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- أزرار الحفظ -->
                        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                            <Button Content="💾 حفظ بيانات العيادة" Style="{StaticResource SettingsButtonStyle}"
                                    Command="{Binding SaveClinicSettingsCommand}"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            
            <!-- الإعدادات العامة -->
            <TabItem Header="⚙️ الإعدادات العامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- إعدادات النظام -->
                        <GroupBox Grid.Row="0" Header="إعدادات النظام" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="عملة النظام *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.Currency}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="رمز العملة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.CurrencySymbol}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="نسق الألوان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <ComboBox ItemsSource="{Binding ColorSchemes}" 
                                            SelectedItem="{Binding SystemSettings.ColorScheme}"
                                            FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="الخط الافتراضي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <ComboBox ItemsSource="{Binding Fonts}" 
                                            SelectedItem="{Binding SystemSettings.DefaultFont}"
                                            FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="حجم الخط" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <Slider Value="{Binding SystemSettings.DefaultFontSize}" 
                                          Minimum="8" Maximum="24" TickFrequency="1" 
                                          IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding SystemSettings.DefaultFontSize}" 
                                           HorizontalAlignment="Center" FontWeight="Bold"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="اللغة الافتراضية" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <ComboBox ItemsSource="{Binding Languages}" 
                                            SelectedItem="{Binding ClinicSettings.DefaultLanguage}"
                                            FontSize="14" Padding="10,8"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات الأمان -->
                        <GroupBox Grid.Row="1" Header="إعدادات الأمان" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <CheckBox Content="تطلب كلمة مرور للإعدادات" 
                                            IsChecked="{Binding SystemSettings.RequirePasswordForSettings}"
                                            Margin="0,5"/>
                                    <CheckBox Content="تفعيل سجل التدقيق" 
                                            IsChecked="{Binding SystemSettings.EnableAuditLog}"
                                            Margin="0,5"/>
                                    <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي" 
                                            IsChecked="{Binding SystemSettings.EnableAutoBackup}"
                                            Margin="0,5"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="مهلة الجلسة (دقائق)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.SessionTimeoutMinutes}" 
                                           FontSize="14" Padding="10,8"/>
                                    
                                    <TextBlock Text="فترة النسخ الاحتياطي (ساعات)" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.AutoBackupIntervalHours}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات الطباعة -->
                        <GroupBox Grid.Row="2" Header="إعدادات الطباعة" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="ترويسة الفواتير" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.InvoiceHeader}" 
                                           FontSize="14" Padding="10,8"/>
                                    
                                    <TextBlock Text="ترويسة الإيصالات" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.ReceiptHeader}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <CheckBox Content="إظهار الشعار في الطباعة" 
                                            IsChecked="{Binding SystemSettings.ShowLogoOnPrint}"
                                            Margin="0,5"/>
                                    <CheckBox Content="إظهار التوقيع في الطباعة" 
                                            IsChecked="{Binding SystemSettings.ShowSignatureOnPrint}"
                                            Margin="0,5"/>
                                    
                                    <TextBlock Text="ملاحظة الطباعة" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.PrintFooter}" 
                                           FontSize="14" Padding="10,8" Height="60" 
                                           TextWrapping="Wrap" AcceptsReturn="True"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات النسخ الاحتياطي -->
                        <GroupBox Grid.Row="3" Header="إعدادات النسخ الاحتياطي" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="مسار النسخ الاحتياطي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding SystemSettings.BackupPath}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Bottom">
                                    <Button Content="🔍 اختبار المسار" Style="{StaticResource SettingsButtonStyle}"
                                            Command="{Binding TestBackupPathCommand}"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- أزرار الحفظ -->
                        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                            <Button Content="💾 حفظ الإعدادات العامة" Style="{StaticResource SettingsButtonStyle}"
                                    Command="{Binding SaveSystemSettingsCommand}"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            
            <!-- إعدادات الإشعارات -->
            <TabItem Header="🔔 الإشعارات">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- الإشعارات الداخلية -->
                        <GroupBox Grid.Row="0" Header="الإشعارات الداخلية" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <CheckBox Content="تفعيل الإشعارات الداخلية" 
                                            IsChecked="{Binding NotificationSettings.EnableInternalNotifications}"
                                            Margin="0,5"/>
                                    <CheckBox Content="إشعارات المواعيد الجديدة" 
                                            IsChecked="{Binding NotificationSettings.NotifyNewAppointments}"
                                            Margin="0,5"/>
                                    <CheckBox Content="تذكيرات المواعيد" 
                                            IsChecked="{Binding NotificationSettings.NotifyAppointmentReminders}"
                                            Margin="0,5"/>
                                    <CheckBox Content="إشعارات المرضى الجدد" 
                                            IsChecked="{Binding NotificationSettings.NotifyNewPatients}"
                                            Margin="0,5"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <CheckBox Content="إشعارات المخزون المنخفض" 
                                            IsChecked="{Binding NotificationSettings.NotifyLowInventory}"
                                            Margin="0,5"/>
                                    <CheckBox Content="إشعارات المدفوعات المستحقة" 
                                            IsChecked="{Binding NotificationSettings.NotifyPaymentDue}"
                                            Margin="0,5"/>
                                    <CheckBox Content="إشعارات أخطاء النظام" 
                                            IsChecked="{Binding NotificationSettings.NotifySystemErrors}"
                                            Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات واتساب -->
                        <GroupBox Grid.Row="1" Header="إعدادات واتساب" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <CheckBox Grid.Row="0" Grid.ColumnSpan="2" Content="تفعيل إشعارات واتساب" 
                                        IsChecked="{Binding NotificationSettings.EnableWhatsAppNotifications}"
                                        Margin="0,5"/>
                                
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="رقم واتساب" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding NotificationSettings.WhatsAppNumber}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="رابط API" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding NotificationSettings.WhatsAppApiUrl}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="مفتاح API" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <PasswordBox x:Name="WhatsAppApiKeyBox" FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,0">
                                    <CheckBox Content="تذكيرات المواعيد" 
                                            IsChecked="{Binding NotificationSettings.WhatsAppAppointmentReminders}"
                                            Margin="0,5"/>
                                    <CheckBox Content="تذكيرات المدفوعات" 
                                            IsChecked="{Binding NotificationSettings.WhatsAppPaymentReminders}"
                                            Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- إعدادات SMS -->
                        <GroupBox Grid.Row="2" Header="إعدادات SMS" Style="{StaticResource SettingsGroupStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <CheckBox Grid.Row="0" Grid.ColumnSpan="2" Content="تفعيل إشعارات SMS" 
                                        IsChecked="{Binding NotificationSettings.EnableSmsNotifications}"
                                        Margin="0,5"/>
                                
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="مزود الخدمة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding NotificationSettings.SmsProvider}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="رابط API" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding NotificationSettings.SmsApiUrl}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="اسم المستخدم" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBox Text="{Binding NotificationSettings.SmsUsername}" 
                                           FontSize="14" Padding="10,8"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="كلمة المرور" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <PasswordBox x:Name="SmsPasswordBox" FontSize="14" Padding="10,8"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <!-- أزرار الحفظ -->
                        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                            <Button Content="💾 حفظ إعدادات الإشعارات" Style="{StaticResource SettingsButtonStyle}"
                                    Command="{Binding SaveNotificationSettingsCommand}"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            
            <!-- القوائم المساعدة -->
            <TabItem Header="🏷️ القوائم المساعدة">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- شريط الأدوات -->
                    <Border Grid.Row="0" Background="White" BorderBrush="#DDD" BorderThickness="1" 
                          CornerRadius="5" Padding="15" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock Text="نوع القائمة:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <ComboBox ItemsSource="{Binding LookupTypes}" 
                                        SelectedItem="{Binding SelectedLookupType}"
                                        Width="200" FontSize="14" Padding="10,8"/>
                            </StackPanel>
                            
                            <Button Grid.Column="1" Content="➕ إضافة عنصر" Style="{StaticResource SettingsButtonStyle}"
                                    Command="{Binding AddLookupCommand}"/>
                        </Grid>
                    </Border>
                    
                    <!-- قائمة العناصر -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding Lookups}" 
                            AutoGenerateColumns="False" CanUserAddRows="False"
                            Background="White" BorderBrush="#DDD" BorderThickness="1"
                            GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="الترتيب" Binding="{Binding SortOrder}" Width="80"/>
                            <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="✏️" Style="{StaticResource SettingsButtonStyle}"
                                                    Background="#28A745" Padding="8,4" Margin="2"
                                                    Command="{Binding DataContext.EditLookupCommand, 
                                                              RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"/>
                                            <Button Content="🗑️" Style="{StaticResource SettingsButtonStyle}"
                                                    Background="#DC3545" Padding="8,4" Margin="2"
                                                    Command="{Binding DataContext.DeleteLookupCommand, 
                                                              RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- أدوات النظام -->
            <TabItem Header="🛠️ أدوات النظام">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- استيراد/تصدير -->
                        <GroupBox Grid.Row="0" Header="استيراد/تصدير الإعدادات" Style="{StaticResource SettingsGroupStyle}">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="📤 تصدير الإعدادات" Style="{StaticResource SettingsButtonStyle}"
                                        Command="{Binding ExportSettingsCommand}" Margin="0,0,10,0"/>
                                <Button Content="📥 استيراد الإعدادات" Style="{StaticResource SettingsButtonStyle}"
                                        Command="{Binding ImportSettingsCommand}" Margin="10,0,0,0"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- إعادة تعيين -->
                        <GroupBox Grid.Row="1" Header="إعادة تعيين النظام" Style="{StaticResource SettingsGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="تحذير: سيتم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية" 
                                         Foreground="#DC3545" FontWeight="Bold" Margin="0,0,0,10"/>
                                <Button Content="🔄 إعادة تعيين الإعدادات" Style="{StaticResource SettingsButtonStyle}"
                                        Background="#DC3545" Command="{Binding ResetToDefaultsCommand}"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- التحديثات -->
                        <GroupBox Grid.Row="2" Header="تحديث النظام" Style="{StaticResource SettingsGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="النسخة الحالية:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SystemSettings.CurrentVersion}" 
                                         FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                                <Button Content="🔍 التحقق من التحديثات" Style="{StaticResource SettingsButtonStyle}"
                                        Command="{Binding CheckForUpdatesCommand}"/>
                            </StackPanel>
                        </GroupBox>
                    </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- Loading Overlay -->
        <Grid Grid.Row="1" Background="#80000000" Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="جاري التحميل..." Foreground="White" FontSize="18" 
                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="4"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 