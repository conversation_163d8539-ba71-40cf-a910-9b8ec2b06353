using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace AqlanCenterProApp.Services.Implementations
{
    public class BackupService : IBackupService
    {
        private readonly AqlanCenterDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly IActivityLogService _activityLogService;
        private readonly IUserService _userService;
        private readonly string _backupDirectory;

        public BackupService(
            AqlanCenterDbContext context,
            IConfiguration configuration,
            IActivityLogService activityLogService,
            IUserService userService)
        {
            _context = context;
            _configuration = configuration;
            _activityLogService = activityLogService;
            _userService = userService;
            _backupDirectory = GetBackupDirectoryAsync().Result;
        }

        public async Task<BackupInfo> CreateBackupAsync(string description = "", string createdBy = "")
        {
            try
            {
                // التحقق من الصلاحيات
                if (!string.IsNullOrEmpty(createdBy) && !await HasBackupPermissionAsync(createdBy))
                {
                    throw new UnauthorizedAccessException("ليس لديك صلاحية لإنشاء نسخة احتياطية");
                }

                // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
                await CreateBackupDirectoryAsync();

                // الحصول على إعدادات النسخ الاحتياطي
                var settings = await GetBackupSettingsAsync();

                // إنشاء اسم الملف
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"AqlanCenter_Backup_{timestamp}.db";
                var filePath = Path.Combine(_backupDirectory, fileName);

                // إنشاء سجل النسخ الاحتياطي
                var backupInfo = new BackupInfo
                {
                    FilePath = filePath,
                    FileName = fileName,
                    BackupType = "Manual",
                    Description = description,
                    BackupDate = DateTime.Now,
                    Status = "InProgress",
                    IsCompressed = settings.EnableCompression,
                    IsEncrypted = settings.EnableEncryption
                };

                _context.BackupInfos.Add(backupInfo);
                await _context.SaveChangesAsync();

                // نسخ قاعدة البيانات
                var dbPath = GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    File.Copy(dbPath, filePath, true);
                }
                else
                {
                    throw new FileNotFoundException("ملف قاعدة البيانات غير موجود");
                }

                // حساب حجم الملف وعدد السجلات
                var fileInfo = new FileInfo(filePath);
                backupInfo.FileSize = fileInfo.Length;
                backupInfo.RecordsCount = await GetTotalRecordsCountAsync();

                // ضغط الملف إذا كان مطلوباً
                if (settings.EnableCompression)
                {
                    var compressedPath = filePath + ".gz";
                    if (await CompressBackupAsync(filePath, compressedPath))
                    {
                        File.Delete(filePath);
                        backupInfo.FilePath = compressedPath;
                        backupInfo.FileName = Path.GetFileName(compressedPath);
                        backupInfo.FileSize = new FileInfo(compressedPath).Length;
                    }
                }

                // تشفير الملف إذا كان مطلوباً
                if (settings.EnableEncryption && !string.IsNullOrEmpty(settings.EncryptionPassword))
                {
                    var encryptedPath = backupInfo.FilePath + ".enc";
                    if (await EncryptBackupAsync(backupInfo.FilePath, encryptedPath, settings.EncryptionPassword))
                    {
                        File.Delete(backupInfo.FilePath);
                        backupInfo.FilePath = encryptedPath;
                        backupInfo.FileName = Path.GetFileName(encryptedPath);
                        backupInfo.FileSize = new FileInfo(encryptedPath).Length;
                        backupInfo.EncryptionKey = settings.EncryptionPassword;
                    }
                }

                // حساب Checksum
                if (settings.EnableChecksumValidation)
                {
                    backupInfo.Checksum = await CalculateChecksumAsync(backupInfo.FilePath);
                }

                // تحديث الحالة
                backupInfo.Status = "Success";
                backupInfo.ExpiryDate = DateTime.Now.AddDays(settings.RetentionDays);

                await _context.SaveChangesAsync();

                // تسجيل النشاط
                var user = await _userService.GetUserByUsernameAsync(createdBy);
                await _activityLogService.LogActivityAsync(
                    user.Id,
                    "Create",
                    "BackupInfo",
                    backupInfo.Id,
                    $"تم إنشاء نسخة احتياطية: {backupInfo.FileName}"
                );

                // إرسال إشعار
                if (settings.EnableNotifications && settings.NotifyOnSuccess)
                {
                    await SendBackupNotificationAsync($"تم إنشاء نسخة احتياطية بنجاح: {backupInfo.FileName}", true);
                }

                return backupInfo;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                var user = await _userService.GetUserByUsernameAsync(createdBy);
                await _activityLogService.LogActivityAsync(
                    user.Id,
                    "Create",
                    "BackupInfo",
                    null,
                    $"فشل في إنشاء نسخة احتياطية: {ex.Message}"
                );

                throw;
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupFilePath, string restoredBy = "")
        {
            try
            {
                // التحقق من الصلاحيات
                if (!string.IsNullOrEmpty(restoredBy) && !await HasRestorePermissionAsync(restoredBy))
                {
                    throw new UnauthorizedAccessException("ليس لديك صلاحية لاسترجاع نسخة احتياطية");
                }

                // التحقق من وجود الملف
                if (!File.Exists(backupFilePath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                // التحقق من صحة الملف
                if (!await ValidateBackupFileAsync(backupFilePath))
                {
                    throw new InvalidOperationException("ملف النسخة الاحتياطية غير صالح");
                }

                var settings = await GetBackupSettingsAsync();
                var tempPath = backupFilePath;

                // فك التشفير إذا كان مشفراً
                if (backupFilePath.EndsWith(".enc") && !string.IsNullOrEmpty(settings.EncryptionPassword))
                {
                    var decryptedPath = backupFilePath.Replace(".enc", "_decrypted.db");
                    if (await DecryptBackupAsync(backupFilePath, decryptedPath, settings.EncryptionPassword))
                    {
                        tempPath = decryptedPath;
                    }
                }

                // فك الضغط إذا كان مضغوطاً
                if (backupFilePath.EndsWith(".gz"))
                {
                    var decompressedPath = backupFilePath.Replace(".gz", "_decompressed.db");
                    if (await DecompressBackupAsync(tempPath, decompressedPath))
                    {
                        if (tempPath != backupFilePath) File.Delete(tempPath);
                        tempPath = decompressedPath;
                    }
                }

                // إيقاف التطبيق مؤقتاً (في التطبيق الحقيقي)
                // Application.Current.Shutdown();

                // نسخ الملف إلى قاعدة البيانات
                var dbPath = GetDatabasePath();
                File.Copy(tempPath, dbPath, true);

                // تنظيف الملفات المؤقتة
                if (tempPath != backupFilePath && File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }

                // تسجيل النشاط
                var user = await _userService.GetUserByUsernameAsync(restoredBy);
                await _activityLogService.LogActivityAsync(
                    user.Id,
                    "Restore",
                    "BackupInfo",
                    null,
                    $"تم استرجاع نسخة احتياطية: {Path.GetFileName(backupFilePath)}"
                );

                // إرسال إشعار
                if (settings.EnableNotifications)
                {
                    await SendBackupNotificationAsync($"تم استرجاع النسخة الاحتياطية بنجاح: {Path.GetFileName(backupFilePath)}", true);
                }

                return true;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                var user = await _userService.GetUserByUsernameAsync(restoredBy);
                await _activityLogService.LogActivityAsync(
                    user.Id,
                    "Restore",
                    "BackupInfo",
                    null,
                    $"فشل في استرجاع نسخة احتياطية: {ex.Message}"
                );

                throw;
            }
        }

        public async Task<bool> VerifyBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                    return false;

                // التحقق من حجم الملف
                var fileInfo = new FileInfo(backupFilePath);
                if (fileInfo.Length == 0)
                    return false;

                // التحقق من Checksum إذا كان موجوداً
                var backupInfo = await _context.BackupInfos
                    .FirstOrDefaultAsync(b => b.FilePath == backupFilePath);

                if (backupInfo?.Checksum != null)
                {
                    var currentChecksum = await CalculateChecksumAsync(backupFilePath);
                    if (currentChecksum != backupInfo.Checksum)
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<BackupInfo>> GetAllBackupsAsync()
        {
            return await _context.BackupInfos
                .OrderByDescending(b => b.BackupDate)
                .ToListAsync();
        }

        public async Task<BackupInfo?> GetBackupByIdAsync(int id)
        {
            return await _context.BackupInfos.FindAsync(id);
        }

        public async Task<bool> DeleteBackupAsync(int id)
        {
            try
            {
                var backup = await _context.BackupInfos.FindAsync(id);
                if (backup == null)
                    return false;

                // حذف الملف
                if (File.Exists(backup.FilePath))
                {
                    File.Delete(backup.FilePath);
                }

                // حذف من قاعدة البيانات
                _context.BackupInfos.Remove(backup);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExpiredBackupsAsync()
        {
            try
            {
                var expiredBackups = await _context.BackupInfos
                    .Where(b => b.ExpiryDate.HasValue && b.ExpiryDate.Value < DateTime.Now)
                    .ToListAsync();

                foreach (var backup in expiredBackups)
                {
                    if (File.Exists(backup.FilePath))
                    {
                        File.Delete(backup.FilePath);
                    }
                }

                _context.BackupInfos.RemoveRange(expiredBackups);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<BackupSettings> GetBackupSettingsAsync()
        {
            var settings = await _context.BackupSettings.FirstOrDefaultAsync();
            if (settings == null)
            {
                await InitializeBackupSettingsAsync();
                settings = await _context.BackupSettings.FirstOrDefaultAsync();
            }
            return settings!;
        }

        public async Task<bool> UpdateBackupSettingsAsync(BackupSettings settings)
        {
            try
            {
                var existingSettings = await _context.BackupSettings.FirstOrDefaultAsync();
                if (existingSettings != null)
                {
                    _context.Entry(existingSettings).CurrentValues.SetValues(settings);
                }
                else
                {
                    _context.BackupSettings.Add(settings);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> InitializeBackupSettingsAsync()
        {
            try
            {
                var settings = new BackupSettings
                {
                    BackupDirectory = _backupDirectory,
                    EnableAutoBackup = false,
                    AutoBackupFrequency = "Daily",
                    AutoBackupHour = 2,
                    AutoBackupMinute = 0,
                    RetentionDays = 30,
                    EnableCompression = true,
                    EnableEncryption = false,
                    EnableNotifications = true,
                    NotifyOnSuccess = true,
                    NotifyOnFailure = true,
                    NotifyOnRetentionExpiry = true,
                    EnableCloudBackup = false,
                    MaxBackupSizeMB = 1000,
                    EnableBackupVerification = true,
                    EnableChecksumValidation = true
                };

                _context.BackupSettings.Add(settings);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ScheduleBackupAsync()
        {
            // في التطبيق الحقيقي، سيتم استخدام Windows Task Scheduler أو Quartz.NET
            // هنا نضع علامة في قاعدة البيانات
            var settings = await GetBackupSettingsAsync();
            settings.EnableAutoBackup = true;
            return await UpdateBackupSettingsAsync(settings);
        }

        public async Task<bool> CancelScheduledBackupAsync()
        {
            var settings = await GetBackupSettingsAsync();
            settings.EnableAutoBackup = false;
            return await UpdateBackupSettingsAsync(settings);
        }

        public async Task<bool> IsBackupScheduledAsync()
        {
            var settings = await GetBackupSettingsAsync();
            return settings.EnableAutoBackup;
        }

        public async Task<bool> ValidateBackupFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                    return false;

                // محاولة فتح الملف كقاعدة بيانات SQLite
                var connectionString = $"Data Source={filePath}";
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);
                return await context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> CalculateChecksumAsync(string filePath)
        {
            using var sha256 = SHA256.Create();
            using var stream = File.OpenRead(filePath);
            var hash = await Task.Run(() => sha256.ComputeHash(stream));
            return Convert.ToBase64String(hash);
        }

        public async Task<bool> CompressBackupAsync(string sourcePath, string destinationPath)
        {
            try
            {
                using var sourceStream = File.OpenRead(sourcePath);
                using var destinationStream = File.Create(destinationPath);
                using var gzipStream = new GZipStream(destinationStream, CompressionMode.Compress);
                await sourceStream.CopyToAsync(gzipStream);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DecompressBackupAsync(string sourcePath, string destinationPath)
        {
            try
            {
                using var sourceStream = File.OpenRead(sourcePath);
                using var destinationStream = File.Create(destinationPath);
                using var gzipStream = new GZipStream(sourceStream, CompressionMode.Decompress);
                await gzipStream.CopyToAsync(destinationStream);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> EncryptBackupAsync(string sourcePath, string destinationPath, string password)
        {
            try
            {
                var key = Encoding.UTF8.GetBytes(password.PadRight(32).Substring(0, 32));
                var iv = Encoding.UTF8.GetBytes(password.PadRight(16).Substring(0, 16));

                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;

                using var sourceStream = File.OpenRead(sourcePath);
                using var destinationStream = File.Create(destinationPath);
                using var cryptoStream = new CryptoStream(destinationStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
                await sourceStream.CopyToAsync(cryptoStream);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DecryptBackupAsync(string sourcePath, string destinationPath, string password)
        {
            try
            {
                var key = Encoding.UTF8.GetBytes(password.PadRight(32).Substring(0, 32));
                var iv = Encoding.UTF8.GetBytes(password.PadRight(16).Substring(0, 16));

                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;

                using var sourceStream = File.OpenRead(sourcePath);
                using var destinationStream = File.Create(destinationPath);
                using var cryptoStream = new CryptoStream(sourceStream, aes.CreateDecryptor(), CryptoStreamMode.Read);
                await cryptoStream.CopyToAsync(destinationStream);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendBackupNotificationAsync(string message, bool isSuccess = true)
        {
            try
            {
                // في التطبيق الحقيقي، سيتم إرسال إشعار للمستخدم
                Console.WriteLine($"Backup Notification: {message}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GenerateBackupReportAsync()
        {
            var backups = await GetAllBackupsAsync();
            var settings = await GetBackupSettingsAsync();
            var statistics = await GetBackupStatisticsAsync();

            var report = new StringBuilder();
            report.AppendLine("=== تقرير النسخ الاحتياطي ===");
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"إجمالي النسخ الاحتياطية: {backups.Count()}");
            report.AppendLine($"النسخ الناجحة: {backups.Count(b => b.Status == "Success")}");
            report.AppendLine($"النسخ الفاشلة: {backups.Count(b => b.Status == "Failed")}");
            report.AppendLine($"حجم مجلد النسخ الاحتياطي: {statistics["DirectorySize"]} MB");
            report.AppendLine();

            report.AppendLine("=== آخر 10 نسخ احتياطية ===");
            foreach (var backup in backups.Take(10))
            {
                report.AppendLine($"- {backup.FileName} ({backup.BackupDate:yyyy-MM-dd HH:mm}) - {backup.Status}");
            }

            return report.ToString();
        }

        public async Task<Dictionary<string, object>> GetBackupStatisticsAsync()
        {
            var backups = await GetAllBackupsAsync();
            var directorySize = await GetBackupDirectorySizeAsync();

            return new Dictionary<string, object>
            {
                ["TotalBackups"] = backups.Count(),
                ["SuccessfulBackups"] = backups.Count(b => b.Status == "Success"),
                ["FailedBackups"] = backups.Count(b => b.Status == "Failed"),
                ["DirectorySize"] = directorySize / (1024 * 1024), // MB
                ["LastBackupDate"] = backups.FirstOrDefault()?.BackupDate,
                ["ExpiredBackups"] = backups.Count(b => b.IsRetentionExpired)
            };
        }

        public async Task<string> GetBackupDirectoryAsync()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
            return Path.Combine(projectRoot, "Backups");
        }

        public async Task<bool> CreateBackupDirectoryAsync()
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    Directory.CreateDirectory(_backupDirectory);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<long> GetBackupDirectorySizeAsync()
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                    return 0;

                var files = Directory.GetFiles(_backupDirectory, "*", SearchOption.AllDirectories);
                return files.Sum(file => new FileInfo(file).Length);
            }
            catch
            {
                return 0;
            }
        }

        public async Task<bool> CleanupBackupDirectoryAsync()
        {
            try
            {
                await DeleteExpiredBackupsAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> HasBackupPermissionAsync(string username)
        {
            var user = await _userService.GetUserByUsernameAsync(username);
            if (user == null) return false;

            var role = await _context.Roles.FindAsync(user.RoleId);
            return role?.CanManageBackup == true;
        }

        public async Task<bool> HasRestorePermissionAsync(string username)
        {
            var user = await _userService.GetUserByUsernameAsync(username);
            if (user == null) return false;

            var role = await _context.Roles.FindAsync(user.RoleId);
            return role?.CanManageBackup == true;
        }

        private string GetDatabasePath()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
            return Path.Combine(projectRoot, "AqlanCenter.db");
        }

        private async Task<int> GetTotalRecordsCountAsync()
        {
            var counts = new[]
            {
                await _context.Patients.CountAsync().ConfigureAwait(false),
                await _context.Doctors.CountAsync().ConfigureAwait(false),
                await _context.Employees.CountAsync().ConfigureAwait(false),
                await _context.Appointments.CountAsync().ConfigureAwait(false),
                await _context.Invoices.CountAsync().ConfigureAwait(false),
                await _context.Receipts.CountAsync().ConfigureAwait(false),
                await _context.LabOrders.CountAsync().ConfigureAwait(false),
                await _context.Purchases.CountAsync().ConfigureAwait(false),
                await _context.InventoryItems.CountAsync().ConfigureAwait(false),
                await _context.Users.CountAsync().ConfigureAwait(false)
            };

            return counts.Sum();
        }
    }
}