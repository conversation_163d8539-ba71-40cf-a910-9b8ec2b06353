using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using System.IO;

namespace AqlanCenterProApp.Helpers
{
    public static class DatabaseHelper
    {
        public static async Task<bool> VerifyDatabaseAsync(string connectionString)
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);

                // التحقق من إمكانية الاتصال
                await context.Database.CanConnectAsync();

                // التحقق من وجود الجداول
                var tables = new[]
                {
                    "Patients", "Doctors", "Employees", "Sessions", "Payments",
                    "Invoices", "InvoiceItems", "Appointments", "Labs", "LabOrders",
                    "ProsthesisTypes", "Suppliers", "Purchases", "PurchaseItems",
                    "InventoryItems", "Users", "Roles", "ActivityLogs",
                    "PatientFiles", "Notifications", "EmployeeAttendances"
                };

                // التحقق من وجود الجداول بطريقة آمنة
                var tableExists = await context.Database.CanConnectAsync();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في التحقق من قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        public static async Task<Dictionary<string, int>> GetTableCountsAsync(string connectionString)
        {
            var counts = new Dictionary<string, int>();

            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);

                counts["Patients"] = await context.Patients.CountAsync();
                counts["Doctors"] = await context.Doctors.CountAsync();
                counts["Employees"] = await context.Employees.CountAsync();
                counts["Sessions"] = await context.Sessions.CountAsync();
                counts["Payments"] = await context.Payments.CountAsync();
                counts["Invoices"] = await context.Invoices.CountAsync();
                counts["Appointments"] = await context.Appointments.CountAsync();
                counts["Labs"] = await context.Labs.CountAsync();
                counts["LabOrders"] = await context.LabOrders.CountAsync();
                counts["ProsthesisTypes"] = await context.ProsthesisTypes.CountAsync();
                counts["Suppliers"] = await context.Suppliers.CountAsync();
                counts["Purchases"] = await context.Purchases.CountAsync();
                counts["InventoryItems"] = await context.InventoryItems.CountAsync();
                counts["Users"] = await context.Users.CountAsync();
                counts["Roles"] = await context.Roles.CountAsync();
                counts["ActivityLogs"] = await context.ActivityLogs.CountAsync();
                counts["PatientFiles"] = await context.PatientFiles.CountAsync();
                counts["Notifications"] = await context.Notifications.CountAsync();
                counts["EmployeeAttendances"] = await context.EmployeeAttendances.CountAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب عدد السجلات: {ex.Message}");
            }

            return counts;
        }

        public static async Task<bool> SeedInitialDataAsync(string connectionString)
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);

                await DatabaseInitializer.InitializeAsync(context);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تهيئة البيانات الأولية: {ex.Message}");
                return false;
            }
        }

        public static string GetDatabaseInfo(string connectionString)
        {
            try
            {
                var fileInfo = new FileInfo(connectionString.Replace("Data Source=", ""));
                if (fileInfo.Exists)
                {
                    return $"قاعدة البيانات موجودة\n" +
                           $"المسار: {fileInfo.FullName}\n" +
                           $"الحجم: {fileInfo.Length / 1024.0:F2} KB\n" +
                           $"تاريخ الإنشاء: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"آخر تعديل: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}";
                }
                else
                {
                    return "قاعدة البيانات غير موجودة";
                }
            }
            catch (Exception ex)
            {
                return $"خطأ في قراءة معلومات قاعدة البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// حذف البيانات من جدول Notifications لحل مشكلة الترحيل
        /// </summary>
        public static async Task ClearNotificationsTableAsync()
        {
            try
            {
                // إنشاء مسار قاعدة البيانات
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
                var dbPath = Path.Combine(projectRoot, "AqlanCenter.db");
                var connectionString = $"Data Source={dbPath}";

                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);
                var notifications = await context.Notifications.ToListAsync();
                context.Notifications.RemoveRange(notifications);
                await context.SaveChangesAsync();
                Console.WriteLine("✅ تم حذف البيانات من جدول Notifications بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حذف بيانات Notifications: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف البيانات من جدول Appointments لحل مشكلة الترحيل
        /// </summary>
        public static async Task ClearAppointmentsTableAsync()
        {
            try
            {
                // إنشاء مسار قاعدة البيانات
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
                var dbPath = Path.Combine(projectRoot, "AqlanCenter.db");
                var connectionString = $"Data Source={dbPath}";

                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);
                var appointments = await context.Appointments.ToListAsync();
                context.Appointments.RemoveRange(appointments);
                await context.SaveChangesAsync();
                Console.WriteLine("✅ تم حذف البيانات من جدول Appointments بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حذف بيانات Appointments: {ex.Message}");
            }
        }
    }
}
