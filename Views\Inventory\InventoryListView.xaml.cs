using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Inventory;

namespace AqlanCenterProApp.Views.Inventory
{
    /// <summary>
    /// Interaction logic for InventoryListView.xaml
    /// </summary>
    public partial class InventoryListView : UserControl
    {
        public InventoryListView()
        {
            InitializeComponent();
        }

        public InventoryListView(InventoryListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += InventoryListView_Loaded;
        }

        private void InventoryListView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is InventoryListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadInventoryAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadInventoryAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات المخزون: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في InventoryListView_Loaded: {ex.Message}");
            }
        }
    }
}