using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class BackupInfo : BaseEntity
    {
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string FileName { get; set; } = string.Empty;

        public long FileSize { get; set; }

        [Required]
        [MaxLength(50)]
        public string BackupType { get; set; } = string.Empty; // Manual, Scheduled, Auto

        [MaxLength(500)]
        public string? Description { get; set; }

        public DateTime BackupDate { get; set; }

        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Success, Failed, InProgress

        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        public int RecordsCount { get; set; }

        public bool IsCompressed { get; set; }

        [MaxLength(100)]
        public string? Checksum { get; set; }

        public bool IsEncrypted { get; set; }

        [MaxLength(100)]
        public string? EncryptionKey { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public bool IsRetentionExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

        // خصائص محسوبة للعرض
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} بايت";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F2} كيلوبايت";
                else if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024.0 * 1024.0):F2} ميجابايت";
                else
                    return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F2} جيجابايت";
            }
        }

        public string StatusText
        {
            get
            {
                return Status switch
                {
                    "Success" => "نجح",
                    "Failed" => "فشل",
                    "InProgress" => "قيد التنفيذ",
                    _ => Status
                };
            }
        }
    }
}