using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج حضور وانصراف الموظفين
    /// </summary>
    public class EmployeeAttendance : BaseEntity
    {
        /// <summary>
        /// معرف السجل
        /// </summary>
        public int AttendanceId { get; set; }

        /// <summary>
        /// معرف الموظف
        /// </summary>
        [Required]
        public int EmployeeId { get; set; }

        /// <summary>
        /// تاريخ الحضور
        /// </summary>
        [Required]
        public DateTime AttendanceDate { get; set; } = DateTime.Today;

        /// <summary>
        /// وقت الدخول
        /// </summary>
        public TimeSpan? CheckInTime { get; set; }

        /// <summary>
        /// وقت الخروج
        /// </summary>
        public TimeSpan? CheckOutTime { get; set; }

        /// <summary>
        /// وقت الدخول المطلوب
        /// </summary>
        public TimeSpan RequiredCheckInTime { get; set; } = new TimeSpan(8, 0, 0); // 8:00 AM

        /// <summary>
        /// وقت الخروج المطلوب
        /// </summary>
        public TimeSpan RequiredCheckOutTime { get; set; } = new TimeSpan(16, 0, 0); // 4:00 PM

        /// <summary>
        /// عدد ساعات العمل الفعلية
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal? ActualWorkHours { get; set; }

        /// <summary>
        /// عدد ساعات العمل المطلوبة
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal RequiredWorkHours { get; set; } = 8.0m;

        /// <summary>
        /// حالة الحضور (حاضر، غائب، متأخر، إجازة)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string AttendanceStatus { get; set; } = "حاضر";

        /// <summary>
        /// عدد دقائق التأخير
        /// </summary>
        public int? LateMinutes { get; set; }

        /// <summary>
        /// عدد دقائق الانصراف المبكر
        /// </summary>
        public int? EarlyLeaveMinutes { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public new string? Notes { get; set; }

        /// <summary>
        /// هل تم تعديل السجل يدوياً
        /// </summary>
        public bool IsManuallyEdited { get; set; } = false;

        /// <summary>
        /// من قام بالتعديل
        /// </summary>
        [StringLength(100)]
        public string? EditedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? EditedAt { get; set; }

        // Navigation Property
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}
