-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE IF NOT EXISTS Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Email TEXT,
    Phone TEXT,
    RoleId INTEGER,
    IsActive INTEGER DEFAULT 1,
    LastLoginDate TEXT,
    LastPasswordChangeDate TEXT,
    FailedLoginAttempts INTEGER DEFAULT 0,
    LockoutEndDate TEXT,
    UserImage TEXT,
    Language TEXT DEFAULT 'ar',
    Theme TEXT DEFAULT 'Light',
    Notes TEXT,
    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الأدوار
CREATE TABLE IF NOT EXISTS Roles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    RoleName TEXT NOT NULL,
    Description TEXT,
    IsActive INTEGER DEFAULT 1,
    CanViewPatients INTEGER DEFAULT 0,
    CanAddPatients INTEGER DEFAULT 0,
    CanEditPatients INTEGER DEFAULT 0,
    CanDeletePatients INTEGER DEFAULT 0,
    CanViewDoctors INTEGER DEFAULT 0,
    CanAddDoctors INTEGER DEFAULT 0,
    CanEditDoctors INTEGER DEFAULT 0,
    CanDeleteDoctors INTEGER DEFAULT 0,
    CanViewEmployees INTEGER DEFAULT 0,
    CanAddEmployees INTEGER DEFAULT 0,
    CanEditEmployees INTEGER DEFAULT 0,
    CanDeleteEmployees INTEGER DEFAULT 0,
    CanViewAppointments INTEGER DEFAULT 0,
    CanAddAppointments INTEGER DEFAULT 0,
    CanEditAppointments INTEGER DEFAULT 0,
    CanDeleteAppointments INTEGER DEFAULT 0,
    CanViewPayments INTEGER DEFAULT 0,
    CanAddPayments INTEGER DEFAULT 0,
    CanEditPayments INTEGER DEFAULT 0,
    CanDeletePayments INTEGER DEFAULT 0,
    CanViewReports INTEGER DEFAULT 0,
    CanGenerateReports INTEGER DEFAULT 0,
    CanViewSettings INTEGER DEFAULT 0,
    CanEditSettings INTEGER DEFAULT 0,
    CanBackupRestore INTEGER DEFAULT 0,
    CanManageUsers INTEGER DEFAULT 0,
    CanCancelReceipts INTEGER DEFAULT 0,
    Notes TEXT,
    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
);

-- إدراج الأدوار الأساسية
INSERT INTO Roles (RoleName, Description, IsActive, CanViewPatients, CanAddPatients, CanEditPatients, CanDeletePatients, CanViewDoctors, CanAddDoctors, CanEditDoctors, CanDeleteDoctors, CanViewEmployees, CanAddEmployees, CanEditEmployees, CanDeleteEmployees, CanViewAppointments, CanAddAppointments, CanEditAppointments, CanDeleteAppointments, CanViewPayments, CanAddPayments, CanEditPayments, CanDeletePayments, CanViewReports, CanGenerateReports, CanViewSettings, CanEditSettings, CanBackupRestore, CanManageUsers, CanCancelReceipts) VALUES 
('مدير النظام', 'صلاحيات كاملة لإدارة النظام', 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
('طبيب', 'صلاحيات الطبيب', 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0),
('موظف استقبال', 'صلاحيات موظف الاستقبال', 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- إدراج المستخدمين الأساسيين
INSERT INTO Users (Username, PasswordHash, FullName, Email, Phone, RoleId, IsActive, LastLoginDate) VALUES 
('admin', 'admin123', 'مدير النظام', '<EMAIL>', '777-000000', 1, 1, datetime('now')),
('doctor', 'doctor123', 'د. عقلان محمد الحكيمي', '<EMAIL>', '777-111111', 2, 1, datetime('now')),
('reception', 'reception123', 'موظف الاستقبال', '<EMAIL>', '777-222222', 3, 1, datetime('now'));
