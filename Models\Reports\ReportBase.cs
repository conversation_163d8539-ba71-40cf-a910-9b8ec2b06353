using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// الكلاس الأساسي للتقارير
    /// </summary>
    public abstract class ReportBase
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "عنوان التقرير مطلوب")]
        [StringLength(200, ErrorMessage = "عنوان التقرير لا يمكن أن يتجاوز 200 حرف")]
        public string Title { get; set; } = string.Empty;
        
        [StringLength(1000, ErrorMessage = "وصف التقرير لا يمكن أن يتجاوز 1000 حرف")]
        public string? Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? GeneratedAt { get; set; }
        
        public string CreatedBy { get; set; } = string.Empty;
        
        public ReportType Type { get; set; }
        
        public ReportStatus Status { get; set; } = ReportStatus.Draft;
        
        public DateTime? StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        public string? Filters { get; set; } // JSON string for filters
        
        public string? Parameters { get; set; } // JSON string for parameters
        
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// أنواع التقارير
    /// </summary>
    public enum ReportType
    {
        PatientReport = 1,
        AppointmentReport = 2,
        RevenueReport = 3,
        ExpenseReport = 4,
        TreatmentSessionReport = 5,
        InvoiceReport = 6,
        InventoryReport = 7,
        PerformanceReport = 8,
        FinancialReport = 9,
        CustomReport = 10
    }

    /// <summary>
    /// حالة التقرير
    /// </summary>
    public enum ReportStatus
    {
        Draft = 1,
        Generated = 2,
        Exported = 3,
        Archived = 4
    }
} 