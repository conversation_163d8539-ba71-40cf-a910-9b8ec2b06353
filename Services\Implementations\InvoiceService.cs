using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة إدارة الفواتير
    /// </summary>
    public class InvoiceService : IInvoiceService
    {
        private readonly AqlanCenterDbContext _context;

        public InvoiceService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        #region العمليات الأساسية (CRUD)

        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Include(i => i.InvoiceItems)
                .Include(i => i.Payments)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Include(i => i.InvoiceItems)
                .Include(i => i.Payments)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Include(i => i.InvoiceItems)
                .Include(i => i.Payments)
                .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByPatientAsync(int patientId)
        {
            return await _context.Invoices
                .Include(i => i.InvoiceItems)
                .Include(i => i.Payments)
                .Where(i => i.PatientId == patientId)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice> CreateInvoiceAsync(Invoice invoice)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateInvoiceAsync(invoice);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // تعيين رقم الفاتورة إذا لم يكن محدداً
            if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                invoice.InvoiceNumber = await GetNextInvoiceNumberAsync();

            // حساب المبالغ
            CalculateInvoiceAmounts(invoice);

            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateInvoiceAsync(invoice);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // حساب المبالغ
            CalculateInvoiceAmounts(invoice);

            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null)
                return false;

            invoice.IsDeleted = true;
            invoice.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return true;
        }

        #endregion

        #region إدارة عناصر الفاتورة

        public async Task<InvoiceItem> AddInvoiceItemAsync(InvoiceItem item)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateInvoiceItemAsync(item);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // حساب إجمالي العنصر
            item.TotalPrice = item.Quantity * item.UnitPrice;

            _context.InvoiceItems.Add(item);
            await _context.SaveChangesAsync();

            // تحديث مبالغ الفاتورة
            await UpdateInvoiceAmountsAsync(item.InvoiceId);

            return item;
        }

        public async Task<InvoiceItem> UpdateInvoiceItemAsync(InvoiceItem item)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateInvoiceItemAsync(item);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // حساب إجمالي العنصر
            item.TotalPrice = item.Quantity * item.UnitPrice;

            _context.InvoiceItems.Update(item);
            await _context.SaveChangesAsync();

            // تحديث مبالغ الفاتورة
            await UpdateInvoiceAmountsAsync(item.InvoiceId);

            return item;
        }

        public async Task<bool> DeleteInvoiceItemAsync(int itemId)
        {
            var item = await _context.InvoiceItems.FindAsync(itemId);
            if (item == null)
                return false;

            var invoiceId = item.InvoiceId;

            _context.InvoiceItems.Remove(item);
            await _context.SaveChangesAsync();

            // تحديث مبالغ الفاتورة
            await UpdateInvoiceAmountsAsync(invoiceId);

            return true;
        }

        public async Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId)
        {
            return await _context.InvoiceItems
                .Where(ii => ii.InvoiceId == invoiceId)
                .OrderBy(ii => ii.Id)
                .ToListAsync();
        }

        #endregion

        #region حساب المبالغ

        public async Task<decimal> CalculateInvoiceTotalAsync(int invoiceId)
        {
            var items = await _context.InvoiceItems
                .Where(ii => ii.InvoiceId == invoiceId)
                .ToListAsync();

            return items.Sum(ii => ii.TotalPrice);
        }

        public async Task<bool> UpdateInvoiceAmountsAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null)
                return false;

            // حساب المجموع الفرعي
            invoice.SubTotal = await CalculateInvoiceTotalAsync(invoiceId);

            // حساب المبلغ الإجمالي (بعد الخصم والضريبة)
            invoice.TotalAmount = invoice.SubTotal - invoice.DiscountAmount + invoice.TaxAmount;

            // حساب المبلغ المدفوع
            var payments = await _context.Payments
                .Where(p => p.InvoiceId == invoiceId)
                .SumAsync(p => p.Amount);
            invoice.PaidAmount = payments;

            // حساب المبلغ المتبقي
            invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

            // تحديث حالة الفاتورة
            if (invoice.RemainingAmount <= 0)
                invoice.InvoiceStatus = "مدفوعة";
            else if (invoice.DueDate.HasValue && invoice.DueDate.Value < DateTime.Now)
                invoice.InvoiceStatus = "متأخرة";
            else
                invoice.InvoiceStatus = "مفتوحة";

            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<decimal> CalculateRemainingAmountAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null)
                return 0;

            var payments = await _context.Payments
                .Where(p => p.InvoiceId == invoiceId)
                .SumAsync(p => p.Amount);

            return invoice.TotalAmount - payments;
        }

        #endregion

        #region البحث والفلترة

        public async Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.InvoiceNumber.Contains(searchTerm) ||
                           (i.Patient != null && i.Patient.FullName.Contains(searchTerm)) ||
                           (i.IssuedBy != null && i.IssuedBy.Contains(searchTerm)))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByDateAsync(DateTime date)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.InvoiceDate.Date == date.Date)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.InvoiceDate.Date >= startDate.Date && i.InvoiceDate.Date <= endDate.Date)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(string status)
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.InvoiceStatus == status)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetUnpaidInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.RemainingAmount > 0)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Patient)
                .Where(i => i.DueDate.HasValue && i.DueDate.Value < DateTime.Now && i.RemainingAmount > 0)
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        #endregion

        #region الترقيم التلقائي

        public async Task<string> GetNextInvoiceNumberAsync()
        {
            var lastInvoice = await _context.Invoices
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefaultAsync();

            if (lastInvoice == null)
                return "INV-0001";

            var lastNumber = lastInvoice.InvoiceNumber;
            if (lastNumber.StartsWith("INV-") && int.TryParse(lastNumber.Substring(4), out int number))
            {
                return $"INV-{(number + 1):D4}";
            }

            return $"INV-{DateTime.Now:yyyyMMdd}-{1:D3}";
        }

        public async Task<bool> IsInvoiceNumberAvailableAsync(string invoiceNumber)
        {
            return !await _context.Invoices.AnyAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        #endregion

        #region الإحصائيات والتقارير

        public async Task<InvoiceStatistics> GetInvoiceStatisticsAsync()
        {
            var invoices = await _context.Invoices.ToListAsync();
            var statistics = new InvoiceStatistics
            {
                TotalInvoices = invoices.Count,
                TotalAmount = invoices.Sum(i => i.TotalAmount),
                PaidAmount = invoices.Sum(i => i.PaidAmount),
                UnpaidAmount = invoices.Sum(i => i.RemainingAmount),
                OverdueAmount = invoices.Where(i => i.DueDate.HasValue && i.DueDate.Value < DateTime.Now)
                                      .Sum(i => i.RemainingAmount)
            };

            // إحصائيات حسب الحالة
            statistics.PaidInvoices = invoices.Count(i => i.InvoiceStatus == "مدفوعة");
            statistics.UnpaidInvoices = invoices.Count(i => i.InvoiceStatus == "مفتوحة");
            statistics.OverdueInvoices = invoices.Count(i => i.InvoiceStatus == "متأخرة");
            statistics.CancelledInvoices = invoices.Count(i => i.InvoiceStatus == "ملغية");

            // إحصائيات حسب الشهر
            var currentYear = DateTime.Now.Year;
            for (int month = 1; month <= 12; month++)
            {
                var monthInvoices = invoices.Where(i => i.InvoiceDate.Year == currentYear && i.InvoiceDate.Month == month);
                var monthKey = $"{currentYear}-{month:D2}";
                statistics.InvoicesByMonth[monthKey] = monthInvoices.Count();
                statistics.SalesByMonth[monthKey] = monthInvoices.Sum(i => i.TotalAmount);
            }

            return statistics;
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Invoices
                .Where(i => i.InvoiceDate.Date >= startDate.Date && i.InvoiceDate.Date <= endDate.Date)
                .SumAsync(i => i.TotalAmount);
        }

        public async Task<decimal> GetTotalDebtsAsync()
        {
            return await _context.Invoices
                .Where(i => i.RemainingAmount > 0)
                .SumAsync(i => i.RemainingAmount);
        }

        public async Task<IEnumerable<Patient>> GetTopPayingPatientsAsync(int count = 10)
        {
            return await _context.Patients
                .Include(p => p.Invoices)
                .OrderByDescending(p => p.Invoices.Sum(i => i.PaidAmount))
                .Take(count)
                .ToListAsync();
        }

        #endregion

        #region الطباعة والتصدير

        public async Task<bool> PrintInvoiceAsync(int invoiceId)
        {
            // تنفيذ الطباعة (سيتم تنفيذها لاحقاً)
            await Task.Delay(100); // محاكاة عملية الطباعة
            return true;
        }

        public async Task<byte[]> ExportInvoiceToPdfAsync(int invoiceId)
        {
            // تنفيذ التصدير إلى PDF (سيتم تنفيذها لاحقاً)
            await Task.Delay(100); // محاكاة عملية التصدير
            return new byte[0];
        }

        public async Task<byte[]> ExportInvoiceToExcelAsync(int invoiceId)
        {
            // تنفيذ التصدير إلى Excel (سيتم تنفيذها لاحقاً)
            await Task.Delay(100); // محاكاة عملية التصدير
            return new byte[0];
        }

        #endregion

        #region التحقق من صحة البيانات

        public async Task<(bool IsValid, string ErrorMessage)> ValidateInvoiceAsync(Invoice invoice)
        {
            if (invoice == null)
                return (false, "الفاتورة مطلوبة");

            if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                return (false, "رقم الفاتورة مطلوب");

            if (invoice.PatientId <= 0)
                return (false, "المريض مطلوب");

            if (invoice.InvoiceDate == default)
                return (false, "تاريخ الفاتورة مطلوب");

            if (invoice.TotalAmount < 0)
                return (false, "المبلغ الإجمالي يجب أن يكون موجباً");

            // التحقق من عدم تكرار رقم الفاتورة
            if (await _context.Invoices.AnyAsync(i => i.InvoiceNumber == invoice.InvoiceNumber && i.Id != invoice.Id))
                return (false, "رقم الفاتورة مستخدم بالفعل");

            return (true, string.Empty);
        }

        public async Task<(bool IsValid, string ErrorMessage)> ValidateInvoiceItemAsync(InvoiceItem item)
        {
            if (item == null)
                return (false, "عنصر الفاتورة مطلوب");

            if (item.InvoiceId <= 0)
                return (false, "معرف الفاتورة مطلوب");

            if (string.IsNullOrEmpty(item.ItemDescription))
                return (false, "وصف العنصر مطلوب");

            if (item.Quantity <= 0)
                return (false, "الكمية يجب أن تكون أكبر من صفر");

            if (item.UnitPrice < 0)
                return (false, "سعر الوحدة يجب أن يكون موجباً");

            return (true, string.Empty);
        }

        #endregion

        #region الطرق المساعدة

        private void CalculateInvoiceAmounts(Invoice invoice)
        {
            if (invoice.InvoiceItems != null && invoice.InvoiceItems.Any())
            {
                invoice.SubTotal = invoice.InvoiceItems.Sum(ii => ii.TotalPrice);
                invoice.TotalAmount = invoice.SubTotal - invoice.DiscountAmount + invoice.TaxAmount;
                invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;
            }
        }

        #endregion
    }
}