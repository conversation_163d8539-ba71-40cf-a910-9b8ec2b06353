using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AqlanCenterProApp.Views.Controls
{
    public partial class SidebarControl : UserControl
    {
        public event EventHandler<string>? MenuItemSelected;
        private Button? _activeButton;
        private Expander? _activeExpander;

        public SidebarControl()
        {
            InitializeComponent();
            // تعيين الداشبورد كالصفحة النشطة افتراضياً
            SetActiveButton(DashboardButton);
        }

        private void OnMenuItemClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string menuItem)
            {
                SetActiveButton(button);
                MenuItemSelected?.Invoke(this, menuItem);
            }
        }

        private void SetActiveButton(Button button)
        {
            // إزالة التنسيق النشط من الزر السابق
            if (_activeButton != null)
            {
                _activeButton.Background = System.Windows.Media.Brushes.Transparent;
                _activeButton.Foreground = System.Windows.Media.Brushes.White;
            }

            // تطبيق التنسيق النشط على الزر الجديد
            _activeButton = button;
            _activeButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(247, 147, 29)); // #F7931D
            _activeButton.Foreground = System.Windows.Media.Brushes.White;

            // إغلاق جميع القوائم المفتوحة الأخرى
            CloseOtherExpanders(button);
        }

        private void CloseOtherExpanders(Button activeButton)
        {
            // إغلاق جميع القوائم المفتوحة ما عدا القائمة التي يحتوي عليها الزر النشط
            var expanders = new[] 
            { 
                PatientsExpander, DoctorsExpander, EmployeesExpander, 
                AppointmentsExpander, FinancialExpander, LabsExpander, 
                InventoryExpander, ReportsExpander, UsersExpander, 
                BackupExpander, SettingsExpander 
            };

            foreach (var expander in expanders)
            {
                if (expander != null && !IsButtonInExpander(activeButton, expander))
                {
                    expander.IsExpanded = false;
                }
            }
        }

        private bool IsButtonInExpander(Button button, Expander expander)
        {
            // التحقق من أن الزر موجود داخل القائمة المحددة
            var parent = VisualTreeHelper.GetParent(button);
            while (parent != null)
            {
                if (parent == expander)
                    return true;
                parent = VisualTreeHelper.GetParent(parent);
            }
            return false;
        }

        public void SetActiveMenuItem(string menuItem)
        {
            // البحث عن الزر المناسب وتعيينه كنشط
            Button? targetButton = null;

            // البحث في جميع الأزرار الفرعية
            var allButtons = FindVisualChildren<Button>(this);
            foreach (var button in allButtons)
            {
                if (button.Tag?.ToString() == menuItem)
                {
                    targetButton = button;
                    break;
                }
            }

            if (targetButton != null)
            {
                SetActiveButton(targetButton);
                
                // فتح القائمة التي يحتوي عليها الزر إذا كانت مغلقة
                var parentExpander = FindParentExpander(targetButton);
                if (parentExpander != null)
                {
                    parentExpander.IsExpanded = true;
                }
            }
        }

        private Expander? FindParentExpander(Button button)
        {
            var parent = VisualTreeHelper.GetParent(button);
            while (parent != null)
            {
                if (parent is Expander expander)
                    return expander;
                parent = VisualTreeHelper.GetParent(parent);
            }
            return null;
        }

        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj == null) yield break;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);
                if (child is T t)
                    yield return t;

                foreach (T childOfChild in FindVisualChildren<T>(child))
                    yield return childOfChild;
            }
        }
    }
}
