using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class AdvancedSearchWindow : Window
    {
        public PatientSearchCriteria? SearchCriteria { get; private set; }

        public AdvancedSearchWindow()
        {
            InitializeComponent();
            InitializeControls();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            GenderComboBox.SelectedIndex = 0; // الكل
            CategoryComboBox.SelectedIndex = 0; // الكل
            FileStatusComboBox.SelectedIndex = 0; // الكل
            DebtStatusComboBox.SelectedIndex = 0; // الكل
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء معايير البحث
                SearchCriteria = new PatientSearchCriteria
                {
                    FullName = string.IsNullOrWhiteSpace(NameTextBox.Text) ? null : NameTextBox.Text.Trim(),
                    PhoneNumber = string.IsNullOrWhiteSpace(PhoneTextBox.Text) ? null : PhoneTextBox.Text.Trim(),
                    Gender = GenderComboBox.SelectedIndex == 0 ? null : (GenderComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString(),
                    Category = CategoryComboBox.SelectedIndex == 0 ? null : (CategoryComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString(),
                    FileStatus = FileStatusComboBox.SelectedIndex == 0 ? null : (FileStatusComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString(),
                    RegistrationDateFrom = RegistrationFromDatePicker.SelectedDate,
                    RegistrationDateTo = RegistrationToDatePicker.SelectedDate,
                    LastVisitDateFrom = LastVisitFromDatePicker.SelectedDate,
                    LastVisitDateTo = LastVisitToDatePicker.SelectedDate
                };

                // معالجة رقم الملف
                if (!string.IsNullOrWhiteSpace(FileNumberTextBox.Text) && int.TryParse(FileNumberTextBox.Text, out int fileNumber))
                {
                    SearchCriteria.FileNumber = fileNumber;
                }

                // معالجة العمر
                if (!string.IsNullOrWhiteSpace(AgeFromTextBox.Text) && int.TryParse(AgeFromTextBox.Text, out int ageFrom))
                {
                    SearchCriteria.AgeFrom = ageFrom;
                }

                if (!string.IsNullOrWhiteSpace(AgeToTextBox.Text) && int.TryParse(AgeToTextBox.Text, out int ageTo))
                {
                    SearchCriteria.AgeTo = ageTo;
                }

                // معالجة الرصيد
                if (!string.IsNullOrWhiteSpace(BalanceFromTextBox.Text) && decimal.TryParse(BalanceFromTextBox.Text, out decimal balanceFrom))
                {
                    SearchCriteria.BalanceFrom = balanceFrom;
                }

                if (!string.IsNullOrWhiteSpace(BalanceToTextBox.Text) && decimal.TryParse(BalanceToTextBox.Text, out decimal balanceTo))
                {
                    SearchCriteria.BalanceTo = balanceTo;
                }

                // معالجة إجمالي المدفوعات
                if (!string.IsNullOrWhiteSpace(TotalPaymentsFromTextBox.Text) && decimal.TryParse(TotalPaymentsFromTextBox.Text, out decimal paymentsFrom))
                {
                    SearchCriteria.TotalPaymentsFrom = paymentsFrom;
                }

                if (!string.IsNullOrWhiteSpace(TotalPaymentsToTextBox.Text) && decimal.TryParse(TotalPaymentsToTextBox.Text, out decimal paymentsTo))
                {
                    SearchCriteria.TotalPaymentsTo = paymentsTo;
                }

                // معالجة حالة الدين
                if (DebtStatusComboBox.SelectedIndex > 0)
                {
                    var debtStatus = (DebtStatusComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                    switch (debtStatus)
                    {
                        case "مدين":
                            SearchCriteria.IsDebtor = true;
                            break;
                        case "غير مدين":
                            SearchCriteria.IsDebtor = false;
                            break;
                        case "رصيد إيجابي":
                            SearchCriteria.HasPositiveBalance = true;
                            break;
                    }
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معايير البحث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع الحقول
            NameTextBox.Clear();
            FileNumberTextBox.Clear();
            PhoneTextBox.Clear();
            AgeFromTextBox.Clear();
            AgeToTextBox.Clear();
            BalanceFromTextBox.Clear();
            BalanceToTextBox.Clear();
            TotalPaymentsFromTextBox.Clear();
            TotalPaymentsToTextBox.Clear();

            // إعادة تعيين القوائم المنسدلة
            GenderComboBox.SelectedIndex = 0;
            CategoryComboBox.SelectedIndex = 0;
            FileStatusComboBox.SelectedIndex = 0;
            DebtStatusComboBox.SelectedIndex = 0;

            // مسح التواريخ
            RegistrationFromDatePicker.SelectedDate = null;
            RegistrationToDatePicker.SelectedDate = null;
            LastVisitFromDatePicker.SelectedDate = null;
            LastVisitToDatePicker.SelectedDate = null;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    /// <summary>
    /// معايير البحث المتقدم للمرضى
    /// </summary>
    public class PatientSearchCriteria
    {
        public string? FullName { get; set; }
        public int? FileNumber { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Gender { get; set; }
        public string? Category { get; set; }
        public string? FileStatus { get; set; }
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public DateTime? RegistrationDateFrom { get; set; }
        public DateTime? RegistrationDateTo { get; set; }
        public DateTime? LastVisitDateFrom { get; set; }
        public DateTime? LastVisitDateTo { get; set; }
        public decimal? BalanceFrom { get; set; }
        public decimal? BalanceTo { get; set; }
        public decimal? TotalPaymentsFrom { get; set; }
        public decimal? TotalPaymentsTo { get; set; }
        public bool? IsDebtor { get; set; }
        public bool? HasPositiveBalance { get; set; }
    }
}
