using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using System.Text;
using System.IO;

namespace AqlanCenterProApp.Helpers;

/// <summary>
/// أداة اختبار قاعدة البيانات
/// </summary>
public static class DatabaseTestHelper
{
    /// <summary>
    /// اختبار شامل لقاعدة البيانات
    /// </summary>
    public static async Task<string> TestDatabaseAsync()
    {
        var report = new StringBuilder();
        report.AppendLine("=== تقرير اختبار قاعدة البيانات ===");
        report.AppendLine($"التاريخ والوقت: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine();

        try
        {
            // إنشاء مسار قاعدة البيانات
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            var dbPath = Path.Combine(dataDirectory, "AqlanCenter.db");

            report.AppendLine($"📁 مجلد التطبيق: {baseDirectory}");
            report.AppendLine($"📁 مجلد البيانات: {dataDirectory}");
            report.AppendLine($"🗄️ مسار قاعدة البيانات: {dbPath}");
            report.AppendLine();

            // التحقق من وجود المجلد
            if (!Directory.Exists(dataDirectory))
            {
                report.AppendLine("⚠️ مجلد Data غير موجود - سيتم إنشاؤه...");
                Directory.CreateDirectory(dataDirectory);
                report.AppendLine("✅ تم إنشاء مجلد Data بنجاح");
            }
            else
            {
                report.AppendLine("✅ مجلد Data موجود");
            }

            // التحقق من وجود ملف قاعدة البيانات
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                report.AppendLine($"✅ ملف قاعدة البيانات موجود");
                report.AppendLine($"   📊 الحجم: {fileInfo.Length / 1024.0:F2} KB");
                report.AppendLine($"   📅 تاريخ الإنشاء: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"   🔄 آخر تعديل: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
            }
            else
            {
                report.AppendLine("⚠️ ملف قاعدة البيانات غير موجود - سيتم إنشاؤه...");
            }

            report.AppendLine();

            // اختبار الاتصال بقاعدة البيانات
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);

            using var context = new AqlanCenterDbContext(optionsBuilder.Options);

            // اختبار إنشاء قاعدة البيانات
            report.AppendLine("🔧 اختبار إنشاء قاعدة البيانات...");
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                report.AppendLine("✅ تم إنشاء قاعدة البيانات بنجاح");
            }
            else
            {
                report.AppendLine("ℹ️ قاعدة البيانات موجودة مسبقاً");
            }

            // اختبار الاتصال
            report.AppendLine("🔗 اختبار الاتصال...");
            var canConnect = await context.Database.CanConnectAsync();
            if (canConnect)
            {
                report.AppendLine("✅ تم الاتصال بقاعدة البيانات بنجاح");
            }
            else
            {
                report.AppendLine("❌ فشل في الاتصال بقاعدة البيانات");
                return report.ToString();
            }

            // اختبار الجداول
            report.AppendLine();
            report.AppendLine("📋 اختبار الجداول:");

            var tables = new Dictionary<string, Func<Task<int>>>
            {
                ["المستخدمين (Users)"] = () => context.Users.CountAsync(),
                ["الأدوار (Roles)"] = () => context.Roles.CountAsync(),
                ["المرضى (Patients)"] = () => context.Patients.CountAsync(),
                ["الأطباء (Doctors)"] = () => context.Doctors.CountAsync(),
                ["الموظفين (Employees)"] = () => context.Employees.CountAsync(),
                ["الجلسات (Sessions)"] = () => context.Sessions.CountAsync(),
                ["المدفوعات (Payments)"] = () => context.Payments.CountAsync(),
                ["الفواتير (Invoices)"] = () => context.Invoices.CountAsync(),
                ["المواعيد (Appointments)"] = () => context.Appointments.CountAsync(),
                ["المعامل (Labs)"] = () => context.Labs.CountAsync(),
                ["أنواع التركيبات (ProsthesisTypes)"] = () => context.ProsthesisTypes.CountAsync(),
                ["الموردين (Suppliers)"] = () => context.Suppliers.CountAsync(),
                ["المشتريات (Purchases)"] = () => context.Purchases.CountAsync(),
                ["المخزون (InventoryItems)"] = () => context.InventoryItems.CountAsync(),
                ["سجل النشاطات (ActivityLogs)"] = () => context.ActivityLogs.CountAsync(),
                ["ملفات المرضى (PatientFiles)"] = () => context.PatientFiles.CountAsync(),
                ["الإشعارات (Notifications)"] = () => context.Notifications.CountAsync(),
                ["حضور الموظفين (EmployeeAttendances)"] = () => context.EmployeeAttendances.CountAsync()
            };

            int successCount = 0;
            foreach (var table in tables)
            {
                try
                {
                    var count = await table.Value();
                    report.AppendLine($"   ✅ {table.Key}: {count} سجل");
                    successCount++;
                }
                catch (Exception ex)
                {
                    report.AppendLine($"   ❌ {table.Key}: خطأ - {ex.Message}");
                }
            }

            report.AppendLine();
            report.AppendLine($"📊 ملخص الاختبار:");
            report.AppendLine($"   🎯 الجداول المختبرة: {tables.Count}");
            report.AppendLine($"   ✅ الجداول الناجحة: {successCount}");
            report.AppendLine($"   ❌ الجداول الفاشلة: {tables.Count - successCount}");

            if (successCount == tables.Count)
            {
                report.AppendLine();
                report.AppendLine("🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل صحيح.");

                // تهيئة البيانات الأولية إذا لم تكن موجودة
                if (await context.Users.CountAsync() == 0)
                {
                    report.AppendLine();
                    report.AppendLine("🔄 تهيئة البيانات الأولية...");
                    await DatabaseInitializer.InitializeAsync(context);
                    report.AppendLine("✅ تم تهيئة البيانات الأولية بنجاح");
                }
            }
            else
            {
                report.AppendLine();
                report.AppendLine("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.");
            }
        }
        catch (Exception ex)
        {
            report.AppendLine();
            report.AppendLine($"❌ خطأ عام في اختبار قاعدة البيانات:");
            report.AppendLine($"   {ex.Message}");
            if (ex.InnerException != null)
            {
                report.AppendLine($"   التفاصيل: {ex.InnerException.Message}");
            }
        }

        report.AppendLine();
        report.AppendLine("=== انتهى تقرير الاختبار ===");
        return report.ToString();
    }

    /// <summary>
    /// إعادة إنشاء قاعدة البيانات من الصفر
    /// </summary>
    public static async Task<string> RecreateDatabase()
    {
        var report = new StringBuilder();
        report.AppendLine("=== إعادة إنشاء قاعدة البيانات ===");

        try
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            var dbPath = Path.Combine(dataDirectory, "AqlanCenter.db");

            // حذف قاعدة البيانات الموجودة
            if (File.Exists(dbPath))
            {
                File.Delete(dbPath);
                report.AppendLine("🗑️ تم حذف قاعدة البيانات القديمة");
            }

            // حذف ملفات SQLite المساعدة
            var shmFile = dbPath + "-shm";
            var walFile = dbPath + "-wal";

            if (File.Exists(shmFile)) File.Delete(shmFile);
            if (File.Exists(walFile)) File.Delete(walFile);

            // إنشاء قاعدة البيانات الجديدة
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);

            using var context = new AqlanCenterDbContext(optionsBuilder.Options);

            await context.Database.EnsureCreatedAsync();
            report.AppendLine("✅ تم إنشاء قاعدة البيانات الجديدة");

            await DatabaseInitializer.InitializeAsync(context);
            report.AppendLine("✅ تم تهيئة البيانات الأولية");

            report.AppendLine("🎉 تم إعادة إنشاء قاعدة البيانات بنجاح!");
        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ في إعادة إنشاء قاعدة البيانات: {ex.Message}");
        }

        return report.ToString();
    }
}
