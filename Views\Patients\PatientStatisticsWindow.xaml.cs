using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Models;
using Microsoft.Extensions.DependencyInjection;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class PatientStatisticsWindow : Window
    {
        private readonly IPatientService _patientService;

        public PatientStatisticsWindow()
        {
            InitializeComponent();

            // الحصول على خدمة المرضى من DI Container
            try
            {
                var app = (App)Application.Current;
                _patientService = App.Services.GetRequiredService<IPatientService>();
            }
            catch
            {
                // في حالة عدم توفر الخدمة، استخدم بيانات وهمية
                _patientService = null!;
            }

            LoadStatistics();
        }

        private async void LoadStatistics()
        {
            try
            {
                // استخدام بيانات وهمية للعرض
                var statistics = CreateSampleStatistics();

                // تحديث الإحصائيات العامة
                TotalPatientsText.Text = statistics.TotalPatients.ToString();
                ActivePatientsText.Text = statistics.ActivePatients.ToString();
                NewPatientsText.Text = statistics.NewPatientsThisMonth.ToString();
                DebtorPatientsText.Text = statistics.DebtorPatients.ToString();
                AverageAgeText.Text = $"{statistics.AverageAge:F1} سنة";
                TotalDebtsText.Text = $"{statistics.TotalDebts:N0} ريال";
                TotalPaymentsText.Text = $"{statistics.TotalPayments:N0} ريال";
                LastPatientText.Text = statistics.LastPatientName ?? "لا يوجد";

                // تحديث توزيع الجنس
                var totalForGender = statistics.MalePatients + statistics.FemalePatients;
                MalePatientsText.Text = statistics.MalePatients.ToString();
                FemalePatientsText.Text = statistics.FemalePatients.ToString();

                if (totalForGender > 0)
                {
                    MalePercentageText.Text = $"({(statistics.MalePatients * 100.0 / totalForGender):F1}%)";
                    FemalePercentageText.Text = $"({(statistics.FemalePatients * 100.0 / totalForGender):F1}%)";
                }

                // تحديث إحصائيات الأعمار
                ChildrenCountText.Text = statistics.ChildrenCount.ToString();
                TeenagersCountText.Text = statistics.TeenagersCount.ToString();
                YoungAdultsCountText.Text = statistics.YoungAdultsCount.ToString();
                MiddleAgedCountText.Text = statistics.MiddleAgedCount.ToString();
                SeniorsCountText.Text = statistics.SeniorsCount.ToString();

                // تحديث التصنيفات
                LoadCategoriesStatistics(statistics);

                // تحديث الإحصائيات الشهرية
                LoadMonthlyStatistics(statistics);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private SamplePatientStatistics CreateSampleStatistics()
        {
            return new SamplePatientStatistics
            {
                TotalPatients = 1247,
                ActivePatients = 1156,
                NewPatientsThisMonth = 89,
                DebtorPatients = 234,
                AverageAge = 32.5,
                TotalDebts = 125000,
                TotalPayments = 2450000,
                LastPatientName = "مريم أحمد سالم",
                MalePatients = 567,
                FemalePatients = 680,
                ChildrenCount = 156,
                TeenagersCount = 234,
                YoungAdultsCount = 445,
                MiddleAgedCount = 312,
                SeniorsCount = 100,
                RegularPatients = 890,
                VipPatients = 267,
                EmployeePatients = 90
            };
        }

        private void LoadCategoriesStatistics(SamplePatientStatistics statistics)
        {
            CategoriesPanel.Children.Clear();

            // إضافة التصنيفات (يمكن تحسينها لاحقاً لتكون ديناميكية)
            var categories = new[]
            {
                new { Name = "عادي", Count = statistics.RegularPatients },
                new { Name = "VIP", Count = statistics.VipPatients },
                new { Name = "موظف", Count = statistics.EmployeePatients }
            };

            foreach (var category in categories)
            {
                var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };

                var nameBlock = new TextBlock
                {
                    Text = $"{category.Name}:",
                    FontWeight = FontWeights.SemiBold,
                    Width = 80
                };

                var countBlock = new TextBlock
                {
                    Text = category.Count.ToString(),
                    FontWeight = FontWeights.Bold,
                    Foreground = System.Windows.Media.Brushes.DarkBlue
                };

                panel.Children.Add(nameBlock);
                panel.Children.Add(countBlock);
                CategoriesPanel.Children.Add(panel);
            }
        }

        private void LoadMonthlyStatistics(SamplePatientStatistics statistics)
        {
            // مسح البيانات السابقة
            MonthlyNewPatientsPanel.Children.Clear();
            MonthlyVisitsPanel.Children.Clear();

            // إضافة بيانات وهمية للعرض (يمكن تحسينها لاحقاً)
            var months = new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" };
            var newPatientsData = new[] { 15, 23, 18, 31, 27, 19 };
            var visitsData = new[] { 145, 167, 134, 189, 156, 142 };

            for (int i = 0; i < months.Length; i++)
            {
                // المرضى الجدد
                var newPatientPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 3) };
                newPatientPanel.Children.Add(new TextBlock { Text = $"{months[i]}:", Width = 60, FontWeight = FontWeights.SemiBold });
                newPatientPanel.Children.Add(new TextBlock { Text = newPatientsData[i].ToString(), FontWeight = FontWeights.Bold, Foreground = System.Windows.Media.Brushes.Green });
                MonthlyNewPatientsPanel.Children.Add(newPatientPanel);

                // الزيارات
                var visitsPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 3) };
                visitsPanel.Children.Add(new TextBlock { Text = $"{months[i]}:", Width = 60, FontWeight = FontWeights.SemiBold });
                visitsPanel.Children.Add(new TextBlock { Text = visitsData[i].ToString(), FontWeight = FontWeights.Bold, Foreground = System.Windows.Media.Brushes.Blue });
                MonthlyVisitsPanel.Children.Add(visitsPanel);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadStatistics();
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تطوير وظيفة التصدير قريباً", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    // نموذج إحصائيات المرضى للعرض
    public class SamplePatientStatistics
    {
        public int TotalPatients { get; set; }
        public int ActivePatients { get; set; }
        public int NewPatientsThisMonth { get; set; }
        public int DebtorPatients { get; set; }
        public double AverageAge { get; set; }
        public decimal TotalDebts { get; set; }
        public decimal TotalPayments { get; set; }
        public string? LastPatientName { get; set; }
        public int MalePatients { get; set; }
        public int FemalePatients { get; set; }
        public int ChildrenCount { get; set; }
        public int TeenagersCount { get; set; }
        public int YoungAdultsCount { get; set; }
        public int MiddleAgedCount { get; set; }
        public int SeniorsCount { get; set; }
        public int RegularPatients { get; set; }
        public int VipPatients { get; set; }
        public int EmployeePatients { get; set; }
    }
}
