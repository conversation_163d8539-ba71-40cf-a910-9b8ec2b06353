<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeLeavesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="سجلات إجازات الموظف" Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="سجلات إجازات الموظف" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        <DataGrid x:Name="LeavesDataGrid"
                  ItemsSource="{Binding LeaveRecords}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Margin="20"
                  RowHeight="32">
            <DataGrid.Columns>
                <DataGridTextColumn Header="نوع الإجازة" Binding="{Binding LeaveType}" Width="*"/>
                <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat=dd/MM/yyyy}" Width="*"/>
                <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding EndDate, StringFormat=dd/MM/yyyy}" Width="*"/>
                <DataGridTextColumn Header="عدد الأيام" Binding="{Binding DaysCount}" Width="*"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="*"/>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>
        <Button Content="إغلاق" Width="100" Height="36" Margin="20" DockPanel.Dock="Bottom" HorizontalAlignment="Center" Click="Close_Click"/>
    </DockPanel>
</Window> 