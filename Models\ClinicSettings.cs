using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class ClinicSettings : BaseEntity
    {
        [Required(ErrorMessage = "اسم العيادة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم العيادة لا يمكن أن يتجاوز 200 حرف")]
        public string ClinicName { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "العنوان لا يمكن أن يتجاوز 500 حرف")]
        public string? Address { get; set; }
        
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرف")]
        public string? PhoneNumber { get; set; }
        
        [StringLength(20, ErrorMessage = "رقم الهاتف المحمول لا يمكن أن يتجاوز 20 حرف")]
        public string? MobileNumber { get; set; }
        
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        public string? Email { get; set; }
        
        [StringLength(100, ErrorMessage = "الموقع الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        public string? Website { get; set; }
        
        [StringLength(500, ErrorMessage = "ملاحظة الفواتير لا يمكن أن تتجاوز 500 حرف")]
        public string? InvoiceNote { get; set; }
        
        [StringLength(500, ErrorMessage = "ملاحظة الإيصالات لا يمكن أن تتجاوز 500 حرف")]
        public string? ReceiptNote { get; set; }
        
        [StringLength(50, ErrorMessage = "اللغة الافتراضية لا يمكن أن تتجاوز 50 حرف")]
        public string DefaultLanguage { get; set; } = "ar";
        
        [StringLength(100, ErrorMessage = "اسم المدير لا يمكن أن يتجاوز 100 حرف")]
        public string? ManagerName { get; set; }
        
        [StringLength(50, ErrorMessage = "رقم الترخيص لا يمكن أن يتجاوز 50 حرف")]
        public string? LicenseNumber { get; set; }
        
        [StringLength(50, ErrorMessage = "رقم الضريبي لا يمكن أن يتجاوز 50 حرف")]
        public string? TaxNumber { get; set; }
        
        // مسار الشعار
        [StringLength(500, ErrorMessage = "مسار الشعار لا يمكن أن يتجاوز 500 حرف")]
        public string? LogoPath { get; set; }
        
        // مسار التوقيع
        [StringLength(500, ErrorMessage = "مسار التوقيع لا يمكن أن يتجاوز 500 حرف")]
        public string? SignaturePath { get; set; }
    }
} 