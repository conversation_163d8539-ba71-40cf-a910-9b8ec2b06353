using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.Users
{
    public class ChangePasswordViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly IActivityLogService _activityLogService;
        private readonly User _currentUser;

        public ChangePasswordViewModel(IUserService userService, IActivityLogService activityLogService, User currentUser)
        {
            _userService = userService;
            _activityLogService = activityLogService;
            _currentUser = currentUser;

            ChangePasswordCommand = new RelayCommand(async () => await ChangePasswordAsync(), () => CanChangePassword());
            CancelCommand = new RelayCommand(() => CloseWindow());
        }

        private string _currentPassword = string.Empty;
        private string _newPassword = string.Empty;
        private string _confirmPassword = string.Empty;
        private string _errorMessage = string.Empty;

        public string CurrentPassword { get => _currentPassword; set { SetProperty(ref _currentPassword, value); OnPropertyChanged(nameof(CanChangePassword)); } }
        public string NewPassword { get => _newPassword; set { SetProperty(ref _newPassword, value); OnPropertyChanged(nameof(CanChangePassword)); } }
        public string ConfirmPassword { get => _confirmPassword; set { SetProperty(ref _confirmPassword, value); OnPropertyChanged(nameof(CanChangePassword)); } }
        public string ErrorMessage { get => _errorMessage; set => SetProperty(ref _errorMessage, value); }

        public ICommand ChangePasswordCommand { get; }
        public ICommand CancelCommand { get; set; }

        public bool CanChangePassword() => !string.IsNullOrWhiteSpace(CurrentPassword) && !string.IsNullOrWhiteSpace(NewPassword) && !string.IsNullOrWhiteSpace(ConfirmPassword);

        private async Task ChangePasswordAsync()
        {
            IsBusy = true;
            ErrorMessage = string.Empty;

            try
            {
                // التحقق من كلمة المرور الحالية
                if (!await _userService.ValidatePasswordAsync(_currentUser.Id, CurrentPassword))
                {
                    ErrorMessage = "كلمة المرور الحالية غير صحيحة";
                    return;
                }

                // التحقق من تطابق كلمة المرور الجديدة
                if (NewPassword != ConfirmPassword)
                {
                    ErrorMessage = "كلمة المرور الجديدة وتأكيدها غير متطابقين";
                    return;
                }

                // التحقق من قوة كلمة المرور
                if (!IsPasswordStrong(NewPassword))
                {
                    ErrorMessage = "كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم ورمز خاص";
                    return;
                }

                // تغيير كلمة المرور
                await _userService.ChangePasswordAsync(_currentUser.Id, CurrentPassword, NewPassword);

                // تسجيل العملية
                await _activityLogService.LogActivityAsync(_currentUser.Id, "ChangePassword", "User", _currentUser.Id, $"تم تغيير كلمة المرور للمستخدم {_currentUser.FullName}", null, null);

                // إغلاق النافذة
                CloseWindow();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"حدث خطأ أثناء تغيير كلمة المرور: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private bool IsPasswordStrong(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;

            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsLower(c)) hasLower = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else hasSpecial = true;
            }

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        private void CloseWindow()
        {
            // سيتم التعامل مع إغلاق النافذة في الكود الخلفي
        }
    }
} 