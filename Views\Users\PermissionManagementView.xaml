<Window x:Class="AqlanCenterProApp.Views.Users.PermissionManagementView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة صلاحيات المستخدمين" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#21618C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#E74C3C"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C0392B"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#A93226"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1E8449"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="إدارة صلاحيات المستخدمين" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           VerticalAlignment="Center"/>
                <Button Content="تحديث" 
                        Command="{Binding RefreshCommand}"
                        Margin="20,0,0,0"
                        Width="80"
                        Height="30"
                        Background="#3498DB"
                        Foreground="White"
                        BorderThickness="0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- Users Panel -->
            <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="15" Margin="5">
                <StackPanel>
                    <TextBlock Text="المستخدمون" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#2C3E50" 
                               Margin="0,0,0,10"/>
                    
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,0,10"
                             Padding="10,8"
                             FontSize="12"
                             Background="#F8F9FA"
                             BorderBrush="#DEE2E6"
                             BorderThickness="1"/>

                    <ListBox ItemsSource="{Binding Users}"
                             SelectedItem="{Binding SelectedUser}"
                             MaxHeight="400"
                             Background="Transparent"
                             BorderThickness="0">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="10,8" Margin="0,2" Background="#F8F9FA" CornerRadius="4">
                                    <StackPanel>
                                        <TextBlock Text="{Binding FullName}" 
                                                   FontWeight="SemiBold" 
                                                   Foreground="#2C3E50"/>
                                        <TextBlock Text="{Binding Username}" 
                                                   FontSize="11" 
                                                   Foreground="#7F8C8D"/>
                                        <TextBlock Text="{Binding Role.Name}" 
                                                   FontSize="10" 
                                                   Foreground="#95A5A6"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </Border>

            <!-- Permissions Panel -->
            <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="15" Margin="10,5,5,5">
                <StackPanel>
                    <TextBlock Text="الصلاحيات المتاحة" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#2C3E50" 
                               Margin="0,0,0,10"/>
                    
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <ComboBox Grid.Column="0"
                                  ItemsSource="{Binding AvailableModules}"
                                  SelectedItem="{Binding SelectedModule}"
                                  Padding="10,8"
                                  FontSize="12"
                                  Background="White"
                                  BorderBrush="#DEE2E6"
                                  BorderThickness="1"/>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,0">
                            <Button Content="منح الكل" 
                                    Command="{Binding GrantAllPermissionsCommand}"
                                    Width="80"
                                    Height="30"
                                    Background="#27AE60"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Margin="5"/>
                            <Button Content="إلغاء الكل" 
                                    Command="{Binding RevokeAllPermissionsCommand}"
                                    Width="80"
                                    Height="30"
                                    Background="#E74C3C"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Margin="5"/>
                        </StackPanel>
                    </Grid>

                    <DataGrid ItemsSource="{Binding AllPermissions}"
                              SelectedItem="{Binding SelectedPermission}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeRows="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              BorderBrush="#DEE2E6"
                              BorderThickness="1"
                              RowBackground="#FFFFFF"
                              AlternatingRowBackground="#F8F9FA"
                              MaxHeight="400">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Module}" Width="100"/>
                            <DataGridTextColumn Header="اسم الصلاحية" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Content="منح الصلاحية" 
                                Command="{Binding GrantPermissionCommand}"
                                Width="100"
                                Height="30"
                                Background="#27AE60"
                                Foreground="White"
                                BorderThickness="0"
                                Margin="5"/>
                        <Button Content="إلغاء الصلاحية" 
                                Command="{Binding RevokePermissionCommand}"
                                Width="100"
                                Height="30"
                                Background="#E74C3C"
                                Foreground="White"
                                BorderThickness="0"
                                Margin="5"/>
                        <Button Content="منح صلاحيات الوحدة" 
                                Command="{Binding GrantModulePermissionsCommand}"
                                Width="120"
                                Height="30"
                                Background="#3498DB"
                                Foreground="White"
                                BorderThickness="0"
                                Margin="5"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- User Permissions Panel -->
            <Border Grid.Column="2" Background="White" CornerRadius="8" Padding="15" Margin="5">
                <StackPanel>
                    <TextBlock Text="صلاحيات المستخدم المحدد" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#2C3E50" 
                               Margin="0,0,0,10"/>
                    
                    <TextBlock Text="{Binding SelectedUser.FullName, StringFormat='المستخدم: {0}'}"
                               FontWeight="SemiBold"
                               Foreground="#2C3E50"
                               Margin="0,0,0,10"/>

                    <DataGrid ItemsSource="{Binding UserPermissions}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeRows="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              BorderBrush="#DEE2E6"
                              BorderThickness="1"
                              RowBackground="#FFFFFF"
                              AlternatingRowBackground="#F8F9FA"
                              MaxHeight="400">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Permission.Module}" Width="80"/>
                            <DataGridTextColumn Header="الصلاحية" Binding="{Binding Permission.Name}" Width="*"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Text="{Binding UserPermissions.Count, StringFormat='عدد الصلاحيات: {0}'}"
                               FontSize="12"
                               Foreground="#7F8C8D"
                               Margin="0,10,0,0"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.Row="1" Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="جاري التحميل..." Foreground="White" FontSize="14"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 