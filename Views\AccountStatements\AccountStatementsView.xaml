<UserControl x:Class="AqlanCenterProApp.Views.AccountStatements.AccountStatementsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
        xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
        mc:Ignorable="d"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <UserControl.Resources>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#EEEEEE"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
        </Style>
        <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#1976D2"/>
        </Style>
    </UserControl.Resources>
    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  PanningMode="VerticalOnly">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <!-- شريط الأدوات -->
            <Border Grid.Row="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="0,0,0,1" Padding="20,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="البحث:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <TextBox Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}" Width="200" Height="35" Padding="10,5" VerticalAlignment="Center" Margin="0,0,15,0" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        <TextBlock Text="النوع:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <ComboBox ItemsSource="{Binding StatementTypes}" SelectedItem="{Binding SelectedType}" Width="120" Height="35" Padding="10,5" VerticalAlignment="Center" Margin="0,0,15,0" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        <TextBlock Text="من:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <DatePicker SelectedDate="{Binding StartDate}" Width="120" Height="35" VerticalAlignment="Center" Margin="0,0,15,0" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        <TextBlock Text="إلى:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                        <DatePicker SelectedDate="{Binding EndDate}" Width="120" Height="35" VerticalAlignment="Center" Margin="0,0,15,0" BorderBrush="#DDDDDD" BorderThickness="1"/>
                    </StackPanel>
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Content="🔍 بحث" Command="{Binding SearchCommand}" Style="{StaticResource ActionButtonStyle}" Background="#FF9800"/>
                        <Button Content="🔄 تحديث" Command="{Binding RefreshCommand}" Style="{StaticResource ActionButtonStyle}" Background="#9C27B0"/>
                        <Button Content="🖨️ طباعة" Command="{Binding PrintCommand}" Style="{StaticResource ActionButtonStyle}" Background="#607D8B"/>
                        <Button Content="📤 تصدير" Command="{Binding ExportCommand}" Style="{StaticResource ActionButtonStyle}" Background="#795548"/>
                    </StackPanel>
                </Grid>
            </Border>
            <!-- جدول كشوف الحسابات -->
            <DataGrid Grid.Row="1" ItemsSource="{Binding Statements}" SelectedItem="{Binding SelectedStatement}" Style="{StaticResource DataGridStyle}" ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}" Margin="20" RowHeight="40">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="النوع" Binding="{Binding StatementType}" Width="100"/>
                    <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                    <DataGridTextColumn Header="مدين" Binding="{Binding Debit, StringFormat=N0}" Width="120"/>
                    <DataGridTextColumn Header="دائن" Binding="{Binding Credit, StringFormat=N0}" Width="120"/>
                    <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N0}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
            <!-- مؤشر التحميل -->
            <Grid Grid.Row="0" Grid.RowSpan="2" Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="100" Height="10" Margin="0,0,0,10"/>
                    <TextBlock Text="جاري التحميل..." Foreground="White" HorizontalAlignment="Center" FontSize="16"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl> 