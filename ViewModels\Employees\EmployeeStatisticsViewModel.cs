using System;
using System.ComponentModel;
using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeStatisticsViewModel : INotifyPropertyChanged
    {
        private readonly Employee _employee;

        public string EmployeeName => _employee.FullName;
        public string EmployeeId => _employee.EmployeeId.ToString();
        public string Department => _employee.Department;
        public string Position => _employee.Position;
        public DateTime HireDate => _employee.HireDate;
        public string Status => _employee.IsActive ? "نشط" : "غير نشط";

        public string AttendanceStats
        {
            get
            {
                return "إجمالي الأيام: 0\n" +
                       "أيام الحضور: 0\n" +
                       "أيام الغياب: 0\n" +
                       "أيام التأخير: 0\n" +
                       "نسبة الحضور: 0%";
            }
        }

        public string SalaryStats
        {
            get
            {
                return "عدد الرواتب: 0\n" +
                       "إجمالي الرواتب: 0 ريال\n" +
                       "متوسط الراتب: 0 ريال\n" +
                       "أعلى راتب: 0 ريال\n" +
                       "أقل راتب: 0 ريال";
            }
        }

        public string LeaveStats
        {
            get
            {
                return "إجمالي الإجازات: 0\n" +
                       "إجازات موافق عليها: 0\n" +
                       "إجازات في الانتظار: 0\n" +
                       "إجازات مرفوضة: 0\n" +
                       "إجمالي أيام الإجازة: 0";
            }
        }

        public string DocumentStats
        {
            get
            {
                return "إجمالي المستندات: 0\n" +
                       "مستندات صالحة: 0\n" +
                       "مستندات منتهية: 0\n" +
                       "مستندات تنتهي قريباً: 0";
            }
        }

        public EmployeeStatisticsViewModel(Employee employee)
        {
            _employee = employee;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 