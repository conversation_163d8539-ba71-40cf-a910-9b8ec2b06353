using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using AqlanCenterProApp.Data;

namespace AqlanCenterProApp.Views.Dashboard
{
    /// <summary>
    /// نافذة إعادة تعبئة البيانات التجريبية
    /// </summary>
    public partial class ReseedDataWindow : Window
    {
        private readonly AqlanCenterDbContext _context;

        public ReseedDataWindow()
        {
            InitializeComponent();

            // الحصول على DbContext من DI Container
            _context = App.Services.GetRequiredService<AqlanCenterDbContext>();
        }

        private async void ReseedButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأكيد من المستخدم
                var result = MessageBox.Show(
                    "هل أنت متأكد من رغبتك في حذف جميع البيانات الحالية واستبدالها ببيانات تجريبية؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد إعادة التعبئة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning,
                    MessageBoxResult.No);

                if (result != MessageBoxResult.Yes)
                    return;

                // إظهار شريط التقدم
                ReseedButton.IsEnabled = false;
                ProgressBar.Visibility = Visibility.Visible;
                StatusMessage.Visibility = Visibility.Visible;
                StatusMessage.Text = "جاري حذف البيانات القديمة...";

                // تنفيذ إعادة التعبئة
                await Task.Run(async () =>
                {
                    await DatabaseInitializer.ReseedDashboardDataAsync(_context);
                });

                // إخفاء شريط التقدم وإظهار رسالة النجاح
                ProgressBar.Visibility = Visibility.Collapsed;
                StatusMessage.Text = "✅ تم إعادة تعبئة البيانات بنجاح!";
                StatusMessage.Foreground = System.Windows.Media.Brushes.Green;

                // إظهار رسالة نجاح
                MessageBox.Show(
                    "تم إعادة تعبئة البيانات التجريبية بنجاح!\n\nيمكنك الآن العودة إلى الداشبورد لرؤية البيانات الجديدة.",
                    "تم بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // إغلاق النافذة
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                // إخفاء شريط التقدم وإظهار رسالة الخطأ
                ProgressBar.Visibility = Visibility.Collapsed;
                StatusMessage.Text = $"❌ خطأ: {ex.Message}";
                StatusMessage.Foreground = System.Windows.Media.Brushes.Red;
                ReseedButton.IsEnabled = true;

                MessageBox.Show(
                    $"حدث خطأ أثناء إعادة تعبئة البيانات:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
