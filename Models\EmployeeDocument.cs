using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج مستندات الموظف
    /// </summary>
    public class EmployeeDocument : BaseEntity
    {
        /// <summary>
        /// معرف المستند
        /// </summary>
        public int DocumentId { get; set; }

        /// <summary>
        /// معرف الموظف
        /// </summary>
        [Required]
        public int EmployeeId { get; set; }

        /// <summary>
        /// نوع المستند (عقد، مؤهل، شهادة خبرة، خطاب إنذار، خطاب شكر...)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string DocumentType { get; set; } = string.Empty;

        /// <summary>
        /// عنوان المستند
        /// </summary>
        [Required]
        [StringLength(200)]
        public string DocumentTitle { get; set; } = string.Empty;

        /// <summary>
        /// وصف المستند
        /// </summary>
        [StringLength(500)]
        public string? DocumentDescription { get; set; }

        /// <summary>
        /// مسار الملف
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// اسم الملف الأصلي
        /// </summary>
        [StringLength(200)]
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// حجم الملف (بالبايت)
        /// </summary>
        public long FileSize { get; set; } = 0;

        /// <summary>
        /// نوع الملف (PDF, DOC, JPG...)
        /// </summary>
        [StringLength(20)]
        public string? FileExtension { get; set; }

        /// <summary>
        /// تاريخ المستند
        /// </summary>
        public DateTime? DocumentDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية (إذا كان مطبقاً)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// حالة المستند (صالح، منتهي الصلاحية، ملغي)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DocumentStatus { get; set; } = "صالح";

        /// <summary>
        /// هل المستند مطلوب للتجديد
        /// </summary>
        public bool RequiresRenewal { get; set; } = false;

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(1000)]
        public new string? Notes { get; set; }

        /// <summary>
        /// من رفع المستند
        /// </summary>
        [StringLength(100)]
        public string? UploadedBy { get; set; }

        /// <summary>
        /// تاريخ الرفع
        /// </summary>
        public DateTime UploadDate { get; set; } = DateTime.Now;

        // Navigation Property
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}