using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.ViewModels.Doctors
{
    /// <summary>
    /// ViewModel لنافذة جلسات الطبيب
    /// </summary>
    public partial class DoctorSessionsViewModel : ObservableObject
    {
        #region Properties

        [ObservableProperty]
        private Doctor _doctor;

        [ObservableProperty]
        private ObservableCollection<Session> _sessions = new();

        [ObservableProperty]
        private Session? _selectedSession;

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private string _selectedStatus = "جميع الحالات";

        [ObservableProperty]
        private int _totalSessions;

        [ObservableProperty]
        private int _completedSessions;

        [ObservableProperty]
        private int _pendingSessions;

        [ObservableProperty]
        private decimal _totalEarnings;

        #endregion

        #region Commands

        public ICommand AddSessionCommand { get; }
        public ICommand EditSessionCommand { get; }
        public ICommand ViewSessionDetailsCommand { get; }
        public ICommand CompleteSessionCommand { get; }
        public ICommand CancelSessionCommand { get; }
        public ICommand PrintSessionCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand CloseCommand { get; }

        #endregion

        #region Events

        public event EventHandler<bool>? CloseRequested;

        #endregion

        #region Constructor

        public DoctorSessionsViewModel(Doctor doctor)
        {
            _doctor = doctor ?? throw new ArgumentNullException(nameof(doctor));

            // تهيئة الأوامر
            AddSessionCommand = new RelayCommand(AddSession);
            EditSessionCommand = new RelayCommand(EditSession, () => SelectedSession != null);
            ViewSessionDetailsCommand = new RelayCommand(ViewSessionDetails, () => SelectedSession != null);
            CompleteSessionCommand = new RelayCommand(CompleteSession, () => SelectedSession != null && !SelectedSession.IsCompleted);
            CancelSessionCommand = new RelayCommand(CancelSession, () => SelectedSession != null && !SelectedSession.IsCancelled);
            PrintSessionCommand = new RelayCommand(PrintSession, () => SelectedSession != null);
            RefreshCommand = new RelayCommand(async () => await RefreshAsync());
            CloseCommand = new RelayCommand(Close);

            // تحميل البيانات
            _ = Task.Run(async () => await LoadSessionsAsync());
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل جلسات الطبيب
        /// </summary>
        private async Task LoadSessionsAsync()
        {
            try
            {
                // إنشاء بيانات وهمية للجلسات
                var sessions = GenerateSampleSessions();
                
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Sessions.Clear();
                    foreach (var session in sessions)
                    {
                        Sessions.Add(session);
                    }
                    
                    UpdateStatistics();
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل الجلسات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء بيانات وهمية للجلسات
        /// </summary>
        private List<Session> GenerateSampleSessions()
        {
            var random = new Random();
            var sessions = new List<Session>();
            var treatmentTypes = new[] { "تنظيف أسنان", "حشو أسنان", "خلع ضرس", "تقويم أسنان", "تبييض أسنان", "علاج جذور", "تركيب تاج" };
            var statuses = new[] { "مكتملة", "قيد التنفيذ", "ملغية", "مؤجلة" };
            var patientNames = new[] { "أحمد محمد", "فاطمة علي", "محمد أحمد", "عائشة سالم", "علي حسن", "مريم خالد", "يوسف عبدالله", "زينب محمود" };

            for (int i = 1; i <= 20; i++)
            {
                var sessionDate = DateTime.Now.AddDays(-random.Next(0, 90));
                var session = new Session
                {
                    SessionId = i,
                    DoctorId = Doctor.Id,
                    PatientId = random.Next(1, 100),
                    PatientName = patientNames[random.Next(patientNames.Length)],
                    SessionDate = sessionDate,
                    StartTime = TimeSpan.FromHours(8 + random.Next(0, 10)),
                    Duration = 30 + random.Next(0, 60),
                    TreatmentType = treatmentTypes[random.Next(treatmentTypes.Length)],
                    Amount = random.Next(100, 1000),
                    PaidAmount = random.Next(0, 2) == 0 ? 0 : random.Next(50, 1000),
                    Status = statuses[random.Next(statuses.Length)],
                    TreatmentDescription = $"علاج {treatmentTypes[random.Next(treatmentTypes.Length)]} للمريض",
                    Notes = random.Next(0, 3) == 0 ? "ملاحظات خاصة بالجلسة" : null,
                    DoctorNotes = random.Next(0, 3) == 0 ? "ملاحظات الطبيب" : null,
                    DoctorCommission = random.Next(20, 200),
                    Doctor = Doctor.FullName
                };

                sessions.Add(session);
            }

            return sessions.OrderByDescending(s => s.SessionDate).ToList();
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            TotalSessions = Sessions.Count;
            CompletedSessions = Sessions.Count(s => s.Status == "مكتملة");
            PendingSessions = Sessions.Count(s => s.Status == "قيد التنفيذ" || s.Status == "مؤجلة");
            TotalEarnings = Sessions.Where(s => s.Status == "مكتملة").Sum(s => s.DoctorCommission);
        }

        /// <summary>
        /// إضافة جلسة جديدة
        /// </summary>
        private void AddSession()
        {
            try
            {
                System.Windows.MessageBox.Show("سيتم فتح نافذة إضافة جلسة جديدة قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في إضافة الجلسة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل الجلسة
        /// </summary>
        private void EditSession()
        {
            if (SelectedSession == null) return;

            try
            {
                System.Windows.MessageBox.Show($"سيتم فتح نافذة تعديل الجلسة رقم {SelectedSession.SessionId} قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تعديل الجلسة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الجلسة
        /// </summary>
        private void ViewSessionDetails()
        {
            if (SelectedSession == null) return;

            try
            {
                var details = $"تفاصيل الجلسة رقم: {SelectedSession.SessionId}\n" +
                             $"المريض: {SelectedSession.PatientName}\n" +
                             $"نوع العلاج: {SelectedSession.TreatmentType}\n" +
                             $"التاريخ: {SelectedSession.SessionDate:yyyy-MM-dd}\n" +
                             $"الوقت: {SelectedSession.StartTime}\n" +
                             $"المدة: {SelectedSession.Duration} دقيقة\n" +
                             $"التكلفة: {SelectedSession.Cost:N0} ريال\n" +
                             $"المدفوع: {SelectedSession.PaidAmount:N0} ريال\n" +
                             $"المتبقي: {SelectedSession.RemainingAmount:N0} ريال\n" +
                             $"الحالة: {SelectedSession.Status}\n" +
                             $"وصف العلاج: {SelectedSession.TreatmentDescription}\n" +
                             $"ملاحظات: {SelectedSession.Notes}";

                System.Windows.MessageBox.Show(details, "تفاصيل الجلسة",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في عرض تفاصيل الجلسة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تأكيد اكتمال الجلسة
        /// </summary>
        private void CompleteSession()
        {
            if (SelectedSession == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                    $"هل تريد تأكيد اكتمال الجلسة رقم {SelectedSession.SessionId}؟",
                    "تأكيد الاكتمال",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    SelectedSession.Status = "مكتملة";
                    UpdateStatistics();
                    System.Windows.MessageBox.Show("تم تأكيد اكتمال الجلسة", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تأكيد الاكتمال: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء الجلسة
        /// </summary>
        private void CancelSession()
        {
            if (SelectedSession == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                    $"هل تريد إلغاء الجلسة رقم {SelectedSession.SessionId}؟",
                    "تأكيد الإلغاء",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    SelectedSession.Status = "ملغية";
                    UpdateStatistics();
                    System.Windows.MessageBox.Show("تم إلغاء الجلسة", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في إلغاء الجلسة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة الجلسة
        /// </summary>
        private void PrintSession()
        {
            if (SelectedSession == null) return;

            try
            {
                System.Windows.MessageBox.Show("سيتم تنفيذ وظيفة طباعة الجلسة قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في طباعة الجلسة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async Task RefreshAsync()
        {
            await LoadSessionsAsync();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, false);
        }

        #endregion
    }
}
