using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج إجازة الموظف
    /// </summary>
    public class EmployeeLeave : BaseEntity
    {
        /// <summary>
        /// معرف الإجازة
        /// </summary>
        public int LeaveId { get; set; }

        /// <summary>
        /// معرف الموظف
        /// </summary>
        [Required]
        public int EmployeeId { get; set; }

        /// <summary>
        /// نوع الإجازة (سنوية، طارئة، مرضية، غير مدفوعة)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string LeaveType { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ بداية الإجازة
        /// </summary>
        [Required]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الإجازة
        /// </summary>
        [Required]
        public DateTime EndDate { get; set; }

        /// <summary>
        /// عدد أيام الإجازة
        /// </summary>
        public int LeaveDays { get; set; } = 0;

        /// <summary>
        /// سبب الإجازة
        /// </summary>
        [StringLength(500)]
        public string? LeaveReason { get; set; }

        /// <summary>
        /// حالة الإجازة (معلق، موافق عليه، مرفوض، ملغي)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string LeaveStatus { get; set; } = "معلق";

        /// <summary>
        /// تاريخ تقديم الطلب
        /// </summary>
        [Required]
        public DateTime RequestDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ الموافقة/الرفض
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// من وافق على الإجازة
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// ملاحظات الموافقة/الرفض
        /// </summary>
        [StringLength(1000)]
        public string? ApprovalNotes { get; set; }

        /// <summary>
        /// هل الإجازة مدفوعة
        /// </summary>
        public bool IsPaidLeave { get; set; } = true;

        /// <summary>
        /// مرفقات (مستندات طبية، خطابات...)
        /// </summary>
        [StringLength(500)]
        public string? Attachments { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        public new string? Notes { get; set; }

        // Navigation Property
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}