using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج الطبيب
    /// </summary>
    public class Doctor : BaseEntity
    {
        /// <summary>
        /// رقم الطبيب (تسلسلي تلقائي)
        /// </summary>
        public int DoctorId { get; set; }

        /// <summary>
        /// اسم الطبيب الكامل
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// التخصص
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Specialization { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف الثابت
        /// </summary>
        [StringLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// رقم الجوال
        /// </summary>
        [StringLength(20)]
        public string? Mobile { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [StringLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// نوع التعاقد (دائم، دوام جزئي، بالنسبة)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ContractType { get; set; } = "دائم";

        /// <summary>
        /// النسبة المئوية للطبيب (في حالة التعاقد بالنسبة)
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal CommissionPercentage { get; set; } = 0;

        /// <summary>
        /// عملة العمولة (ر.ي، ر.س، $)
        /// </summary>
        [MaxLength(10)]
        public string? CommissionCurrency { get; set; } = "ر.ي";

        /// <summary>
        /// الراتب الثابت (في حالة التعاقد الدائم)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? FixedSalary { get; set; }

        /// <summary>
        /// عملة الراتب (ر.ي، ر.س، $)
        /// </summary>
        [MaxLength(10)]
        public string? SalaryCurrency { get; set; } = "ر.ي";

        /// <summary>
        /// صورة الطبيب (مسار الملف)
        /// </summary>
        [StringLength(500)]
        public string? DoctorImage { get; set; }

        /// <summary>
        /// حالة الطبيب (نشط، غير نشط، في إجازة)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط";

        /// <summary>
        /// هل الطبيب نشط (للتوافق مع النظام القديم)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// رقم الترخيص الطبي
        /// </summary>
        [StringLength(50)]
        public string? LicenseNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الترخيص
        /// </summary>
        public DateTime? LicenseExpiryDate { get; set; }

        /// <summary>
        /// تاريخ بداية العمل
        /// </summary>
        [Required]
        public DateTime JoinDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ انتهاء العقد (اختياري)
        /// </summary>
        public DateTime? ContractEndDate { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// الجنس
        /// </summary>
        [StringLength(10)]
        public string? Gender { get; set; }

        /// <summary>
        /// الجنسية
        /// </summary>
        [StringLength(50)]
        public string? Nationality { get; set; }

        /// <summary>
        /// المؤهلات العلمية
        /// </summary>
        [StringLength(500)]
        public string? Qualifications { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        public string? AdditionalNotes { get; set; }

        /// <summary>
        /// هل الطبيب متاح للمواعيد
        /// </summary>
        public bool IsAvailableForAppointments { get; set; } = true;

        /// <summary>
        /// تقييم الطبيب (من 1 إلى 5)
        /// </summary>
        [Column(TypeName = "decimal(3,2)")]
        public decimal? Rating { get; set; }

        /// <summary>
        /// عدد التقييمات
        /// </summary>
        public int RatingCount { get; set; } = 0;

        /// <summary>
        /// إجمالي الدخل المحقق
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalEarnings { get; set; } = 0;

        /// <summary>
        /// عدد المرضى المعالجين
        /// </summary>
        public int TotalPatientsCount { get; set; } = 0;

        /// <summary>
        /// عدد الجلسات المكتملة
        /// </summary>
        public int CompletedSessionsCount { get; set; } = 0;

        // Navigation Properties
        public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    }
}
