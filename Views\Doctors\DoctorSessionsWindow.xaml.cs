using System;
using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Doctors;

namespace AqlanCenterProApp.Views.Doctors
{
    /// <summary>
    /// Interaction logic for DoctorSessionsWindow.xaml
    /// </summary>
    public partial class DoctorSessionsWindow : Window
    {
        public DoctorSessionsWindow(Doctor doctor)
        {
            InitializeComponent();

            try
            {
                // إنشاء ViewModel
                var viewModel = new DoctorSessionsViewModel(doctor);
                DataContext = viewModel;

                // ربط الأحداث
                viewModel.CloseRequested += (sender, result) =>
                {
                    DialogResult = result;
                    Close();
                };
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                Close();
            }
        }
    }
}
