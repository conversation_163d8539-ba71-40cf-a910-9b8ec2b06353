<Window x:Class="AqlanCenterProApp.Views.Purchases.AddEditPurchaseView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:AqlanCenterProApp.Views.Purchases"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernDataGrid" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="False"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20,15">
            <TextBlock Text="{Binding WindowTitle}" 
                       Foreground="White" 
                       FontSize="20" 
                       FontWeight="Bold"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <!-- Purchase Details -->
                <GroupBox Header="تفاصيل المشتريات" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Invoice Number -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                            <TextBlock Text="رقم الفاتورة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Purchase.InvoiceNumber}" 
                                     Padding="10,8" Margin="5"
                                     IsReadOnly="{Binding IsEditMode}"/>
                        </StackPanel>

                        <!-- Supplier -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                            <TextBlock Text="المورد:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Suppliers}"
                                      SelectedItem="{Binding SelectedSupplier}"
                                      DisplayMemberPath="Name"
                                      Padding="10,8" Margin="5"/>
                        </StackPanel>

                        <!-- Purchase Date -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                            <TextBlock Text="تاريخ المشتريات:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding Purchase.PurchaseDate}" 
                                        Margin="5" 
                                        IsEnabled="False"/>
                        </StackPanel>

                        <!-- Total Amount -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                            <TextBlock Text="المجموع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Purchase.TotalAmount, StringFormat=C}" 
                                     Padding="10,8" Margin="5"
                                     IsReadOnly="True"
                                     Background="#F5F5F5"/>
                        </StackPanel>

                        <!-- Notes -->
                        <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Margin="5">
                            <TextBlock Text="ملاحظات:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Purchase.Notes}" 
                                     Padding="10,8" Margin="5"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Purchase Items -->
                <GroupBox Header="عناصر المشتريات" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <!-- Items Controls -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Button Content="إضافة عنصر" 
                                    Command="{Binding AddItemCommand}"
                                    Background="#2196F3" Foreground="White"
                                    Padding="15,8" Margin="5"/>
                            <Button Content="حذف العنصر المحدد" 
                                    Command="{Binding RemoveItemCommand}"
                                    Background="#FF9800" Foreground="White"
                                    Padding="15,8" Margin="5"/>
                        </StackPanel>

                        <!-- Items DataGrid -->
                        <DataGrid ItemsSource="{Binding PurchaseItems}"
                                  SelectedItem="{Binding SelectedPurchaseItem}"
                                  AutoGenerateColumns="False"
                                  Height="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="معرف العنصر" 
                                                    Binding="{Binding InventoryItemId}" 
                                                    Width="100"/>
                                <DataGridTextColumn Header="الكمية" 
                                                    Binding="{Binding Quantity}" 
                                                    Width="100"/>
                                <DataGridTextColumn Header="سعر الوحدة" 
                                                    Binding="{Binding UnitPrice, StringFormat=C}" 
                                                    Width="120"/>
                                <DataGridTextColumn Header="المجموع" 
                                                    Binding="{Binding TotalPrice, StringFormat=C}" 
                                                    Width="120"
                                                    IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="حفظ" 
                        Command="{Binding SaveCommand}"
                        Background="#2196F3" Foreground="White"
                        Padding="15,8" Margin="5" Width="120"/>
                <Button Content="إلغاء" 
                        Command="{Binding CancelCommand}"
                        Background="#F44336" Foreground="White"
                        Padding="15,8" Margin="5" Width="120"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                        VerticalAlignment="Center">
                <TextBlock Text="جاري الحفظ..." 
                           Foreground="White" 
                           FontSize="16" 
                           HorizontalAlignment="Center"/>
                <ProgressBar IsIndeterminate="True" 
                             Width="200" 
                             Height="4" 
                             Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 