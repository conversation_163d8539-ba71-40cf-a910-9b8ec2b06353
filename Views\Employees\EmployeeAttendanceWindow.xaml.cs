using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Employees;

namespace AqlanCenterProApp.Views.Employees
{
    public partial class EmployeeAttendanceWindow : Window
    {
        public EmployeeAttendanceWindow()
        {
            InitializeComponent();
        }

        public EmployeeAttendanceWindow(IEmployeeService employeeService, Employee employee)
        {
            InitializeComponent();
            DataContext = new EmployeeAttendanceViewModel(employeeService, employee);
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
} 