using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace AqlanCenterProApp.Services.Implementations
{
    public class LabOrderService : ILabOrderService
    {
        private readonly AqlanCenterDbContext _context;

        public LabOrderService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        // CRUD Operations
        public async Task<IEnumerable<LabOrder>> GetAllLabOrdersAsync()
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Include(lo => lo.Shade)
                .Include(lo => lo.Appointment)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync()
                .ConfigureAwait(false);
        }

        public async Task<LabOrder?> GetLabOrderByIdAsync(int id)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Include(lo => lo.Shade)
                .Include(lo => lo.Appointment)
                .FirstOrDefaultAsync(lo => lo.LabOrderId == id);
        }

        public async Task<LabOrder> AddLabOrderAsync(LabOrder labOrder)
        {
            labOrder.CreatedAt = DateTime.Now;
            labOrder.UpdatedAt = DateTime.Now;
            labOrder.OrderNumber = await GenerateOrderNumberAsync();

            _context.LabOrders.Add(labOrder);
            await _context.SaveChangesAsync();
            return labOrder;
        }

        public async Task<LabOrder> UpdateLabOrderAsync(LabOrder labOrder)
        {
            labOrder.UpdatedAt = DateTime.Now;
            _context.LabOrders.Update(labOrder);
            await _context.SaveChangesAsync();
            return labOrder;
        }

        public async Task<bool> DeleteLabOrderAsync(int id)
        {
            var labOrder = await _context.LabOrders.FindAsync(id);
            if (labOrder == null) return false;

            _context.LabOrders.Remove(labOrder);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> LabOrderExistsAsync(int id)
        {
            return await _context.LabOrders.AnyAsync(lo => lo.LabOrderId == id);
        }

        // Search and Filter
        public async Task<IEnumerable<LabOrder>> SearchLabOrdersAsync(string searchTerm)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.OrderNumber.Contains(searchTerm) ||
                           lo.Patient.FullName.Contains(searchTerm) ||
                           lo.Doctor.FullName.Contains(searchTerm) ||
                           lo.Lab.Name.Contains(searchTerm) ||
                           lo.WorkType.Contains(searchTerm))
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetLabOrdersByPatientAsync(int patientId)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.PatientId == patientId)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetLabOrdersByDoctorAsync(int doctorId)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.DoctorId == doctorId)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetLabOrdersByLabAsync(int labId)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.LabId == labId)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetLabOrdersByStatusAsync(string status)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.Status == status)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetLabOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.SendDate >= startDate && lo.SendDate <= endDate)
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetOverdueLabOrdersAsync()
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.Status == "قيد التنفيذ" &&
                           lo.ExpectedReturnDate.HasValue &&
                           lo.ExpectedReturnDate.Value < DateTime.Now)
                .OrderBy(lo => lo.ExpectedReturnDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LabOrder>> GetReadyLabOrdersAsync()
        {
            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.Status == "جاهز")
                .OrderByDescending(lo => lo.SendDate)
                .ToListAsync();
        }

        // Special Operations
        public async Task<string> GenerateOrderNumberAsync()
        {
            var today = DateTime.Now;
            var prefix = $"LO{today:yyyyMMdd}";

            var lastOrder = await _context.LabOrders
                .Where(lo => lo.OrderNumber.StartsWith(prefix))
                .OrderByDescending(lo => lo.OrderNumber)
                .FirstOrDefaultAsync();

            if (lastOrder == null)
            {
                return $"{prefix}001";
            }

            var lastNumber = int.Parse(lastOrder.OrderNumber.Substring(prefix.Length));
            return $"{prefix}{(lastNumber + 1):D3}";
        }

        public async Task<bool> UpdateLabOrderStatusAsync(int labOrderId, string status)
        {
            var labOrder = await _context.LabOrders.FindAsync(labOrderId);
            if (labOrder == null) return false;

            labOrder.Status = status;
            labOrder.UpdatedAt = DateTime.Now;

            if (status == "تم التسليم" && !labOrder.ActualReturnDate.HasValue)
            {
                labOrder.ActualReturnDate = DateTime.Now;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<LabOrder> DuplicateLabOrderAsync(int labOrderId)
        {
            var originalOrder = await GetLabOrderByIdAsync(labOrderId);
            if (originalOrder == null) throw new ArgumentException("الطلب غير موجود");

            var newOrder = new LabOrder
            {
                PatientId = originalOrder.PatientId,
                DoctorId = originalOrder.DoctorId,
                LabId = originalOrder.LabId,
                WorkType = originalOrder.WorkType,
                PiecesCount = originalOrder.PiecesCount,
                ShadeId = originalOrder.ShadeId,
                SendDate = DateTime.Now,
                Status = "منشأ",
                Notes = $"نسخة من الطلب {originalOrder.OrderNumber}",
                Cost = originalOrder.Cost
            };

            return await AddLabOrderAsync(newOrder);
        }

        public async Task<bool> RateLabOrderAsync(int labOrderId, int rating, string? notes)
        {
            var labOrder = await _context.LabOrders.FindAsync(labOrderId);
            if (labOrder == null) return false;

            labOrder.Rating = rating;
            labOrder.RatingNotes = notes;
            labOrder.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAsPaidAsync(int labOrderId, string? invoiceNumber)
        {
            var labOrder = await _context.LabOrders.FindAsync(labOrderId);
            if (labOrder == null) return false;

            labOrder.IsPaid = true;
            labOrder.PaymentDate = DateTime.Now;
            labOrder.InvoiceNumber = invoiceNumber;
            labOrder.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        // Reports and Analytics
        public async Task<IEnumerable<LabOrder>> GetLabOrdersReportAsync(DateTime startDate, DateTime endDate, string? status = null, int? labId = null)
        {
            var query = _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => lo.SendDate >= startDate && lo.SendDate <= endDate);

            if (!string.IsNullOrEmpty(status))
                query = query.Where(lo => lo.Status == status);

            if (labId.HasValue)
                query = query.Where(lo => lo.LabId == labId.Value);

            return await query.OrderByDescending(lo => lo.SendDate).ToListAsync();
        }

        public async Task<object> GetLabOrdersStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            var orders = await _context.LabOrders
                .Where(lo => lo.SendDate >= startDate && lo.SendDate <= endDate)
                .ToListAsync();

            return new
            {
                TotalOrders = orders.Count,
                OrdersByStatus = orders.GroupBy(lo => lo.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() }),
                TotalCost = orders.Where(lo => lo.Cost.HasValue).Sum(lo => lo.Cost.Value),
                OverdueOrders = orders.Count(lo => lo.IsOverdue),
                AverageRating = orders.Where(lo => lo.Rating.HasValue).Average(lo => lo.Rating.Value)
            };
        }

        public async Task<IEnumerable<object>> GetLabPerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            var labs = await _context.Labs
                .Include(l => l.LabOrders.Where(lo => lo.SendDate >= startDate && lo.SendDate <= endDate))
                .Where(l => l.IsActive)
                .ToListAsync();

            return labs.Select(lab => new
            {
                LabName = lab.Name,
                TotalOrders = lab.LabOrders.Count,
                CompletedOrders = lab.LabOrders.Count(lo => lo.Status == "تم التسليم"),
                OverdueOrders = lab.LabOrders.Count(lo => lo.IsOverdue),
                AverageRating = lab.LabOrders.Where(lo => lo.Rating.HasValue).Average(lo => lo.Rating.Value),
                TotalCost = lab.LabOrders.Where(lo => lo.Cost.HasValue).Sum(lo => lo.Cost.Value)
            });
        }

        // Notifications
        public async Task<IEnumerable<LabOrder>> GetOrdersNeedingNotificationAsync()
        {
            var today = DateTime.Now;
            var threeDaysFromNow = today.AddDays(3);

            return await _context.LabOrders
                .Include(lo => lo.Patient)
                .Include(lo => lo.Doctor)
                .Include(lo => lo.Lab)
                .Where(lo => (lo.Status == "قيد التنفيذ" && lo.ExpectedReturnDate.HasValue &&
                             lo.ExpectedReturnDate.Value <= threeDaysFromNow) ||
                           (lo.Status == "جاهز" && lo.TrialDate.HasValue &&
                            lo.TrialDate.Value <= threeDaysFromNow))
                .ToListAsync();
        }

        public async Task<bool> SendWhatsAppNotificationAsync(int labOrderId, string message)
        {
            // Implementation for WhatsApp notification
            await Task.Delay(100); // Simulate sending
            return true;
        }

        public async Task<bool> SendSMSNotificationAsync(int labOrderId, string message)
        {
            // Implementation for SMS notification
            await Task.Delay(100); // Simulate sending
            return true;
        }

        // Export
        public async Task<byte[]> ExportLabOrdersToPdfAsync(IEnumerable<LabOrder> labOrders)
        {
            // Implementation for PDF export
            await Task.Delay(100); // Simulate export
            return Encoding.UTF8.GetBytes("PDF Content");
        }

        public async Task<byte[]> ExportLabOrdersToExcelAsync(IEnumerable<LabOrder> labOrders)
        {
            // Implementation for Excel export
            await Task.Delay(100); // Simulate export
            return Encoding.UTF8.GetBytes("Excel Content");
        }

        public async Task<byte[]> ExportLabOrdersToCsvAsync(IEnumerable<LabOrder> labOrders)
        {
            // Implementation for CSV export
            await Task.Delay(100); // Simulate export
            return Encoding.UTF8.GetBytes("CSV Content");
        }

        // Print
        public async Task<bool> PrintLabOrderAsync(int labOrderId)
        {
            // Implementation for printing
            await Task.Delay(100); // Simulate printing
            return true;
        }

        public async Task<bool> PrintLabOrderReportAsync(IEnumerable<LabOrder> labOrders)
        {
            // Implementation for printing report
            await Task.Delay(100); // Simulate printing
            return true;
        }
    }
}