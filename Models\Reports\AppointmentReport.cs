using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// تقرير المواعيد
    /// </summary>
    public class AppointmentReport : ReportBase
    {
        public AppointmentReport()
        {
            Type = ReportType.AppointmentReport;
        }

        // إحصائيات عامة
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int RescheduledAppointments { get; set; }
        public int NoShowAppointments { get; set; }

        // معدلات الحضور
        public double AttendanceRate { get; set; }
        public double CancellationRate { get; set; }
        public double NoShowRate { get; set; }

        // إحصائيات حسب الطبيب
        public List<DoctorAppointmentStats> DoctorStats { get; set; } = new();

        // إحصائيات حسب اليوم
        public List<DailyAppointmentCount> DailyStats { get; set; } = new();

        // إحصائيات حسب الشهر
        public List<MonthlyAppointmentCount> MonthlyStats { get; set; } = new();

        // المواعيد حسب الوقت
        public int MorningAppointments { get; set; }
        public int AfternoonAppointments { get; set; }
        public int EveningAppointments { get; set; }

        // متوسط مدة الموعد
        public double AverageAppointmentDuration { get; set; }

        // المواعيد المتأخرة
        public int DelayedAppointments { get; set; }
        public double AverageDelayMinutes { get; set; }

        // المرضى الأكثر التزاماً
        public List<PatientCommitment> TopCommittedPatients { get; set; } = new();

        // المرضى الأقل التزاماً
        public List<PatientCommitment> LeastCommittedPatients { get; set; } = new();
    }

    /// <summary>
    /// إحصائيات المواعيد حسب الطبيب
    /// </summary>
    public class DoctorAppointmentStats
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public double CompletionRate { get; set; }
        public double AverageRating { get; set; }
    }

    /// <summary>
    /// عدد المواعيد حسب اليوم
    /// </summary>
    public class DailyAppointmentCount
    {
        public DateTime Date { get; set; }
        public string DayName { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
    }

    /// <summary>
    /// عدد المواعيد حسب الشهر
    /// </summary>
    public class MonthlyAppointmentCount
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
    }

    /// <summary>
    /// التزام المريض
    /// </summary>
    public class PatientCommitment
    {
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public int TotalAppointments { get; set; }
        public int AttendedAppointments { get; set; }
        public int MissedAppointments { get; set; }
        public double AttendanceRate { get; set; }
        public int ConsecutiveMissed { get; set; }
    }
} 