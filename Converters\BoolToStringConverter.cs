using System;
using System.Globalization;
using System.Windows.Data;

namespace AqlanCenterProApp.Converters
{
    /// <summary>
    /// محول لتحويل القيم المنطقية إلى نصوص عربية
    /// </summary>
    public class BoolToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "نشط" : "غير نشط";
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Equals("نشط", StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }
    }
} 