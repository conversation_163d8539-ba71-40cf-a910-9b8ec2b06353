using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class NotificationSettings : BaseEntity
    {
        // إعدادات الإشعارات الداخلية
        public bool EnableInternalNotifications { get; set; } = true;
        
        public bool NotifyNewAppointments { get; set; } = true;
        
        public bool NotifyAppointmentReminders { get; set; } = true;
        
        public bool NotifyNewPatients { get; set; } = false;
        
        public bool NotifyLowInventory { get; set; } = true;
        
        public bool NotifyPaymentDue { get; set; } = true;
        
        public bool NotifySystemErrors { get; set; } = true;
        
        // إعدادات واتساب
        public bool EnableWhatsAppNotifications { get; set; } = false;
        
        [StringLength(20, ErrorMessage = "رقم واتساب لا يمكن أن يتجاوز 20 حرف")]
        public string? WhatsAppNumber { get; set; }
        
        [StringLength(500, ErrorMessage = "رابط واتساب لا يمكن أن يتجاوز 500 حرف")]
        public string? WhatsAppApiUrl { get; set; }
        
        [StringLength(200, ErrorMessage = "مفتاح واتساب لا يمكن أن يتجاوز 200 حرف")]
        public string? WhatsAppApiKey { get; set; }
        
        public bool WhatsAppAppointmentReminders { get; set; } = false;
        
        public bool WhatsAppPaymentReminders { get; set; } = false;
        
        public bool WhatsAppMarketingMessages { get; set; } = false;
        
        // إعدادات SMS
        public bool EnableSmsNotifications { get; set; } = false;
        
        [StringLength(200, ErrorMessage = "مزود خدمة SMS لا يمكن أن يتجاوز 200 حرف")]
        public string? SmsProvider { get; set; }
        
        [StringLength(500, ErrorMessage = "رابط SMS لا يمكن أن يتجاوز 500 حرف")]
        public string? SmsApiUrl { get; set; }
        
        [StringLength(200, ErrorMessage = "مفتاح SMS لا يمكن أن يتجاوز 200 حرف")]
        public string? SmsApiKey { get; set; }
        
        [StringLength(200, ErrorMessage = "اسم المستخدم SMS لا يمكن أن يتجاوز 200 حرف")]
        public string? SmsUsername { get; set; }
        
        [StringLength(200, ErrorMessage = "كلمة مرور SMS لا يمكن أن تتجاوز 200 حرف")]
        public string? SmsPassword { get; set; }
        
        public bool SmsAppointmentReminders { get; set; } = false;
        
        public bool SmsPaymentReminders { get; set; } = false;
        
        public bool SmsMarketingMessages { get; set; } = false;
        
        // إعدادات البريد الإلكتروني
        public bool EnableEmailNotifications { get; set; } = false;
        
        [StringLength(100, ErrorMessage = "خادم SMTP لا يمكن أن يتجاوز 100 حرف")]
        public string? SmtpServer { get; set; }
        
        public int SmtpPort { get; set; } = 587;
        
        [StringLength(100, ErrorMessage = "بريد المرسل لا يمكن أن يتجاوز 100 حرف")]
        public string? SmtpEmail { get; set; }
        
        [StringLength(200, ErrorMessage = "كلمة مرور البريد لا يمكن أن تتجاوز 200 حرف")]
        public string? SmtpPassword { get; set; }
        
        public bool SmtpUseSsl { get; set; } = true;
        
        public bool EmailAppointmentReminders { get; set; } = false;
        
        public bool EmailPaymentReminders { get; set; } = false;
        
        public bool EmailMarketingMessages { get; set; } = false;
        
        // إعدادات عامة للإشعارات
        public int ReminderHoursBeforeAppointment { get; set; } = 24;
        
        public int ReminderHoursBeforePayment { get; set; } = 48;
        
        public bool SendNotificationsOnWeekends { get; set; } = false;
        
        public string NotificationStartTime { get; set; } = "08:00";
        
        public string NotificationEndTime { get; set; } = "20:00";
    }
} 