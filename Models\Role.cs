using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class Role : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string RoleName { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // Permissions
        public bool CanViewPatients { get; set; } = false;
        public bool CanAddPatients { get; set; } = false;
        public bool CanEditPatients { get; set; } = false;
        public bool CanDeletePatients { get; set; } = false;
        
        public bool CanViewDoctors { get; set; } = false;
        public bool CanAddDoctors { get; set; } = false;
        public bool CanEditDoctors { get; set; } = false;
        public bool CanDeleteDoctors { get; set; } = false;
        
        public bool CanViewEmployees { get; set; } = false;
        public bool CanAddEmployees { get; set; } = false;
        public bool CanEditEmployees { get; set; } = false;
        public bool CanDeleteEmployees { get; set; } = false;
        
        public bool CanViewAppointments { get; set; } = false;
        public bool CanAddAppointments { get; set; } = false;
        public bool CanEditAppointments { get; set; } = false;
        public bool CanDeleteAppointments { get; set; } = false;
        
        public bool CanViewInvoices { get; set; } = false;
        public bool CanAddInvoices { get; set; } = false;
        public bool CanEditInvoices { get; set; } = false;
        public bool CanDeleteInvoices { get; set; } = false;
        
        public bool CanViewPayments { get; set; } = false;
        public bool CanAddPayments { get; set; } = false;
        public bool CanEditPayments { get; set; } = false;
        public bool CanDeletePayments { get; set; } = false;
        
        public bool CanViewReports { get; set; } = false;
        public bool CanExportReports { get; set; } = false;
        
        public bool CanViewSettings { get; set; } = false;
        public bool CanEditSettings { get; set; } = false;
        
        public bool CanBackupRestore { get; set; } = false;
        
        public bool CanManageBackup { get; set; } = false;
        
        /// <summary>
        /// هل يمكن للمستخدم إلغاء إيصالات القبض؟
        /// </summary>
        public bool CanCancelReceipts { get; set; } = false;
        
        // Navigation Properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}
