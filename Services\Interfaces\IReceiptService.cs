using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة سندات القبض
    /// </summary>
    public interface IReceiptService
    {
        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// جلب جميع سندات القبض
        /// </summary>
        Task<IEnumerable<Receipt>> GetAllReceiptsAsync();

        /// <summary>
        /// جلب سند قبض بالمعرف
        /// </summary>
        Task<Receipt?> GetReceiptByIdAsync(int id);

        /// <summary>
        /// جلب سند قبض برقم السند
        /// </summary>
        Task<Receipt?> GetReceiptByNumberAsync(string receiptNumber);

        /// <summary>
        /// جلب سندات قبض مريض معين
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByPatientAsync(int patientId);

        /// <summary>
        /// إنشاء سند قبض جديد
        /// </summary>
        Task<Receipt> CreateReceiptAsync(Receipt receipt);

        /// <summary>
        /// تحديث سند قبض
        /// </summary>
        Task<Receipt> UpdateReceiptAsync(Receipt receipt);

        /// <summary>
        /// حذف سند قبض
        /// </summary>
        Task<bool> DeleteReceiptAsync(int id);

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في سندات القبض
        /// </summary>
        Task<IEnumerable<Receipt>> SearchReceiptsAsync(string searchTerm);

        /// <summary>
        /// جلب سندات القبض حسب التاريخ
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByDateAsync(DateTime date);

        /// <summary>
        /// جلب سندات القبض في فترة زمنية
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب سندات القبض حسب طريقة الدفع
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByPaymentMethodAsync(string paymentMethod);

        /// <summary>
        /// جلب سندات القبض حسب الحالة
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByStatusAsync(string status);

        #endregion

        #region الترقيم التلقائي

        /// <summary>
        /// الحصول على رقم سند القبض التالي
        /// </summary>
        Task<string> GetNextReceiptNumberAsync();

        /// <summary>
        /// التحقق من توفر رقم سند قبض
        /// </summary>
        Task<bool> IsReceiptNumberAvailableAsync(string receiptNumber);

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// جلب إحصائيات سندات القبض
        /// </summary>
        Task<ReceiptStatistics> GetReceiptStatisticsAsync();

        /// <summary>
        /// جلب إجمالي المدفوعات في فترة زمنية
        /// </summary>
        Task<decimal> GetTotalPaymentsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إحصائيات الدفع حسب الطريقة
        /// </summary>
        Task<Dictionary<string, decimal>> GetPaymentsByMethodAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إحصائيات الدفع حسب اليوم
        /// </summary>
        Task<Dictionary<DateTime, decimal>> GetDailyPaymentsAsync(DateTime startDate, DateTime endDate);

        #endregion

        #region الطباعة والتصدير

        /// <summary>
        /// طباعة سند قبض
        /// </summary>
        Task<bool> PrintReceiptAsync(int receiptId);

        /// <summary>
        /// تصدير سند قبض إلى PDF
        /// </summary>
        Task<byte[]> ExportReceiptToPdfAsync(int receiptId);

        /// <summary>
        /// تصدير سند قبض إلى Excel
        /// </summary>
        Task<byte[]> ExportReceiptToExcelAsync(int receiptId);

        #endregion

        #region الربط مع الفواتير والجلسات

        /// <summary>
        /// ربط سند قبض بفاتورة
        /// </summary>
        Task<bool> LinkReceiptToInvoiceAsync(int receiptId, int invoiceId);

        /// <summary>
        /// ربط سند قبض بجلسة
        /// </summary>
        Task<bool> LinkReceiptToSessionAsync(int receiptId, int sessionId);

        /// <summary>
        /// جلب سندات القبض المرتبطة بفاتورة
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsByInvoiceAsync(int invoiceId);

        /// <summary>
        /// جلب سندات القبض المرتبطة بجلسة
        /// </summary>
        Task<IEnumerable<Receipt>> GetReceiptsBySessionAsync(int sessionId);

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة بيانات سند القبض
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateReceiptAsync(Receipt receipt);

        /// <summary>
        /// التحقق من صحة المبلغ المدفوع
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidatePaymentAmountAsync(int patientId, decimal amount);

        #endregion
    }

    /// <summary>
    /// نموذج إحصائيات سندات القبض
    /// </summary>
    public class ReceiptStatistics
    {
        public int TotalReceipts { get; set; }
        public int CompletedReceipts { get; set; }
        public int PendingReceipts { get; set; }
        public int CancelledReceipts { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal CompletedAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public Dictionary<string, int> ReceiptsByStatus { get; set; } = new();
        public Dictionary<string, decimal> PaymentsByMethod { get; set; } = new();
        public Dictionary<string, int> ReceiptsByMonth { get; set; } = new();
    }
} 