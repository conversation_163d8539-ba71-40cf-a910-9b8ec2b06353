using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// تقرير المرضى
    /// </summary>
    public class PatientReport : ReportBase
    {
        public PatientReport()
        {
            Type = ReportType.PatientReport;
        }

        // إحصائيات عامة
        public int TotalPatients { get; set; }
        public int NewPatients { get; set; }
        public int ActivePatients { get; set; }
        public int InactivePatients { get; set; }

        // إحصائيات حسب الجنس
        public int MalePatients { get; set; }
        public int FemalePatients { get; set; }

        // إحصائيات حسب العمر
        public int ChildrenCount { get; set; } // 0-12
        public int TeenagersCount { get; set; } // 13-19
        public int AdultsCount { get; set; } // 20-59
        public int ElderlyCount { get; set; } // 60+

        // إحصائيات حسب نوع العلاج
        public int OrthodonticPatients { get; set; }
        public int GeneralDentalPatients { get; set; }
        public int SurgeryPatients { get; set; }
        public int CosmeticPatients { get; set; }

        // إحصائيات حسب الحالة
        public int UnderTreatmentPatients { get; set; }
        public int CompletedTreatmentPatients { get; set; }
        public int DiscontinuedPatients { get; set; }

        // متوسط العمر
        public double AverageAge { get; set; }

        // توزيع المرضى حسب المنطقة
        public Dictionary<string, int> PatientsByRegion { get; set; } = new();

        // المرضى الأكثر نشاطاً
        public List<PatientActivity> TopActivePatients { get; set; } = new();

        // المرضى الجدد حسب الشهر
        public List<MonthlyPatientCount> NewPatientsByMonth { get; set; } = new();
    }

    /// <summary>
    /// نشاط المريض
    /// </summary>
    public class PatientActivity
    {
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public int AppointmentsCount { get; set; }
        public int TreatmentsCount { get; set; }
        public decimal TotalPaid { get; set; }
        public DateTime LastVisit { get; set; }
    }

    /// <summary>
    /// عدد المرضى الجدد حسب الشهر
    /// </summary>
    public class MonthlyPatientCount
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int Count { get; set; }
    }
} 