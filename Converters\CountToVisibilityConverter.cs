using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace AqlanCenterProApp.Converters
{
    public class CountToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return Visibility.Collapsed;

                if (value is int count)
                    return count > 0 ? Visibility.Visible : Visibility.Collapsed;

                if (value is long lcount)
                    return lcount > 0 ? Visibility.Visible : Visibility.Collapsed;

                if (value is double dcount)
                    return dcount > 0 ? Visibility.Visible : Visibility.Collapsed;

                // محاولة تحويل إلى رقم
                if (int.TryParse(value.ToString(), out int parsedCount))
                    return parsedCount > 0 ? Visibility.Visible : Visibility.Collapsed;

                return Visibility.Collapsed;
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}