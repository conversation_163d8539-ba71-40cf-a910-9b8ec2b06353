using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using AqlanCenterProApp.Helpers;

namespace AqlanCenterProApp.Data
{
    public class AqlanCenterDbContextFactory : IDesignTimeDbContextFactory<AqlanCenterDbContext>
    {
        public AqlanCenterDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();

            // استخدام قاعدة البيانات الموحدة
            var connectionString = DatabasePathHelper.GetConnectionString();
            optionsBuilder.UseSqlite(connectionString);

            return new AqlanCenterDbContext(optionsBuilder.Options);
        }
    }
}
