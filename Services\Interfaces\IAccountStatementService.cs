using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة كشوف الحساب
    /// </summary>
    public interface IAccountStatementService
    {
        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// جلب جميع كشوف الحساب
        /// </summary>
        Task<IEnumerable<AccountStatement>> GetAllStatementsAsync();

        /// <summary>
        /// جلب كشف حساب بالمعرف
        /// </summary>
        Task<AccountStatement?> GetStatementByIdAsync(int id);

        /// <summary>
        /// جلب كشف حساب برقم الكشف
        /// </summary>
        Task<AccountStatement?> GetStatementByNumberAsync(string statementNumber);

        /// <summary>
        /// إنشاء كشف حساب جديد
        /// </summary>
        Task<AccountStatement> CreateStatementAsync(AccountStatement statement);

        /// <summary>
        /// تحديث كشف حساب
        /// </summary>
        Task<AccountStatement> UpdateStatementAsync(AccountStatement statement);

        /// <summary>
        /// حذف كشف حساب
        /// </summary>
        Task<bool> DeleteStatementAsync(int id);

        #endregion

        #region إنشاء كشوف الحساب التلقائية

        /// <summary>
        /// إنشاء كشف حساب لمريض
        /// </summary>
        Task<AccountStatement> GeneratePatientStatementAsync(int patientId, DateTime startDate, DateTime endDate, string generatedBy);

        /// <summary>
        /// إنشاء كشف حساب لطبيب
        /// </summary>
        Task<AccountStatement> GenerateDoctorStatementAsync(int doctorId, DateTime startDate, DateTime endDate, string generatedBy);

        /// <summary>
        /// إنشاء كشف حساب لموظف
        /// </summary>
        Task<AccountStatement> GenerateEmployeeStatementAsync(int employeeId, DateTime startDate, DateTime endDate, string generatedBy);

        /// <summary>
        /// إنشاء كشف حساب لمورد
        /// </summary>
        Task<AccountStatement> GenerateSupplierStatementAsync(int supplierId, DateTime startDate, DateTime endDate, string generatedBy);

        /// <summary>
        /// إنشاء كشف حساب لمعمل
        /// </summary>
        Task<AccountStatement> GenerateLabStatementAsync(int labId, DateTime startDate, DateTime endDate, string generatedBy);

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في كشوف الحساب
        /// </summary>
        Task<IEnumerable<AccountStatement>> SearchStatementsAsync(string searchTerm);

        /// <summary>
        /// جلب كشوف الحساب حسب التاريخ
        /// </summary>
        Task<IEnumerable<AccountStatement>> GetStatementsByDateAsync(DateTime date);

        /// <summary>
        /// جلب كشوف الحساب في فترة زمنية
        /// </summary>
        Task<IEnumerable<AccountStatement>> GetStatementsByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب كشوف الحساب حسب نوع الكيان
        /// </summary>
        Task<IEnumerable<AccountStatement>> GetStatementsByEntityTypeAsync(string entityType);

        /// <summary>
        /// جلب كشوف الحساب حسب الكيان
        /// </summary>
        Task<IEnumerable<AccountStatement>> GetStatementsByEntityAsync(string entityType, int entityId);

        #endregion

        #region الترقيم التلقائي

        /// <summary>
        /// الحصول على رقم كشف الحساب التالي
        /// </summary>
        Task<string> GetNextStatementNumberAsync();

        /// <summary>
        /// التحقق من توفر رقم كشف حساب
        /// </summary>
        Task<bool> IsStatementNumberAvailableAsync(string statementNumber);

        #endregion

        #region حساب الأرصدة

        /// <summary>
        /// حساب رصيد مريض
        /// </summary>
        Task<decimal> CalculatePatientBalanceAsync(int patientId, DateTime? asOfDate = null);

        /// <summary>
        /// حساب رصيد طبيب
        /// </summary>
        Task<decimal> CalculateDoctorBalanceAsync(int doctorId, DateTime? asOfDate = null);

        /// <summary>
        /// حساب رصيد موظف
        /// </summary>
        Task<decimal> CalculateEmployeeBalanceAsync(int employeeId, DateTime? asOfDate = null);

        /// <summary>
        /// حساب رصيد مورد
        /// </summary>
        Task<decimal> CalculateSupplierBalanceAsync(int supplierId, DateTime? asOfDate = null);

        /// <summary>
        /// حساب رصيد معمل
        /// </summary>
        Task<decimal> CalculateLabBalanceAsync(int labId, DateTime? asOfDate = null);

        #endregion

        #region تفاصيل الحركات المالية

        /// <summary>
        /// جلب حركات مريض مالية
        /// </summary>
        Task<IEnumerable<FinancialTransaction>> GetPatientTransactionsAsync(int patientId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب حركات طبيب مالية
        /// </summary>
        Task<IEnumerable<FinancialTransaction>> GetDoctorTransactionsAsync(int doctorId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب حركات موظف مالية
        /// </summary>
        Task<IEnumerable<FinancialTransaction>> GetEmployeeTransactionsAsync(int employeeId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب حركات مورد مالية
        /// </summary>
        Task<IEnumerable<FinancialTransaction>> GetSupplierTransactionsAsync(int supplierId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب حركات معمل مالية
        /// </summary>
        Task<IEnumerable<FinancialTransaction>> GetLabTransactionsAsync(int labId, DateTime startDate, DateTime endDate);

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// جلب إحصائيات كشوف الحساب
        /// </summary>
        Task<AccountStatementStatistics> GetStatementStatisticsAsync();

        /// <summary>
        /// جلب إجمالي الأرصدة حسب النوع
        /// </summary>
        Task<Dictionary<string, decimal>> GetTotalBalancesByTypeAsync();

        /// <summary>
        /// جلب المرضى المدينين
        /// </summary>
        Task<IEnumerable<Patient>> GetDebtorPatientsAsync(decimal minimumDebt = 0);

        /// <summary>
        /// جلب المرضى الدائنين
        /// </summary>
        Task<IEnumerable<Patient>> GetCreditorPatientsAsync(decimal minimumCredit = 0);

        #endregion

        #region الطباعة والتصدير

        /// <summary>
        /// طباعة كشف حساب
        /// </summary>
        Task<bool> PrintStatementAsync(int statementId);

        /// <summary>
        /// تصدير كشف حساب إلى PDF
        /// </summary>
        Task<byte[]> ExportStatementToPdfAsync(int statementId);

        /// <summary>
        /// تصدير كشف حساب إلى Excel
        /// </summary>
        Task<byte[]> ExportStatementToExcelAsync(int statementId);

        /// <summary>
        /// تصدير كشف حساب إلى CSV
        /// </summary>
        Task<byte[]> ExportStatementToCsvAsync(int statementId);

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة بيانات كشف الحساب
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateStatementAsync(AccountStatement statement);

        /// <summary>
        /// التحقق من صحة الفترة الزمنية
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateDateRangeAsync(DateTime startDate, DateTime endDate);

        #endregion

        /// <summary>
        /// جلب جميع كشوف الحساب
        /// </summary>
        Task<List<AccountStatement>> GetAccountStatementsAsync(string searchTerm, string selectedType, DateTime? startDate, DateTime? endDate);
    }
} 