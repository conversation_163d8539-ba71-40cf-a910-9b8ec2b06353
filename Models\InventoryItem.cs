using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class InventoryItem : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [StringLength(50)]
        public string? Code { get; set; }
        
        [StringLength(50)]
        public string? Barcode { get; set; }
        
        [Required]
        public string Category { get; set; } = string.Empty; // أدوية، أدوات، أجهزة، مواد مختبرية، إلخ
        
        [StringLength(50)]
        public string? Unit { get; set; } // قطعة، علبة، كيلو، لتر، إلخ
        
        public decimal CurrentQuantity { get; set; } = 0;
        
        public decimal MinimumQuantity { get; set; } = 0; // الحد الأدنى للتنبيه
        
        public decimal MaximumQuantity { get; set; } = 0; // الحد الأقصى للتخزين
        
        public decimal AverageCost { get; set; } = 0; // متوسط التكلفة
        
        public decimal LastPurchasePrice { get; set; } = 0; // آخر سعر شراء
        
        public DateTime? ExpiryDate { get; set; } // تاريخ الصلاحية
        
        public int? ExpiryWarningDays { get; set; } = 30; // عدد أيام التنبيه قبل انتهاء الصلاحية
        
        [StringLength(100)]
        public string? Location { get; set; } // موقع التخزين
        
        public bool IsActive { get; set; } = true;
        
        public bool RequiresExpiryTracking { get; set; } = false; // هل يتطلب تتبع الصلاحية
        
        [StringLength(500)]
        public new string? Notes { get; set; }
        
        // Navigation Properties
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
    }
}
