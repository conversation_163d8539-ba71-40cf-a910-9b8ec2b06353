<Window x:Class="AqlanCenterProApp.Views.LabOrders.AddEditLabOrderView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding WindowTitle}" Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="FormGroupStyle" TargetType="StackPanel">
            <Setter Property="Margin" Value="10"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="{Binding WindowTitle}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="حفظ" 
                            Command="{Binding SaveCommand}"
                            Style="{StaticResource SuccessButtonStyle}"/>
                    <Button Content="إلغاء" 
                            Command="{Binding CancelCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column -->
                <StackPanel Grid.Column="0">
                    <!-- Patient Information -->
                    <GroupBox Header="معلومات المريض" Margin="0,0,10,20" Padding="15">
                        <StackPanel>
                            <TextBlock Text="المريض" Style="{StaticResource FormLabelStyle}"/>
                            <ComboBox ItemsSource="{Binding Patients}"
                                      SelectedItem="{Binding SelectedPatient}"
                                      DisplayMemberPath="FullName"
                                      Style="{StaticResource ModernComboBoxStyle}"/>

                            <TextBlock Text="الطبيب" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <ComboBox ItemsSource="{Binding Doctors}"
                                      SelectedItem="{Binding SelectedDoctor}"
                                      DisplayMemberPath="FullName"
                                      Style="{StaticResource ModernComboBoxStyle}"/>

                            <TextBlock Text="الموعد (اختياري)" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <ComboBox ItemsSource="{Binding Appointments}"
                                      SelectedItem="{Binding SelectedAppointment}"
                                      DisplayMemberPath="AppointmentDateTime"
                                      Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Lab Information -->
                    <GroupBox Header="معلومات المعمل" Margin="0,0,10,20" Padding="15">
                        <StackPanel>
                            <TextBlock Text="المعمل" Style="{StaticResource FormLabelStyle}"/>
                            <ComboBox ItemsSource="{Binding Labs}"
                                      SelectedItem="{Binding SelectedLab}"
                                      DisplayMemberPath="Name"
                                      Style="{StaticResource ModernComboBoxStyle}"/>

                            <TextBlock Text="اللون (اختياري)" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <ComboBox ItemsSource="{Binding Shades}"
                                      SelectedItem="{Binding SelectedShade}"
                                      DisplayMemberPath="Name"
                                      Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>

                <!-- Right Column -->
                <StackPanel Grid.Column="1">
                    <!-- Order Details -->
                    <GroupBox Header="تفاصيل الطلب" Margin="10,0,0,20" Padding="15">
                        <StackPanel>
                            <TextBlock Text="رقم الطلب" Style="{StaticResource FormLabelStyle}"/>
                            <TextBox Text="{Binding LabOrder.OrderNumber}" 
                                     Style="{StaticResource ModernTextBoxStyle}"/>

                            <TextBlock Text="نوع العمل" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <TextBox Text="{Binding LabOrder.WorkType}" 
                                     Style="{StaticResource ModernTextBoxStyle}"/>

                            <TextBlock Text="عدد القطع" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <TextBox Text="{Binding LabOrder.PiecesCount}" 
                                     Style="{StaticResource ModernTextBoxStyle}"/>

                            <TextBlock Text="التكلفة" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <TextBox Text="{Binding LabOrder.Cost}" 
                                     Style="{StaticResource ModernTextBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Dates -->
                    <GroupBox Header="التواريخ" Margin="10,0,0,20" Padding="15">
                        <StackPanel>
                            <TextBlock Text="تاريخ الإرسال" Style="{StaticResource FormLabelStyle}"/>
                            <DatePicker SelectedDate="{Binding LabOrder.SendDate}" 
                                        Style="{StaticResource ModernDatePickerStyle}"/>

                            <TextBlock Text="تاريخ الاستلام المتوقع" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <DatePicker SelectedDate="{Binding LabOrder.ExpectedReturnDate}" 
                                        Style="{StaticResource ModernDatePickerStyle}"/>

                            <TextBlock Text="تاريخ التجربة (اختياري)" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <DatePicker SelectedDate="{Binding LabOrder.TrialDate}" 
                                        Style="{StaticResource ModernDatePickerStyle}"/>

                            <TextBlock Text="تاريخ التركيب النهائي (اختياري)" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <DatePicker SelectedDate="{Binding LabOrder.FinalInstallDate}" 
                                        Style="{StaticResource ModernDatePickerStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Status and Notes -->
                    <GroupBox Header="الحالة والملاحظات" Margin="10,0,0,20" Padding="15">
                        <StackPanel>
                            <TextBlock Text="حالة الطلب" Style="{StaticResource FormLabelStyle}"/>
                            <ComboBox SelectedItem="{Binding LabOrder.Status}" 
                                      Style="{StaticResource ModernComboBoxStyle}">
                                <ComboBoxItem Content="منشأ"/>
                                <ComboBoxItem Content="قيد التنفيذ"/>
                                <ComboBoxItem Content="جاهز"/>
                                <ComboBoxItem Content="تم التسليم"/>
                                <ComboBoxItem Content="ملغي"/>
                            </ComboBox>

                            <TextBlock Text="الملاحظات" Style="{StaticResource FormLabelStyle}" Margin="0,15,0,5"/>
                            <TextBox Text="{Binding LabOrder.Notes}" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     Height="80"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#FAFAFA" Padding="20,10" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="حالة التحقق: " FontWeight="SemiBold"/>
                    <TextBlock Text="{Binding IsValid, Converter={StaticResource BoolToTextConverter}}" 
                               Foreground="{Binding IsValid, Converter={StaticResource BoolToColorConverter}}" 
                               FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="طباعة" 
                            Command="{Binding PrintCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                    <Button Content="إرسال واتساب" 
                            Command="{Binding SendWhatsAppCommand}"
                            Style="{StaticResource ModernButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                           Foreground="White" 
                           FontSize="16" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 