using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Data;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة التسجيل المتقدمة
    /// </summary>
    public class LoggingService : ILoggingService
    {
        private readonly ILogger<LoggingService> _logger;
        private readonly AqlanCenterDbContext _context;
        private readonly string _logDirectory;
        private readonly string _applicationVersion;

        public LoggingService(ILogger<LoggingService> logger, AqlanCenterDbContext context)
        {
            _logger = logger;
            _context = context;
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            _applicationVersion = GetApplicationVersion();

            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            Directory.CreateDirectory(_logDirectory);
        }

        public async Task LogInfoAsync(string message, string category = "General", Dictionary<string, object>? properties = null)
        {
            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Info, message, category, null, properties);
            await WriteLogAsync(logEntry);
            _logger.LogInformation("[{Category}] {Message}", category, message);
        }

        public async Task LogWarningAsync(string message, string category = "General", Dictionary<string, object>? properties = null)
        {
            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Warning, message, category, null, properties);
            await WriteLogAsync(logEntry);
            _logger.LogWarning("[{Category}] {Message}", category, message);
        }

        public async Task LogErrorAsync(Exception exception, string message = "", string category = "General", Dictionary<string, object>? properties = null)
        {
            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Error, message, category, exception, properties);
            await WriteLogAsync(logEntry);
            _logger.LogError(exception, "[{Category}] {Message}", category, message);
        }

        public async Task LogCriticalAsync(Exception exception, string message = "", string category = "General", Dictionary<string, object>? properties = null)
        {
            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Critical, message, category, exception, properties);
            await WriteLogAsync(logEntry);
            _logger.LogCritical(exception, "[{Category}] {Message}", category, message);
        }

        public async Task<string> LogOperationStartAsync(string operationName, string category = "Operation", Dictionary<string, object>? properties = null)
        {
            var operationId = Guid.NewGuid().ToString();
            var props = properties ?? new Dictionary<string, object>();
            props["OperationId"] = operationId;
            props["StartTime"] = DateTime.Now;

            await LogInfoAsync($"بدء العملية: {operationName}", category, props);
            return operationId;
        }

        public async Task LogOperationEndAsync(string operationId, bool success = true, string? errorMessage = null, Dictionary<string, object>? properties = null)
        {
            var props = properties ?? new Dictionary<string, object>();
            props["OperationId"] = operationId;
            props["Success"] = success;
            props["EndTime"] = DateTime.Now;

            var message = success ? $"انتهت العملية بنجاح: {operationId}" : $"فشلت العملية: {operationId} - {errorMessage}";

            if (success)
                await LogInfoAsync(message, "Operation", props);
            else
                await LogErrorAsync(new Exception(errorMessage ?? "عملية فاشلة"), message, "Operation", props);
        }

        public async Task LogPerformanceAsync(string operationName, TimeSpan duration, string category = "Performance", Dictionary<string, object>? properties = null)
        {
            var props = properties ?? new Dictionary<string, object>();
            props["Duration"] = duration.TotalMilliseconds;
            props["DurationFormatted"] = duration.ToString(@"mm\:ss\.fff");

            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Performance, $"أداء العملية: {operationName} - {duration.TotalMilliseconds:F2}ms", category, null, props);
            await WriteLogAsync(logEntry);
        }

        public async Task LogDatabaseOperationAsync(string operation, string tableName, TimeSpan? duration = null, bool success = true, string? errorMessage = null)
        {
            var props = new Dictionary<string, object>
            {
                ["Operation"] = operation,
                ["TableName"] = tableName,
                ["Success"] = success
            };

            if (duration.HasValue)
            {
                props["Duration"] = duration.Value.TotalMilliseconds;
                props["DurationFormatted"] = duration.Value.ToString(@"mm\:ss\.fff");
            }

            var message = success
                ? $"عملية قاعدة البيانات: {operation} على {tableName}"
                : $"فشل في عملية قاعدة البيانات: {operation} على {tableName} - {errorMessage}";

            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.Database, message, "Database", success ? null : new Exception(errorMessage ?? "خطأ في قاعدة البيانات"), props);
            await WriteLogAsync(logEntry);
        }

        public async Task LogUserInteractionAsync(string action, string module, string? userId = null, Dictionary<string, object>? properties = null)
        {
            var props = properties ?? new Dictionary<string, object>();
            props["Action"] = action;
            props["Module"] = module;
            if (!string.IsNullOrEmpty(userId))
                props["UserId"] = userId;

            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.UserInteraction, $"تفاعل المستخدم: {action} في {module}", "UserInteraction", null, props);
            logEntry.UserId = userId;
            await WriteLogAsync(logEntry);
        }

        public async Task LogSystemStatusAsync(string component, string status, Dictionary<string, object>? metrics = null)
        {
            var props = metrics ?? new Dictionary<string, object>();
            props["Component"] = component;
            props["Status"] = status;

            var logEntry = CreateLogEntry(Services.Interfaces.LogLevel.SystemStatus, $"حالة النظام: {component} - {status}", "SystemStatus", null, props);
            await WriteLogAsync(logEntry);
        }

        public async Task<IEnumerable<LogEntry>> GetLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? category = null, Services.Interfaces.LogLevel? level = null)
        {
            try
            {
                var logs = new List<LogEntry>();
                var logFiles = Directory.GetFiles(_logDirectory, "*.json")
                    .OrderByDescending(f => File.GetCreationTime(f));

                foreach (var file in logFiles)
                {
                    try
                    {
                        var content = await File.ReadAllTextAsync(file);
                        var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                        foreach (var line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;

                            var logEntry = JsonSerializer.Deserialize<LogEntry>(line);
                            if (logEntry == null) continue;

                            // تطبيق المرشحات
                            if (fromDate.HasValue && logEntry.Timestamp < fromDate.Value) continue;
                            if (toDate.HasValue && logEntry.Timestamp > toDate.Value) continue;
                            if (!string.IsNullOrEmpty(category) && !logEntry.Category.Equals(category, StringComparison.OrdinalIgnoreCase)) continue;
                            if (level.HasValue && logEntry.Level != (Services.Interfaces.LogLevel)level.Value) continue;

                            logs.Add(logEntry);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في قراءة ملف السجل: {File}", file);
                    }
                }

                return logs.OrderByDescending(l => l.Timestamp);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على السجلات");
                return Enumerable.Empty<LogEntry>();
            }
        }

        public async Task CleanupOldLogsAsync(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(_logDirectory, "*.json");

                foreach (var file in logFiles)
                {
                    var fileDate = File.GetCreationTime(file);
                    if (fileDate < cutoffDate)
                    {
                        File.Delete(file);
                        await LogInfoAsync($"تم حذف ملف السجل القديم: {Path.GetFileName(file)}", "Maintenance");
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "خطأ في تنظيف السجلات القديمة", "Maintenance");
            }
        }

        public async Task<byte[]> ExportLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string format = "CSV")
        {
            try
            {
                var logs = await GetLogsAsync(fromDate, toDate);

                if (format.ToUpper() == "CSV")
                {
                    return await ExportToCsvAsync(logs);
                }
                else
                {
                    return await ExportToJsonAsync(logs);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "خطأ في تصدير السجلات", "Export");
                return Array.Empty<byte>();
            }
        }

        private LogEntry CreateLogEntry(Services.Interfaces.LogLevel level, string message, string category, Exception? exception, Dictionary<string, object>? properties)
        {
            var logEntry = new LogEntry
            {
                Level = level,
                Category = category,
                Message = message,
                Properties = properties ?? new Dictionary<string, object>(),
                ApplicationVersion = _applicationVersion,
                SessionId = GetSessionId()
            };

            if (exception != null)
            {
                logEntry.Exception = exception.Message;
                logEntry.StackTrace = exception.StackTrace;
            }

            return logEntry;
        }

        private async Task WriteLogAsync(LogEntry logEntry)
        {
            try
            {
                var fileName = $"log_{DateTime.Now:yyyyMMdd}.json";
                var filePath = Path.Combine(_logDirectory, fileName);

                var json = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.AppendAllTextAsync(filePath, json + Environment.NewLine);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في كتابة السجل إلى الملف");
            }
        }

        private async Task<byte[]> ExportToCsvAsync(IEnumerable<LogEntry> logs)
        {
            var csv = "Timestamp,Level,Category,Message,Exception,UserId,MachineName\n";

            foreach (var log in logs)
            {
                csv += $"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.Level},{log.Category},\"{log.Message.Replace("\"", "\"\"")}\",\"{log.Exception?.Replace("\"", "\"\"")}\",{log.UserId},{log.MachineName}\n";
            }

            return System.Text.Encoding.UTF8.GetBytes(csv);
        }

        private async Task<byte[]> ExportToJsonAsync(IEnumerable<LogEntry> logs)
        {
            var json = JsonSerializer.Serialize(logs, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            return System.Text.Encoding.UTF8.GetBytes(json);
        }

        private string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "*******";
            }
            catch
            {
                return "*******";
            }
        }

        private string GetSessionId()
        {
            // يمكن تحسين هذا لاحقاً لربطه بجلسة المستخدم الفعلية
            return Process.GetCurrentProcess().Id.ToString();
        }
    }
}
