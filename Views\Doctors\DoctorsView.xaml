<UserControl x:Class="AqlanCenterProApp.Views.Doctors.DoctorsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="800"
             d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             Loaded="DoctorsView_Loaded">

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0"
                Background="White"
                Padding="20,15"
                Margin="10,10,10,0">
            <TextBlock Text="إدارة الأطباء"
                       FontSize="28"
                       FontWeight="Bold"
                       Foreground="#333"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        </Border>

        <!-- الإحصائيات السريعة -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="0,0,0,1"
                Padding="20,15">
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <!-- إجمالي الأطباء -->
                <Border Background="#E3F2FD"
                        CornerRadius="8"
                        Padding="15,10"
                        Margin="0,0,20,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="👥"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <StackPanel>
                            <TextBlock Text="إجمالي الأطباء"
                                       FontSize="12"
                                       Foreground="#666"
                                       FontWeight="Bold"/>
                            <TextBlock Text="{Binding TotalDoctors}"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#1976D2"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- الأطباء النشطين -->
                <Border Background="#E8F5E8"
                        CornerRadius="8"
                        Padding="15,10"
                        Margin="0,0,20,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✅"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <StackPanel>
                            <TextBlock Text="الأطباء النشطين"
                                       FontSize="12"
                                       Foreground="#666"
                                       FontWeight="Bold"/>
                            <TextBlock Text="{Binding ActiveDoctors}"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#388E3C"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- الأطباء غير النشطين -->
                <Border Background="#FFEBEE"
                        CornerRadius="8"
                        Padding="15,10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <StackPanel>
                            <TextBlock Text="غير النشطين"
                                       FontSize="12"
                                       Foreground="#666"
                                       FontWeight="Bold"/>
                            <TextBlock Text="{Binding InactiveDoctors}"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#D32F2F"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>



        <!-- شريط البحث والأدوات -->
        <Border Grid.Row="2"
                Background="White"
                Padding="15,10"
                Margin="10,5,10,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- البحث والتصفية -->
                <StackPanel Grid.Column="0"
                            Orientation="Horizontal">
                    <!-- البحث -->
                    <Border Background="#F5F5F5"
                            CornerRadius="20"
                            Padding="15,8"
                            Margin="0,0,15,0"
                            Width="300">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔍"
                                       FontSize="16"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBox x:Name="SearchTextBox"
                                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     FontSize="14"
                                     VerticalAlignment="Center"
                                     Width="250"/>
                        </StackPanel>
                    </Border>

                    <!-- تصفية التخصص -->
                    <ComboBox ItemsSource="{Binding Specializations}"
                              SelectedItem="{Binding SelectedSpecialization}"
                              Width="200"
                              Height="35"
                              Margin="0,0,10,0"
                              FontSize="14"/>

                    <!-- تصفية الحالة -->
                    <ComboBox ItemsSource="{Binding StatusList}"
                              SelectedItem="{Binding SelectedStatus}"
                              Width="120"
                              Height="35"
                              Margin="0,0,10,0"
                              FontSize="14"/>

                    <!-- إظهار غير النشطين -->
                    <CheckBox Content="إظهار غير النشطين"
                              IsChecked="{Binding ShowInactiveDoctors}"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"
                              FontSize="14"/>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <Button Content="➕ إضافة طبيب"
                            Command="{Binding AddDoctorCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            FontWeight="Bold"
                            FontSize="14"/>

                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="#2196F3"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            FontWeight="Bold"
                            FontSize="14"/>

                    <Button Content="📊 إحصائيات"
                            Command="{Binding ShowGeneralStatisticsCommand}"
                            Background="#FF9800"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            FontWeight="Bold"
                            FontSize="14"/>

                    <Button Content="🖨️ طباعة"
                            Command="{Binding PrintCommand}"
                            Background="#9C27B0"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            FontWeight="Bold"
                            FontSize="14"/>

                    <Button Content="📤 تصدير"
                            Command="{Binding ExportCommand}"
                            Background="#607D8B"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            FontWeight="Bold"
                            FontSize="14"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- قائمة الأطباء -->
        <Border Grid.Row="3"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="8"
                Margin="10,5,10,10">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto">
                <DataGrid x:Name="DoctorsDataGrid"
                          ItemsSource="{Binding Doctors}"
                          SelectedItem="{Binding SelectedDoctor}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          CanUserAddRows="False"
                          RowHeight="50"
                          EnableRowVirtualization="True"
                          EnableColumnVirtualization="True"
                          VirtualizingPanel.IsVirtualizing="True"
                          VirtualizingPanel.VirtualizationMode="Recycling"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#E0E0E0"
                          AlternatingRowBackground="#F9F9F9"
                          Background="White"
                          BorderThickness="0"
                          FontSize="14">

                    <!-- أنماط DataGrid -->
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background"
                                    Value="#2196F3"/>
                            <Setter Property="Foreground"
                                    Value="White"/>
                            <Setter Property="FontWeight"
                                    Value="Bold"/>
                            <Setter Property="FontSize"
                                    Value="14"/>
                            <Setter Property="Padding"
                                    Value="12,8"/>
                            <Setter Property="HorizontalContentAlignment"
                                    Value="Center"/>
                            <Setter Property="BorderBrush"
                                    Value="#1976D2"/>
                            <Setter Property="BorderThickness"
                                    Value="0,0,1,0"/>
                        </Style>

                        <Style TargetType="DataGridRow">
                            <Setter Property="Background"
                                    Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                        Value="True">
                                    <Setter Property="Background"
                                            Value="#E3F2FD"/>
                                </Trigger>
                                <Trigger Property="IsSelected"
                                        Value="True">
                                    <Setter Property="Background"
                                            Value="#BBDEFB"/>
                                    <Setter Property="Foreground"
                                            Value="#1976D2"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>

                        <Style TargetType="DataGridCell">
                            <Setter Property="Padding"
                                    Value="8,4"/>
                            <Setter Property="BorderThickness"
                                    Value="0"/>
                            <Setter Property="VerticalAlignment"
                                    Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsSelected"
                                        Value="True">
                                    <Setter Property="Background"
                                            Value="Transparent"/>
                                    <Setter Property="Foreground"
                                            Value="#1976D2"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Resources>

                    <!-- قائمة السياق -->
                    <DataGrid.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="📝 تعديل الطبيب"
                                      Command="{Binding EditDoctorCommand}"
                                      Icon="✏️"/>
                            <MenuItem Header="👁️ عرض التفاصيل"
                                      Command="{Binding ViewDetailsCommand}"
                                      Icon="👁️"/>
                            <Separator/>
                            <MenuItem Header="📊 إحصائيات الطبيب"
                                      Command="{Binding ViewStatisticsCommand}"
                                      Icon="📈"/>
                            <MenuItem Header="📱 إرسال واتساب"
                                      Command="{Binding SendWhatsAppCommand}"
                                      CommandParameter="{Binding SelectedDoctor}"
                                      Icon="💬"/>
                            <Separator/>
                            <MenuItem Header="🔄 تفعيل/تعطيل"
                                      Command="{Binding ToggleStatusCommand}"
                                      Icon="🔄"/>
                            <MenuItem Header="📋 نسخ البيانات"
                                      Command="{Binding CopyCommand}"
                                      Icon="📋"/>
                            <Separator/>
                            <MenuItem Header="🗑️ حذف الطبيب"
                                      Command="{Binding DeleteDoctorCommand}"
                                      Icon="❌"/>
                        </ContextMenu>
                    </DataGrid.ContextMenu>

                    <DataGrid.Columns>
                        <!-- الصورة -->
                        <DataGridTemplateColumn Header="الصورة"
                                                Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Width="40"
                                            Height="40"
                                            CornerRadius="20"
                                            Background="#E0E0E0">
                                        <TextBlock Text="👨‍⚕️"
                                                   FontSize="20"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- رقم الطبيب -->
                        <DataGridTextColumn Header="الرقم"
                                            Width="80"
                                            Binding="{Binding Id}"/>

                        <!-- اسم الطبيب -->
                        <DataGridTextColumn Header="اسم الطبيب"
                                            Width="200"
                                            Binding="{Binding FullName}"/>

                        <!-- التخصص -->
                        <DataGridTextColumn Header="التخصص"
                                            Width="180"
                                            Binding="{Binding Specialization}"/>

                        <!-- رقم الجوال -->
                        <DataGridTextColumn Header="رقم الجوال"
                                            Width="120"
                                            Binding="{Binding Mobile}"/>

                        <!-- نوع التعاقد -->
                        <DataGridTextColumn Header="نوع التعاقد"
                                            Width="100"
                                            Binding="{Binding ContractType}"/>

                        <!-- النسبة/الراتب -->
                        <DataGridTemplateColumn Header="النسبة/الراتب"
                                                Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="{}{0}%">
                                                <Binding Path="CommissionPercentage"/>
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- تاريخ الانضمام -->
                        <DataGridTextColumn Header="تاريخ الانضمام"
                                            Width="120"
                                            Binding="{Binding JoinDate, StringFormat=yyyy/MM/dd}"/>

                        <!-- الحالة -->
                        <DataGridTemplateColumn Header="الحالة"
                                                Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10"
                                            Padding="8,4">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}"
                                                                 Value="نشط">
                                                        <Setter Property="Background"
                                                                Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}"
                                                                 Value="غير نشط">
                                                        <Setter Property="Background"
                                                                Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}"
                                                                 Value="في إجازة">
                                                        <Setter Property="Background"
                                                                Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   HorizontalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}"
                                                                     Value="نشط">
                                                            <Setter Property="Foreground"
                                                                    Value="#388E3C"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}"
                                                                     Value="غير نشط">
                                                            <Setter Property="Foreground"
                                                                    Value="#D32F2F"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}"
                                                                     Value="في إجازة">
                                                            <Setter Property="Foreground"
                                                                    Value="#F57C00"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- التقييم -->
                        <DataGridTemplateColumn Header="التقييم"
                                                Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <TextBlock Text="⭐"
                                                   FontSize="14"/>
                                        <TextBlock Text="{Binding Rating, StringFormat=F1}"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Margin="4,0,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </ScrollViewer>
        </Border>

        <!-- مؤشر التحميل -->
        <Border Grid.Row="0"
                Grid.RowSpan="4"
                Background="#80000000"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True"
                             Width="200"
                             Height="10"
                             Margin="0,0,0,10"/>
                <TextBlock Text="جاري التحميل..."
                           Foreground="White"
                           FontSize="16"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
