using AqlanCenterProApp.ViewModels.Users;
using AqlanCenterProApp.ViewModels.Base;
using System.Windows;

namespace AqlanCenterProApp.Views.Users
{
    public partial class ChangePasswordWindow : Window
    {
        public ChangePasswordWindow(ChangePasswordViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;

            // ربط PasswordBox مع ViewModel
            CurrentPasswordBox.PasswordChanged += (s, e) => viewModel.CurrentPassword = CurrentPasswordBox.Password;
            NewPasswordBox.PasswordChanged += (s, e) => viewModel.NewPassword = NewPasswordBox.Password;
            ConfirmPasswordBox.PasswordChanged += (s, e) => viewModel.ConfirmPassword = ConfirmPasswordBox.Password;

            // إغلاق النافذة عند الضغط على إلغاء
            viewModel.CancelCommand = new RelayCommand(() => Close());
        }
    }
} 