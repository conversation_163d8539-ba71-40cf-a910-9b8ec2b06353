using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace AqlanCenterProApp.Converters
{
    public class NotificationTypeToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string type)
            {
                return type switch
                {
                    "Info" => new SolidColorBrush(Color.FromRgb(232, 244, 253)), // Light Blue
                    "Warning" => new SolidColorBrush(Color.FromRgb(255, 248, 225)), // Light Yellow
                    "Error" => new SolidColorBrush(Color.FromRgb(255, 235, 238)), // Light Red
                    "Success" => new SolidColorBrush(Color.FromRgb(232, 245, 233)), // Light Green
                    _ => new SolidColorBrush(Colors.White)
                };
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class NotificationTypeToTextColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string type)
            {
                return type switch
                {
                    "Info" => new SolidColorBrush(Color.FromRgb(25, 118, 210)), // Blue
                    "Warning" => new SolidColorBrush(Color.FromRgb(245, 124, 0)), // Orange
                    "Error" => new SolidColorBrush(Color.FromRgb(211, 47, 47)), // Red
                    "Success" => new SolidColorBrush(Color.FromRgb(56, 142, 60)), // Green
                    _ => new SolidColorBrush(Colors.Black)
                };
            }
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 