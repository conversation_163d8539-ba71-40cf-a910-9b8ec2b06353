using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class ProsthesisType : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DefaultCost { get; set; } = 0;
        
        public int DefaultDeliveryDays { get; set; } = 7;
        
        [StringLength(50)]
        public string Category { get; set; } = string.Empty; // تقويم، زراعة، تجميل، تركيبات
        
        public bool IsActive { get; set; } = true;
        
        // Navigation Properties
        public virtual ICollection<LabOrder> LabOrders { get; set; } = new List<LabOrder>();
    }
}
