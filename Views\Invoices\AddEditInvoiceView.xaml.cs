using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AqlanCenterProApp.ViewModels.Invoices;

namespace AqlanCenterProApp.Views.Invoices
{
    /// <summary>
    /// Interaction logic for AddEditInvoiceView.xaml
    /// </summary>
    public partial class AddEditInvoiceView : Window
    {
        public AddEditInvoiceView(AddEditInvoiceViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            this.PreviewKeyDown += AddEditInvoiceView_PreviewKeyDown;
        }

        private void AddEditInvoiceView_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            var vm = DataContext as AddEditInvoiceViewModel;
            if (e.Key == Key.Enter && (Keyboard.Modifiers & ModifierKeys.Control) == 0)
            {
                if (vm?.SaveCommand.CanExecute(null) == true)
                {
                    vm.SaveCommand.Execute(null);
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Escape)
            {
                if (vm?.CancelCommand.CanExecute(null) == true)
                {
                    vm.CancelCommand.Execute(null);
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Delete)
            {
                if (vm?.RemoveItemCommand.CanExecute(null) == true)
                {
                    vm.RemoveItemCommand.Execute(null);
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.P && Keyboard.Modifiers == ModifierKeys.Control)
            {
                if (vm?.PrintCommand.CanExecute(null) == true)
                {
                    vm.PrintCommand.Execute(null);
                    e.Handled = true;
                }
            }
        }
    }
} 