using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using System.Threading;

namespace AqlanCenterProApp.Services.Implementations
{
    public class AppointmentService : IAppointmentService
    {
        private readonly AqlanCenterDbContext _context;

        public AppointmentService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Appointment>> GetAllAppointmentsAsync()
        {
            try
            {
                // إضافة timeout للاستعلام
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(20));

                // استعلام محسن مع timeout
                return await _context.Appointments
                    .Include(a => a.Patient)
                    .Include(a => a.Doctor)
                    .AsNoTracking() // تحسين الأداء
                    .OrderByDescending(a => a.AppointmentDateTime)
                    .ToListAsync(cts.Token)
                    .ConfigureAwait(false);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Timeout in GetAllAppointmentsAsync");
                return new List<Appointment>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetAllAppointmentsAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new List<Appointment>();
            }
        }

        public async Task<Appointment?> GetAppointmentByIdAsync(int id)
        {
            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .FirstOrDefaultAsync(a => a.Id == id);
        }

        public async Task<IEnumerable<Appointment>> GetAppointmentsByPatientAsync(int patientId)
        {
            return await _context.Appointments
                .Include(a => a.Doctor)
                .Where(a => a.PatientId == patientId)
                .OrderByDescending(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Appointment>> GetAppointmentsByDoctorAsync(int doctorId)
        {
            return await _context.Appointments
                .Include(a => a.Patient)
                .Where(a => a.DoctorId == doctorId)
                .OrderByDescending(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Appointment>> GetAppointmentsByDateAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .Where(a => a.AppointmentDateTime >= startDate && a.AppointmentDateTime < endDate)
                .OrderBy(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Appointment>> GetAppointmentsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .Where(a => a.AppointmentDateTime >= startDate && a.AppointmentDateTime <= endDate)
                .OrderBy(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Appointment>> GetUpcomingAppointmentsAsync(int days = 7)
        {
            var startDate = DateTime.Now;
            var endDate = startDate.AddDays(days);

            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .Where(a => a.AppointmentDateTime >= startDate && a.AppointmentDateTime <= endDate && a.Status == "مجدول")
                .OrderBy(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Appointment>> GetOverdueAppointmentsAsync()
        {
            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .Where(a => a.AppointmentDateTime < DateTime.Now && a.Status == "مجدول")
                .OrderBy(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<Appointment> CreateAppointmentAsync(Appointment appointment)
        {
            appointment.CreatedAt = DateTime.Now;
            appointment.Status = "مجدول";

            _context.Appointments.Add(appointment);
            await _context.SaveChangesAsync();

            return appointment;
        }

        public async Task<Appointment> UpdateAppointmentAsync(Appointment appointment)
        {
            appointment.UpdatedAt = DateTime.Now;

            _context.Appointments.Update(appointment);
            await _context.SaveChangesAsync();

            return appointment;
        }

        public async Task<bool> DeleteAppointmentAsync(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null) return false;

            _context.Appointments.Remove(appointment);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> CancelAppointmentAsync(int id, string reason)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null) return false;

            appointment.Status = "ملغي";
            appointment.CancellationReason = reason;
            appointment.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RescheduleAppointmentAsync(int id, DateTime newDateTime)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null) return false;

            appointment.AppointmentDateTime = newDateTime;
            appointment.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAppointmentAsCompletedAsync(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null) return false;

            appointment.Status = "مكتمل";
            appointment.CompletionDate = DateTime.Now;
            appointment.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAppointmentAsNoShowAsync(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null) return false;

            appointment.Status = "لم يحضر";
            appointment.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Appointment>> SearchAppointmentsAsync(string searchTerm)
        {
            return await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .Where(a => (a.Patient != null && a.Patient.FullName.Contains(searchTerm)) ||
                           (a.Doctor != null && a.Doctor.FullName.Contains(searchTerm)) ||
                           (a.Notes != null && a.Notes.Contains(searchTerm)))
                .OrderByDescending(a => a.AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<int> GetAppointmentsCountAsync()
        {
            return await _context.Appointments.CountAsync();
        }

        public async Task<Dictionary<string, int>> GetAppointmentsStatisticsAsync()
        {
            var stats = new Dictionary<string, int>();

            stats["إجمالي المواعيد"] = await _context.Appointments.CountAsync();
            stats["المواعيد المجدولة"] = await _context.Appointments.CountAsync(a => a.Status == "مجدول");
            stats["المواعيد المكتملة"] = await _context.Appointments.CountAsync(a => a.Status == "مكتمل");
            stats["المواعيد الملغية"] = await _context.Appointments.CountAsync(a => a.Status == "ملغي");
            stats["المواعيد التي لم يحضرها المريض"] = await _context.Appointments.CountAsync(a => a.Status == "لم يحضر");

            return stats;
        }

        public async Task<bool> IsTimeSlotAvailableAsync(int doctorId, DateTime dateTime, int durationMinutes = 30, int? excludeAppointmentId = null)
        {
            var endTime = dateTime.AddMinutes(durationMinutes);

            var conflictingAppointments = await _context.Appointments
                .Where(a => a.DoctorId == doctorId &&
                           a.Status != "ملغي" &&
                           a.Status != "لم يحضر" &&
                           (excludeAppointmentId == null || a.Id != excludeAppointmentId) &&
                           ((a.AppointmentDateTime <= dateTime && a.AppointmentDateTime.AddMinutes(a.DurationMinutes) > dateTime) ||
                            (a.AppointmentDateTime < endTime && a.AppointmentDateTime.AddMinutes(a.DurationMinutes) >= endTime) ||
                            (a.AppointmentDateTime >= dateTime && a.AppointmentDateTime.AddMinutes(a.DurationMinutes) <= endTime)))
                .ToListAsync()
                .ConfigureAwait(false);

            return !conflictingAppointments.Any();
        }
    }
}