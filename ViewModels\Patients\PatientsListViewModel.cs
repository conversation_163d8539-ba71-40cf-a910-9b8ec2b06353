using System.Collections.ObjectModel;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Helpers;

namespace AqlanCenterProApp.ViewModels.Patients;

/// <summary>
/// ViewModel لقائمة المرضى مع البحث والفلترة والترقيم
/// </summary>
public class PatientsListViewModel : BaseViewModel
{
    private readonly IPatientService _patientService;

    #region Properties

    private ObservableCollection<Patient> _patients = new();
    private Patient? _selectedPatient;
    private string _searchText = string.Empty;
    private string? _selectedCategory;
    private string? _selectedFileStatus;
    private int _currentPage = 1;
    private int _totalPages = 1;
    private int _totalCount = 0;
    private const int PageSize = 50;

    // إضافة خصائص الجلسات
    private ObservableCollection<Session> _patientSessions = new();
    private Session? _selectedSession;

    /// <summary>
    /// قائمة المرضى
    /// </summary>
    public ObservableCollection<Patient> Patients
    {
        get => _patients;
        set => SetProperty(ref _patients, value);
    }

    /// <summary>
    /// المريض المحدد
    /// </summary>
    public Patient? SelectedPatient
    {
        get => _selectedPatient;
        set => SetProperty(ref _selectedPatient, value, OnSelectedPatientChanged);
    }

    /// <summary>
    /// جلسات المريض المحدد
    /// </summary>
    public ObservableCollection<Session> PatientSessions
    {
        get => _patientSessions;
        set => SetProperty(ref _patientSessions, value);
    }

    /// <summary>
    /// الجلسة المحددة
    /// </summary>
    public Session? SelectedSession
    {
        get => _selectedSession;
        set => SetProperty(ref _selectedSession, value);
    }

    /// <summary>
    /// نص البحث
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set => SetProperty(ref _searchText, value, OnSearchTextChanged);
    }

    /// <summary>
    /// التصنيف المحدد للفلترة
    /// </summary>
    public string? SelectedCategory
    {
        get => _selectedCategory;
        set => SetProperty(ref _selectedCategory, value, OnFilterChanged);
    }

    /// <summary>
    /// حالة الملف المحددة للفلترة
    /// </summary>
    public string? SelectedFileStatus
    {
        get => _selectedFileStatus;
        set => SetProperty(ref _selectedFileStatus, value, OnFilterChanged);
    }

    /// <summary>
    /// الصفحة الحالية
    /// </summary>
    public int CurrentPage
    {
        get => _currentPage;
        set
        {
            if (SetProperty(ref _currentPage, value))
            {
                OnPropertyChanged(nameof(CanGoPrevious));
                OnPropertyChanged(nameof(CanGoNext));
            }
        }
    }

    /// <summary>
    /// إجمالي الصفحات
    /// </summary>
    public int TotalPages
    {
        get => _totalPages;
        set
        {
            if (SetProperty(ref _totalPages, value))
            {
                OnPropertyChanged(nameof(CanGoPrevious));
                OnPropertyChanged(nameof(CanGoNext));
            }
        }
    }

    /// <summary>
    /// إجمالي عدد المرضى
    /// </summary>
    public int TotalCount
    {
        get => _totalCount;
        set => SetProperty(ref _totalCount, value);
    }

    /// <summary>
    /// إمكانية الانتقال للصفحة السابقة
    /// </summary>
    public bool CanGoPrevious => CurrentPage > 1;

    /// <summary>
    /// إمكانية الانتقال للصفحة التالية
    /// </summary>
    public bool CanGoNext => CurrentPage < TotalPages;

    /// <summary>
    /// هل يوجد مريض محدد
    /// </summary>
    public bool HasSelectedPatient => SelectedPatient != null;

    /// <summary>
    /// قائمة التصنيفات المتاحة
    /// </summary>
    public ObservableCollection<string> Categories { get; } = new();

    /// <summary>
    /// قائمة حالات الملف المتاحة
    /// </summary>
    public ObservableCollection<string> FileStatuses { get; } = new()
    {
        "نشط", "مؤرشف", "معلق", "VIP"
    };

    #endregion

    #region Statistics Properties

    private PatientStatistics? _statistics;

    /// <summary>
    /// إحصائيات المرضى
    /// </summary>
    public PatientStatistics? Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    #endregion

    #region Commands

    /// <summary>
    /// أمر البحث
    /// </summary>
    public ICommand SearchCommand { get; }

    /// <summary>
    /// أمر مسح البحث
    /// </summary>
    public ICommand ClearSearchCommand { get; }

    /// <summary>
    /// أمر إضافة مريض جديد
    /// </summary>
    public ICommand AddPatientCommand { get; }

    /// <summary>
    /// أمر تعديل المريض المحدد
    /// </summary>
    public ICommand EditPatientCommand { get; }

    /// <summary>
    /// أمر حذف المريض المحدد
    /// </summary>
    public ICommand DeletePatientCommand { get; }

    /// <summary>
    /// أمر عرض تفاصيل المريض
    /// </summary>
    public ICommand ViewPatientDetailsCommand { get; }

    /// <summary>
    /// أمر تحديث القائمة
    /// </summary>
    public ICommand RefreshCommand { get; }

    /// <summary>
    /// أمر الانتقال للصفحة التالية
    /// </summary>
    public ICommand NextPageCommand { get; }

    /// <summary>
    /// أمر الانتقال للصفحة السابقة
    /// </summary>
    public ICommand PreviousPageCommand { get; }

    /// <summary>
    /// أمر إضافة جلسة جديدة
    /// </summary>
    public ICommand AddSessionCommand { get; }

    /// <summary>
    /// أمر تعديل الجلسة المحددة
    /// </summary>
    public ICommand EditSessionCommand { get; }

    /// <summary>
    /// أمر حذف الجلسة المحددة
    /// </summary>
    public ICommand DeleteSessionCommand { get; }

    /// <summary>
    /// أمر تحديث جلسات المريض
    /// </summary>
    public ICommand RefreshSessionsCommand { get; }

    /// <summary>
    /// أمر عرض مدفوعات المريض
    /// </summary>
    public ICommand ViewPaymentsCommand { get; }

    /// <summary>
    /// أمر عرض مواعيد المريض
    /// </summary>
    public ICommand ViewAppointmentsCommand { get; }

    /// <summary>
    /// أمر إرسال واتساب
    /// </summary>
    public ICommand SendWhatsAppCommand { get; }

    /// <summary>
    /// أمر طباعة سند المعاينة
    /// </summary>
    public ICommand PrintConsultationReceiptCommand { get; }

    /// <summary>
    /// أمر عرض الإحصائيات
    /// </summary>
    public ICommand ShowStatisticsCommand { get; }

    /// <summary>
    /// أمر البحث المتقدم
    /// </summary>
    public ICommand AdvancedSearchCommand { get; }

    /// <summary>
    /// أمر عرض السجلات الطبية
    /// </summary>
    public ICommand ViewMedicalRecordsCommand { get; }

    /// <summary>
    /// أمر عرض الوصفات الطبية
    /// </summary>
    public ICommand ViewPrescriptionsCommand { get; }

    /// <summary>
    /// أمر عرض نتائج الفحوصات
    /// </summary>
    public ICommand ViewTestResultsCommand { get; }

    /// <summary>
    /// أمر عرض الصور الطبية
    /// </summary>
    public ICommand ViewMedicalImagesCommand { get; }

    /// <summary>
    /// أمر عرض التقارير
    /// </summary>
    public ICommand ViewReportsCommand { get; }

    /// <summary>
    /// أمر طباعة بيانات المريض
    /// </summary>
    public ICommand PrintPatientCommand { get; }

    /// <summary>
    /// أمر عرض الأرشيف
    /// </summary>
    public ICommand ViewArchiveCommand { get; }

    /// <summary>
    /// أمر عرض ملفات المرضى
    /// </summary>
    public ICommand ViewPatientFilesCommand { get; }

    /// <summary>
    /// أمر الطباعة
    /// </summary>
    public ICommand PrintCommand { get; }

    /// <summary>
    /// أمر تصدير Excel
    /// </summary>
    public ICommand ExportExcelCommand { get; }

    /// <summary>
    /// أمر تصدير PDF
    /// </summary>
    public ICommand ExportPdfCommand { get; }

    /// <summary>
    /// أمر الانتقال لصفحة محددة
    /// </summary>
    public ICommand GoToPageCommand { get; }

    /// <summary>
    /// أمر تصدير البيانات
    /// </summary>
    public ICommand ExportCommand { get; }

    // أوامر إضافية للنسخ والطباعة
    public ICommand CopyCommand { get; }

    /// <summary>
    /// أمر عرض كشف حساب المريض المحدد
    /// </summary>
    public ICommand ShowAccountStatementCommand { get; }

    #endregion

    #region Events

    /// <summary>
    /// حدث طلب عرض تفاصيل مريض
    /// </summary>
    public event EventHandler<Patient>? ViewPatientDetailsRequested;

    #endregion

    #region Constructor

    public PatientsListViewModel(IPatientService patientService)
    {
        _patientService = patientService ?? throw new ArgumentNullException(nameof(patientService));

        // تهيئة البيانات الأساسية
        InitializeBasicData();

        // تهيئة الأوامر
        SearchCommand = new AsyncRelayCommand(SearchAsync);
        ClearSearchCommand = new RelayCommand(ClearSearch);
        AddPatientCommand = new RelayCommand(AddPatient);
        EditPatientCommand = new RelayCommand(EditPatient, () => SelectedPatient != null);
        DeletePatientCommand = new AsyncRelayCommand(DeletePatientAsync, () => SelectedPatient != null);
        ViewPatientDetailsCommand = new RelayCommand(ViewPatientDetails, () => SelectedPatient != null);
        RefreshCommand = new AsyncRelayCommand(LoadPatientsAsync);
        NextPageCommand = new AsyncRelayCommand(NextPageAsync, () => CurrentPage < TotalPages);
        PreviousPageCommand = new AsyncRelayCommand(PreviousPageAsync, () => CurrentPage > 1);
        AddSessionCommand = new RelayCommand(AddSession, () => SelectedPatient != null);
        EditSessionCommand = new RelayCommand(() => EditSession(SelectedSession), () => SelectedSession != null);
        DeleteSessionCommand = new RelayCommand(() => DeleteSession(SelectedSession), () => SelectedSession != null);
        RefreshSessionsCommand = new AsyncRelayCommand(RefreshSessionsAsync);
        GoToPageCommand = new AsyncRelayCommand(param => GoToPageAsync((int)param!));
        ExportCommand = new RelayCommand(Export);
        CopyCommand = new RelayCommand(Copy, () => SelectedPatient != null);
        PrintCommand = new RelayCommand(Print);
        ViewPaymentsCommand = new RelayCommand(param => ViewPatientPayments(param as Patient), param => param is Patient);
        ViewAppointmentsCommand = new RelayCommand(param => ViewPatientAppointments(param as Patient), param => param is Patient);
        SendWhatsAppCommand = new RelayCommand(param => SendWhatsApp(param as Patient), param => param is Patient);
        PrintConsultationReceiptCommand = new RelayCommand(() => PrintConsultationReceipt(SelectedPatient), () => SelectedPatient != null);
        ShowStatisticsCommand = new RelayCommand(ShowStatistics);
        AdvancedSearchCommand = new RelayCommand(ShowAdvancedSearch);
        ViewMedicalRecordsCommand = new RelayCommand(ViewMedicalRecords);
        ViewPrescriptionsCommand = new RelayCommand(ViewPrescriptions);
        ViewTestResultsCommand = new RelayCommand(ViewTestResults);
        ViewMedicalImagesCommand = new RelayCommand(ViewMedicalImages);
        ViewReportsCommand = new RelayCommand(ViewReports);
        PrintPatientCommand = new RelayCommand(param => PrintPatient(param as Patient), param => param is Patient);
        ViewArchiveCommand = new RelayCommand(ViewArchive);
        ViewPatientFilesCommand = new RelayCommand(ViewPatientFiles);
        ExportExcelCommand = new RelayCommand(ExportExcel);
        ExportPdfCommand = new RelayCommand(ExportPdf);
        ShowAccountStatementCommand = new RelayCommand(ShowAccountStatement, () => SelectedPatient != null);

        // تحميل البيانات الأولية (سيتم تحميلها عند فتح الصفحة)
        // _ = InitializeAsync(); // تم نقله لتجنب التعليق
    }

    /// <summary>
    /// تهيئة البيانات الأساسية (غير async)
    /// </summary>
    private void InitializeBasicData()
    {
        // إضافة تصنيفات افتراضية
        Categories.Add("الكل");
        Categories.Add("جديد");
        Categories.Add("متابع");
        Categories.Add("VIP");

        // إحصائيات افتراضية
        Statistics = new PatientStatistics
        {
            TotalPatients = 0,
            ActivePatients = 0,
            NewPatientsThisMonth = 0,
            DebtorPatients = 0,
            CreditorPatients = 0
        };

        // إضافة بيانات تجريبية للاختبار
        AddSampleData();
    }

    /// <summary>
    /// إضافة بيانات تجريبية للاختبار
    /// </summary>
    private void AddSampleData()
    {
        var samplePatients = new[]
        {
            new Patient
            {
                Id = 8500,
                FullName = "أحمد محمد الحكيمي",
                Gender = "ذكر",
                Phone = "777-123456",
                PatientCategory = "جديد",
                FileStatus = "نشط",
                CurrentBalance = 0,
                RegistrationDate = DateTime.Now.AddDays(-5),
                CreatedAt = DateTime.Now.AddDays(-5)
            },
            new Patient
            {
                Id = 8501,
                FullName = "فاطمة علي الزهراني",
                Gender = "أنثى",
                Phone = "777-234567",
                PatientCategory = "متابع",
                FileStatus = "نشط",
                CurrentBalance = -150000,
                RegistrationDate = DateTime.Now.AddDays(-15),
                CreatedAt = DateTime.Now.AddDays(-15)
            },
            new Patient
            {
                Id = 8502,
                FullName = "محمد سالم الشامي",
                Gender = "ذكر",
                Phone = "777-345678",
                PatientCategory = "VIP",
                FileStatus = "نشط",
                CurrentBalance = 50000,
                RegistrationDate = DateTime.Now.AddDays(-30),
                CreatedAt = DateTime.Now.AddDays(-30)
            },
            new Patient
            {
                Id = 8503,
                FullName = "عائشة حسن المقطري",
                Gender = "أنثى",
                Phone = "777-456789",
                PatientCategory = "جديد",
                FileStatus = "نشط",
                CurrentBalance = -75000,
                RegistrationDate = DateTime.Now.AddDays(-2),
                CreatedAt = DateTime.Now.AddDays(-2)
            },
            new Patient
            {
                Id = 8504,
                FullName = "يوسف عبدالله الأهدل",
                Gender = "ذكر",
                Phone = "777-567890",
                PatientCategory = "متابع",
                FileStatus = "مؤرشف",
                CurrentBalance = 25000,
                RegistrationDate = DateTime.Now.AddDays(-60),
                CreatedAt = DateTime.Now.AddDays(-60)
            }
        };

        foreach (var patient in samplePatients)
        {
            Patients.Add(patient);
        }

        System.Diagnostics.Debug.WriteLine($"تم إضافة {samplePatients.Length} مرضى تجريبيين. إجمالي المرضى: {Patients.Count}");

        // تحديث الإحصائيات
        Statistics = new PatientStatistics
        {
            TotalPatients = samplePatients.Length,
            ActivePatients = samplePatients.Count(p => p.FileStatus == "نشط"),
            NewPatientsThisMonth = samplePatients.Count(p => p.RegistrationDate >= DateTime.Now.AddDays(-30)),
            DebtorPatients = samplePatients.Count(p => p.CurrentBalance < 0),
            CreditorPatients = samplePatients.Count(p => p.CurrentBalance > 0)
        };

        TotalCount = samplePatients.Length;
        TotalPages = 1;
        CurrentPage = 1;
    }

    #endregion

    #region Initialization

    /// <summary>
    /// تهيئة البيانات الأولية (يجب استدعاؤها بعد إنشاء الكائن)
    /// </summary>
    public async Task InitializeAsync()
    {
        System.Diagnostics.Debug.WriteLine("PatientsListViewModel: بدء InitializeAsync");

        try
        {
            // إظهار مؤشر التحميل
            IsLoading = true;
            LoadingMessage = "جاري تحميل البيانات...";
            System.Diagnostics.Debug.WriteLine("PatientsListViewModel: تم تعيين IsLoading = true");

            // تحميل التصنيفات بدون مؤشر تحميل (سريع)
            await LoadCategoriesAsync();
            System.Diagnostics.Debug.WriteLine("PatientsListViewModel: تم تحميل التصنيفات");

            // تحميل قائمة المرضى من قاعدة البيانات
            await LoadPatientsAsync();
            System.Diagnostics.Debug.WriteLine($"PatientsListViewModel: تم تحميل المرضى - عدد المرضى: {Patients.Count}");

            // تحميل الإحصائيات
            await LoadStatisticsAsync();
            System.Diagnostics.Debug.WriteLine($"PatientsListViewModel: تم تحديث الإحصائيات");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة البيانات: {ex.Message}");

            // تأكد من وجود بيانات أساسية حتى لو فشلت التهيئة
            if (!Categories.Any())
            {
                Categories.Add("الكل");
                Categories.Add("جديد");
            }
        }
        finally
        {
            // إخفاء مؤشر التحميل
            IsLoading = false;
            System.Diagnostics.Debug.WriteLine("PatientsListViewModel: تم تعيين IsLoading = false");
        }
    }

    /// <summary>
    /// تحديث الإحصائيات من البيانات الحالية
    /// </summary>
    private void UpdateStatisticsFromCurrentData()
    {
        Statistics = new PatientStatistics
        {
            TotalPatients = Patients.Count,
            ActivePatients = Patients.Count(p => p.FileStatus == "نشط"),
            NewPatientsThisMonth = Patients.Count(p => p.RegistrationDate >= DateTime.Now.AddDays(-30)),
            DebtorPatients = Patients.Count(p => p.CurrentBalance < 0),
            CreditorPatients = Patients.Count(p => p.CurrentBalance > 0)
        };

        TotalCount = Patients.Count;
        TotalPages = Math.Max(1, (int)Math.Ceiling((double)TotalCount / PageSize));
        CurrentPage = 1;
    }

    /// <summary>
    /// تحميل التصنيفات المتاحة
    /// </summary>
    private async Task LoadCategoriesAsync()
    {
        try
        {
            var categories = await _patientService.GetPatientCategoriesAsync();
            Categories.Clear();
            Categories.Add("الكل"); // خيار لعرض جميع المرضى
            foreach (var category in categories)
            {
                Categories.Add(category);
            }
        }
        catch (Exception ex)
        {
            // في حالة الخطأ، استخدم تصنيفات افتراضية
            Categories.Clear();
            Categories.Add("الكل");
            Categories.Add("جديد");
            Categories.Add("متابع");
            Categories.Add("VIP");

            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التصنيفات: {ex.Message}");
        }
    }

    #endregion

    #region Data Loading

    /// <summary>
    /// تحميل قائمة المرضى
    /// </summary>
    private async Task LoadPatientsAsync()
    {
        try
        {
            var result = await _patientService.GetPatientsAsync(
                CurrentPage,
                PageSize,
                string.IsNullOrWhiteSpace(SearchText) ? null : SearchText,
                SelectedCategory == "الكل" ? null : SelectedCategory,
                SelectedFileStatus);

            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Patients.Clear();
                foreach (var patient in result.Patients)
                {
                    Patients.Add(patient);
                }

                TotalCount = result.TotalCount;
                TotalPages = result.TotalPages;
            });
        }
        catch (Exception ex)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Patients.Clear();
                TotalCount = 0;
                TotalPages = 1;
            });
            // يمكن إضافة تسجيل الخطأ هنا
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المرضى: {ex.Message}");
        }
    }

    /// <summary>
    /// تحميل الإحصائيات
    /// </summary>
    private async Task LoadStatisticsAsync()
    {
        try
        {
            Statistics = await _patientService.GetPatientStatisticsAsync();
        }
        catch (Exception ex)
        {
            // في حالة الخطأ، استخدم إحصائيات افتراضية
            Statistics = new PatientStatistics
            {
                TotalPatients = 0,
                ActivePatients = 0,
                NewPatientsThisMonth = 0,
                DebtorPatients = 0,
                CreditorPatients = 0
            };

            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
        }
    }

    #endregion

    #region Search and Filter

    /// <summary>
    /// تنفيذ البحث
    /// </summary>
    private async Task SearchAsync()
    {
        CurrentPage = 1; // العودة للصفحة الأولى عند البحث
        await ExecuteAsync(LoadPatientsAsync, "جاري البحث...");
    }

    /// <summary>
    /// مسح البحث
    /// </summary>
    private void ClearSearch()
    {
        SearchText = string.Empty;
        SelectedCategory = null;
        SelectedFileStatus = null;
        CurrentPage = 1;
        _ = LoadPatientsAsync();
    }

    /// <summary>
    /// معالج تغيير نص البحث (مع تأخير)
    /// </summary>
    private void OnSearchTextChanged()
    {
        // تأخير البحث لتحسين الأداء
        _ = Task.Delay(300).ContinueWith(async _ =>
        {
            if (!IsLoading)
                await SearchAsync();
        });
    }

    /// <summary>
    /// معالج تغيير الفلاتر
    /// </summary>
    private void OnFilterChanged()
    {
        CurrentPage = 1;
        _ = LoadPatientsAsync();
    }

    #endregion

    #region Patient Operations

    /// <summary>
    /// إضافة مريض جديد
    /// </summary>
    private void AddPatient()
    {
        try
        {
            // إنشاء نافذة إضافة مريض حديثة
            var addPatientWindow = new Views.Patients.ModernAddPatientWindow();

            // تعيين المالك فقط إذا لم تكن النافذة الحالية هي MainWindow
            var mainWindow = System.Windows.Application.Current.MainWindow;
            if (mainWindow != null && mainWindow != addPatientWindow)
            {
                addPatientWindow.Owner = mainWindow;
                addPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;
            }
            else
            {
                addPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
            }

            var result = addPatientWindow.ShowDialog();

            if (result == true)
            {
                // تحديث قائمة المرضى بعد الإضافة
                _ = LoadPatientsAsync();
                _ = LoadStatisticsAsync();

                System.Windows.MessageBox.Show("تم إضافة المريض بنجاح", "نجح",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            DatabaseErrorHandler.ShowDatabaseError(ex, "فتح نافذة إضافة المريض");
        }
    }

    /// <summary>
    /// تعديل المريض المحدد
    /// </summary>
    private void EditPatient()
    {
        if (SelectedPatient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        try
        {
            // إنشاء نافذة تعديل مريض حديثة
            var editPatientWindow = new Views.Patients.ModernEditPatientWindow(SelectedPatient);

            // تعيين المالك فقط إذا لم تكن النافذة الحالية هي MainWindow
            var mainWindow = System.Windows.Application.Current.MainWindow;
            if (mainWindow != null && mainWindow != editPatientWindow)
            {
                editPatientWindow.Owner = mainWindow;
                editPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;
            }
            else
            {
                editPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
            }

            var result = editPatientWindow.ShowDialog();

            if (result == true)
            {
                // تحديث قائمة المرضى بعد التعديل
                _ = LoadPatientsAsync();
                _ = LoadStatisticsAsync();

                System.Windows.MessageBox.Show("تم تعديل بيانات المريض بنجاح", "نجح",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            DatabaseErrorHandler.ShowDatabaseError(ex, "فتح نافذة تعديل المريض");
        }
    }

    /// <summary>
    /// حذف المريض المحدد
    /// </summary>
    private async Task DeletePatientAsync()
    {
        if (SelectedPatient == null) return;

        // هنا يجب إضافة تأكيد الحذف
        var result = System.Windows.MessageBox.Show(
            $"هل أنت متأكد من حذف المريض: {SelectedPatient.FullName}؟",
            "تأكيد الحذف",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Question);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                await _patientService.DeletePatientAsync(SelectedPatient.Id, "CurrentUser"); // يجب تمرير المستخدم الحالي
                await LoadPatientsAsync();
                await LoadStatisticsAsync();
                SelectedPatient = null;
            }, "جاري حذف المريض...");
        }
    }

    /// <summary>
    /// عرض تفاصيل المريض
    /// </summary>
    private void ViewPatientDetails()
    {
        if (SelectedPatient != null)
        {
            ViewPatientDetailsRequested?.Invoke(this, SelectedPatient);
        }
    }

    /// <summary>
    /// عرض مدفوعات المريض
    /// </summary>
    private void ViewPatientPayments(Patient? patient)
    {
        if (patient != null)
        {
            // سيتم تطوير صفحة المدفوعات لاحقاً
            System.Windows.MessageBox.Show($"سيتم فتح صفحة مدفوعات المريض: {patient.FullName}\nهذه الوظيفة قيد التطوير",
                "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// عرض مواعيد المريض
    /// </summary>
    private void ViewPatientAppointments(Patient? patient)
    {
        if (patient != null)
        {
            // سيتم تطوير صفحة المواعيد لاحقاً
            System.Windows.MessageBox.Show($"سيتم فتح صفحة مواعيد المريض: {patient.FullName}\nهذه الوظيفة قيد التطوير",
                "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// معالج تغيير المريض المحدد
    /// </summary>
    private void OnSelectedPatientChanged()
    {
        OnPropertyChanged(nameof(HasSelectedPatient));
        CommandManager.InvalidateRequerySuggested();
        LoadSessionsForSelectedPatient();
    }

    private void LoadSessionsForSelectedPatient()
    {
        PatientSessions.Clear();
        if (SelectedPatient != null && SelectedPatient.Sessions != null)
        {
            foreach (var session in SelectedPatient.Sessions)
                PatientSessions.Add(session);
        }
        OnPropertyChanged(nameof(PatientSessions));
    }

    #endregion

    #region Pagination

    /// <summary>
    /// الانتقال للصفحة التالية
    /// </summary>
    private async Task NextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadPatientsAsync();
        }
    }

    /// <summary>
    /// الانتقال للصفحة السابقة
    /// </summary>
    private async Task PreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadPatientsAsync();
        }
    }

    /// <summary>
    /// الانتقال لصفحة محددة
    /// </summary>
    private async Task GoToPageAsync(int pageNumber)
    {
        if (pageNumber >= 1 && pageNumber <= TotalPages && pageNumber != CurrentPage)
        {
            CurrentPage = pageNumber;
            await LoadPatientsAsync();
        }
    }

    #endregion

    #region Export

    /// <summary>
    /// تصدير بيانات المرضى
    /// </summary>
    private async Task ExportPatientsAsync()
    {
        await ExecuteAsync(async () =>
        {
            // هنا سيتم تطبيق تصدير Excel/PDF
            // مؤقتاً سنعرض رسالة
            await Task.Delay(100); // لتجنب تحذير عدم وجود await
            System.Windows.MessageBox.Show("سيتم تطوير وظيفة التصدير قريباً", "معلومات");
        }, "جاري تصدير البيانات...");
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// تحديث القائمة من الخارج
    /// </summary>
    public async Task RefreshAsync()
    {
        await LoadPatientsAsync();
        await LoadStatisticsAsync();
    }

    /// <summary>
    /// إضافة مريض جديد للقائمة
    /// </summary>
    public void AddPatientToList(Patient patient)
    {
        Patients.Insert(0, patient); // إضافة في المقدمة
        TotalCount++;
        _ = LoadStatisticsAsync(); // تحديث الإحصائيات
    }

    /// <summary>
    /// تحديث مريض في القائمة
    /// </summary>
    public void UpdatePatientInList(Patient updatedPatient)
    {
        var existingPatient = Patients.FirstOrDefault(p => p.Id == updatedPatient.Id);
        if (existingPatient != null)
        {
            var index = Patients.IndexOf(existingPatient);
            Patients[index] = updatedPatient;
        }
    }

    /// <summary>
    /// إزالة مريض من القائمة
    /// </summary>
    public void RemovePatientFromList(int patientId)
    {
        var patient = Patients.FirstOrDefault(p => p.Id == patientId);
        if (patient != null)
        {
            Patients.Remove(patient);
            TotalCount--;
            if (SelectedPatient?.Id == patientId)
                SelectedPatient = null;
        }
    }

    #endregion

    #region Additional Commands Implementation

    /// <summary>
    /// إرسال واتساب للمريض
    /// </summary>
    private void SendWhatsApp(Patient? patient)
    {
        if (patient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        if (string.IsNullOrWhiteSpace(patient.Phone))
        {
            System.Windows.MessageBox.Show("لا يوجد رقم هاتف مسجل لهذا المريض", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        try
        {
            // تنظيف رقم الهاتف
            var phoneNumber = patient.Phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // إضافة رمز الدولة إذا لم يكن موجوداً
            if (!phoneNumber.StartsWith("+"))
            {
                if (phoneNumber.StartsWith("966"))
                {
                    phoneNumber = "+" + phoneNumber;
                }
                else if (phoneNumber.StartsWith("0"))
                {
                    phoneNumber = "+966" + phoneNumber.Substring(1);
                }
                else
                {
                    phoneNumber = "+966" + phoneNumber;
                }
            }

            // رسالة افتراضية
            var message = $"مرحباً {patient.FullName}،\n\nهذه رسالة من عيادة الأسنان.\nنتمنى لك دوام الصحة والعافية.\n\nشكراً لثقتك بنا.";

            // ترميز الرسالة للـ URL
            var encodedMessage = System.Web.HttpUtility.UrlEncode(message);

            // إنشاء رابط الواتساب
            var whatsappUrl = $"https://wa.me/{phoneNumber}?text={encodedMessage}";

            // فتح الواتساب
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = whatsappUrl,
                UseShellExecute = true
            });

            System.Windows.MessageBox.Show($"تم فتح الواتساب للمريض: {patient.FullName}\nرقم الهاتف: {phoneNumber}",
                "واتساب", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح الواتساب: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// طباعة سند المعاينة للمريض
    /// </summary>
    private void PrintConsultationReceipt(Patient? patient)
    {
        if (patient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        try
        {
            // فتح نافذة سند المعاينة
            var receiptWindow = new Views.Patients.ConsultationReceiptWindow(patient);
            receiptWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح سند المعاينة: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض الإحصائيات
    /// </summary>
    private void ShowStatistics()
    {
        try
        {
            var statisticsWindow = new Views.Patients.PatientStatisticsWindow();
            statisticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة الإحصائيات: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض نافذة البحث المتقدم
    /// </summary>
    private void ShowAdvancedSearch()
    {
        try
        {
            var advancedSearchWindow = new Views.Patients.AdvancedSearchWindow();
            if (advancedSearchWindow.ShowDialog() == true && advancedSearchWindow.SearchCriteria != null)
            {
                // تطبيق معايير البحث المتقدم
                PerformAdvancedSearch(advancedSearchWindow.SearchCriteria);
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة البحث المتقدم: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تطبيق البحث المتقدم
    /// </summary>
    private async void PerformAdvancedSearch(Views.Patients.PatientSearchCriteria criteria)
    {
        try
        {
            IsLoading = true;
            LoadingMessage = "جاري البحث...";

            // تطبيق معايير البحث على قائمة المرضى
            var filteredPatients = Patients.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(criteria.FullName))
            {
                filteredPatients = filteredPatients.Where(p => p.FullName.Contains(criteria.FullName, StringComparison.OrdinalIgnoreCase));
            }

            if (criteria.FileNumber.HasValue)
            {
                filteredPatients = filteredPatients.Where(p => p.FileNumber == criteria.FileNumber.Value);
            }

            if (!string.IsNullOrWhiteSpace(criteria.PhoneNumber))
            {
                filteredPatients = filteredPatients.Where(p => p.Phone.Contains(criteria.PhoneNumber));
            }

            if (!string.IsNullOrWhiteSpace(criteria.Gender))
            {
                filteredPatients = filteredPatients.Where(p => p.Gender == criteria.Gender);
            }

            if (!string.IsNullOrWhiteSpace(criteria.Category))
            {
                filteredPatients = filteredPatients.Where(p => p.PatientCategory == criteria.Category);
            }

            if (criteria.AgeFrom.HasValue || criteria.AgeTo.HasValue)
            {
                filteredPatients = filteredPatients.Where(p =>
                {
                    if (p.DateOfBirth.HasValue)
                    {
                        var age = DateTime.Now.Year - p.DateOfBirth.Value.Year;
                        return (!criteria.AgeFrom.HasValue || age >= criteria.AgeFrom.Value) &&
                               (!criteria.AgeTo.HasValue || age <= criteria.AgeTo.Value);
                    }
                    return false;
                });
            }

            // تحديث القائمة
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Patients.Clear();
                foreach (var patient in filteredPatients)
                {
                    Patients.Add(patient);
                }
            });

            System.Windows.MessageBox.Show($"تم العثور على {Patients.Count} مريض", "نتائج البحث",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في البحث المتقدم: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// عرض السجلات الطبية
    /// </summary>
    private void ViewMedicalRecords()
    {
        try
        {
            var medicalRecordsWindow = new Views.Patients.MedicalRecordsWindow();
            medicalRecordsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة السجلات الطبية: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض الوصفات الطبية
    /// </summary>
    private void ViewPrescriptions()
    {
        try
        {
            var prescriptionsWindow = new Views.Patients.PrescriptionsWindow();
            prescriptionsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة الوصفات الطبية: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض نتائج الفحوصات
    /// </summary>
    private void ViewTestResults()
    {
        try
        {
            var testResultsWindow = new Views.Patients.TestResultsWindow();
            testResultsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة نتائج الفحوصات: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض الصور الطبية
    /// </summary>
    private void ViewMedicalImages()
    {
        System.Windows.MessageBox.Show("سيتم فتح صفحة الصور الطبية\nهذه الوظيفة قيد التطوير",
            "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// عرض التقارير
    /// </summary>
    private void ViewReports()
    {
        System.Windows.MessageBox.Show("سيتم فتح صفحة تقارير المرضى\nهذه الوظيفة قيد التطوير",
            "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// عرض الأرشيف
    /// </summary>
    private void ViewArchive()
    {
        System.Windows.MessageBox.Show("سيتم فتح صفحة أرشيف المرضى\nهذه الوظيفة قيد التطوير",
            "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// عرض ملفات المرضى
    /// </summary>
    private void ViewPatientFiles()
    {
        System.Windows.MessageBox.Show("سيتم فتح صفحة ملفات المرضى\nهذه الوظيفة قيد التطوير",
            "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// طباعة
    /// </summary>
    private void Print()
    {
        try
        {
            // إنشاء محتوى HTML للطباعة
            var html = GeneratePatientListHtml();

            // حفظ كملف HTML مؤقت
            var tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(), $"قائمة_المرضى_{DateTime.Now:yyyyMMdd_HHmmss}.html");
            System.IO.File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);

            // فتح الملف في المتصفح للطباعة
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = tempFile,
                UseShellExecute = true
            });

            System.Windows.MessageBox.Show("تم فتح قائمة المرضى في المتصفح\nيمكنك الآن طباعتها باستخدام Ctrl+P",
                "طباعة", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض كشف حساب المريض المحدد
    /// </summary>
    private void ShowAccountStatement()
    {
        if (SelectedPatient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }
        try
        {
            var window = new Views.Patients.PatientAccountStatementWindow(SelectedPatient);
            window.ShowDialog();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح كشف الحساب: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    #endregion

    #region Session Operations

    /// <summary>
    /// إضافة جلسة جديدة
    /// </summary>
    private void AddSession()
    {
        if (SelectedPatient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        try
        {
            // إنشاء ViewModel للجلسة الجديدة
            var sessionViewModel = new ViewModels.Patients.AddSessionViewModel(SelectedPatient);

            // فتح نافذة إضافة الجلسة
            var addSessionWindow = new Views.Patients.AddSessionView(sessionViewModel);

            if (addSessionWindow.ShowDialog() == true)
            {
                // تحديث قائمة الجلسات بعد الإضافة
                _ = RefreshSessionsAsync();
                System.Windows.MessageBox.Show("تم إضافة الجلسة بنجاح", "نجح",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الجلسة: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تعديل الجلسة المحددة
    /// </summary>
    private void EditSession(Session? session)
    {
        if (session != null)
        {
            // سيتم تطوير وظيفة تعديل الجلسة المحددة لاحقاً
            System.Windows.MessageBox.Show($"سيتم تعديل الجلسة: {session.TreatmentType}", "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// حذف الجلسة المحددة
    /// </summary>
    private void DeleteSession(Session? session)
    {
        if (session != null)
        {
            // سيتم تطوير وظيفة حذف الجلسة المحددة لاحقاً
            System.Windows.MessageBox.Show($"سيتم حذف الجلسة: {session.TreatmentType}", "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// تحديث جلسات المريض
    /// </summary>
    private Task RefreshSessionsAsync()
    {
        if (SelectedPatient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return Task.CompletedTask;
        }

        // سيتم تطوير وظيفة تحديث جلسات المريض لاحقاً
        System.Windows.MessageBox.Show($"سيتم تحديث جلسات المريض: {SelectedPatient.FullName}", "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        return Task.CompletedTask;
    }

    #endregion

    #region Placeholder Methods

    private void Copy()
    {
        if (SelectedPatient != null)
        {
            // نسخ بيانات المريض المحدد إلى الحافظة
            var patientInfo = $"رقم الملف: {SelectedPatient.FileNumber}\n" +
                             $"الاسم: {SelectedPatient.FullName}\n" +
                             $"الجنس: {SelectedPatient.Gender}\n" +
                             $"الهاتف: {SelectedPatient.Phone}\n" +
                             $"التصنيف: {SelectedPatient.PatientCategory}\n" +
                             $"تاريخ التسجيل: {SelectedPatient.RegistrationDate:yyyy/MM/dd}\n" +
                             $"الرصيد الحالي: {SelectedPatient.CurrentBalance:N2}\n" +
                             $"الحالة: {SelectedPatient.FileStatus}";

            System.Windows.Clipboard.SetText(patientInfo);
            System.Windows.MessageBox.Show("تم نسخ بيانات المريض إلى الحافظة", "تم النسخ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
        else
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        }
    }

    private void Export()
    {
        try
        {
            // عرض خيارات التصدير
            var result = System.Windows.MessageBox.Show(
                "اختر نوع التصدير:\n\nYes = Excel\nNo = PDF\nCancel = إلغاء",
                "تصدير البيانات",
                System.Windows.MessageBoxButton.YesNoCancel,
                System.Windows.MessageBoxImage.Question);

            switch (result)
            {
                case System.Windows.MessageBoxResult.Yes:
                    ExportExcel();
                    break;
                case System.Windows.MessageBoxResult.No:
                    ExportPdf();
                    break;
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private void ExportExcel()
    {
        try
        {
            var saveDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                Title = "حفظ ملف Excel",
                FileName = $"قائمة_المرضى_{DateTime.Now:yyyy-MM-dd}.xlsx"
            };

            if (saveDialog.ShowDialog() == true)
            {
                // إنشاء محتوى CSV بسيط (يمكن فتحه في Excel)
                var csv = new System.Text.StringBuilder();
                csv.AppendLine("رقم الملف,الاسم الكامل,الجنس,رقم الهاتف,تاريخ التسجيل,الرصيد الحالي,حالة الملف");

                foreach (var patient in Patients)
                {
                    csv.AppendLine($"{patient.FileNumber},{patient.FullName},{patient.Gender},{patient.Phone},{patient.RegistrationDate:yyyy-MM-dd},{patient.CurrentBalance},{patient.FileStatus}");
                }

                System.IO.File.WriteAllText(saveDialog.FileName, csv.ToString(), System.Text.Encoding.UTF8);
                System.Windows.MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveDialog.FileName}", "نجح التصدير",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private void ExportPdf()
    {
        try
        {
            var saveDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "PDF Files|*.pdf",
                Title = "حفظ ملف PDF",
                FileName = $"قائمة_المرضى_{DateTime.Now:yyyy-MM-dd}.pdf"
            };

            if (saveDialog.ShowDialog() == true)
            {
                // إنشاء محتوى HTML بسيط وتحويله لـ PDF
                var html = GeneratePatientListHtml();

                // حفظ كملف HTML مؤقت (يمكن طباعته كـ PDF)
                var tempHtmlFile = System.IO.Path.ChangeExtension(saveDialog.FileName, ".html");
                System.IO.File.WriteAllText(tempHtmlFile, html, System.Text.Encoding.UTF8);

                System.Windows.MessageBox.Show($"تم إنشاء ملف HTML:\n{tempHtmlFile}\n\nيمكنك فتحه في المتصفح وطباعته كـ PDF", "نجح التصدير",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                // فتح الملف في المتصفح
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempHtmlFile,
                    UseShellExecute = true
                });
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private string GeneratePatientListHtml()
    {
        var html = new System.Text.StringBuilder();
        html.AppendLine("<!DOCTYPE html>");
        html.AppendLine("<html dir='rtl'>");
        html.AppendLine("<head>");
        html.AppendLine("<meta charset='UTF-8'>");
        html.AppendLine("<title>قائمة المرضى</title>");
        html.AppendLine("<style>");
        html.AppendLine("body { font-family: Arial, sans-serif; direction: rtl; }");
        html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
        html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
        html.AppendLine("th { background-color: #4A90E2; color: white; }");
        html.AppendLine("tr:nth-child(even) { background-color: #f2f2f2; }");
        html.AppendLine("h1 { text-align: center; color: #4A90E2; }");
        html.AppendLine("</style>");
        html.AppendLine("</head>");
        html.AppendLine("<body>");
        html.AppendLine("<h1>قائمة المرضى</h1>");
        html.AppendLine($"<p>تاريخ التصدير: {DateTime.Now:yyyy-MM-dd HH:mm}</p>");
        html.AppendLine("<table>");
        html.AppendLine("<tr><th>رقم الملف</th><th>الاسم الكامل</th><th>الجنس</th><th>رقم الهاتف</th><th>تاريخ التسجيل</th><th>الرصيد الحالي</th><th>حالة الملف</th></tr>");

        foreach (var patient in Patients)
        {
            html.AppendLine($"<tr>");
            html.AppendLine($"<td>{patient.FileNumber}</td>");
            html.AppendLine($"<td>{patient.FullName}</td>");
            html.AppendLine($"<td>{patient.Gender}</td>");
            html.AppendLine($"<td>{patient.Phone}</td>");
            html.AppendLine($"<td>{patient.RegistrationDate:yyyy-MM-dd}</td>");
            html.AppendLine($"<td>{patient.CurrentBalance:N2}</td>");
            html.AppendLine($"<td>{patient.FileStatus}</td>");
            html.AppendLine($"</tr>");
        }

        html.AppendLine("</table>");
        html.AppendLine("</body>");
        html.AppendLine("</html>");

        return html.ToString();
    }

    /// <summary>
    /// طباعة بيانات المريض
    /// </summary>
    private void PrintPatient(Patient? patient)
    {
        if (patient == null)
        {
            System.Windows.MessageBox.Show("يرجى تحديد مريض أولاً", "تنبيه",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
            return;
        }

        try
        {
            // إنشاء محتوى HTML لبيانات المريض
            var html = GeneratePatientDetailsHtml(patient);

            // حفظ كملف HTML مؤقت
            var tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(), $"بيانات_المريض_{patient.FileNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
            System.IO.File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);

            // فتح الملف في المتصفح للطباعة
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = tempFile,
                UseShellExecute = true
            });

            System.Windows.MessageBox.Show($"تم فتح بيانات المريض {patient.FullName} في المتصفح\nيمكنك الآن طباعتها باستخدام Ctrl+P",
                "طباعة", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في طباعة بيانات المريض: {ex.Message}", "خطأ",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private string GeneratePatientDetailsHtml(Patient patient)
    {
        var html = new System.Text.StringBuilder();
        html.AppendLine("<!DOCTYPE html>");
        html.AppendLine("<html dir='rtl'>");
        html.AppendLine("<head>");
        html.AppendLine("<meta charset='UTF-8'>");
        html.AppendLine($"<title>بيانات المريض - {patient.FullName}</title>");
        html.AppendLine("<style>");
        html.AppendLine("body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }");
        html.AppendLine(".header { text-align: center; color: #4A90E2; border-bottom: 2px solid #4A90E2; padding-bottom: 10px; margin-bottom: 20px; }");
        html.AppendLine(".info-section { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }");
        html.AppendLine(".info-title { font-weight: bold; color: #4A90E2; margin-bottom: 10px; }");
        html.AppendLine(".info-row { margin: 5px 0; }");
        html.AppendLine(".label { font-weight: bold; display: inline-block; width: 150px; }");
        html.AppendLine("</style>");
        html.AppendLine("</head>");
        html.AppendLine("<body>");

        html.AppendLine("<div class='header'>");
        html.AppendLine("<h1>بيانات المريض</h1>");
        html.AppendLine($"<h2>{patient.FullName}</h2>");
        html.AppendLine($"<p>رقم الملف: {patient.FileNumber}</p>");
        html.AppendLine("</div>");

        html.AppendLine("<div class='info-section'>");
        html.AppendLine("<div class='info-title'>المعلومات الأساسية</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>الاسم الكامل:</span> {patient.FullName}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>رقم الملف:</span> {patient.FileNumber}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>الجنس:</span> {patient.Gender}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>تاريخ الميلاد:</span> {patient.DateOfBirth?.ToString("yyyy/MM/dd") ?? "غير محدد"}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>رقم الهاتف:</span> {patient.Phone ?? "غير محدد"}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>العنوان:</span> {patient.Address ?? "غير محدد"}</div>");
        html.AppendLine("</div>");

        html.AppendLine("<div class='info-section'>");
        html.AppendLine("<div class='info-title'>معلومات الملف</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>تصنيف المريض:</span> {patient.PatientCategory}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>حالة الملف:</span> {patient.FileStatus}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>تاريخ التسجيل:</span> {patient.RegistrationDate.ToString("yyyy/MM/dd")}</div>");
        html.AppendLine($"<div class='info-row'><span class='label'>الرصيد الحالي:</span> {patient.CurrentBalance:N2} ريال</div>");
        html.AppendLine("</div>");

        if (!string.IsNullOrEmpty(patient.MedicalHistory) || !string.IsNullOrEmpty(patient.Allergies))
        {
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<div class='info-title'>المعلومات الطبية</div>");
            if (!string.IsNullOrEmpty(patient.MedicalHistory))
                html.AppendLine($"<div class='info-row'><span class='label'>التاريخ المرضي:</span> {patient.MedicalHistory}</div>");
            if (!string.IsNullOrEmpty(patient.Allergies))
                html.AppendLine($"<div class='info-row'><span class='label'>الحساسية:</span> {patient.Allergies}</div>");
            html.AppendLine("</div>");
        }

        if (!string.IsNullOrEmpty(patient.EmergencyContact))
        {
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<div class='info-title'>جهة الاتصال في الطوارئ</div>");
            html.AppendLine($"<div class='info-row'><span class='label'>الاسم:</span> {patient.EmergencyContact}</div>");
            html.AppendLine($"<div class='info-row'><span class='label'>رقم الهاتف:</span> {patient.EmergencyPhone ?? "غير محدد"}</div>");
            html.AppendLine("</div>");
        }

        if (!string.IsNullOrEmpty(patient.Notes))
        {
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<div class='info-title'>ملاحظات</div>");
            html.AppendLine($"<div class='info-row'>{patient.Notes}</div>");
            html.AppendLine("</div>");
        }

        html.AppendLine($"<div style='margin-top: 30px; text-align: center; font-size: 12px; color: #666;'>");
        html.AppendLine($"تم طباعة هذا التقرير في: {DateTime.Now:yyyy/MM/dd HH:mm}");
        html.AppendLine("</div>");

        html.AppendLine("</body>");
        html.AppendLine("</html>");

        return html.ToString();
    }

    #endregion
}
