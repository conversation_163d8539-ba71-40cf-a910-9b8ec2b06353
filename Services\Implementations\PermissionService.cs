using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    public class PermissionService : IPermissionService
    {
        private readonly AqlanCenterDbContext _context;

        public PermissionService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            return await _context.Permissions
                .Where(p => p.IsActive)
                .OrderBy(p => p.Module)
                .ThenBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Permission>> GetPermissionsByModuleAsync(string module)
        {
            return await _context.Permissions
                .Where(p => p.Module == module && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Permission?> GetPermissionByIdAsync(int id)
        {
            return await _context.Permissions
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);
        }

        public async Task<Permission?> GetPermissionByActionAsync(string action)
        {
            return await _context.Permissions
                .FirstOrDefaultAsync(p => p.Action == action && p.IsActive);
        }

        public async Task<bool> AddPermissionAsync(Permission permission)
        {
            try
            {
                _context.Permissions.Add(permission);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdatePermissionAsync(Permission permission)
        {
            try
            {
                _context.Permissions.Update(permission);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeletePermissionAsync(int id)
        {
            try
            {
                var permission = await GetPermissionByIdAsync(id);
                if (permission == null) return false;

                permission.IsDeleted = true;
                permission.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> InitializePermissionsAsync()
        {
            try
            {
                var existingPermissions = await _context.Permissions.ToListAsync();
                var allPermissions = Permissions.GetAllPermissions();

                foreach (var permission in allPermissions)
                {
                    if (!existingPermissions.Any(ep => ep.Action == permission.Action))
                    {
                        _context.Permissions.Add(permission);
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<UserPermission>> GetUserPermissionsAsync(int userId)
        {
            return await _context.UserPermissions
                .Include(up => up.Permission)
                .Where(up => up.UserId == userId && up.IsGranted)
                .OrderBy(up => up.Permission.Module)
                .ThenBy(up => up.Permission.Name)
                .ToListAsync();
        }

        public async Task<bool> GrantPermissionToUserAsync(int userId, int permissionId, string? notes = null)
        {
            try
            {
                var existingPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

                if (existingPermission != null)
                {
                    existingPermission.IsGranted = true;
                    existingPermission.Notes = notes;
                    existingPermission.UpdatedAt = DateTime.Now;
                }
                else
                {
                    var userPermission = new UserPermission
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        IsGranted = true,
                        Notes = notes
                    };
                    _context.UserPermissions.Add(userPermission);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RevokePermissionFromUserAsync(int userId, int permissionId)
        {
            try
            {
                var userPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

                if (userPermission != null)
                {
                    userPermission.IsGranted = false;
                    userPermission.UpdatedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUserPermissionAsync(int userId, int permissionId, bool isGranted, string? notes = null)
        {
            try
            {
                var userPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

                if (userPermission != null)
                {
                    userPermission.IsGranted = isGranted;
                    userPermission.Notes = notes;
                    userPermission.UpdatedAt = DateTime.Now;
                }
                else if (isGranted)
                {
                    userPermission = new UserPermission
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        IsGranted = true,
                        Notes = notes
                    };
                    _context.UserPermissions.Add(userPermission);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> HasUserPermissionAsync(int userId, string permissionAction)
        {
            return await _context.UserPermissions
                .Include(up => up.Permission)
                .AnyAsync(up => up.UserId == userId &&
                               up.IsGranted &&
                               up.Permission.Action == permissionAction &&
                               up.Permission.IsActive)
                .ConfigureAwait(false);
        }

        public async Task<bool> GrantMultiplePermissionsToUserAsync(int userId, List<int> permissionIds, string? notes = null)
        {
            try
            {
                foreach (var permissionId in permissionIds)
                {
                    await GrantPermissionToUserAsync(userId, permissionId, notes)
                        .ConfigureAwait(false);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RevokeMultiplePermissionsFromUserAsync(int userId, List<int> permissionIds)
        {
            try
            {
                foreach (var permissionId in permissionIds)
                {
                    await RevokePermissionFromUserAsync(userId, permissionId)
                        .ConfigureAwait(false);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> GrantAllPermissionsToUserAsync(int userId, string? notes = null)
        {
            try
            {
                var allPermissions = await GetAllPermissionsAsync()
                    .ConfigureAwait(false);
                var permissionIds = allPermissions.Select(p => p.Id).ToList();
                return await GrantMultiplePermissionsToUserAsync(userId, permissionIds, notes)
                    .ConfigureAwait(false);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RevokeAllPermissionsFromUserAsync(int userId)
        {
            try
            {
                var userPermissions = await _context.UserPermissions
                    .Where(up => up.UserId == userId)
                    .ToListAsync();

                foreach (var userPermission in userPermissions)
                {
                    userPermission.IsGranted = false;
                    userPermission.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> GrantPermissionsByModuleToUserAsync(int userId, string module, string? notes = null)
        {
            try
            {
                var modulePermissions = await GetPermissionsByModuleAsync(module);
                var permissionIds = modulePermissions.Select(p => p.Id).ToList();
                return await GrantMultiplePermissionsToUserAsync(userId, permissionIds, notes);
            }
            catch
            {
                return false;
            }
        }
    }
}