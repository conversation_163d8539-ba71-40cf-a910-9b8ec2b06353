using System.Windows;
using AqlanCenterProApp.Models;
using System.Diagnostics;
using System.Windows.Input;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class PatientDetailsDialog : Window
    {
        public PatientDetailsDialog(Patient patient)
        {
            InitializeComponent();
            DataContext = patient;
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void WhatsApp_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is Patient patient && !string.IsNullOrWhiteSpace(patient.Phone))
            {
                string phone = patient.Phone.Replace("-", "").Replace(" ", "");
                string msg = $"مرحباً {patient.FullName}, نرحب بك في مركز الدكتور عقلان الكامل!";
                string url = $"https://wa.me/966{phone}?text={System.Net.WebUtility.UrlEncode(msg)}";
                Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
            }
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم دعم طباعة بيانات المريض قريباً.", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CopyPhone_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is Patient patient && !string.IsNullOrWhiteSpace(patient.Phone))
            {
                Clipboard.SetText(patient.Phone);
                MessageBox.Show("تم نسخ رقم الهاتف!", "نسخ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
} 