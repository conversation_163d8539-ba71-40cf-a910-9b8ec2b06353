using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة إدارة كشوف الحساب
    /// </summary>
    public class AccountStatementService : IAccountStatementService
    {
        private readonly AqlanCenterDbContext _context;

        public AccountStatementService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        // العمليات الأساسية (CRUD)
        public async Task<IEnumerable<AccountStatement>> GetAllStatementsAsync()
        {
            return await _context.AccountStatements
                .OrderByDescending(as_ => as_.StatementDate)
                .ToListAsync();
        }

        public async Task<AccountStatement?> GetStatementByIdAsync(int id)
        {
            return await _context.AccountStatements
                .FirstOrDefaultAsync(as_ => as_.Id == id);
        }

        public async Task<AccountStatement?> GetStatementByNumberAsync(string statementNumber)
        {
            return await _context.AccountStatements
                .FirstOrDefaultAsync(as_ => as_.StatementNumber == statementNumber);
        }

        public async Task<AccountStatement> CreateStatementAsync(AccountStatement statement)
        {
            var validation = await ValidateStatementAsync(statement);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            if (string.IsNullOrEmpty(statement.StatementNumber))
                statement.StatementNumber = await GetNextStatementNumberAsync();

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<AccountStatement> UpdateStatementAsync(AccountStatement statement)
        {
            var validation = await ValidateStatementAsync(statement);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            _context.AccountStatements.Update(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<bool> DeleteStatementAsync(int id)
        {
            var statement = await _context.AccountStatements.FindAsync(id);
            if (statement == null)
                return false;

            statement.IsDeleted = true;
            statement.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return true;
        }

        // إنشاء كشوف الحساب التلقائية
        public async Task<AccountStatement> GeneratePatientStatementAsync(int patientId, DateTime startDate, DateTime endDate, string generatedBy)
        {
            var patient = await _context.Patients.FindAsync(patientId);
            if (patient == null)
                throw new InvalidOperationException("المريض غير موجود");

            var openingBalance = await CalculatePatientBalanceAsync(patientId, startDate.AddDays(-1));
            var transactions = await GetPatientTransactionsAsync(patientId, startDate, endDate);
            var totalDebits = transactions.Sum(t => t.DebitAmount);
            var totalCredits = transactions.Sum(t => t.CreditAmount);
            var closingBalance = openingBalance + totalDebits - totalCredits;

            var statement = new AccountStatement
            {
                StatementNumber = await GetNextStatementNumberAsync(),
                StatementDate = DateTime.Now,
                EntityType = "مريض",
                EntityId = patientId,
                EntityName = patient.FullName,
                StartDate = startDate,
                EndDate = endDate,
                OpeningBalance = openingBalance,
                TotalDebits = totalDebits,
                TotalCredits = totalCredits,
                ClosingBalance = closingBalance,
                BalanceType = closingBalance > 0 ? "مدين" : closingBalance < 0 ? "دائن" : "صفر",
                GeneratedBy = generatedBy
            };

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<AccountStatement> GenerateDoctorStatementAsync(int doctorId, DateTime startDate, DateTime endDate, string generatedBy)
        {
            var doctor = await _context.Doctors.FindAsync(doctorId);
            if (doctor == null)
                throw new InvalidOperationException("الطبيب غير موجود");

            var openingBalance = await CalculateDoctorBalanceAsync(doctorId, startDate.AddDays(-1));
            var transactions = await GetDoctorTransactionsAsync(doctorId, startDate, endDate);
            var totalDebits = transactions.Sum(t => t.DebitAmount);
            var totalCredits = transactions.Sum(t => t.CreditAmount);
            var closingBalance = openingBalance + totalDebits - totalCredits;

            var statement = new AccountStatement
            {
                StatementNumber = await GetNextStatementNumberAsync(),
                StatementDate = DateTime.Now,
                EntityType = "طبيب",
                EntityId = doctorId,
                EntityName = doctor.FullName,
                StartDate = startDate,
                EndDate = endDate,
                OpeningBalance = openingBalance,
                TotalDebits = totalDebits,
                TotalCredits = totalCredits,
                ClosingBalance = closingBalance,
                BalanceType = closingBalance > 0 ? "مدين" : closingBalance < 0 ? "دائن" : "صفر",
                GeneratedBy = generatedBy
            };

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<AccountStatement> GenerateEmployeeStatementAsync(int employeeId, DateTime startDate, DateTime endDate, string generatedBy)
        {
            var employee = await _context.Employees.FindAsync(employeeId);
            if (employee == null)
                throw new InvalidOperationException("الموظف غير موجود");

            var openingBalance = await CalculateEmployeeBalanceAsync(employeeId, startDate.AddDays(-1));
            var transactions = await GetEmployeeTransactionsAsync(employeeId, startDate, endDate);
            var totalDebits = transactions.Sum(t => t.DebitAmount);
            var totalCredits = transactions.Sum(t => t.CreditAmount);
            var closingBalance = openingBalance + totalDebits - totalCredits;

            var statement = new AccountStatement
            {
                StatementNumber = await GetNextStatementNumberAsync(),
                StatementDate = DateTime.Now,
                EntityType = "موظف",
                EntityId = employeeId,
                EntityName = employee.FullName,
                StartDate = startDate,
                EndDate = endDate,
                OpeningBalance = openingBalance,
                TotalDebits = totalDebits,
                TotalCredits = totalCredits,
                ClosingBalance = closingBalance,
                BalanceType = closingBalance > 0 ? "مدين" : closingBalance < 0 ? "دائن" : "صفر",
                GeneratedBy = generatedBy
            };

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<AccountStatement> GenerateSupplierStatementAsync(int supplierId, DateTime startDate, DateTime endDate, string generatedBy)
        {
            var supplier = await _context.Suppliers.FindAsync(supplierId);
            if (supplier == null)
                throw new InvalidOperationException("المورد غير موجود");

            var openingBalance = await CalculateSupplierBalanceAsync(supplierId, startDate.AddDays(-1));
            var transactions = await GetSupplierTransactionsAsync(supplierId, startDate, endDate);
            var totalDebits = transactions.Sum(t => t.DebitAmount);
            var totalCredits = transactions.Sum(t => t.CreditAmount);
            var closingBalance = openingBalance + totalDebits - totalCredits;

            var statement = new AccountStatement
            {
                StatementNumber = await GetNextStatementNumberAsync(),
                StatementDate = DateTime.Now,
                EntityType = "مورد",
                EntityId = supplierId,
                EntityName = supplier.Name,
                StartDate = startDate,
                EndDate = endDate,
                OpeningBalance = openingBalance,
                TotalDebits = totalDebits,
                TotalCredits = totalCredits,
                ClosingBalance = closingBalance,
                BalanceType = closingBalance > 0 ? "مدين" : closingBalance < 0 ? "دائن" : "صفر",
                GeneratedBy = generatedBy
            };

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        public async Task<AccountStatement> GenerateLabStatementAsync(int labId, DateTime startDate, DateTime endDate, string generatedBy)
        {
            var lab = await _context.Labs.FindAsync(labId);
            if (lab == null)
                throw new InvalidOperationException("المختبر غير موجود");

            var openingBalance = await CalculateLabBalanceAsync(labId, startDate.AddDays(-1));
            var transactions = await GetLabTransactionsAsync(labId, startDate, endDate);
            var totalDebits = transactions.Sum(t => t.DebitAmount);
            var totalCredits = transactions.Sum(t => t.CreditAmount);
            var closingBalance = openingBalance + totalDebits - totalCredits;

            var statement = new AccountStatement
            {
                StatementNumber = await GetNextStatementNumberAsync(),
                StatementDate = DateTime.Now,
                EntityType = "مختبر",
                EntityId = labId,
                EntityName = lab.Name,
                StartDate = startDate,
                EndDate = endDate,
                OpeningBalance = openingBalance,
                TotalDebits = totalDebits,
                TotalCredits = totalCredits,
                ClosingBalance = closingBalance,
                BalanceType = closingBalance > 0 ? "مدين" : closingBalance < 0 ? "دائن" : "صفر",
                GeneratedBy = generatedBy
            };

            _context.AccountStatements.Add(statement);
            await _context.SaveChangesAsync();

            return statement;
        }

        // البحث والتصفية
        public async Task<IEnumerable<AccountStatement>> SearchStatementsAsync(string searchTerm)
        {
            return await _context.AccountStatements
                .Where(s => s.EntityName.Contains(searchTerm) || s.StatementNumber.Contains(searchTerm))
                .OrderByDescending(s => s.StatementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<AccountStatement>> GetStatementsByDateAsync(DateTime date)
        {
            return await _context.AccountStatements
                .Where(s => s.StatementDate.Date == date.Date)
                .OrderByDescending(s => s.StatementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<AccountStatement>> GetStatementsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.AccountStatements
                .Where(s => s.StatementDate >= startDate && s.StatementDate <= endDate)
                .OrderByDescending(s => s.StatementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<AccountStatement>> GetStatementsByEntityTypeAsync(string entityType)
        {
            return await _context.AccountStatements
                .Where(s => s.EntityType == entityType)
                .OrderByDescending(s => s.StatementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<AccountStatement>> GetStatementsByEntityAsync(string entityType, int entityId)
        {
            return await _context.AccountStatements
                .Where(s => s.EntityType == entityType && s.EntityId == entityId)
                .OrderByDescending(s => s.StatementDate)
                .ToListAsync();
        }

        // إدارة أرقام الكشوف
        public async Task<string> GetNextStatementNumberAsync()
        {
            var lastStatement = await _context.AccountStatements
                .OrderByDescending(s => s.StatementNumber)
                .FirstOrDefaultAsync();

            if (lastStatement == null)
                return "ST-001";

            var lastNumber = lastStatement.StatementNumber;
            if (lastNumber.StartsWith("ST-"))
            {
                if (int.TryParse(lastNumber.Substring(3), out int number))
                {
                    return $"ST-{(number + 1):D3}";
                }
            }

            return $"ST-{DateTime.Now:yyyyMMdd}-001";
        }

        public async Task<bool> IsStatementNumberAvailableAsync(string statementNumber)
        {
            return !await _context.AccountStatements.AnyAsync(s => s.StatementNumber == statementNumber);
        }

        // حساب الأرصدة
        public async Task<decimal> CalculatePatientBalanceAsync(int patientId, DateTime? asOfDate = null)
        {
            var query = _context.Sessions.Where(s => s.PatientId == patientId);
            if (asOfDate.HasValue)
                query = query.Where(s => s.SessionDate <= asOfDate.Value);

            var totalDebits = await query.SumAsync(s => s.Cost);

            var receiptsQuery = _context.Receipts.Where(r => r.PatientId == patientId);
            if (asOfDate.HasValue)
                receiptsQuery = receiptsQuery.Where(r => r.ReceiptDate <= asOfDate.Value);

            var totalCredits = await receiptsQuery.SumAsync(r => r.Amount);

            return totalDebits - totalCredits;
        }

        public async Task<decimal> CalculateDoctorBalanceAsync(int doctorId, DateTime? asOfDate = null)
        {
            var query = _context.Appointments.Where(a => a.DoctorId == doctorId);
            if (asOfDate.HasValue)
                query = query.Where(a => a.AppointmentDate <= asOfDate.Value);

            var totalDebits = await query.SumAsync(a => a.Cost);

            var vouchersQuery = _context.PaymentVouchers.Where(pv => pv.DoctorId == doctorId);
            if (asOfDate.HasValue)
                vouchersQuery = vouchersQuery.Where(pv => pv.VoucherDate <= asOfDate.Value);

            var totalCredits = await vouchersQuery.SumAsync(pv => pv.Amount);

            return totalDebits - totalCredits;
        }

        public async Task<decimal> CalculateEmployeeBalanceAsync(int employeeId, DateTime? asOfDate = null)
        {
            var salariesQuery = _context.EmployeeSalaries.Where(es => es.EmployeeId == employeeId);
            if (asOfDate.HasValue)
                salariesQuery = salariesQuery.Where(es => es.SalaryDate <= asOfDate.Value);

            var totalDebits = await salariesQuery.SumAsync(es => es.Amount);

            var vouchersQuery = _context.PaymentVouchers.Where(pv => pv.EmployeeId == employeeId);
            if (asOfDate.HasValue)
                vouchersQuery = vouchersQuery.Where(pv => pv.VoucherDate <= asOfDate.Value);

            var totalCredits = await vouchersQuery.SumAsync(pv => pv.Amount);

            return totalDebits - totalCredits;
        }

        public async Task<decimal> CalculateSupplierBalanceAsync(int supplierId, DateTime? asOfDate = null)
        {
            var purchasesQuery = _context.Purchases.Where(p => p.SupplierId == supplierId);
            if (asOfDate.HasValue)
                purchasesQuery = purchasesQuery.Where(p => p.PurchaseDate <= asOfDate.Value);

            var totalDebits = await purchasesQuery.SumAsync(p => p.TotalAmount);

            var vouchersQuery = _context.PaymentVouchers.Where(pv => pv.SupplierId == supplierId);
            if (asOfDate.HasValue)
                vouchersQuery = vouchersQuery.Where(pv => pv.VoucherDate <= asOfDate.Value);

            var totalCredits = await vouchersQuery.SumAsync(pv => pv.Amount);

            return totalDebits - totalCredits;
        }

        public async Task<decimal> CalculateLabBalanceAsync(int labId, DateTime? asOfDate = null)
        {
            var ordersQuery = _context.LabOrders.Where(lo => lo.LabId == labId);
            if (asOfDate.HasValue)
                ordersQuery = ordersQuery.Where(lo => lo.SendDate <= asOfDate.Value);

            var totalDebits = await ordersQuery.SumAsync(lo => lo.Cost ?? 0);

            var vouchersQuery = _context.PaymentVouchers.Where(pv => pv.LabId == labId);
            if (asOfDate.HasValue)
                vouchersQuery = vouchersQuery.Where(pv => pv.VoucherDate <= asOfDate.Value);

            var totalCredits = await vouchersQuery.SumAsync(pv => pv.Amount);

            return totalDebits - totalCredits;
        }

        // جلب الحركات المالية
        public async Task<IEnumerable<FinancialTransaction>> GetPatientTransactionsAsync(int patientId, DateTime startDate, DateTime endDate)
        {
            var transactions = new List<FinancialTransaction>();
            var sessions = await _context.Sessions
                .Where(s => s.PatientId == patientId && s.SessionDate >= startDate && s.SessionDate <= endDate)
                .OrderBy(s => s.SessionDate)
                .ToListAsync();

            foreach (var session in sessions)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = session.SessionDate,
                    TransactionType = "جلسة علاجية",
                    Description = session.TreatmentDescription ?? "جلسة علاجية",
                    DebitAmount = session.Cost,
                    CreditAmount = 0m,
                    ReferenceNumber = session.Id.ToString(),
                    Notes = session.Notes ?? string.Empty
                });
            }

            var receipts = await _context.Receipts
                .Where(r => r.PatientId == patientId && r.ReceiptDate >= startDate && r.ReceiptDate <= endDate)
                .OrderBy(r => r.ReceiptDate)
                .ToListAsync();

            foreach (var receipt in receipts)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = receipt.ReceiptDate,
                    TransactionType = "إيصال دفع",
                    Description = receipt.Description,
                    DebitAmount = 0m,
                    CreditAmount = receipt.Amount,
                    ReferenceNumber = receipt.ReceiptNumber,
                    Notes = receipt.Notes ?? string.Empty
                });
            }

            return transactions.OrderBy(t => t.TransactionDate);
        }

        public async Task<IEnumerable<FinancialTransaction>> GetDoctorTransactionsAsync(int doctorId, DateTime startDate, DateTime endDate)
        {
            var transactions = new List<FinancialTransaction>();
            var appointments = await _context.Appointments
                .Where(a => a.DoctorId == doctorId && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate)
                .OrderBy(a => a.AppointmentDate)
                .ToListAsync();

            foreach (var appointment in appointments)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = appointment.AppointmentDate,
                    TransactionType = "موعد طبي",
                    Description = appointment.Description ?? "موعد طبي",
                    DebitAmount = appointment.Cost,
                    CreditAmount = 0m,
                    ReferenceNumber = appointment.Id.ToString(),
                    Notes = appointment.Notes ?? string.Empty
                });
            }

            var vouchers = await _context.PaymentVouchers
                .Where(pv => pv.DoctorId == doctorId && pv.VoucherDate >= startDate && pv.VoucherDate <= endDate)
                .OrderBy(pv => pv.VoucherDate)
                .ToListAsync();

            foreach (var voucher in vouchers)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = voucher.VoucherDate,
                    TransactionType = "سند صرف",
                    Description = voucher.Description ?? string.Empty,
                    DebitAmount = 0m,
                    CreditAmount = voucher.Amount,
                    ReferenceNumber = voucher.VoucherNumber ?? string.Empty,
                    Notes = voucher.Notes ?? string.Empty
                });
            }

            return transactions.OrderBy(t => t.TransactionDate);
        }

        public async Task<IEnumerable<FinancialTransaction>> GetEmployeeTransactionsAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            var transactions = new List<FinancialTransaction>();
            var salaries = await _context.EmployeeSalaries
                .Where(es => es.EmployeeId == employeeId && es.SalaryDate >= startDate && es.SalaryDate <= endDate)
                .OrderBy(es => es.SalaryDate)
                .ToListAsync();

            foreach (var salary in salaries)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = salary.SalaryDate,
                    TransactionType = "راتب",
                    Description = $"راتب {salary.Month}/{salary.Year}",
                    DebitAmount = salary.Amount,
                    CreditAmount = 0m,
                    ReferenceNumber = salary.Id.ToString(),
                    Notes = salary.Notes ?? string.Empty
                });
            }

            var vouchers = await _context.PaymentVouchers
                .Where(pv => pv.EmployeeId == employeeId && pv.VoucherDate >= startDate && pv.VoucherDate <= endDate)
                .OrderBy(pv => pv.VoucherDate)
                .ToListAsync();

            foreach (var voucher in vouchers)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = voucher.VoucherDate,
                    TransactionType = "سند صرف",
                    Description = voucher.Description ?? string.Empty,
                    DebitAmount = 0m,
                    CreditAmount = voucher.Amount,
                    ReferenceNumber = voucher.VoucherNumber ?? string.Empty,
                    Notes = voucher.Notes ?? string.Empty
                });
            }

            return transactions.OrderBy(t => t.TransactionDate);
        }

        public async Task<IEnumerable<FinancialTransaction>> GetSupplierTransactionsAsync(int supplierId, DateTime startDate, DateTime endDate)
        {
            var transactions = new List<FinancialTransaction>();
            var purchases = await _context.Purchases
                .Where(p => p.SupplierId == supplierId && p.PurchaseDate >= startDate && p.PurchaseDate <= endDate)
                .OrderBy(p => p.PurchaseDate)
                .ToListAsync();

            foreach (var purchase in purchases)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = purchase.PurchaseDate,
                    TransactionType = "مشتريات",
                    Description = purchase.Notes ?? "مشتريات",
                    DebitAmount = purchase.TotalAmount,
                    CreditAmount = 0m,
                    ReferenceNumber = purchase.InvoiceNumber,
                    Notes = purchase.Notes
                });
            }

            var vouchers = await _context.PaymentVouchers
                .Where(pv => pv.SupplierId == supplierId && pv.VoucherDate >= startDate && pv.VoucherDate <= endDate)
                .OrderBy(pv => pv.VoucherDate)
                .ToListAsync();

            foreach (var voucher in vouchers)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = voucher.VoucherDate,
                    TransactionType = "سند صرف",
                    Description = voucher.Description ?? string.Empty,
                    DebitAmount = 0m,
                    CreditAmount = voucher.Amount,
                    ReferenceNumber = voucher.VoucherNumber ?? string.Empty,
                    Notes = voucher.Notes ?? string.Empty
                });
            }

            return transactions.OrderBy(t => t.TransactionDate);
        }

        public async Task<IEnumerable<FinancialTransaction>> GetLabTransactionsAsync(int labId, DateTime startDate, DateTime endDate)
        {
            var transactions = new List<FinancialTransaction>();
            var orders = await _context.LabOrders
                .Where(lo => lo.LabId == labId && lo.SendDate >= startDate && lo.SendDate <= endDate)
                .OrderBy(lo => lo.SendDate)
                .ToListAsync();

            foreach (var order in orders)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = order.SendDate,
                    TransactionType = "طلب مختبر",
                    Description = order.WorkType ?? string.Empty,
                    DebitAmount = order.Cost ?? 0,
                    CreditAmount = 0m,
                    ReferenceNumber = order.OrderNumber ?? string.Empty,
                    Notes = order.Notes ?? string.Empty
                });
            }

            var vouchers = await _context.PaymentVouchers
                .Where(pv => pv.LabId == labId && pv.VoucherDate >= startDate && pv.VoucherDate <= endDate)
                .OrderBy(pv => pv.VoucherDate)
                .ToListAsync();

            foreach (var voucher in vouchers)
            {
                transactions.Add(new FinancialTransaction
                {
                    TransactionDate = voucher.VoucherDate,
                    TransactionType = "سند صرف",
                    Description = voucher.Description ?? string.Empty,
                    DebitAmount = 0m,
                    CreditAmount = voucher.Amount,
                    ReferenceNumber = voucher.VoucherNumber ?? string.Empty,
                    Notes = voucher.Notes ?? string.Empty
                });
            }

            return transactions.OrderBy(t => t.TransactionDate);
        }

        // إحصائيات وتقارير
        public async Task<AccountStatementStatistics> GetStatementStatisticsAsync()
        {
            var statements = await _context.AccountStatements.ToListAsync();
            var statistics = new AccountStatementStatistics
            {
                TotalStatements = statements.Count,
                PrintedStatements = statements.Count(s => s.IsPrinted),
                UnprintedStatements = statements.Count(s => !s.IsPrinted),
                TotalDebitBalance = statements.Sum(s => s.TotalDebits),
                TotalCreditBalance = statements.Sum(s => s.TotalCredits),
                DebtorEntities = statements.Count(s => s.BalanceType == "مدين"),
                CreditorEntities = statements.Count(s => s.BalanceType == "دائن")
            };

            var entityTypes = statements.GroupBy(s => s.EntityType);
            foreach (var type in entityTypes)
            {
                var key = type.Key ?? "غير محدد";
                statistics.StatementsByEntityType[key] = type.Count();
            }

            var currentYear = DateTime.Now.Year;
            for (int month = 1; month <= 12; month++)
            {
                var monthStatements = statements.Where(s => s.StatementDate.Year == currentYear && s.StatementDate.Month == month);
                var monthKey = $"{currentYear}-{month:D2}";
                statistics.StatementsByMonth[monthKey] = monthStatements.Count();
            }

            return statistics;
        }

        public async Task<Dictionary<string, decimal>> GetTotalBalancesByTypeAsync()
        {
            var statements = await _context.AccountStatements.ToListAsync();
            return statements.GroupBy(s => s.EntityType)
                           .ToDictionary(g => g.Key, g => g.Sum(s => s.ClosingBalance));
        }

        public async Task<IEnumerable<Patient>> GetDebtorPatientsAsync(decimal minimumDebt = 0)
        {
            var patients = await _context.Patients.ToListAsync();
            var debtorPatients = new List<Patient>();

            foreach (var patient in patients)
            {
                var balance = await CalculatePatientBalanceAsync(patient.Id);
                if (balance >= minimumDebt)
                {
                    debtorPatients.Add(patient);
                }
            }

            return debtorPatients.OrderByDescending(p => p.Id);
        }

        public async Task<IEnumerable<Patient>> GetCreditorPatientsAsync(decimal minimumCredit = 0)
        {
            var patients = await _context.Patients.ToListAsync();
            var creditorPatients = new List<Patient>();

            foreach (var patient in patients)
            {
                var balance = await CalculatePatientBalanceAsync(patient.Id);
                if (balance <= -minimumCredit)
                {
                    creditorPatients.Add(patient);
                }
            }

            return creditorPatients.OrderBy(p => p.Id);
        }

        // الطباعة والتصدير
        public async Task<bool> PrintStatementAsync(int statementId)
        {
            await Task.Delay(100); // محاكاة عملية الطباعة
            return true;
        }

        public async Task<byte[]> ExportStatementToPdfAsync(int statementId)
        {
            await Task.Delay(100); // محاكاة عملية التصدير
            return new byte[0];
        }

        public async Task<byte[]> ExportStatementToExcelAsync(int statementId)
        {
            await Task.Delay(100); // محاكاة عملية التصدير
            return new byte[0];
        }

        public async Task<byte[]> ExportStatementToCsvAsync(int statementId)
        {
            await Task.Delay(100); // محاكاة عملية التصدير
            return new byte[0];
        }

        // التحقق من صحة البيانات
        public async Task<(bool IsValid, string ErrorMessage)> ValidateStatementAsync(AccountStatement statement)
        {
            if (statement == null)
                return (false, "كشف الحساب مطلوب");

            if (string.IsNullOrEmpty(statement.StatementNumber))
                return (false, "رقم الكشف مطلوب");

            if (string.IsNullOrEmpty(statement.EntityType))
                return (false, "نوع الكيان مطلوب");

            if (statement.EntityId <= 0)
                return (false, "معرف الكيان مطلوب");

            if (string.IsNullOrEmpty(statement.EntityName))
                return (false, "اسم الكيان مطلوب");

            if (statement.StartDate >= statement.EndDate)
                return (false, "تاريخ البداية يجب أن يكون قبل تاريخ النهاية");

            if (await _context.AccountStatements.AnyAsync(s => s.StatementNumber == statement.StatementNumber && s.Id != statement.Id))
                return (false, "رقم الكشف مستخدم بالفعل");

            return (true, string.Empty);
        }

        public Task<(bool IsValid, string ErrorMessage)> ValidateDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate >= endDate)
                return Task.FromResult((false, "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"));

            if (startDate > DateTime.Now)
                return Task.FromResult((false, "تاريخ البداية لا يمكن أن يكون في المستقبل"));

            if (endDate > DateTime.Now)
                return Task.FromResult((false, "تاريخ النهاية لا يمكن أن يكون في المستقبل"));

            return Task.FromResult((true, string.Empty));
        }

        public async Task<List<AccountStatement>> GetAccountStatementsAsync(string searchTerm, string selectedType, DateTime? startDate, DateTime? endDate)
        {
            var query = _context.AccountStatements.AsQueryable();
            if (!string.IsNullOrWhiteSpace(searchTerm))
                query = query.Where(s => s.EntityName.Contains(searchTerm));
            if (selectedType != "الكل")
                query = query.Where(s => s.EntityType == selectedType);
            if (startDate.HasValue)
                query = query.Where(s => s.StatementDate >= startDate.Value);
            if (endDate.HasValue)
                query = query.Where(s => s.StatementDate <= endDate.Value);
            return await query.ToListAsync();
        }
    }
}