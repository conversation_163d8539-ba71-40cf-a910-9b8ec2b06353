using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج كشف الحساب
    /// </summary>
    public class AccountStatement : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string StatementNumber { get; set; } = string.Empty;

        [Required]
        public DateTime StatementDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty; // مريض، طبيب، موظف، معمل، مورد

        [Required]
        public int EntityId { get; set; } // معرف الكيان

        [Required]
        [StringLength(100)]
        public string EntityName { get; set; } = string.Empty; // اسم الكيان

        public DateTime StartDate { get; set; } // تاريخ بداية الفترة

        public DateTime EndDate { get; set; } // تاريخ نهاية الفترة

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0; // الرصيد الافتتاحي

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebits { get; set; } = 0; // إجمالي المدين

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredits { get; set; } = 0; // إجمالي الدائن

        [Column(TypeName = "decimal(18,2)")]
        public decimal ClosingBalance { get; set; } = 0; // الرصيد الختامي

        [StringLength(20)]
        public string BalanceType { get; set; } = "مدين"; // مدين، دائن، صفر

        [StringLength(500)]
        public new string? Notes { get; set; }

        [StringLength(100)]
        public string? GeneratedBy { get; set; } // من أنشأ الكشف

        public bool IsPrinted { get; set; } = false; // هل تم طباعته

        public DateTime? PrintedAt { get; set; } // تاريخ الطباعة

        [StringLength(100)]
        public string? PrintedBy { get; set; } // من طبع الكشف
    }
}