<Window x:Class="AqlanCenterProApp.Views.Patients.PatientStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إحصائيات المرضى"
        Width="1000"
        Height="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F6FA">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="إحصائيات المرضى التفصيلية"
                   FontSize="28"
                   FontWeight="Bold"
                   Foreground="#3498DB"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- المحتوى -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- الإحصائيات العامة -->
                <GroupBox Header="الإحصائيات العامة" Margin="0,0,0,20" Padding="15">
                    <UniformGrid Columns="4" Rows="2">
                        <!-- إجمالي المرضى -->
                        <Border Background="#E3F0FF" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="📊" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="إجمالي المرضى" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalPatientsText" Text="0" FontSize="24" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#3498DB"/>
                            </StackPanel>
                        </Border>

                        <!-- المرضى النشطين -->
                        <Border Background="#E3FFD3" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="✅" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="المرضى النشطين" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="ActivePatientsText" Text="0" FontSize="24" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#27AE60"/>
                            </StackPanel>
                        </Border>

                        <!-- الجدد هذا الشهر -->
                        <Border Background="#FFF9D3" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="🆕" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="الجدد هذا الشهر" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="NewPatientsText" Text="0" FontSize="24" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#F39C12"/>
                            </StackPanel>
                        </Border>

                        <!-- المدينين -->
                        <Border Background="#FFD7D7" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="💰" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="المرضى المدينين" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="DebtorPatientsText" Text="0" FontSize="24" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#E74C3C"/>
                            </StackPanel>
                        </Border>

                        <!-- متوسط العمر -->
                        <Border Background="#F0E6FF" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="🎂" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="متوسط العمر" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="AverageAgeText" Text="0" FontSize="24" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#9B59B6"/>
                            </StackPanel>
                        </Border>

                        <!-- إجمالي الديون -->
                        <Border Background="#FFE6E6" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="📉" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="إجمالي الديون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalDebtsText" Text="0 ريال" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#E74C3C"/>
                            </StackPanel>
                        </Border>

                        <!-- إجمالي المدفوعات -->
                        <Border Background="#E6FFE6" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="📈" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="إجمالي المدفوعات" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalPaymentsText" Text="0 ريال" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#27AE60"/>
                            </StackPanel>
                        </Border>

                        <!-- آخر مريض -->
                        <Border Background="#E6F3FF" CornerRadius="10" Margin="5" Padding="15">
                            <StackPanel>
                                <TextBlock Text="👤" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="آخر مريض مسجل" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="LastPatientText" Text="لا يوجد" FontSize="16" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#3498DB" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                </GroupBox>

                <!-- إحصائيات التصنيفات -->
                <GroupBox Header="إحصائيات التصنيفات" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- توزيع الجنس -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="توزيع الجنس" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                            <Border Background="White" CornerRadius="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                        <TextBlock Text="👨 ذكور:" FontWeight="SemiBold" Width="80"/>
                                        <TextBlock x:Name="MalePatientsText" Text="0" FontWeight="Bold" Foreground="#3498DB"/>
                                        <TextBlock x:Name="MalePercentageText" Text="(0%)" Margin="5,0,0,0" Foreground="#7F8C8D"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="👩 إناث:" FontWeight="SemiBold" Width="80"/>
                                        <TextBlock x:Name="FemalePatientsText" Text="0" FontWeight="Bold" Foreground="#E91E63"/>
                                        <TextBlock x:Name="FemalePercentageText" Text="(0%)" Margin="5,0,0,0" Foreground="#7F8C8D"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- توزيع التصنيفات -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="توزيع التصنيفات" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                            <Border Background="White" CornerRadius="8" Padding="15">
                                <StackPanel x:Name="CategoriesPanel">
                                    <!-- سيتم ملؤها برمجياً -->
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- إحصائيات الأعمار -->
                <GroupBox Header="إحصائيات الأعمار" Margin="0,0,0,20" Padding="15">
                    <UniformGrid Columns="5">
                        <Border Background="#E8F5E8" CornerRadius="8" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="👶 أطفال" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="(0-12 سنة)" FontSize="12" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                <TextBlock x:Name="ChildrenCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#27AE60"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#E8F0FF" CornerRadius="8" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="🧒 مراهقين" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="(13-19 سنة)" FontSize="12" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                <TextBlock x:Name="TeenagersCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#3498DB"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#FFF0E8" CornerRadius="8" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="👨 شباب" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="(20-39 سنة)" FontSize="12" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                <TextBlock x:Name="YoungAdultsCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#F39C12"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#F0E8FF" CornerRadius="8" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="👩 متوسطي العمر" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="(40-59 سنة)" FontSize="12" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                <TextBlock x:Name="MiddleAgedCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#9B59B6"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#FFE8E8" CornerRadius="8" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="👴 كبار السن" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="(60+ سنة)" FontSize="12" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                <TextBlock x:Name="SeniorsCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Foreground="#E74C3C"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                </GroupBox>

                <!-- إحصائيات شهرية -->
                <GroupBox Header="إحصائيات شهرية" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- المرضى الجدد آخر 6 أشهر -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="المرضى الجدد آخر 6 أشهر" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                            <Border Background="White" CornerRadius="8" Padding="15">
                                <StackPanel x:Name="MonthlyNewPatientsPanel">
                                    <!-- سيتم ملؤها برمجياً -->
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- الزيارات الشهرية -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="الزيارات آخر 6 أشهر" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                            <Border Background="White" CornerRadius="8" Padding="15">
                                <StackPanel x:Name="MonthlyVisitsPanel">
                                    <!-- سيتم ملؤها برمجياً -->
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="RefreshButton"
                    Content="🔄 تحديث"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="RefreshButton_Click"/>
            
            <Button x:Name="ExportButton"
                    Content="📤 تصدير"
                    Width="120"
                    Height="40"
                    Background="#27AE60"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="ExportButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="❌ إغلاق"
                    Width="120"
                    Height="40"
                    Background="#E74C3C"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
