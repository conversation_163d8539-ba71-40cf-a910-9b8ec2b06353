using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using AqlanCenterProApp.Models.Dashboard;

namespace AqlanCenterProApp.Views.Controls
{
    /// <summary>
    /// Interaction logic for SmartAlertCard.xaml
    /// </summary>
    public partial class SmartAlertCard : UserControl
    {
        public SmartAlertCard()
        {
            InitializeComponent();
        }

        // خصائص التنبيه الأساسية
        public static readonly DependencyProperty AlertTitleProperty =
            DependencyProperty.Register("AlertTitle", typeof(string), typeof(SmartAlertCard), new PropertyMetadata(string.Empty));

        public string AlertTitle
        {
            get { return (string)GetValue(AlertTitleProperty); }
            set { SetValue(AlertTitleProperty, value); }
        }

        public static readonly DependencyProperty AlertMessageProperty =
            DependencyProperty.Register("AlertMessage", typeof(string), typeof(SmartAlertCard), new PropertyMetadata(string.Empty));

        public string AlertMessage
        {
            get { return (string)GetValue(AlertMessageProperty); }
            set { SetValue(AlertMessageProperty, value); }
        }

        public static readonly DependencyProperty AlertIconProperty =
            DependencyProperty.Register("AlertIcon", typeof(string), typeof(SmartAlertCard), new PropertyMetadata("ℹ"));

        public string AlertIcon
        {
            get { return (string)GetValue(AlertIconProperty); }
            set { SetValue(AlertIconProperty, value); }
        }

        public static readonly DependencyProperty AlertTimeProperty =
            DependencyProperty.Register("AlertTime", typeof(string), typeof(SmartAlertCard), new PropertyMetadata(string.Empty));

        public string AlertTime
        {
            get { return (string)GetValue(AlertTimeProperty); }
            set { SetValue(AlertTimeProperty, value); }
        }

        // خصائص التصميم
        public static readonly DependencyProperty AlertBackgroundProperty =
            DependencyProperty.Register("AlertBackground", typeof(Brush), typeof(SmartAlertCard), 
                new PropertyMetadata(new SolidColorBrush(Colors.White)));

        public Brush AlertBackground
        {
            get { return (Brush)GetValue(AlertBackgroundProperty); }
            set { SetValue(AlertBackgroundProperty, value); }
        }

        public static readonly DependencyProperty AlertBorderColorProperty =
            DependencyProperty.Register("AlertBorderColor", typeof(Brush), typeof(SmartAlertCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(220, 220, 220))));

        public Brush AlertBorderColor
        {
            get { return (Brush)GetValue(AlertBorderColorProperty); }
            set { SetValue(AlertBorderColorProperty, value); }
        }

        public static readonly DependencyProperty IconBackgroundProperty =
            DependencyProperty.Register("IconBackground", typeof(Brush), typeof(SmartAlertCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(52, 152, 219))));

        public Brush IconBackground
        {
            get { return (Brush)GetValue(IconBackgroundProperty); }
            set { SetValue(IconBackgroundProperty, value); }
        }

        public static readonly DependencyProperty PriorityColorProperty =
            DependencyProperty.Register("PriorityColor", typeof(Brush), typeof(SmartAlertCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(52, 152, 219))));

        public Brush PriorityColor
        {
            get { return (Brush)GetValue(PriorityColorProperty); }
            set { SetValue(PriorityColorProperty, value); }
        }

        // خصائص الإجراءات
        public static readonly DependencyProperty ShowActionProperty =
            DependencyProperty.Register("ShowAction", typeof(bool), typeof(SmartAlertCard), new PropertyMetadata(false));

        public bool ShowAction
        {
            get { return (bool)GetValue(ShowActionProperty); }
            set { SetValue(ShowActionProperty, value); }
        }

        public static readonly DependencyProperty ActionTextProperty =
            DependencyProperty.Register("ActionText", typeof(string), typeof(SmartAlertCard), new PropertyMetadata("عرض"));

        public string ActionText
        {
            get { return (string)GetValue(ActionTextProperty); }
            set { SetValue(ActionTextProperty, value); }
        }

        public static readonly DependencyProperty ActionCommandProperty =
            DependencyProperty.Register("ActionCommand", typeof(ICommand), typeof(SmartAlertCard), new PropertyMetadata(null));

        public ICommand ActionCommand
        {
            get { return (ICommand)GetValue(ActionCommandProperty); }
            set { SetValue(ActionCommandProperty, value); }
        }

        public static readonly DependencyProperty CloseCommandProperty =
            DependencyProperty.Register("CloseCommand", typeof(ICommand), typeof(SmartAlertCard), new PropertyMetadata(null));

        public ICommand CloseCommand
        {
            get { return (ICommand)GetValue(CloseCommandProperty); }
            set { SetValue(CloseCommandProperty, value); }
        }

        // طرق مساعدة لتعيين أنماط التنبيهات
        public void SetInfoStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(232, 242, 253));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(52, 152, 219));
            IconBackground = new SolidColorBrush(Color.FromRgb(52, 152, 219));
            PriorityColor = new SolidColorBrush(Color.FromRgb(52, 152, 219));
            AlertIcon = "ℹ";
        }

        public void SetSuccessStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(232, 245, 233));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            IconBackground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            PriorityColor = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            AlertIcon = "✓";
        }

        public void SetWarningStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(255, 243, 224));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            IconBackground = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            PriorityColor = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            AlertIcon = "⚠";
        }

        public void SetErrorStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(255, 235, 238));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            IconBackground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            PriorityColor = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            AlertIcon = "✕";
        }

        public void SetPaymentStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(252, 243, 207));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(243, 156, 18));
            IconBackground = new SolidColorBrush(Color.FromRgb(243, 156, 18));
            PriorityColor = new SolidColorBrush(Color.FromRgb(243, 156, 18));
            AlertIcon = "💰";
        }

        public void SetInventoryStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(235, 245, 251));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(26, 188, 156));
            IconBackground = new SolidColorBrush(Color.FromRgb(26, 188, 156));
            PriorityColor = new SolidColorBrush(Color.FromRgb(26, 188, 156));
            AlertIcon = "📦";
        }

        public void SetAppointmentStyle()
        {
            AlertBackground = new SolidColorBrush(Color.FromRgb(248, 235, 255));
            AlertBorderColor = new SolidColorBrush(Color.FromRgb(155, 89, 182));
            IconBackground = new SolidColorBrush(Color.FromRgb(155, 89, 182));
            PriorityColor = new SolidColorBrush(Color.FromRgb(155, 89, 182));
            AlertIcon = "📅";
        }

        public void SetPriorityStyle(AlertPriority priority)
        {
            switch (priority)
            {
                case AlertPriority.Low:
                    PriorityColor = new SolidColorBrush(Color.FromRgb(149, 165, 166));
                    break;
                case AlertPriority.Normal:
                    PriorityColor = new SolidColorBrush(Color.FromRgb(52, 152, 219));
                    break;
                case AlertPriority.High:
                    PriorityColor = new SolidColorBrush(Color.FromRgb(255, 152, 0));
                    break;
                case AlertPriority.Critical:
                    PriorityColor = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                    break;
            }
        }

        public void SetAlertFromModel(EnhancedSmartAlert alert)
        {
            AlertTitle = alert.Title;
            AlertMessage = alert.Message;
            AlertTime = GetRelativeTime(alert.CreatedAt);
            
            if (!string.IsNullOrEmpty(alert.ActionText))
            {
                ActionText = alert.ActionText;
                ShowAction = alert.IsActionRequired;
            }

            // تعيين النمط حسب النوع
            switch (alert.Type)
            {
                case AlertType.Info:
                    SetInfoStyle();
                    break;
                case AlertType.Success:
                    SetSuccessStyle();
                    break;
                case AlertType.Warning:
                    SetWarningStyle();
                    break;
                case AlertType.Error:
                    SetErrorStyle();
                    break;
                case AlertType.Payment:
                    SetPaymentStyle();
                    break;
                case AlertType.Inventory:
                    SetInventoryStyle();
                    break;
                case AlertType.Appointment:
                    SetAppointmentStyle();
                    break;
            }

            SetPriorityStyle(alert.Priority);

            if (!string.IsNullOrEmpty(alert.Icon))
            {
                AlertIcon = alert.Icon;
            }
        }

        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;
            
            if (timeSpan.TotalMinutes < 1)
                return "الآن";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} د";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} س";
            else
                return $"{(int)timeSpan.TotalDays} ي";
        }
    }
}
