<UserControl x:Class="AqlanCenterProApp.Views.Reports.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <Style x:Key="ReportHeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,0,2"/>
            <Setter Property="Padding" Value="30,20"/>
        </Style>

        <Style x:Key="ReportFooterStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2,0,0,0"/>
            <Setter Property="Padding" Value="30,20"/>
        </Style>

        <Style x:Key="ReportContentStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="30,20"/>
        </Style>

        <Style x:Key="CenterNameStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C4A7A"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ReportTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="ReportDateStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="ContactInfoStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#CCCCCC" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatValueStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="StatLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI, Cairo, Arial"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header - اسم المركز والشعار -->
            <Border Grid.Row="0" Style="{StaticResource ReportHeaderStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- الشعار -->
                    <Image Grid.Column="0" 
                           Source="/Resources/logo.png" 
                           Width="80" 
                           Height="80" 
                           Stretch="Uniform"
                           Margin="0,0,20,0"/>

                    <!-- اسم المركز -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="مركز الدكتور عقلان الكامل" 
                                 Style="{StaticResource CenterNameStyle}"/>
                        <TextBlock Text="طب الأسنان وتقويم الأسنان" 
                                 FontSize="16" 
                                 Foreground="#666666" 
                                 TextAlignment="Center"
                                 Margin="0,5,0,0"/>
                    </StackPanel>

                    <!-- معلومات إضافية -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">
                        <TextBlock Text="📞 0123456789" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                        <TextBlock Text="📧 <EMAIL>" 
                                 FontSize="12" 
                                 Foreground="#666666"
                                 Margin="0,2,0,0"/>
                        <TextBlock Text="📍 الرياض، المملكة العربية السعودية" 
                                 FontSize="12" 
                                 Foreground="#666666"
                                 Margin="0,2,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content - بيانات التقرير -->
            <Border Grid.Row="1" Style="{StaticResource ReportContentStyle}">
                <StackPanel>
                    <!-- عنوان التقرير -->
                    <TextBox Text="{Binding ReportTitle, UpdateSourceTrigger=PropertyChanged}" 
                             Style="{StaticResource ModernTextBoxStyle}"
                             FontSize="22"
                             FontWeight="Bold"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,10"/>
                    <!-- تاريخ التقرير -->
                    <DatePicker SelectedDate="{Binding ReportDateValue, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Margin="0,0,0,20"
                                HorizontalAlignment="Center"
                                Width="200"/>
                    <!-- تفاصيل التقرير -->
                    <TextBlock Text="📋 تفاصيل التقرير" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             Margin="0,30,0,15"/>
                    <TextBox Text="{Binding ReportDetails, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBoxStyle}"
                             AcceptsReturn="True"
                             Height="80"
                             TextWrapping="Wrap"
                             VerticalScrollBarVisibility="Auto"/>
                    <!-- إحصائيات سريعة -->
                    <TextBlock Text="📊 الإحصائيات العامة" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             Margin="0,20,0,15"/>
                    <ItemsControl ItemsSource="{Binding QuickStats}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource StatCardStyle}" 
                                        Width="200" Height="120">
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged}" 
                                                 FontSize="24" 
                                                 HorizontalAlignment="Center"/>
                                        <TextBox Text="{Binding Label, UpdateSourceTrigger=PropertyChanged}" 
                                                 FontSize="14" 
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    <!-- جدول البيانات -->
                    <TextBlock Text="📊 البيانات التفصيلية" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             Margin="0,20,0,15"/>
                    <DataGrid ItemsSource="{Binding ReportData}" 
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              BorderBrush="#E0E0E0"
                              BorderThickness="1"
                              Margin="0,0,0,20">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="البيان" 
                                                Binding="{Binding Label, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                Width="*"/>
                            <DataGridTextColumn Header="القيمة" 
                                                Binding="{Binding Value, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="النسبة" 
                                                Binding="{Binding Percentage, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                                Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    <!-- ملاحظات -->
                    <TextBlock Text="📝 ملاحظات" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             Margin="0,20,0,15"/>
                    <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBoxStyle}"
                             AcceptsReturn="True"
                             Height="60"
                             TextWrapping="Wrap"
                             VerticalScrollBarVisibility="Auto"/>
                    <!-- أزرار الحفظ وإعادة التعيين -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button Content="💾 حفظ التعديلات" 
                                Style="{StaticResource SuccessButtonStyle}"
                                Command="{Binding SaveCommand}"/>
                        <Button Content="↺ إعادة تعيين" 
                                Style="{StaticResource ModernButtonStyle}"
                                Command="{Binding ResetCommand}"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Footer - العنوان وأرقام التواصل -->
            <Border Grid.Row="2" Style="{StaticResource ReportFooterStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- العنوان -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Right">
                        <TextBlock Text="📍 العنوان:" 
                                 FontWeight="Bold" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                        <TextBlock Text="شارع الملك فهد، حي النزهة" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                        <TextBlock Text="الرياض، المملكة العربية السعودية" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                    </StackPanel>

                    <!-- أزرار التصدير والطباعة -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Button Content="📤 تصدير PDF" 
                                Style="{StaticResource ModernButtonStyle}"
                                Command="{Binding ExportCommand}"/>
                        <Button Content="🖨️ طباعة" 
                                Style="{StaticResource WarningButtonStyle}"
                                Command="{Binding PrintCommand}"/>
                    </StackPanel>

                    <!-- ساعات العمل -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Left">
                        <TextBlock Text="🕒 ساعات العمل:" 
                                 FontWeight="Bold" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                        <TextBlock Text="الأحد - الخميس: 8:00 ص - 8:00 م" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                        <TextBlock Text="الجمعة: 2:00 م - 8:00 م" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                        <TextBlock Text="السبت: 9:00 ص - 6:00 م" 
                                 Style="{StaticResource ContactInfoStyle}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl> 