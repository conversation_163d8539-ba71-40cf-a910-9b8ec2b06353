using System.Threading.Tasks;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IDashboardDataService
    {
        Task<int> GetTotalPatientsAsync();
        Task<int> GetTotalAppointmentsAsync();
        Task<decimal> GetTotalRevenueAsync();
        Task<int> GetPendingLabOrdersAsync();
        Task<int> GetNewPatientsThisMonthAsync();
        Task<decimal> GetTodayRevenueAsync();
        Task<int> GetLowStockItemsAsync();
        Task<int> GetTotalDoctorsAsync();
    }
}