<Window x:Class="AqlanCenterProApp.Views.Patients.AdvancedSearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="البحث المتقدم في المرضى"
        Width="800"
        Height="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F6FA">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="البحث المتقدم في المرضى"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#3498DB"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- معايير البحث -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- البحث الأساسي -->
                <GroupBox Header="البحث الأساسي" Margin="0,0,0,15" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الاسم -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="الاسم الكامل:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="NameTextBox" Height="35" FontSize="14"/>
                        </StackPanel>

                        <!-- رقم الملف -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="رقم الملف:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="FileNumberTextBox" Height="35" FontSize="14"/>
                        </StackPanel>

                        <!-- رقم الهاتف -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="رقم الهاتف:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="PhoneTextBox" Height="35" FontSize="14"/>
                        </StackPanel>

                        <!-- العمر -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="العمر:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBox x:Name="AgeFromTextBox" Width="80" Height="35" FontSize="14"/>
                                <TextBlock Text="إلى" VerticalAlignment="Center" Margin="10,0"/>
                                <TextBox x:Name="AgeToTextBox" Width="80" Height="35" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- التصنيفات -->
                <GroupBox Header="التصنيفات والحالة" Margin="0,0,0,15" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الجنس -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="الجنس:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="GenderComboBox" Height="35" FontSize="14">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="ذكر"/>
                                <ComboBoxItem Content="أنثى"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- التصنيف -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="التصنيف:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CategoryComboBox" Height="35" FontSize="14">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="عادي"/>
                                <ComboBoxItem Content="VIP"/>
                                <ComboBoxItem Content="موظف"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- حالة الملف -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="حالة الملف:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="FileStatusComboBox" Height="35" FontSize="14">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="نشط"/>
                                <ComboBoxItem Content="غير نشط"/>
                                <ComboBoxItem Content="مؤرشف"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- حالة الدين -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="حالة الدين:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="DebtStatusComboBox" Height="35" FontSize="14">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="مدين"/>
                                <ComboBoxItem Content="غير مدين"/>
                                <ComboBoxItem Content="رصيد إيجابي"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- التواريخ -->
                <GroupBox Header="التواريخ" Margin="0,0,0,15" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تاريخ التسجيل -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="تاريخ التسجيل من:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="RegistrationFromDatePicker" Height="35" FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="إلى:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="RegistrationToDatePicker" Height="35" FontSize="14"/>
                        </StackPanel>

                        <!-- آخر زيارة -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="آخر زيارة من:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="LastVisitFromDatePicker" Height="35" FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,10">
                            <TextBlock Text="إلى:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="LastVisitToDatePicker" Height="35" FontSize="14"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- المبالغ -->
                <GroupBox Header="المبالغ المالية" Margin="0,0,0,15" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الرصيد -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="الرصيد:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBox x:Name="BalanceFromTextBox" Width="100" Height="35" FontSize="14"/>
                                <TextBlock Text="إلى" VerticalAlignment="Center" Margin="10,0"/>
                                <TextBox x:Name="BalanceToTextBox" Width="100" Height="35" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- إجمالي المدفوعات -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="إجمالي المدفوعات:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBox x:Name="TotalPaymentsFromTextBox" Width="100" Height="35" FontSize="14"/>
                                <TextBlock Text="إلى" VerticalAlignment="Center" Margin="10,0"/>
                                <TextBox x:Name="TotalPaymentsToTextBox" Width="100" Height="35" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SearchButton"
                    Content="🔍 بحث"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="SearchButton_Click"/>
            
            <Button x:Name="ClearButton"
                    Content="🗑️ مسح"
                    Width="120"
                    Height="40"
                    Background="#95A5A6"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="ClearButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="❌ إغلاق"
                    Width="120"
                    Height="40"
                    Background="#E74C3C"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
