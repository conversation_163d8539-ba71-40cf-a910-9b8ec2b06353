using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class InvoiceItem : BaseEntity
    {
        [Required]
        public int InvoiceId { get; set; }
        
        public int? InventoryItemId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string ItemDescription { get; set; } = string.Empty;
        
        public int Quantity { get; set; } = 1;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; } = 0;
        
        [StringLength(500)]
        public string? ItemNotes { get; set; }
        
        // Navigation Properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;
        
        [ForeignKey("InventoryItemId")]
        public virtual InventoryItem? InventoryItem { get; set; }
    }
}
