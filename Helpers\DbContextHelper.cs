using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.Services;

namespace AqlanCenterProApp.Helpers
{
    /// <summary>
    /// مساعد لإنشاء DbContext والخدمات للنوافذ المستقلة
    /// يضمن استخدام قاعدة البيانات الموحدة في جميع أنحاء النظام
    /// </summary>
    public static class DbContextHelper
    {
        /// <summary>
        /// إنشاء DbContext جديد باستخدام قاعدة البيانات الموحدة
        /// </summary>
        /// <returns>DbContext جديد</returns>
        public static AqlanCenterDbContext CreateDbContext()
        {
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            var connectionString = DatabasePathHelper.GetConnectionString();
            optionsBuilder.UseSqlite(connectionString);

            return new AqlanCenterDbContext(optionsBuilder.Options);
        }

        /// <summary>
        /// إنشاء خدمة المرضى مع DbContext موحد
        /// </summary>
        /// <returns>خدمة المرضى</returns>
        public static IPatientService CreatePatientService()
        {
            var context = CreateDbContext();
            return new PatientService(context);
        }

        /// <summary>
        /// إنشاء خدمة المواعيد مع DbContext موحد
        /// </summary>
        /// <returns>خدمة المواعيد</returns>
        public static IAppointmentService CreateAppointmentService()
        {
            var context = CreateDbContext();
            return new AppointmentService(context);
        }

        /// <summary>
        /// إنشاء خدمة الأطباء مع DbContext موحد
        /// </summary>
        /// <returns>خدمة الأطباء</returns>
        public static IDoctorService CreateDoctorService()
        {
            var context = CreateDbContext();
            return new DoctorService(context);
        }

        /// <summary>
        /// إنشاء خدمة الإعدادات مع DbContext موحد
        /// </summary>
        /// <returns>خدمة الإعدادات</returns>
        public static ISettingsService CreateSettingsService()
        {
            var context = CreateDbContext();
            return new SettingsService(context, null!); // Logger يمكن أن يكون null للاستخدام المؤقت
        }

        /// <summary>
        /// إنشاء خدمة النسخ الاحتياطي مع DbContext موحد
        /// </summary>
        /// <returns>خدمة النسخ الاحتياطي</returns>
        public static IBackupService CreateBackupService()
        {
            var context = CreateDbContext();
            // إنشاء خدمات مساعدة مؤقتة
            var activityLogService = CreateActivityLogService();
            var userService = CreateUserService();
            return new BackupService(context, null!, activityLogService, userService);
        }

        /// <summary>
        /// إنشاء خدمة التقارير مع DbContext موحد
        /// </summary>
        /// <returns>خدمة التقارير</returns>
        public static IReportService CreateReportService()
        {
            var context = CreateDbContext();
            return new ReportService(context);
        }

        /// <summary>
        /// إنشاء خدمة المستخدمين مع DbContext موحد
        /// </summary>
        /// <returns>خدمة المستخدمين</returns>
        public static IUserService CreateUserService()
        {
            var context = CreateDbContext();
            return new UserService(context);
        }

        /// <summary>
        /// إنشاء خدمة سجل الأنشطة مع DbContext موحد
        /// </summary>
        /// <returns>خدمة سجل الأنشطة</returns>
        public static IActivityLogService CreateActivityLogService()
        {
            var context = CreateDbContext();
            return new ActivityLogService(context);
        }

        /// <summary>
        /// إنشاء خدمة الأدوار مع DbContext موحد
        /// </summary>
        /// <returns>خدمة الأدوار</returns>
        public static IRoleService CreateRoleService()
        {
            var context = CreateDbContext();
            return new RoleService(context);
        }

        /// <summary>
        /// إنشاء خدمة المشتريات مع DbContext موحد
        /// </summary>
        /// <returns>خدمة المشتريات</returns>
        public static IPurchaseService CreatePurchaseService()
        {
            var context = CreateDbContext();
            var inventoryItemService = CreateInventoryItemService();
            return new PurchaseService(context, inventoryItemService);
        }

        /// <summary>
        /// إنشاء خدمة المخزون مع DbContext موحد
        /// </summary>
        /// <returns>خدمة المخزون</returns>
        public static IInventoryItemService CreateInventoryItemService()
        {
            var context = CreateDbContext();
            return new InventoryItemService(context);
        }

        /// <summary>
        /// إنشاء خدمة الموردين مع DbContext موحد
        /// </summary>
        /// <returns>خدمة الموردين</returns>
        public static ISupplierService CreateSupplierService()
        {
            var context = CreateDbContext();
            return new SupplierService(context);
        }

        /// <summary>
        /// إنشاء خدمة طلبات المختبر مع DbContext موحد
        /// </summary>
        /// <returns>خدمة طلبات المختبر</returns>
        public static ILabOrderService CreateLabOrderService()
        {
            var context = CreateDbContext();
            return new LabOrderService(context);
        }

        /// <summary>
        /// إنشاء خدمة خطط التقويم مع DbContext موحد
        /// </summary>
        /// <returns>خدمة خطط التقويم</returns>
        public static IOrthodonticPlanService CreateOrthodonticPlanService()
        {
            var appointmentService = CreateAppointmentService();
            var context = CreateDbContext();
            return new OrthodonticPlanService(context, appointmentService);
        }

        /// <summary>
        /// إنشاء خدمة لوحة التحكم مع DbContext موحد
        /// </summary>
        /// <returns>خدمة لوحة التحكم</returns>
        public static IDashboardService CreateDashboardService()
        {
            var context = CreateDbContext();
            return new DashboardService(context, null!); // Logger يمكن أن يكون null للاستخدام المؤقت
        }

        /// <summary>
        /// التحقق من صحة اتصال قاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال صحيحاً</returns>
        public static async Task<bool> TestDatabaseConnectionAsync()
        {
            try
            {
                using var context = CreateDbContext();
                return await context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تسجيل معلومات قاعدة البيانات
        /// </summary>
        public static void LogDatabaseInfo()
        {
            DatabasePathHelper.LogDatabaseInfo();
        }

        /// <summary>
        /// التأكد من إنشاء قاعدة البيانات
        /// </summary>
        /// <returns>true إذا تم إنشاء قاعدة البيانات بنجاح</returns>
        public static async Task<bool> EnsureDatabaseCreatedAsync()
        {
            try
            {
                using var context = CreateDbContext();
                return await context.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }
    }
}
