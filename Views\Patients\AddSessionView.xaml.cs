using System.Windows;
using AqlanCenterProApp.ViewModels.Patients;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.Views.Patients
{
    /// <summary>
    /// Interaction logic for AddSessionView.xaml
    /// </summary>
    public partial class AddSessionView : Window
    {
        public AddSessionView(AddSessionViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // ربط الأوامر
            viewModel.SaveSessionCommand = new RelayCommand(() => SaveSession());
            viewModel.CancelCommand = new RelayCommand(() => Cancel());
        }

        private void SaveSession()
        {
            var viewModel = DataContext as AddSessionViewModel;
            if (viewModel != null)
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(viewModel.TreatmentType))
                {
                    MessageBox.Show("يرجى إدخال نوع العلاج", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (viewModel.Amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                DialogResult = true;
                Close();
            }
        }

        private void Cancel()
        {
            DialogResult = false;
            Close();
        }
    }
} 