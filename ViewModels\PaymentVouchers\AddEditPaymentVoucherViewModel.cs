using System.Collections.ObjectModel;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.PaymentVouchers
{
    public class AddEditPaymentVoucherViewModel : BaseViewModel
    {
        private readonly IPaymentVoucherService _voucherService;
        private readonly IEmployeeService _employeeService;
        // يمكن إضافة ISupplierService و ILabService إذا كانت متوفرة
        private PaymentVoucher _voucher;
        private bool _isEditMode;
        private List<Employee> _employees;
        private bool _isBusy;
        // يمكن إضافة قوائم الموردين والمعامل لاحقًا

        public AddEditPaymentVoucherViewModel(IPaymentVoucherService voucherService, IEmployeeService employeeService, PaymentVoucher? voucher = null)
        {
            _voucherService = voucherService;
            _employeeService = employeeService;
            _employees = new List<Employee>();

            if (voucher != null)
            {
                _voucher = voucher;
                _isEditMode = true;
            }
            else
            {
                _voucher = new PaymentVoucher
                {
                    VoucherDate = DateTime.Now,
                    Status = "مكتمل",
                    Amount = 0,
                    PaymentMethod = "نقداً",
                    BeneficiaryType = "أخرى"
                };
                _isEditMode = false;
            }

            LoadEmployeesCommand = new RelayCommand(async () => await LoadEmployeesAsync());
            SaveCommand = new RelayCommand(async () => await SaveAsync());
            CancelCommand = new RelayCommand(() => Cancel());
        }

        public void OnLoaded()
        {
            // تحميل البيانات في الخلفية
            _ = Task.Run(async () =>
            {
                await LoadEmployeesAsync();
            });
        }

        #region Properties
        public PaymentVoucher Voucher
        {
            get => _voucher;
            set => SetProperty(ref _voucher, value);
        }
        public List<Employee> Employees
        {
            get => _employees;
            set => SetProperty(ref _employees, value);
        }
        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }
        public string WindowTitle => IsEditMode ? "تعديل سند صرف" : "إصدار سند صرف جديد";
        public string SaveButtonText => IsEditMode ? "تحديث" : "حفظ";
        public List<string> PaymentMethods { get; } = new List<string>
        {
            "نقداً",
            "بطاقة ائتمان",
            "تحويل بنكي",
            "شيك",
            "آجل"
        };
        public List<string> BeneficiaryTypes { get; } = new List<string>
        {
            "موظف",
            "مورد",
            "معمل",
            "أخرى"
        };
        public bool IsBusy
        {
            get => _isBusy;
            set { _isBusy = value; OnPropertyChanged(); }
        }
        #endregion

        #region Commands
        public ICommand LoadEmployeesCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        #endregion

        #region Methods
        private async Task LoadEmployeesAsync()
        {
            try
            {
                IsBusy = true;
                var employees = await _employeeService.GetAllEmployeesAsync();
                Employees = employees.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموظفين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }
        private async Task SaveAsync()
        {
            try
            {
                if (!await ValidateDataAsync())
                    return;
                IsBusy = true;
                if (!IsEditMode && string.IsNullOrEmpty(Voucher.VoucherNumber))
                {
                    Voucher.VoucherNumber = await _voucherService.GetNextVoucherNumberAsync();
                }
                if (IsEditMode)
                {
                    Voucher = await _voucherService.UpdateVoucherAsync(Voucher);
                }
                else
                {
                    Voucher = await _voucherService.CreateVoucherAsync(Voucher);
                }
                MessageBox.Show(
                    IsEditMode ? "تم تحديث السند بنجاح" : "تم إصدار السند بنجاح",
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
                CloseWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }
        private void Cancel()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع البيانات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                CloseWindow();
            }
        }
        private Task<bool> ValidateDataAsync()
        {
            if (string.IsNullOrWhiteSpace(Voucher.BeneficiaryName))
            {
                MessageBox.Show("يرجى إدخال اسم المستفيد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Voucher.VoucherDate == default)
            {
                MessageBox.Show("يرجى تحديد تاريخ السند", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Voucher.Amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (string.IsNullOrWhiteSpace(Voucher.ExpenseType))
            {
                MessageBox.Show("يرجى إدخال نوع المصروف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (string.IsNullOrWhiteSpace(Voucher.Description))
            {
                MessageBox.Show("يرجى إدخال وصف المصروف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (string.IsNullOrWhiteSpace(Voucher.PaymentMethod))
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            return Task.FromResult(true);
        }
        private void CloseWindow()
        {
            if (System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().Any(w => w.IsActive))
            {
                System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().First(w => w.IsActive).Close();
            }
        }
        #endregion
    }
} 