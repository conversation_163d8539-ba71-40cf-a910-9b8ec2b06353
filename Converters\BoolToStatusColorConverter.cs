using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace AqlanCenterProApp.Converters
{
    /// <summary>
    /// محول لتحويل القيمة المنطقية إلى لون الحالة
    /// </summary>
    public class BoolToStatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isActive)
            {
                return isActive ? new SolidColorBrush(Color.FromRgb(76, 175, 80)) : // أخضر للنشط
                                new SolidColorBrush(Color.FromRgb(244, 67, 54));      // أحمر لغير النشط
            }
            return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // رمادي للقيم غير المعروفة
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
