using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class User : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [Required]
        public int RoleId { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime LastLoginDate { get; set; }
        
        public DateTime? LastPasswordChangeDate { get; set; }
        
        public int FailedLoginAttempts { get; set; } = 0;
        
        public DateTime? LockoutEndDate { get; set; }
        
        public string? UserImage { get; set; }
        
        [StringLength(10)]
        public string Language { get; set; } = "ar";
        
        [StringLength(20)]
        public string Theme { get; set; } = "Light";
        
        /// <summary>
        /// هل للمستخدم صلاحية إلغاء إيصالات القبض؟
        /// </summary>
        [NotMapped]
        public bool CanCancelReceipts => Role != null && Role.CanCancelReceipts;
        
        // Navigation Properties
        public virtual Role Role { get; set; } = null!;
        public virtual ICollection<ActivityLog> ActivityLogs { get; set; } = new List<ActivityLog>();
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

        /// <summary>
        /// التحقق من صلاحية معينة للمستخدم
        /// </summary>
        /// <param name="permissionAction">اسم الصلاحية</param>
        /// <returns>true إذا كان لديه الصلاحية</returns>
        public bool HasPermission(string permissionAction)
        {
            if (UserPermissions == null || !UserPermissions.Any())
                return false;

            return UserPermissions.Any(up => up.IsGranted && 
                                           up.Permission != null && 
                                           up.Permission.Action == permissionAction && 
                                           up.Permission.IsActive);
        }
    }
}
