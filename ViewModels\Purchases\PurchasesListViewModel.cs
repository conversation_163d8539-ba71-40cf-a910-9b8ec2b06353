using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Views.Purchases;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.Purchases
{
    public class PurchasesListViewModel : BaseViewModel
    {
        private readonly IPurchaseService _purchaseService;
        private readonly ISupplierService _supplierService;
        private ObservableCollection<Purchase> _purchases;
        private Purchase? _selectedPurchase;
        private string _searchTerm = string.Empty;
        private string _selectedStatus = "الكل";
        private DateTime _startDate = DateTime.Now.AddMonths(-1);
        private DateTime _endDate = DateTime.Now;

        public PurchasesListViewModel(IPurchaseService purchaseService, ISupplierService supplierService)
        {
            _purchaseService = purchaseService;
            _supplierService = supplierService;
            _purchases = new ObservableCollection<Purchase>();

            LoadPurchasesCommand = new RelayCommand(async () => await LoadPurchasesAsync());
            AddPurchaseCommand = new RelayCommand(() => AddPurchase());
            EditPurchaseCommand = new RelayCommand(() => EditPurchase(), () => SelectedPurchase != null);
            DeletePurchaseCommand = new RelayCommand(async () => await DeletePurchaseAsync(), () => SelectedPurchase != null);
            ReceivePurchaseCommand = new RelayCommand(async () => await ReceivePurchaseAsync(), () => CanReceive);
            SearchCommand = new RelayCommand(async () => await SearchPurchasesAsync());
            ExportCommand = new RelayCommand(async () => await ExportPurchasesAsync());
            PrintCommand = new RelayCommand(async () => await PrintPurchasesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadPurchasesAsync());

            // تحميل البيانات عند الإنشاء
            _ = LoadPurchasesAsync();
        }

        public ObservableCollection<Purchase> Purchases
        {
            get => _purchases;
            set => SetProperty(ref _purchases, value);
        }

        public Purchase? SelectedPurchase
        {
            get => _selectedPurchase;
            set
            {
                SetProperty(ref _selectedPurchase, value);
                OnPropertyChanged(nameof(CanEdit));
                OnPropertyChanged(nameof(CanDelete));
                OnPropertyChanged(nameof(CanReceive));
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                SetProperty(ref _searchTerm, value);
                _ = SearchPurchasesAsync();
            }
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                SetProperty(ref _selectedStatus, value);
                _ = LoadPurchasesAsync();
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                SetProperty(ref _startDate, value);
                _ = LoadPurchasesAsync();
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                SetProperty(ref _endDate, value);
                _ = LoadPurchasesAsync();
            }
        }

        public bool CanEdit => SelectedPurchase != null && SelectedPurchase.PurchaseStatus == "Pending";
        public bool CanDelete => SelectedPurchase != null && SelectedPurchase.PurchaseStatus == "Pending";
        public bool CanReceive => SelectedPurchase != null && SelectedPurchase.PurchaseStatus == "Pending";

        public ObservableCollection<string> Statuses { get; } = new ObservableCollection<string>
        {
            "الكل",
            "Pending",
            "Received",
            "Cancelled"
        };

        // Commands
        public ICommand LoadPurchasesCommand { get; }
        public ICommand AddPurchaseCommand { get; }
        public ICommand EditPurchaseCommand { get; }
        public ICommand DeletePurchaseCommand { get; }
        public ICommand ReceivePurchaseCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand RefreshCommand { get; }

        public async Task LoadPurchasesAsync()
        {
            try
            {
                IsBusy = true;
                IEnumerable<Purchase> purchases;

                if (SelectedStatus == "Pending")
                {
                    purchases = await _purchaseService.GetPendingPurchasesAsync();
                }
                else if (SelectedStatus == "Received")
                {
                    purchases = await _purchaseService.GetReceivedPurchasesAsync();
                }
                else
                {
                    purchases = await _purchaseService.GetPurchasesByDateRangeAsync(StartDate, EndDate);
                }

                Purchases.Clear();
                foreach (var purchase in purchases)
                {
                    Purchases.Add(purchase);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تحميل المشتريات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddPurchase()
        {
            var vm = new AddEditPurchaseViewModel(_purchaseService, _supplierService);
            var dialog = new AddEditPurchaseView { DataContext = vm, Owner = Application.Current.MainWindow };
            vm.SaveCompleted += async () =>
            {
                dialog.DialogResult = true;
                dialog.Close();
                await LoadPurchasesAsync();
            };
            vm.CancelRequested += () =>
            {
                dialog.DialogResult = false;
                dialog.Close();
            };
            dialog.ShowDialog();
        }

        private void EditPurchase()
        {
            if (SelectedPurchase == null) return;
            var vm = new AddEditPurchaseViewModel(_purchaseService, _supplierService, SelectedPurchase);
            var dialog = new AddEditPurchaseView { DataContext = vm, Owner = Application.Current.MainWindow };
            vm.SaveCompleted += async () =>
            {
                dialog.DialogResult = true;
                dialog.Close();
                await LoadPurchasesAsync();
            };
            vm.CancelRequested += () =>
            {
                dialog.DialogResult = false;
                dialog.Close();
            };
            dialog.ShowDialog();
        }

        private async Task DeletePurchaseAsync()
        {
            if (SelectedPurchase == null) return;

            var result = MessageHelper.ShowConfirmation($"هل أنت متأكد من حذف المشتريات '{SelectedPurchase.InvoiceNumber}'؟");
            if (!result) return;

            try
            {
                IsBusy = true;
                var success = await _purchaseService.DeletePurchaseAsync(SelectedPurchase.Id);
                if (success)
                {
                    Purchases.Remove(SelectedPurchase);
                    MessageHelper.ShowSuccess("تم حذف المشتريات بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حذف المشتريات");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في حذف المشتريات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ReceivePurchaseAsync()
        {
            if (SelectedPurchase == null) return;

            var result = MessageHelper.ShowConfirmation($"هل أنت متأكد من استلام المشتريات '{SelectedPurchase.InvoiceNumber}'؟");
            if (!result) return;

            try
            {
                IsBusy = true;
                var success = await _purchaseService.ReceivePurchaseAsync(SelectedPurchase.Id, "المستخدم الحالي");
                if (success)
                {
                    await LoadPurchasesAsync(); // إعادة تحميل لتحديث الحالة
                    MessageHelper.ShowSuccess("تم استلام المشتريات بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في استلام المشتريات");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في استلام المشتريات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchPurchasesAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadPurchasesAsync();
                return;
            }

            try
            {
                IsBusy = true;
                var purchases = await _purchaseService.SearchPurchasesAsync(SearchTerm);
                
                Purchases.Clear();
                foreach (var purchase in purchases)
                {
                    Purchases.Add(purchase);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExportPurchasesAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                await Task.Delay(100);
                MessageHelper.ShowSuccess("تم تصدير بيانات المشتريات بنجاح");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task PrintPurchasesAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                await Task.Delay(100);
                MessageHelper.ShowSuccess("تم إرسال بيانات المشتريات للطباعة");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في الطباعة: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
} 