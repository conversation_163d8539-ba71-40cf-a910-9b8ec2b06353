using Microsoft.EntityFrameworkCore;
using Microsoft.Data.Sqlite;
using System.Windows;
using System.IO;

namespace AqlanCenterProApp.Helpers
{
    public static class DatabaseErrorHandler
    {
        /// <summary>
        /// معالجة أخطاء قاعدة البيانات وإرجاع رسالة مفهومة
        /// </summary>
        public static string HandleDatabaseError(Exception ex)
        {
            string errorMessage = "حدث خطأ غير متوقع في قاعدة البيانات";

            try
            {
                switch (ex)
                {
                    case DbUpdateException dbEx:
                        errorMessage = HandleDbUpdateException(dbEx);
                        break;

                    case SqliteException sqliteEx:
                        errorMessage = HandleSqliteException(sqliteEx);
                        break;

                    case InvalidOperationException invalidEx when invalidEx.Message.Contains("database"):
                        errorMessage = "خطأ في عملية قاعدة البيانات: " + invalidEx.Message;
                        break;

                    case UnauthorizedAccessException:
                        errorMessage = "ليس لديك صلاحية للوصول إلى قاعدة البيانات";
                        break;

                    case DirectoryNotFoundException:
                        errorMessage = "مجلد قاعدة البيانات غير موجود";
                        break;

                    case FileNotFoundException:
                        errorMessage = "ملف قاعدة البيانات غير موجود";
                        break;

                    default:
                        errorMessage = $"خطأ في قاعدة البيانات: {ex.Message}";
                        break;
                }
            }
            catch
            {
                errorMessage = "حدث خطأ في معالجة خطأ قاعدة البيانات";
            }

            return errorMessage;
        }

        private static string HandleDbUpdateException(DbUpdateException dbEx)
        {
            if (dbEx.InnerException is SqliteException sqliteEx)
            {
                return HandleSqliteException(sqliteEx);
            }

            return dbEx.InnerException?.Message ?? "خطأ في تحديث قاعدة البيانات";
        }

        private static string HandleSqliteException(SqliteException sqliteEx)
        {
            return sqliteEx.SqliteErrorCode switch
            {
                19 => "انتهاك قيود قاعدة البيانات - تأكد من صحة البيانات المدخلة", // SQLITE_CONSTRAINT
                6 => "قاعدة البيانات مقفلة - حاول مرة أخرى", // SQLITE_LOCKED
                5 => "قاعدة البيانات مشغولة - حاول مرة أخرى", // SQLITE_BUSY
                8 => "قاعدة البيانات للقراءة فقط", // SQLITE_READONLY
                10 => "خطأ في قراءة/كتابة قاعدة البيانات", // SQLITE_IOERR
                11 => "ملف قاعدة البيانات تالف", // SQLITE_CORRUPT
                13 => "قاعدة البيانات ممتلئة", // SQLITE_FULL
                14 => "لا يمكن فتح قاعدة البيانات", // SQLITE_CANTOPEN
                15 => "خطأ في بروتوكول قاعدة البيانات", // SQLITE_PROTOCOL
                17 => "خطأ في هيكل قاعدة البيانات", // SQLITE_SCHEMA
                18 => "البيانات كبيرة جداً", // SQLITE_TOOBIG
                _ => $"خطأ SQLite ({sqliteEx.SqliteErrorCode}): {sqliteEx.Message}"
            };
        }

        /// <summary>
        /// عرض رسالة خطأ قاعدة البيانات للمستخدم
        /// </summary>
        public static void ShowDatabaseError(Exception ex, string operation = "العملية")
        {
            var errorMessage = HandleDatabaseError(ex);
            var fullMessage = $"فشل في {operation}\n\n{errorMessage}";

            MessageBox.Show(fullMessage, "خطأ في قاعدة البيانات",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// محاولة إصلاح مشاكل قاعدة البيانات الشائعة
        /// </summary>
        public static async Task<bool> TryFixDatabaseIssuesAsync()
        {
            try
            {
                // التحقق من وجود مجلد قاعدة البيانات
                var dbPath = DatabasePathHelper.GetDatabasePath();
                var dbDirectory = Path.GetDirectoryName(dbPath);

                if (!Directory.Exists(dbDirectory))
                {
                    Directory.CreateDirectory(dbDirectory!);
                }

                // التحقق من صلاحيات الكتابة
                var testFile = Path.Combine(dbDirectory!, "test_write.tmp");
                try
                {
                    await File.WriteAllTextAsync(testFile, "test");
                    File.Delete(testFile);
                }
                catch
                {
                    throw new UnauthorizedAccessException("ليس لديك صلاحية للكتابة في مجلد قاعدة البيانات");
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowDatabaseError(ex, "إصلاح مشاكل قاعدة البيانات");
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات قبل العمليات الحساسة
        /// </summary>
        public static async Task<bool> CreateBackupAsync()
        {
            try
            {
                var dbPath = DatabasePathHelper.GetDatabasePath();

                if (!File.Exists(dbPath))
                    return true; // لا توجد قاعدة بيانات للنسخ الاحتياطي

                var backupPath = DatabasePathHelper.CreateBackupPath("ErrorHandler");
                await Task.Run(() => File.Copy(dbPath, backupPath));

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة اتصال قاعدة البيانات
        /// </summary>
        public static async Task<(bool IsValid, string Message)> ValidateDatabaseConnectionAsync()
        {
            try
            {
                var dbPath = DatabasePathHelper.GetDatabasePath();
                var connectionString = DatabasePathHelper.GetConnectionString();

                var optionsBuilder = new DbContextOptionsBuilder<Data.AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new Data.AqlanCenterDbContext(optionsBuilder.Options);

                // محاولة الاتصال
                var canConnect = await context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    return (false, "لا يمكن الاتصال بقاعدة البيانات");
                }

                // التحقق من وجود الجداول الأساسية
                var hasUsers = await context.Users.AnyAsync();

                return (true, "اتصال قاعدة البيانات صحيح");
            }
            catch (Exception ex)
            {
                return (false, HandleDatabaseError(ex));
            }
        }
    }
}
