using System.Collections.ObjectModel;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.Receipts
{
    public class AddEditReceiptViewModel : BaseViewModel
    {
        private readonly IReceiptService _receiptService;
        private readonly IPatientService _patientService;
        private Receipt _receipt;
        private Patient? _selectedPatient;
        private List<Patient> _patients;
        private bool _isEditMode;
        private bool _isBusy;

        public AddEditReceiptViewModel(IReceiptService receiptService, IPatientService patientService, Receipt? receipt = null)
        {
            _receiptService = receiptService;
            _patientService = patientService;
            _patients = new List<Patient>();

            if (receipt != null)
            {
                _receipt = receipt;
                _isEditMode = true;
            }
            else
            {
                _receipt = new Receipt
                {
                    ReceiptDate = DateTime.Now,
                    Status = "مكتمل",
                    Amount = 0,
                    PaymentMethod = "نقداً"
                };
                _isEditMode = false;
            }

            LoadPatientsCommand = new RelayCommand(async () => await LoadPatientsAsync());
            SaveCommand = new RelayCommand(async () => await SaveAsync());
            CancelCommand = new RelayCommand(() => Cancel());
        }

        public void OnLoaded()
        {
            // تحميل البيانات في الخلفية
            _ = Task.Run(async () =>
            {
                await LoadPatientsAsync();
            });
        }

        #region Properties
        public Receipt Receipt
        {
            get => _receipt;
            set => SetProperty(ref _receipt, value);
        }
        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set
            {
                SetProperty(ref _selectedPatient, value);
                if (value != null)
                {
                    Receipt.PatientId = value.Id;
                }
            }
        }
        public List<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }
        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }
        public string WindowTitle => IsEditMode ? "تعديل سند قبض" : "إصدار سند قبض جديد";
        public string SaveButtonText => IsEditMode ? "تحديث" : "حفظ";
        public List<string> PaymentMethods { get; } = new List<string>
        {
            "نقداً",
            "بطاقة ائتمان",
            "تحويل بنكي",
            "شيك",
            "آجل"
        };
        public bool IsBusy
        {
            get => _isBusy;
            set { _isBusy = value; OnPropertyChanged(); }
        }
        #endregion

        #region Commands
        public ICommand LoadPatientsCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        #endregion

        #region Methods
        private async Task LoadPatientsAsync()
        {
            try
            {
                IsBusy = true;
                var patients = await _patientService.GetAllPatientsAsync();
                Patients = patients.ToList();
                if (IsEditMode && Receipt.PatientId > 0)
                {
                    SelectedPatient = Patients.FirstOrDefault(p => p.Id == Receipt.PatientId);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المرضى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }
        private async Task SaveAsync()
        {
            try
            {
                if (!await ValidateDataAsync())
                    return;
                IsBusy = true;
                if (!IsEditMode && string.IsNullOrEmpty(Receipt.ReceiptNumber))
                {
                    Receipt.ReceiptNumber = await _receiptService.GetNextReceiptNumberAsync();
                }
                if (IsEditMode)
                {
                    Receipt = await _receiptService.UpdateReceiptAsync(Receipt);
                }
                else
                {
                    Receipt = await _receiptService.CreateReceiptAsync(Receipt);
                }
                MessageBox.Show(
                    IsEditMode ? "تم تحديث السند بنجاح" : "تم إصدار السند بنجاح",
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
                CloseWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }
        private void Cancel()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع البيانات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                CloseWindow();
            }
        }
        private Task<bool> ValidateDataAsync()
        {
            if (SelectedPatient == null)
            {
                MessageBox.Show("يرجى اختيار المريض", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Receipt.ReceiptDate == default)
            {
                MessageBox.Show("يرجى تحديد تاريخ السند", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Receipt.Amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (string.IsNullOrWhiteSpace(Receipt.Purpose))
            {
                MessageBox.Show("يرجى إدخال الغرض من الدفع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (string.IsNullOrWhiteSpace(Receipt.PaymentMethod))
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            return Task.FromResult(true);
        }
        private void CloseWindow()
        {
            if (System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().Any(w => w.IsActive))
            {
                System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().First(w => w.IsActive).Close();
            }
        }
        #endregion
    }
} 