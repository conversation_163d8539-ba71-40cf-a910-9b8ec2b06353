using System;
using System.Linq;
using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.ObjectModel;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class PatientAccountStatementWindow : Window
    {
        private readonly Patient _patient;
        private readonly IReceiptService _receiptService;
        public ObservableCollection<Receipt> Receipts { get; set; } = new();
        public bool CanCancelReceipts { get; set; }

        public PatientAccountStatementWindow(Patient patient)
        {
            InitializeComponent();
            _patient = patient;
            _receiptService = App.Services.GetRequiredService<IReceiptService>();
            var currentUser = App.Services.GetService(typeof(User)) as User;
            CanCancelReceipts = currentUser != null && currentUser.CanCancelReceipts;
            DataContext = this;
            LoadData();
        }

        private async void LoadData()
        {
            PatientNameText.Text = _patient.FullName;
            var receipts = await _receiptService.GetReceiptsByPatientAsync(_patient.Id);
            Receipts.Clear();
            foreach (var r in receipts.OrderBy(r => r.ReceiptDate))
                Receipts.Add(r);
            BalanceText.Text = Receipts.Where(r => r.Status == "مكتمل").Sum(r => r.Amount).ToString("N2") + " ريال";
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var html = GenerateAccountStatementHtml();
                var tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(), $"كشف_حساب_{_patient.FileNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                System.IO.File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة كشف الحساب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateAccountStatementHtml()
        {
            var rows = string.Join("\n", Receipts.Select(r => $@"<tr><td>{r.ReceiptDate:yyyy/MM/dd}</td><td>{r.Purpose}</td><td>{r.Amount:N2}</td><td>{r.PaymentMethod}</td><td>{r.Description}</td><td>{r.Status}</td><td>{r.IssuedBy}</td></tr>"));
            return $@"
<!DOCTYPE html>
<html dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>كشف حساب المريض</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Arial, sans-serif; background: #f8f9fa; }}
        .header {{ background: #4A90E2; color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }}
        .patient-name {{ font-size: 22px; font-weight: bold; }}
        .table-container {{ background: white; padding: 20px; border-radius: 0 0 10px 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
        th, td {{ border: 1px solid #e9ecef; padding: 8px; text-align: center; }}
        th {{ background: #e3f0ff; }}
        .footer {{ margin-top: 20px; font-size: 18px; color: #28A745; font-weight: bold; }}
        @media print {{ .no-print {{ display: none; }} }}
    </style>
</head>
<body>
    <div class='header'>كشف حساب المريض<br><span class='patient-name'>{_patient.FullName} (ملف: {_patient.FileNumber})</span></div>
    <div class='table-container'>
        <table>
            <tr><th>التاريخ</th><th>النوع</th><th>المبلغ</th><th>طريقة الدفع</th><th>الوصف</th><th>الحالة</th><th>المستخدم</th></tr>
            {rows}
        </table>
        <div class='footer'>الرصيد الحالي: {Receipts.Where(r => r.Status == "مكتمل").Sum(r => r.Amount):N2} ريال</div>
    </div>
    <div class='no-print' style='text-align:center;margin:20px;'>
        <button onclick='window.print()' style='background:#4A90E2;color:white;padding:12px 24px;border:none;border-radius:6px;font-size:16px;cursor:pointer;'>🖨️ طباعة كشف الحساب</button>
    </div>
</body>
</html>";
        }

        private async void CancelReceipt_Click(object sender, RoutedEventArgs e)
        {
            if (ReceiptsDataGrid.SelectedItem is Receipt receipt && receipt.Status != "ملغي")
            {
                var reason = Microsoft.VisualBasic.Interaction.InputBox("يرجى إدخال سبب الإلغاء:", "تأكيد الإلغاء", "");
                if (string.IsNullOrWhiteSpace(reason)) return;
                try
                {
                    receipt.Status = "ملغي";
                    receipt.Description += $"\n[إلغاء بواسطة: {Environment.UserName} في {DateTime.Now:yyyy/MM/dd HH:mm} سبب: {reason}]";
                    await _receiptService.UpdateReceiptAsync(receipt);
                    MessageBox.Show("تم إلغاء الإيصال بنجاح.", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoadData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إلغاء الإيصال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }
} 