using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.Users
{
    public class ActivityLogViewModel : BaseViewModel
    {
        private readonly IActivityLogService _activityLogService;
        private readonly IUserService _userService;

        public ObservableCollection<ActivityLog> ActivityLogs { get; } = new();
        public ObservableCollection<User> Users { get; } = new();

        private User? _selectedUser;
        private string? _selectedAction;
        private string? _selectedEntityType;
        private DateTime? _fromDate;
        private DateTime? _toDate;
        private string _searchTerm = string.Empty;

        public ActivityLogViewModel(IActivityLogService activityLogService, IUserService userService)
        {
            _activityLogService = activityLogService;
            _userService = userService;

            LoadLogsCommand = new RelayCommand(async () => await LoadLogsAsync());
            SearchCommand = new RelayCommand(async () => await SearchAsync());
            ClearFiltersCommand = new RelayCommand(async () => await ClearFiltersAsync());

            _ = LoadInitialDataAsync();
        }

        public User? SelectedUser { get => _selectedUser; set { SetProperty(ref _selectedUser, value); } }
        public string? SelectedAction { get => _selectedAction; set { SetProperty(ref _selectedAction, value); } }
        public string? SelectedEntityType { get => _selectedEntityType; set { SetProperty(ref _selectedEntityType, value); } }
        public DateTime? FromDate { get => _fromDate; set { SetProperty(ref _fromDate, value); } }
        public DateTime? ToDate { get => _toDate; set { SetProperty(ref _toDate, value); } }
        public string SearchTerm { get => _searchTerm; set { SetProperty(ref _searchTerm, value); } }

        public ICommand LoadLogsCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ClearFiltersCommand { get; }

        public string[] Actions { get; } = new[] { "Login", "Logout", "Create", "Update", "Delete", "Activate", "Deactivate", "FailedLogin" };
        public string[] EntityTypes { get; } = new[] { "User", "Patient", "Doctor", "Employee", "Invoice", "Receipt", "Appointment", "Role", "Settings" };

        private async Task LoadInitialDataAsync()
        {
            IsBusy = true;
            try
            {
                var users = await _userService.GetAllUsersAsync();
                Users.Clear();
                foreach (var user in users)
                    Users.Add(user);
                await LoadLogsAsync();
            }
            finally { IsBusy = false; }
        }

        private async Task LoadLogsAsync()
        {
            IsBusy = true;
            try
            {
                var logs = await _activityLogService.GetAllActivityLogsAsync(FromDate, ToDate, SelectedAction, SelectedEntityType);
                if (SelectedUser != null)
                    logs = logs.Where(l => l.UserId == SelectedUser.Id);
                if (!string.IsNullOrWhiteSpace(SearchTerm))
                    logs = logs.Where(l => (l.Description?.Contains(SearchTerm) ?? false) || (l.OldValues?.Contains(SearchTerm) ?? false) || (l.NewValues?.Contains(SearchTerm) ?? false));
                ActivityLogs.Clear();
                foreach (var log in logs.OrderByDescending(l => l.ActionDate))
                    ActivityLogs.Add(log);
            }
            finally { IsBusy = false; }
        }

        private async Task SearchAsync() => await LoadLogsAsync();
        private async Task ClearFiltersAsync()
        {
            SelectedUser = null;
            SelectedAction = null;
            SelectedEntityType = null;
            FromDate = null;
            ToDate = null;
            SearchTerm = string.Empty;
            await LoadLogsAsync();
        }
    }
} 