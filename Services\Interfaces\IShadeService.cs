using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IShadeService
    {
        Task<IEnumerable<Shade>> GetAllShadesAsync();
        Task<IEnumerable<Shade>> GetActiveShadesAsync();
        Task<Shade?> GetShadeByIdAsync(int id);
        Task<Shade> AddShadeAsync(Shade shade);
        Task<Shade> UpdateShadeAsync(Shade shade);
        Task<bool> DeleteShadeAsync(int id);
        Task<bool> ShadeExistsAsync(int id);
        Task<IEnumerable<Shade>> SearchShadesAsync(string searchTerm);
        Task<Shade> GetShadeByNameAsync(string name);
    }
} 