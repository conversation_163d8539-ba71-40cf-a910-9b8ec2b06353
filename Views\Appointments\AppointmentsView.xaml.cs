using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Appointments;

namespace AqlanCenterProApp.Views.Appointments
{
    /// <summary>
    /// Interaction logic for AppointmentsView.xaml
    /// </summary>
    public partial class AppointmentsView : UserControl
    {
        public AppointmentsView()
        {
            InitializeComponent();
        }

        public AppointmentsView(AppointmentsListViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            Loaded += AppointmentsView_Loaded;
        }

        private void AppointmentsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is AppointmentsListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadAppointmentsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadAppointmentsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات المواعيد: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في AppointmentsView_Loaded: {ex.Message}");
            }
        }
    }
}