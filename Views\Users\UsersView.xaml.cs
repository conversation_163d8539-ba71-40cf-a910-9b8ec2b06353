using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Users;

namespace AqlanCenterProApp.Views.Users
{
    public partial class UsersView : UserControl
    {
        public UsersView()
        {
            InitializeComponent();
        }

        public UsersView(UsersListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += UsersView_Loaded;
        }

        private void UsersView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is UsersListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadUsersAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadUsersAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات المستخدمين: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في UsersView_Loaded: {ex.Message}");
            }
        }
    }
}