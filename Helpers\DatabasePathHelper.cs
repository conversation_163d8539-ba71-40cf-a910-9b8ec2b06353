using System.IO;

namespace AqlanCenterProApp.Helpers
{
    /// <summary>
    /// مساعد مركزي لإدارة مسار قاعدة البيانات الموحدة
    /// </summary>
    public static class DatabasePathHelper
    {
        /// <summary>
        /// اسم ملف قاعدة البيانات الموحدة
        /// </summary>
        public const string DATABASE_FILENAME = "AqlanCenterDatabase.db";

        /// <summary>
        /// الحصول على مسار قاعدة البيانات الموحدة
        /// </summary>
        /// <returns>المسار الكامل لقاعدة البيانات</returns>
        public static string GetDatabasePath()
        {
            var projectDirectory = Directory.GetCurrentDirectory();
            return Path.Combine(projectDirectory, DATABASE_FILENAME);
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال لقاعدة البيانات الموحدة
        /// </summary>
        /// <returns>سلسلة الاتصال</returns>
        public static string GetConnectionString()
        {
            var dbPath = GetDatabasePath();
            return $"Data Source={dbPath}";
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات الموحدة
        /// </summary>
        /// <returns>true إذا كانت قاعدة البيانات موجودة</returns>
        public static bool DatabaseExists()
        {
            var dbPath = GetDatabasePath();
            return File.Exists(dbPath);
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        /// <returns>معلومات الملف أو null إذا لم يكن موجوداً</returns>
        public static FileInfo? GetDatabaseInfo()
        {
            var dbPath = GetDatabasePath();
            return File.Exists(dbPath) ? new FileInfo(dbPath) : null;
        }

        /// <summary>
        /// الحصول على حجم قاعدة البيانات بالكيلوبايت
        /// </summary>
        /// <returns>حجم قاعدة البيانات بالكيلوبايت أو 0 إذا لم تكن موجودة</returns>
        public static double GetDatabaseSizeKB()
        {
            var info = GetDatabaseInfo();
            return info != null ? Math.Round(info.Length / 1024.0, 2) : 0;
        }

        /// <summary>
        /// الحصول على تاريخ آخر تعديل لقاعدة البيانات
        /// </summary>
        /// <returns>تاريخ آخر تعديل أو null إذا لم تكن موجودة</returns>
        public static DateTime? GetLastModified()
        {
            var info = GetDatabaseInfo();
            return info?.LastWriteTime;
        }

        /// <summary>
        /// إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        /// </summary>
        /// <returns>مسار مجلد النسخ الاحتياطية</returns>
        public static string EnsureBackupDirectory()
        {
            var projectDirectory = Directory.GetCurrentDirectory();
            var backupDirectory = Path.Combine(projectDirectory, "Backups");
            
            if (!Directory.Exists(backupDirectory))
            {
                Directory.CreateDirectory(backupDirectory);
            }
            
            return backupDirectory;
        }

        /// <summary>
        /// إنشاء مسار نسخة احتياطية جديدة
        /// </summary>
        /// <param name="suffix">لاحقة اختيارية للاسم</param>
        /// <returns>مسار النسخة الاحتياطية</returns>
        public static string CreateBackupPath(string suffix = "")
        {
            var backupDirectory = EnsureBackupDirectory();
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var filename = string.IsNullOrEmpty(suffix) 
                ? $"AqlanCenterDatabase_Backup_{timestamp}.db"
                : $"AqlanCenterDatabase_Backup_{suffix}_{timestamp}.db";
            
            return Path.Combine(backupDirectory, filename);
        }

        /// <summary>
        /// تسجيل معلومات قاعدة البيانات في وحدة التحكم
        /// </summary>
        public static void LogDatabaseInfo()
        {
            var dbPath = GetDatabasePath();
            var exists = DatabaseExists();
            
            Console.WriteLine($"📁 مسار قاعدة البيانات: {dbPath}");
            Console.WriteLine($"📊 حالة قاعدة البيانات: {(exists ? "موجودة" : "غير موجودة")}");
            
            if (exists)
            {
                var sizeKB = GetDatabaseSizeKB();
                var lastModified = GetLastModified();
                Console.WriteLine($"📏 حجم قاعدة البيانات: {sizeKB} KB");
                Console.WriteLine($"🕒 آخر تعديل: {lastModified:yyyy-MM-dd HH:mm:ss}");
            }
        }

        /// <summary>
        /// التحقق من صحة مسار قاعدة البيانات
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كان المسار صحيحاً</returns>
        public static string? ValidateDatabasePath()
        {
            try
            {
                var dbPath = GetDatabasePath();
                var directory = Path.GetDirectoryName(dbPath);
                
                if (string.IsNullOrEmpty(directory))
                    return "مسار قاعدة البيانات غير صحيح";
                
                if (!Directory.Exists(directory))
                    return "مجلد قاعدة البيانات غير موجود";
                
                // التحقق من صلاحيات الكتابة
                var testFile = Path.Combine(directory, "test_write_permission.tmp");
                try
                {
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                }
                catch
                {
                    return "لا توجد صلاحية للكتابة في مجلد قاعدة البيانات";
                }
                
                return null; // كل شيء صحيح
            }
            catch (Exception ex)
            {
                return $"خطأ في التحقق من مسار قاعدة البيانات: {ex.Message}";
            }
        }
    }
}
