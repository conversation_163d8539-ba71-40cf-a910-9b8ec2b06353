<Window x:Class="AqlanCenterProApp.Views.Users.AddEditUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding IsEditMode, Converter={StaticResource BoolToStringConverter}, ConverterParameter='تعديل مستخدم|إضافة مستخدم جديد'}"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,20" CornerRadius="8">
            <TextBlock Text="{Binding IsEditMode, Converter={StaticResource BoolToStringConverter}, ConverterParameter='تعديل بيانات المستخدم|إضافة مستخدم جديد'}"
                       FontSize="20" 
                       FontWeight="Bold" 
                       Foreground="#333333"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <Border Background="White" Padding="20" Margin="0,0,0,20" CornerRadius="8">
                    <StackPanel>
                        <TextBlock Text="المعلومات الأساسية" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#333333"
                                   Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المستخدم:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     Text="{Binding User.Username, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     LostFocus="Username_LostFocus"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="الاسم الكامل:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                     Text="{Binding User.FullName, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBoxStyle}"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="البريد الإلكتروني:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <TextBox Grid.Row="2" Grid.Column="1" 
                                     Text="{Binding User.Email, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     LostFocus="Email_LostFocus"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="رقم الهاتف:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <TextBox Grid.Row="3" Grid.Column="1" 
                                     Text="{Binding User.Phone, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBoxStyle}"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="الدور:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <ComboBox Grid.Row="4" Grid.Column="1" 
                                      ItemsSource="{Binding Roles}" 
                                      SelectedValue="{Binding User.RoleId}"
                                      DisplayMemberPath="RoleName"
                                      SelectedValuePath="Id"
                                      Style="{StaticResource ModernComboBoxStyle}"/>

                            <CheckBox Grid.Row="5" Grid.Column="1" 
                                      Content="مستخدم نشط" 
                                      IsChecked="{Binding User.IsActive}"
                                      Style="{StaticResource ModernCheckBoxStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Password Section -->
                <Border Background="White" Padding="20" Margin="0,0,0,20" CornerRadius="8">
                    <StackPanel>
                        <TextBlock Text="كلمة المرور" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#333333"
                                   Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="كلمة المرور:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <PasswordBox Grid.Row="0" Grid.Column="1" 
                                         x:Name="PasswordBox"
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         PasswordChanged="PasswordBox_PasswordChanged"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تأكيد كلمة المرور:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <PasswordBox Grid.Row="1" Grid.Column="1" 
                                         x:Name="ConfirmPasswordBox"
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         PasswordChanged="ConfirmPasswordBox_PasswordChanged"/>

                            <TextBlock Grid.Row="2" Grid.Column="1" 
                                       Text="{Binding PasswordValidationMessage}"
                                       Foreground="Red" 
                                       FontSize="12"
                                       Margin="5,5,5,0"
                                       TextWrapping="Wrap"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Settings -->
                <Border Background="White" Padding="20" CornerRadius="8">
                    <StackPanel>
                        <TextBlock Text="الإعدادات" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#333333"
                                   Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اللغة:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <ComboBox Grid.Row="0" Grid.Column="1" 
                                      SelectedValue="{Binding User.Language}"
                                      Style="{StaticResource ModernComboBoxStyle}">
                                <ComboBoxItem Content="العربية" Tag="ar"/>
                                <ComboBoxItem Content="English" Tag="en"/>
                            </ComboBox>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المظهر:" 
                                       VerticalAlignment="Center" Margin="0,5" FontWeight="SemiBold"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" 
                                      SelectedValue="{Binding User.Theme}"
                                      Style="{StaticResource ModernComboBoxStyle}">
                                <ComboBoxItem Content="فاتح" Tag="Light"/>
                                <ComboBoxItem Content="داكن" Tag="Dark"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="0,20,0,0" CornerRadius="8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="حفظ" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Command="{Binding SaveCommand}"
                        IsEnabled="{Binding CanSave}"/>
                <Button Content="إلغاء" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding CancelCommand}"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" 
                            Width="100" 
                            Height="4" 
                            Margin="0,0,0,10"/>
                <TextBlock Text="جاري الحفظ..." 
                          Foreground="White" 
                          FontSize="16" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 