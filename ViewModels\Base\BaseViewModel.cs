using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.Base;

/// <summary>
/// الفئة الأساسية لجميع ViewModels
/// تحتوي على تطبيق INotifyPropertyChanged والوظائف المساعدة
/// </summary>
public abstract class BaseViewModel : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// إثارة حدث تغيير الخاصية
    /// </summary>
    /// <param name="propertyName">اسم الخاصية (يتم تحديده تلقائياً)</param>
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// تعيين قيمة الخاصية مع إثارة حدث التغيير
    /// </summary>
    /// <typeparam name="T">نوع الخاصية</typeparam>
    /// <param name="field">المتغير المرجعي للخاصية</param>
    /// <param name="value">القيمة الجديدة</param>
    /// <param name="propertyName">اسم الخاصية</param>
    /// <returns>true إذا تم تغيير القيمة</returns>
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    /// <summary>
    /// تعيين قيمة الخاصية مع تنفيذ إجراء إضافي عند التغيير (بدون Action parameter)
    /// </summary>
    /// <typeparam name="T">نوع الخاصية</typeparam>
    /// <param name="field">المتغير المرجعي للخاصية</param>
    /// <param name="value">القيمة الجديدة</param>
    /// <param name="onChanged">الإجراء المراد تنفيذه عند التغيير</param>
    /// <param name="propertyName">اسم الخاصية</param>
    /// <returns>true إذا تم تغيير القيمة</returns>
    protected bool SetProperty<T>(ref T field, T value, System.Action onChanged, [CallerMemberName] string? propertyName = null)
    {
        if (SetProperty(ref field, value, propertyName))
        {
            onChanged?.Invoke();
            return true;
        }
        return false;
    }

    #region Loading State

    private bool _isLoading;
    private string _loadingMessage = "جاري التحميل...";
    private bool _isBusy;

    /// <summary>
    /// حالة التحميل
    /// </summary>
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    /// <summary>
    /// حالة الانشغال
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    /// <summary>
    /// رسالة التحميل
    /// </summary>
    public string LoadingMessage
    {
        get => _loadingMessage;
        set => SetProperty(ref _loadingMessage, value);
    }

    #endregion

    #region Error Handling

    private string? _errorMessage;
    private bool _hasError;

    /// <summary>
    /// رسالة الخطأ
    /// </summary>
    public string? ErrorMessage
    {
        get => _errorMessage;
        set
        {
            if (SetProperty(ref _errorMessage, value))
            {
                HasError = !string.IsNullOrEmpty(value);
            }
        }
    }

    /// <summary>
    /// وجود خطأ
    /// </summary>
    public bool HasError
    {
        get => _hasError;
        private set => SetProperty(ref _hasError, value);
    }

    /// <summary>
    /// مسح رسالة الخطأ
    /// </summary>
    public void ClearError()
    {
        ErrorMessage = null;
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// تنفيذ مهمة مع إدارة حالة التحميل والأخطاء
    /// </summary>
    /// <param name="task">المهمة المراد تنفيذها</param>
    /// <param name="loadingMessage">رسالة التحميل (اختيارية)</param>
    protected async Task ExecuteAsync(Func<Task> task, string? loadingMessage = null)
    {
        try
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ClearError();
                IsLoading = true;
                if (!string.IsNullOrEmpty(loadingMessage))
                    LoadingMessage = loadingMessage;
            });

            await task().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ErrorMessage = ex.Message;
            });
        }
        finally
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                IsLoading = false;
            });
        }
    }

    /// <summary>
    /// تنفيذ مهمة مع إرجاع نتيجة وإدارة حالة التحميل والأخطاء
    /// </summary>
    /// <typeparam name="T">نوع النتيجة</typeparam>
    /// <param name="task">المهمة المراد تنفيذها</param>
    /// <param name="loadingMessage">رسالة التحميل (اختيارية)</param>
    /// <returns>النتيجة أو القيمة الافتراضية في حالة الخطأ</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> task, string? loadingMessage = null)
    {
        try
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ClearError();
                IsLoading = true;
                if (!string.IsNullOrEmpty(loadingMessage))
                    LoadingMessage = loadingMessage;
            });

            return await task().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ErrorMessage = ex.Message;
            });
            return default;
        }
        finally
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                IsLoading = false;
            });
        }
    }

    #endregion
}

/// <summary>
/// فئة مساعدة لإنشاء أوامر بسيطة
/// </summary>
public class RelayCommand : ICommand
{
    private readonly Action<object?> _execute;
    private readonly Func<object?, bool>? _canExecute;

    public RelayCommand(Action<object?> execute, Func<object?, bool>? canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public RelayCommand(Action execute, Func<bool>? canExecute = null)
        : this(_ => execute(), canExecute != null ? _ => canExecute() : null)
    {
    }

    public event EventHandler? CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object? parameter) => _canExecute?.Invoke(parameter) ?? true;

    public void Execute(object? parameter) => _execute(parameter);
}

/// <summary>
/// فئة مساعدة لإنشاء أوامر مع معاملات محددة
/// </summary>
public class RelayCommand<T> : ICommand
{
    private readonly Action<T> _execute;
    private readonly Func<T, bool>? _canExecute;

    public RelayCommand(Action<T> execute, Func<T, bool>? canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler? CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object? parameter) => _canExecute?.Invoke((T)parameter!) ?? true;

    public void Execute(object? parameter) => _execute((T)parameter!);
}

/// <summary>
/// فئة مساعدة لإنشاء أوامر غير متزامنة
/// </summary>
public class AsyncRelayCommand : ICommand
{
    private readonly Func<object?, Task> _execute;
    private readonly Func<object?, bool>? _canExecute;
    private bool _isExecuting;

    public AsyncRelayCommand(Func<object?, Task> execute, Func<object?, bool>? canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public AsyncRelayCommand(Func<Task> execute, Func<bool>? canExecute = null)
        : this(_ => execute(), canExecute != null ? _ => canExecute() : null)
    {
    }

    public bool IsExecuting
    {
        get => _isExecuting;
        private set
        {
            _isExecuting = value;
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    public event EventHandler? CanExecuteChanged;

    public bool CanExecute(object? parameter)
    {
        return !IsExecuting && (_canExecute?.Invoke(parameter) ?? true);
    }

    public async void Execute(object? parameter)
    {
        if (CanExecute(parameter))
        {
            try
            {
                IsExecuting = true;
                await _execute(parameter);
            }
            finally
            {
                IsExecuting = false;
            }
        }
    }
}

/// <summary>
/// فئة مساعدة لعرض الرسائل
/// </summary>
public static class MessageHelper
{
    public static void ShowError(string message)
    {
        System.Windows.MessageBox.Show(message, "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
    }

    public static void ShowSuccess(string message)
    {
        System.Windows.MessageBox.Show(message, "نجح", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    public static void ShowInfo(string message)
    {
        System.Windows.MessageBox.Show(message, "معلومات", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    public static bool ShowConfirmation(string message)
    {
        var result = System.Windows.MessageBox.Show(message, "تأكيد", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
        return result == System.Windows.MessageBoxResult.Yes;
    }
}
