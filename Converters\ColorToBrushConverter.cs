using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace AqlanCenterProApp.Converters
{
    public class ColorToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return new SolidColorBrush(Colors.Transparent);

                if (value is Color color)
                    return new SolidColorBrush(color);

                if (value is string colorString && !string.IsNullOrWhiteSpace(colorString))
                {
                    // إزالة المسافات والتأكد من صحة التنسيق
                    colorString = colorString.Trim();

                    // التأكد من وجود # في البداية
                    if (!colorString.StartsWith("#") && colorString.Length == 6)
                    {
                        colorString = "#" + colorString;
                    }

                    // محاولة تحويل النص إلى لون
                    var convertedColor = (Color)ColorConverter.ConvertFromString(colorString);
                    return new SolidColorBrush(convertedColor);
                }

                // إرجاع فرشاة افتراضية
                return new SolidColorBrush(Colors.LightGray);
            }
            catch (Exception)
            {
                // في حالة فشل التحويل، إرجاع لون افتراضي
                return new SolidColorBrush(Colors.LightGray);
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}