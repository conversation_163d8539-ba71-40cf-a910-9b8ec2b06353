using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Services.Implementations
{
    public class RoleService : IRoleService
    {
        private readonly AqlanCenterDbContext _context;

        public RoleService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await _context.Roles
                .Where(r => !r.IsDeleted)
                .OrderBy(r => r.RoleName)
                .ToListAsync();
        }

        public async Task<Role?> GetRoleByIdAsync(int id)
        {
            return await _context.Roles
                .FirstOrDefaultAsync(r => r.Id == id && !r.IsDeleted);
        }

        public async Task<Role> CreateRoleAsync(Role role)
        {
            role.CreatedAt = DateTime.Now;
            _context.Roles.Add(role);
            await _context.SaveChangesAsync();
            return role;
        }

        public async Task<Role> UpdateRoleAsync(Role role)
        {
            var existingRole = await _context.Roles.FindAsync(role.Id);
            if (existingRole == null)
                throw new ArgumentException("Role not found");

            existingRole.RoleName = role.RoleName;
            existingRole.Description = role.Description;
            existingRole.IsActive = role.IsActive;
            
            // Update permissions
            existingRole.CanViewPatients = role.CanViewPatients;
            existingRole.CanAddPatients = role.CanAddPatients;
            existingRole.CanEditPatients = role.CanEditPatients;
            existingRole.CanDeletePatients = role.CanDeletePatients;
            
            existingRole.CanViewDoctors = role.CanViewDoctors;
            existingRole.CanAddDoctors = role.CanAddDoctors;
            existingRole.CanEditDoctors = role.CanEditDoctors;
            existingRole.CanDeleteDoctors = role.CanDeleteDoctors;
            
            existingRole.CanViewEmployees = role.CanViewEmployees;
            existingRole.CanAddEmployees = role.CanAddEmployees;
            existingRole.CanEditEmployees = role.CanEditEmployees;
            existingRole.CanDeleteEmployees = role.CanDeleteEmployees;
            
            existingRole.CanViewAppointments = role.CanViewAppointments;
            existingRole.CanAddAppointments = role.CanAddAppointments;
            existingRole.CanEditAppointments = role.CanEditAppointments;
            existingRole.CanDeleteAppointments = role.CanDeleteAppointments;
            
            existingRole.CanViewInvoices = role.CanViewInvoices;
            existingRole.CanAddInvoices = role.CanAddInvoices;
            existingRole.CanEditInvoices = role.CanEditInvoices;
            existingRole.CanDeleteInvoices = role.CanDeleteInvoices;
            
            existingRole.CanViewPayments = role.CanViewPayments;
            existingRole.CanAddPayments = role.CanAddPayments;
            existingRole.CanEditPayments = role.CanEditPayments;
            existingRole.CanDeletePayments = role.CanDeletePayments;
            
            existingRole.CanViewReports = role.CanViewReports;
            existingRole.CanExportReports = role.CanExportReports;
            
            existingRole.CanViewSettings = role.CanViewSettings;
            existingRole.CanEditSettings = role.CanEditSettings;
            
            existingRole.CanBackupRestore = role.CanBackupRestore;
            
            existingRole.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return existingRole;
        }

        public async Task<bool> DeleteRoleAsync(int id)
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null) return false;

            // Check if any users are using this role
            var usersWithRole = await _context.Users.AnyAsync(u => u.RoleId == id && !u.IsDeleted);
            if (usersWithRole) return false; // Cannot delete role that is in use

            role.IsDeleted = true;
            role.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ActivateRoleAsync(int id)
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null) return false;

            role.IsActive = true;
            role.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeactivateRoleAsync(int id)
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null) return false;

            role.IsActive = false;
            role.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Role>> GetActiveRolesAsync()
        {
            return await _context.Roles
                .Where(r => !r.IsDeleted && r.IsActive)
                .OrderBy(r => r.RoleName)
                .ToListAsync();
        }

        public async Task<Role?> GetRoleByNameAsync(string roleName)
        {
            return await _context.Roles
                .FirstOrDefaultAsync(r => r.RoleName == roleName && !r.IsDeleted);
        }

        public async Task<bool> IsRoleNameUniqueAsync(string roleName, int? excludeRoleId = null)
        {
            return !await _context.Roles
                .AnyAsync(r => r.RoleName == roleName && 
                              !r.IsDeleted && 
                              (!excludeRoleId.HasValue || r.Id != excludeRoleId.Value));
        }

        public async Task<IEnumerable<User>> GetUsersInRoleAsync(int roleId)
        {
            return await _context.Users
                .Include(u => u.Role)
                .Where(u => !u.IsDeleted && u.RoleId == roleId)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<bool> AssignRoleToUserAsync(int userId, int roleId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            var role = await _context.Roles.FindAsync(roleId);
            if (role == null || role.IsDeleted || !role.IsActive) return false;

            user.RoleId = roleId;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveRoleFromUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            user.RoleId = 0; // Set to default role or handle as needed
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> HasPermissionAsync(int userId, string permission)
        {
            var user = await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

            if (user?.Role == null || !user.Role.IsActive) return false;

            return permission switch
            {
                "CanViewPatients" => user.Role.CanViewPatients,
                "CanAddPatients" => user.Role.CanAddPatients,
                "CanEditPatients" => user.Role.CanEditPatients,
                "CanDeletePatients" => user.Role.CanDeletePatients,
                
                "CanViewDoctors" => user.Role.CanViewDoctors,
                "CanAddDoctors" => user.Role.CanAddDoctors,
                "CanEditDoctors" => user.Role.CanEditDoctors,
                "CanDeleteDoctors" => user.Role.CanDeleteDoctors,
                
                "CanViewEmployees" => user.Role.CanViewEmployees,
                "CanAddEmployees" => user.Role.CanAddEmployees,
                "CanEditEmployees" => user.Role.CanEditEmployees,
                "CanDeleteEmployees" => user.Role.CanDeleteEmployees,
                
                "CanViewAppointments" => user.Role.CanViewAppointments,
                "CanAddAppointments" => user.Role.CanAddAppointments,
                "CanEditAppointments" => user.Role.CanEditAppointments,
                "CanDeleteAppointments" => user.Role.CanDeleteAppointments,
                
                "CanViewInvoices" => user.Role.CanViewInvoices,
                "CanAddInvoices" => user.Role.CanAddInvoices,
                "CanEditInvoices" => user.Role.CanEditInvoices,
                "CanDeleteInvoices" => user.Role.CanDeleteInvoices,
                
                "CanViewPayments" => user.Role.CanViewPayments,
                "CanAddPayments" => user.Role.CanAddPayments,
                "CanEditPayments" => user.Role.CanEditPayments,
                "CanDeletePayments" => user.Role.CanDeletePayments,
                
                "CanViewReports" => user.Role.CanViewReports,
                "CanExportReports" => user.Role.CanExportReports,
                
                "CanViewSettings" => user.Role.CanViewSettings,
                "CanEditSettings" => user.Role.CanEditSettings,
                
                "CanBackupRestore" => user.Role.CanBackupRestore,
                
                _ => false
            };
        }

        public async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId)
        {
            var user = await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

            if (user?.Role == null || !user.Role.IsActive) return new List<string>();

            var permissions = new List<string>();

            if (user.Role.CanViewPatients) permissions.Add("CanViewPatients");
            if (user.Role.CanAddPatients) permissions.Add("CanAddPatients");
            if (user.Role.CanEditPatients) permissions.Add("CanEditPatients");
            if (user.Role.CanDeletePatients) permissions.Add("CanDeletePatients");
            
            if (user.Role.CanViewDoctors) permissions.Add("CanViewDoctors");
            if (user.Role.CanAddDoctors) permissions.Add("CanAddDoctors");
            if (user.Role.CanEditDoctors) permissions.Add("CanEditDoctors");
            if (user.Role.CanDeleteDoctors) permissions.Add("CanDeleteDoctors");
            
            if (user.Role.CanViewEmployees) permissions.Add("CanViewEmployees");
            if (user.Role.CanAddEmployees) permissions.Add("CanAddEmployees");
            if (user.Role.CanEditEmployees) permissions.Add("CanEditEmployees");
            if (user.Role.CanDeleteEmployees) permissions.Add("CanDeleteEmployees");
            
            if (user.Role.CanViewAppointments) permissions.Add("CanViewAppointments");
            if (user.Role.CanAddAppointments) permissions.Add("CanAddAppointments");
            if (user.Role.CanEditAppointments) permissions.Add("CanEditAppointments");
            if (user.Role.CanDeleteAppointments) permissions.Add("CanDeleteAppointments");
            
            if (user.Role.CanViewInvoices) permissions.Add("CanViewInvoices");
            if (user.Role.CanAddInvoices) permissions.Add("CanAddInvoices");
            if (user.Role.CanEditInvoices) permissions.Add("CanEditInvoices");
            if (user.Role.CanDeleteInvoices) permissions.Add("CanDeleteInvoices");
            
            if (user.Role.CanViewPayments) permissions.Add("CanViewPayments");
            if (user.Role.CanAddPayments) permissions.Add("CanAddPayments");
            if (user.Role.CanEditPayments) permissions.Add("CanEditPayments");
            if (user.Role.CanDeletePayments) permissions.Add("CanDeletePayments");
            
            if (user.Role.CanViewReports) permissions.Add("CanViewReports");
            if (user.Role.CanExportReports) permissions.Add("CanExportReports");
            
            if (user.Role.CanViewSettings) permissions.Add("CanViewSettings");
            if (user.Role.CanEditSettings) permissions.Add("CanEditSettings");
            
            if (user.Role.CanBackupRestore) permissions.Add("CanBackupRestore");

            return permissions;
        }

        public async Task<bool> UpdateRolePermissionsAsync(int roleId, Dictionary<string, bool> permissions)
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null) return false;

            foreach (var permission in permissions)
            {
                switch (permission.Key)
                {
                    case "CanViewPatients": role.CanViewPatients = permission.Value; break;
                    case "CanAddPatients": role.CanAddPatients = permission.Value; break;
                    case "CanEditPatients": role.CanEditPatients = permission.Value; break;
                    case "CanDeletePatients": role.CanDeletePatients = permission.Value; break;
                    
                    case "CanViewDoctors": role.CanViewDoctors = permission.Value; break;
                    case "CanAddDoctors": role.CanAddDoctors = permission.Value; break;
                    case "CanEditDoctors": role.CanEditDoctors = permission.Value; break;
                    case "CanDeleteDoctors": role.CanDeleteDoctors = permission.Value; break;
                    
                    case "CanViewEmployees": role.CanViewEmployees = permission.Value; break;
                    case "CanAddEmployees": role.CanAddEmployees = permission.Value; break;
                    case "CanEditEmployees": role.CanEditEmployees = permission.Value; break;
                    case "CanDeleteEmployees": role.CanDeleteEmployees = permission.Value; break;
                    
                    case "CanViewAppointments": role.CanViewAppointments = permission.Value; break;
                    case "CanAddAppointments": role.CanAddAppointments = permission.Value; break;
                    case "CanEditAppointments": role.CanEditAppointments = permission.Value; break;
                    case "CanDeleteAppointments": role.CanDeleteAppointments = permission.Value; break;
                    
                    case "CanViewInvoices": role.CanViewInvoices = permission.Value; break;
                    case "CanAddInvoices": role.CanAddInvoices = permission.Value; break;
                    case "CanEditInvoices": role.CanEditInvoices = permission.Value; break;
                    case "CanDeleteInvoices": role.CanDeleteInvoices = permission.Value; break;
                    
                    case "CanViewPayments": role.CanViewPayments = permission.Value; break;
                    case "CanAddPayments": role.CanAddPayments = permission.Value; break;
                    case "CanEditPayments": role.CanEditPayments = permission.Value; break;
                    case "CanDeletePayments": role.CanDeletePayments = permission.Value; break;
                    
                    case "CanViewReports": role.CanViewReports = permission.Value; break;
                    case "CanExportReports": role.CanExportReports = permission.Value; break;
                    
                    case "CanViewSettings": role.CanViewSettings = permission.Value; break;
                    case "CanEditSettings": role.CanEditSettings = permission.Value; break;
                    
                    case "CanBackupRestore": role.CanBackupRestore = permission.Value; break;
                }
            }

            role.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }
    }
} 