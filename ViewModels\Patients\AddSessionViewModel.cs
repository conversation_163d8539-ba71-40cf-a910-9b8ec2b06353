using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Patients
{
    public class AddSessionViewModel : BaseViewModel
    {
        private DateTime _sessionDate = DateTime.Now;
        private string _treatmentType = string.Empty;
        private decimal _amount;
        private string _notes = string.Empty;
        private string _sessionStatus = "مكتملة";
        private string _paymentStatus = "غير مدفوعة";

        public Patient Patient { get; set; }

        public DateTime SessionDate
        {
            get => _sessionDate;
            set => SetProperty(ref _sessionDate, value);
        }

        public string TreatmentType
        {
            get => _treatmentType;
            set => SetProperty(ref _treatmentType, value);
        }

        public decimal Amount
        {
            get => _amount;
            set => SetProperty(ref _amount, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public string SessionStatus
        {
            get => _sessionStatus;
            set => SetProperty(ref _sessionStatus, value);
        }

        public string PaymentStatus
        {
            get => _paymentStatus;
            set => SetProperty(ref _paymentStatus, value);
        }

        public List<string> TreatmentTypes { get; } = new List<string>
        {
            "استشارة أولية / كشف أولي",
            "تقويم الأسنان الشفاف (Invisalign أو مماثل)",
            "تقويم الأسنان المعدني الثابت",
            "تقويم الأسنان الخزفي (سيراميك)",
            "تعديل الأسلاك وزيارة متابعة التقويم",
            "تركيب الأجهزة المتحركة للأطفال أو الكبار",
            "تركيب مثبت بعد التقويم (Retainer)",
            "خلع الأسنان (بسيط أو جراحي)",
            "تنظيف الأسنان وإزالة الجير",
            "تبييض الأسنان بالعيادة",
            "حشوة سنية ضوئية (كومبوزيت)",
            "حشوة أملغم",
            "علاج عصب (سحب عصب)",
            "تركيب تيجان (كراون) وجسور",
            "تركيب عدسات الفينير/لومينير (تجميل الأسنان)",
            "زراعة الأسنان (Dental Implant)",
            "جراحة لثة / علاج التهاب اللثة",
            "تركيب طقم أسنان متحرك جزئي/كامل",
            "إزالة كيس أو ورم صغير بالفم",
            "تصحيح الابتسامة اللثوية (Gummy Smile)",
            "إصلاح كسر سن أو تصدع",
            "علاج الفلورايد وحماية الأسنان للأطفال",
            "توسيع الفك (Rapid Palatal Expander)",
            "إزالة تقويم أو أجهزة قديمة",
            "إجراءات طبية عامة (قياس ضغط، مراجعة السكر...)",
            "إجراءات أخرى حسب الحاجة (تخصصية أو تجميلية)"
        };

        public List<string> SessionStatuses { get; } = new List<string>
        {
            "مكتملة",
            "ملغية",
            "مؤجلة"
        };

        public List<string> PaymentStatuses { get; } = new List<string>
        {
            "مدفوعة",
            "غير مدفوعة",
            "مدفوعة جزئياً"
        };

        public RelayCommand SaveSessionCommand { get; set; } = null!;
        public RelayCommand CancelCommand { get; set; } = null!;

        public AddSessionViewModel(Patient patient)
        {
            Patient = patient;
        }
    }
}