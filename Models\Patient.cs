using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج بيانات المريض - محسن ومطور
    /// </summary>
    public class Patient : BaseEntity
    {
        /// <summary>
        /// رقم الملف - يبدأ من 8500
        /// </summary>
        [Display(Name = "رقم الملف")]
        public int FileNumber { get; set; }

        /// <summary>
        /// الاسم الثلاثي الكامل
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم لا يجب أن يتجاوز 100 حرف")]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// الجنس
        /// </summary>
        [Required(ErrorMessage = "الجنس مطلوب")]
        [StringLength(10)]
        [Display(Name = "الجنس")]
        public string Gender { get; set; } = string.Empty; // ذكر، أنثى

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        [Display(Name = "تاريخ الميلاد")]
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// العمر المحسوب تلقائياً
        /// </summary>
        [NotMapped]
        [Display(Name = "العمر")]
        public int? Age
        {
            get
            {
                if (DateOfBirth.HasValue)
                {
                    var today = DateTime.Today;
                    var age = today.Year - DateOfBirth.Value.Year;
                    if (DateOfBirth.Value.Date > today.AddYears(-age)) age--;
                    return age;
                }
                return null;
            }
        }

        /// <summary>
        /// رقم الهاتف الأساسي
        /// </summary>
        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        [RegularExpression(@"^(77[0-9]|73[0-9]|70[0-9])[0-9]{6}$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? Phone { get; set; }

        /// <summary>
        /// رقم الهاتف الإضافي
        /// </summary>
        [StringLength(20)]
        [Display(Name = "هاتف إضافي")]
        [RegularExpression(@"^(77[0-9]|73[0-9]|70[0-9])[0-9]{6}$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? Mobile { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [StringLength(100)]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [StringLength(200)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        /// <summary>
        /// تصنيف المريض
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "التصنيف")]
        public string PatientCategory { get; set; } = "جديد"; // جديد، متابع، طارئ، VIP

        /// <summary>
        /// تاريخ التسجيل
        /// </summary>
        [Display(Name = "تاريخ التسجيل")]
        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        [Display(Name = "الرصيد الافتتاحي")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        [Display(Name = "الرصيد الحالي")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// حالة الملف
        /// </summary>
        [Required]
        [StringLength(20)]
        [Display(Name = "حالة الملف")]
        public string FileStatus { get; set; } = "نشط"; // نشط، مؤرشف، معلق

        /// <summary>
        /// صورة المريض
        /// </summary>
        [Display(Name = "صورة المريض")]
        public string? PatientImage { get; set; }

        /// <summary>
        /// التاريخ المرضي
        /// </summary>
        [Display(Name = "التاريخ المرضي")]
        public string? MedicalHistory { get; set; }

        /// <summary>
        /// الحساسية
        /// </summary>
        [Display(Name = "الحساسية")]
        public string? Allergies { get; set; }

        /// <summary>
        /// جهة الاتصال في الطوارئ
        /// </summary>
        [StringLength(100)]
        [Display(Name = "جهة الاتصال في الطوارئ")]
        public string? EmergencyContact { get; set; }

        /// <summary>
        /// هاتف الطوارئ
        /// </summary>
        [StringLength(20)]
        [Display(Name = "هاتف الطوارئ")]
        public string? EmergencyPhone { get; set; }

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [Display(Name = "ملاحظات")]
        public new string? Notes { get; set; }

        /// <summary>
        /// مبلغ المعاينة
        /// </summary>
        [Display(Name = "مبلغ المعاينة")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ConsultationFee { get; set; } = 50.00m;

        /// <summary>
        /// هل المريض مؤرشف؟
        /// </summary>
        [NotMapped]
        public bool IsArchived => FileStatus == "مؤرشف";

        /// <summary>
        /// هل المريض نشط؟
        /// </summary>
        [NotMapped]
        public bool IsActive => FileStatus == "نشط";

        /// <summary>
        /// نص حالة الرصيد
        /// </summary>
        [NotMapped]
        public string BalanceStatus
        {
            get
            {
                if (CurrentBalance > 0) return "دائن";
                if (CurrentBalance < 0) return "مدين";
                return "متوازن";
            }
        }

        /// <summary>
        /// الرصيد المالي الفعلي للمريض (يحسب من جميع الإيصالات)
        /// </summary>
        [NotMapped]
        public decimal FinancialBalance
        {
            get
            {
                // سيتم حساب الرصيد من قاعدة البيانات عند الحاجة
                return 0;
            }
        }

        // Navigation Properties
        public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
        public virtual ICollection<PatientFile> PatientFiles { get; set; } = new List<PatientFile>();
        public virtual ICollection<LabOrder> LabOrders { get; set; } = new List<LabOrder>();
        public virtual ICollection<OrthodonticPlan> OrthodonticPlans { get; set; } = new List<OrthodonticPlan>();
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    }
}
