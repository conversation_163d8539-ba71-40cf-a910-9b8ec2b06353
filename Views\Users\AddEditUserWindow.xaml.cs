using AqlanCenterProApp.ViewModels.Users;
using System.Windows;

namespace AqlanCenterProApp.Views.Users
{
    public partial class AddEditUserWindow : Window
    {
        public AddEditUserWindow(AddEditUserViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            viewModel.SaveCompleted += OnSaveCompleted;
        }

        private void OnSaveCompleted(bool success)
        {
            if (success)
            {
                DialogResult = true;
            }
            else
            {
                DialogResult = false;
            }
            Close();
        }

        private void Username_LostFocus(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddEditUserViewModel viewModel)
            {
                viewModel.ValidateUsernameCommand.Execute(null);
            }
        }

        private void Email_LostFocus(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddEditUserViewModel viewModel)
            {
                viewModel.ValidateEmailCommand.Execute(null);
            }
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddEditUserViewModel viewModel)
            {
                viewModel.Password = PasswordBox.Password;
            }
        }

        private void ConfirmPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddEditUserViewModel viewModel)
            {
                viewModel.ConfirmPassword = ConfirmPasswordBox.Password;
            }
        }
    }
} 