using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces;

/// <summary>
/// واجهة خدمة إدارة المرضى
/// تحتوي على جميع العمليات المطلوبة لإدارة بيانات المرضى
/// </summary>
public interface IPatientService
{
    #region العمليات الأساسية (CRUD)
    
    /// <summary>
    /// الحصول على جميع المرضى مع إمكانية التصفية والترقيم
    /// </summary>
    /// <param name="pageNumber">رقم الصفحة (افتراضي: 1)</param>
    /// <param name="pageSize">حجم الصفحة (افتراضي: 50)</param>
    /// <param name="searchTerm">مصطلح البحث (اختياري)</param>
    /// <param name="category">تصنيف المريض (اختياري)</param>
    /// <param name="fileStatus">حالة الملف (اختياري)</param>
    /// <param name="includeDeleted">تضمين المحذوفين (افتراضي: false)</param>
    /// <returns>قائمة المرضى مع معلومات الترقيم</returns>
    Task<(List<Patient> Patients, int TotalCount, int TotalPages)> GetPatientsAsync(
        int pageNumber = 1, 
        int pageSize = 50, 
        string? searchTerm = null,
        string? category = null,
        string? fileStatus = null,
        bool includeDeleted = false);

    /// <summary>
    /// الحصول على مريض بالمعرف
    /// </summary>
    /// <param name="patientId">معرف المريض</param>
    /// <param name="includeRelated">تضمين البيانات المرتبطة (جلسات، مدفوعات، إلخ)</param>
    /// <returns>بيانات المريض أو null إذا لم يوجد</returns>
    Task<Patient?> GetPatientByIdAsync(int patientId, bool includeRelated = false);

    /// <summary>
    /// الحصول على مريض برقم الملف
    /// </summary>
    /// <param name="fileNumber">رقم الملف</param>
    /// <param name="includeRelated">تضمين البيانات المرتبطة</param>
    /// <returns>بيانات المريض أو null إذا لم يوجد</returns>
    Task<Patient?> GetPatientByFileNumberAsync(int fileNumber, bool includeRelated = false);

    /// <summary>
    /// إضافة مريض جديد مع نظام الترقيم التلقائي
    /// </summary>
    /// <param name="patient">بيانات المريض الجديد</param>
    /// <param name="createdBy">المستخدم الذي أضاف المريض</param>
    /// <returns>المريض المضاف مع رقم الملف المُعين</returns>
    Task<Patient> AddPatientAsync(Patient patient, string createdBy);

    /// <summary>
    /// تحديث بيانات مريض موجود
    /// </summary>
    /// <param name="patient">بيانات المريض المحدثة</param>
    /// <param name="updatedBy">المستخدم الذي حدث البيانات</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdatePatientAsync(Patient patient, string updatedBy);

    /// <summary>
    /// حذف مريض (Soft Delete)
    /// </summary>
    /// <param name="patientId">معرف المريض</param>
    /// <param name="deletedBy">المستخدم الذي حذف المريض</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeletePatientAsync(int patientId, string deletedBy);

    /// <summary>
    /// استعادة مريض محذوف
    /// </summary>
    /// <param name="patientId">معرف المريض</param>
    /// <param name="restoredBy">المستخدم الذي استعاد المريض</param>
    /// <returns>true إذا تم الاستعادة بنجاح</returns>
    Task<bool> RestorePatientAsync(int patientId, string restoredBy);

    #endregion

    #region نظام ترقيم المرضى

    /// <summary>
    /// الحصول على رقم الملف التالي للمرضى الجدد (يبدأ من 8500)
    /// </summary>
    /// <returns>رقم الملف التالي</returns>
    Task<int> GetNextFileNumberAsync();

    /// <summary>
    /// التحقق من توفر رقم ملف معين
    /// </summary>
    /// <param name="fileNumber">رقم الملف المراد التحقق منه</param>
    /// <param name="excludePatientId">معرف المريض المستثنى من التحقق (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاحاً</returns>
    Task<bool> IsFileNumberAvailableAsync(int fileNumber, int? excludePatientId = null);

    /// <summary>
    /// التحقق من صحة رقم الملف حسب النظام المحدد
    /// </summary>
    /// <param name="fileNumber">رقم الملف</param>
    /// <returns>true إذا كان الرقم صحيحاً</returns>
    bool IsValidFileNumber(int fileNumber);

    #endregion

    #region البحث والإحصائيات

    /// <summary>
    /// البحث المتقدم في المرضى
    /// </summary>
    /// <param name="searchCriteria">معايير البحث</param>
    /// <returns>نتائج البحث</returns>
    Task<List<Patient>> SearchPatientsAsync(PatientSearchCriteria searchCriteria);

    /// <summary>
    /// الحصول على إحصائيات المرضى
    /// </summary>
    /// <returns>إحصائيات شاملة</returns>
    Task<PatientStatistics> GetPatientStatisticsAsync();

    /// <summary>
    /// الحصول على المرضى حسب التصنيف
    /// </summary>
    /// <param name="category">التصنيف</param>
    /// <returns>قائمة المرضى</returns>
    Task<List<Patient>> GetPatientsByCategoryAsync(string category);

    /// <summary>
    /// الحصول على المرضى المدينين
    /// </summary>
    /// <param name="minimumDebt">الحد الأدنى للدين</param>
    /// <returns>قائمة المرضى المدينين</returns>
    Task<List<Patient>> GetDebtorPatientsAsync(decimal minimumDebt = 0);

    #endregion

    #region التصنيفات

    /// <summary>
    /// الحصول على جميع تصنيفات المرضى المستخدمة
    /// </summary>
    /// <returns>قائمة التصنيفات</returns>
    Task<List<string>> GetPatientCategoriesAsync();

    /// <summary>
    /// إضافة تصنيف جديد
    /// </summary>
    /// <param name="category">التصنيف الجديد</param>
    /// <returns>true إذا تم الإضافة بنجاح</returns>
    Task<bool> AddPatientCategoryAsync(string category);

    #endregion

    #region التحقق من صحة البيانات

    /// <summary>
    /// التحقق من صحة رقم الهاتف (النمط اليمني)
    /// </summary>
    /// <param name="phoneNumber">رقم الهاتف</param>
    /// <returns>true إذا كان الرقم صحيحاً</returns>
    bool IsValidYemeniPhoneNumber(string phoneNumber);

    /// <summary>
    /// التحقق من عدم تكرار رقم الهاتف
    /// </summary>
    /// <param name="phoneNumber">رقم الهاتف</param>
    /// <param name="excludePatientId">معرف المريض المستثنى</param>
    /// <returns>true إذا كان الرقم متاحاً</returns>
    Task<bool> IsPhoneNumberAvailableAsync(string phoneNumber, int? excludePatientId = null);

    /// <summary>
    /// التحقق من صحة تاريخ الميلاد
    /// </summary>
    /// <param name="dateOfBirth">تاريخ الميلاد</param>
    /// <returns>true إذا كان التاريخ صحيحاً</returns>
    bool IsValidDateOfBirth(DateTime dateOfBirth);

    #endregion

    #region العمليات المساعدة

    /// <summary>
    /// تحديث رصيد المريض
    /// </summary>
    /// <param name="patientId">معرف المريض</param>
    /// <returns>الرصيد المحدث</returns>
    Task<decimal> UpdatePatientBalanceAsync(int patientId);

    /// <summary>
    /// الحصول على آخر نشاط للمريض
    /// </summary>
    /// <param name="patientId">معرف المريض</param>
    /// <returns>تاريخ آخر نشاط</returns>
    Task<DateTime?> GetLastActivityDateAsync(int patientId);

    #endregion

    /// <summary>
    /// الحصول على جميع المرضى
    /// </summary>
    /// <returns>قائمة المرضى</returns>
    Task<List<Patient>> GetAllPatientsAsync();
}

/// <summary>
/// معايير البحث المتقدم للمرضى
/// </summary>
public class PatientSearchCriteria
{
    public string? Name { get; set; }
    public string? PhoneNumber { get; set; }
    public int? FileNumber { get; set; }
    public string? Category { get; set; }
    public string? FileStatus { get; set; }
    public DateTime? RegistrationDateFrom { get; set; }
    public DateTime? RegistrationDateTo { get; set; }
    public decimal? BalanceFrom { get; set; }
    public decimal? BalanceTo { get; set; }
    public string? Gender { get; set; }
    public int? AgeFrom { get; set; }
    public int? AgeTo { get; set; }
}

/// <summary>
/// إحصائيات المرضى
/// </summary>
public class PatientStatistics
{
    public int TotalPatients { get; set; }
    public int ActivePatients { get; set; }
    public int ArchivedPatients { get; set; }
    public int NewPatientsThisMonth { get; set; }
    public int DebtorPatients { get; set; }
    public int CreditorPatients { get; set; }
    public decimal TotalDebt { get; set; }
    public decimal TotalCredit { get; set; }
    public Dictionary<string, int> PatientsByCategory { get; set; } = new();
    public Dictionary<string, int> PatientsByGender { get; set; } = new();
}
