using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class SystemSettings : BaseEntity
    {
        [Required(ErrorMessage = "عملة النظام مطلوبة")]
        [StringLength(10, ErrorMessage = "عملة النظام لا يمكن أن تتجاوز 10 أحرف")]
        public string Currency { get; set; } = "د.ك";
        
        [StringLength(10, ErrorMessage = "رمز العملة لا يمكن أن يتجاوز 10 أحرف")]
        public string CurrencySymbol { get; set; } = "KD";
        
        [StringLength(50, ErrorMessage = "نسق الألوان لا يمكن أن يتجاوز 50 حرف")]
        public string ColorScheme { get; set; } = "Default";
        
        [StringLength(100, ErrorMessage = "الخط الافتراضي لا يمكن أن يتجاوز 100 حرف")]
        public string DefaultFont { get; set; } = "Segoe UI";
        
        [Range(8, 24, ErrorMessage = "حجم الخط يجب أن يكون بين 8 و 24")]
        public int DefaultFontSize { get; set; } = 12;
        
        [StringLength(20, ErrorMessage = "التاريخ الافتراضي لا يمكن أن يتجاوز 20 حرف")]
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        
        [StringLength(20, ErrorMessage = "تنسيق الوقت لا يمكن أن يتجاوز 20 حرف")]
        public string TimeFormat { get; set; } = "HH:mm";
        
        [StringLength(10, ErrorMessage = "المنطقة الزمنية لا يمكن أن تتجاوز 10 أحرف")]
        public string TimeZone { get; set; } = "Asia/Kuwait";
        
        // إعدادات الأمان
        public bool RequirePasswordForSettings { get; set; } = true;
        
        public int SessionTimeoutMinutes { get; set; } = 30;
        
        public bool EnableAuditLog { get; set; } = true;
        
        public bool EnableAutoBackup { get; set; } = true;
        
        public int AutoBackupIntervalHours { get; set; } = 24;
        
        // إعدادات الطباعة
        [StringLength(200, ErrorMessage = "ترويسة الفواتير لا يمكن أن تتجاوز 200 حرف")]
        public string? InvoiceHeader { get; set; }
        
        [StringLength(200, ErrorMessage = "ترويسة الإيصالات لا يمكن أن تتجاوز 200 حرف")]
        public string? ReceiptHeader { get; set; }
        
        [StringLength(500, ErrorMessage = "ملاحظة الطباعة لا يمكن أن تتجاوز 500 حرف")]
        public string? PrintFooter { get; set; }
        
        public bool ShowLogoOnPrint { get; set; } = true;
        
        public bool ShowSignatureOnPrint { get; set; } = true;
        
        // إعدادات النسخ الاحتياطي
        [StringLength(500, ErrorMessage = "مسار النسخ الاحتياطي لا يمكن أن يتجاوز 500 حرف")]
        public string BackupPath { get; set; } = "Backups";
        
        public int MaxBackupFiles { get; set; } = 30;
        
        public bool CompressBackups { get; set; } = true;
        
        // إعدادات التحديث
        public bool EnableAutoUpdate { get; set; } = false;
        
        [StringLength(200, ErrorMessage = "رابط التحديث لا يمكن أن يتجاوز 200 حرف")]
        public string? UpdateUrl { get; set; }
        
        public string CurrentVersion { get; set; } = "1.0.0";
    }
} 