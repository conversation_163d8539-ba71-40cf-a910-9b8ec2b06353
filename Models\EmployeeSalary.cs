using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج راتب الموظف
    /// </summary>
    public class EmployeeSalary : BaseEntity
    {
        /// <summary>
        /// معرف الراتب
        /// </summary>
        public int SalaryId { get; set; }

        /// <summary>
        /// معرف الموظف
        /// </summary>
        [Required]
        public int EmployeeId { get; set; }

        /// <summary>
        /// شهر الراتب
        /// </summary>
        [Required]
        public int SalaryMonth { get; set; }

        /// <summary>
        /// سنة الراتب
        /// </summary>
        [Required]
        public int SalaryYear { get; set; }

        /// <summary>
        /// تاريخ الراتب (للتوافق مع الكود)
        /// </summary>
        public DateTime SalaryDate { get; set; } = DateTime.Now;

        /// <summary>
        /// الشهر (للتوافق مع الكود)
        /// </summary>
        public int Month => SalaryMonth;

        /// <summary>
        /// السنة (للتوافق مع الكود)
        /// </summary>
        public int Year => SalaryYear;

        /// <summary>
        /// المبلغ (للتوافق مع الكود)
        /// </summary>
        public decimal Amount => NetSalary;

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } = 0;

        /// <summary>
        /// بدل السكن
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal HousingAllowance { get; set; } = 0;

        /// <summary>
        /// بدل النقل
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TransportationAllowance { get; set; } = 0;

        /// <summary>
        /// بدل طبيعة عمل
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal NatureOfWorkAllowance { get; set; } = 0;

        /// <summary>
        /// بدلات أخرى
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherAllowances { get; set; } = 0;

        /// <summary>
        /// إجمالي البدلات
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAllowances { get; set; } = 0;

        /// <summary>
        /// التأمينات الاجتماعية
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal SocialInsurance { get; set; } = 0;

        /// <summary>
        /// التأمين الصحي
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal HealthInsurance { get; set; } = 0;

        /// <summary>
        /// الضريبة
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Tax { get; set; } = 0;

        /// <summary>
        /// حسميات أخرى
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherDeductions { get; set; } = 0;

        /// <summary>
        /// إجمالي الحسميات
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDeductions { get; set; } = 0;

        /// <summary>
        /// المكافآت
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Bonuses { get; set; } = 0;

        /// <summary>
        /// العمل الإضافي
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Overtime { get; set; } = 0;

        /// <summary>
        /// إجمالي الإضافات
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAdditions { get; set; } = 0;

        /// <summary>
        /// صافي الراتب
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetSalary { get; set; } = 0;

        /// <summary>
        /// عملة الراتب
        /// </summary>
        [MaxLength(10)]
        public string? SalaryCurrency { get; set; } = "ر.ي";

        /// <summary>
        /// تاريخ صرف الراتب
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// طريقة الدفع (تحويل بنكي، شيك، نقدي)
        /// </summary>
        [StringLength(20)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// رقم المرجع (رقم الشيك أو التحويل)
        /// </summary>
        [StringLength(50)]
        public string? PaymentReference { get; set; }

        /// <summary>
        /// حالة الراتب (معلق، مدفوع، ملغي)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string SalaryStatus { get; set; } = "معلق";

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(1000)]
        public new string? Notes { get; set; }

        /// <summary>
        /// عدد أيام العمل الفعلية
        /// </summary>
        public int ActualWorkDays { get; set; } = 0;

        /// <summary>
        /// عدد أيام العمل المطلوبة
        /// </summary>
        public int RequiredWorkDays { get; set; } = 22;

        /// <summary>
        /// عدد أيام الغياب
        /// </summary>
        public int AbsentDays { get; set; } = 0;

        /// <summary>
        /// عدد أيام الإجازة
        /// </summary>
        public int LeaveDays { get; set; } = 0;

        // Navigation Property
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}