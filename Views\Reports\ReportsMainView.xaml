<UserControl x:Class="AqlanCenterProApp.Views.Reports.ReportsMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Reports"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             Background="#F5F5F5">

    <UserControl.Resources>
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#CCCCCC" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="QuickReportCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#CCCCCC" Direction="270" ShadowDepth="1" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F8F9FA"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="3" Opacity="0.4"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Text="وحدة التقارير" 
                                 FontSize="24" 
                                 FontWeight="Bold" 
                                 Foreground="#333333"/>
                        <TextBlock Text="استخراج وتحليل جميع التقارير الطبية والإدارية والمالية" 
                                 FontSize="14" 
                                 Foreground="#666666"
                                 Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 تحديث" 
                            Style="{StaticResource ModernButtonStyle}"
                            Command="{Binding LoadSavedReportsCommand}"/>
                    <Button Content="📊 تقرير جديد" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Command="{Binding CreateReportCommand}"/>
                    <Button Content="📋 تقرير مخصص" 
                            Style="{StaticResource WarningButtonStyle}"
                            Command="{Binding OpenCustomReportCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Quick Reports -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,10">
            <StackPanel>
                <TextBlock Text="📋 التقارير السريعة" 
                         FontSize="18" 
                         FontWeight="Bold" 
                         Foreground="#333333"
                         Margin="0,0,0,15"/>
                
                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                    <ItemsControl ItemsSource="{Binding QuickReports}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource QuickReportCardStyle}"
                                        Width="250" Height="120"
                                        MouseLeftButtonDown="QuickReport_MouseLeftButtonDown">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                            <TextBlock Text="{Binding Icon}" FontSize="24" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding Title}" 
                                                     FontWeight="Bold" 
                                                     FontSize="14" 
                                                     Foreground="#333333"
                                                     TextWrapping="Wrap"/>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Row="1" 
                                                 Text="{Binding Description}" 
                                                 FontSize="12" 
                                                 Foreground="#666666"
                                                 TextWrapping="Wrap"
                                                 VerticalAlignment="Center"/>
                                        
                                        <Border Grid.Row="2" 
                                                Background="#E3F2FD" 
                                                CornerRadius="4" 
                                                Padding="8,4"
                                                HorizontalAlignment="Left">
                                            <TextBlock Text="انقر للفتح" 
                                                     FontSize="11" 
                                                     Foreground="#1976D2"
                                                     FontWeight="SemiBold"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Report Settings -->
            <Border Grid.Column="0" Background="White" Padding="20" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="⚙️ إعدادات التقرير" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             Margin="0,0,0,20"/>

                    <TextBlock Text="نوع التقرير:" 
                             FontWeight="SemiBold" 
                             Margin="0,0,0,5"/>
                    <ComboBox SelectedItem="{Binding SelectedReportType}"
                              Margin="0,0,0,15"
                              Padding="10,8">
                        <ComboBoxItem Content="تقرير المرضى"/>
                        <ComboBoxItem Content="تقرير المواعيد"/>
                        <ComboBoxItem Content="التقرير المالي"/>
                        <ComboBoxItem Content="تقرير المخزون"/>
                        <ComboBoxItem Content="تقرير الأداء"/>
                    </ComboBox>

                    <TextBlock Text="تاريخ البداية:" 
                             FontWeight="SemiBold" 
                             Margin="0,0,0,5"/>
                    <DatePicker SelectedDate="{Binding StartDate}"
                                Margin="0,0,0,15"
                                Padding="10,8"/>

                    <TextBlock Text="تاريخ النهاية:" 
                             FontWeight="SemiBold" 
                             Margin="0,0,0,5"/>
                    <DatePicker SelectedDate="{Binding EndDate}"
                                Margin="0,0,0,20"
                                Padding="10,8"/>

                    <Button Content="📊 إنشاء التقرير" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Command="{Binding CreateReportCommand}"
                            HorizontalAlignment="Stretch"/>
                </StackPanel>
            </Border>

            <!-- Saved Reports -->
            <Border Grid.Column="1" Background="White" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" 
                                 Text="📁 التقارير المحفوظة" 
                                 FontSize="16" 
                                 FontWeight="Bold" 
                                 Foreground="#333333"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="🖨️ طباعة" 
                                    Style="{StaticResource WarningButtonStyle}"
                                    Command="{Binding PrintReportCommand}"
                                    IsEnabled="{Binding SelectedReport, Converter={StaticResource NotNullConverter}}"/>
                            <Button Content="📤 تصدير" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Command="{Binding ExportReportCommand}"
                                    IsEnabled="{Binding SelectedReport, Converter={StaticResource NotNullConverter}}"/>
                            <Button Content="🗑️ حذف" 
                                    Style="{StaticResource DangerButtonStyle}"
                                    Command="{Binding DeleteReportCommand}"
                                    IsEnabled="{Binding SelectedReport, Converter={StaticResource NotNullConverter}}"/>
                        </StackPanel>
                    </Grid>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding SavedReports}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ModernCardStyle}"
                                            Margin="0,0,0,10"
                                            MouseLeftButtonDown="Report_MouseLeftButtonDown">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Title}" 
                                                         FontWeight="Bold" 
                                                         FontSize="16" 
                                                         Foreground="#333333"/>
                                                <TextBlock Text="{Binding Description}" 
                                                         FontSize="12" 
                                                         Foreground="#666666"
                                                         Margin="0,5,0,0"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                                    <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding CreatedAt, StringFormat='dd/MM/yyyy HH:mm'}" 
                                                             FontSize="12" 
                                                             Foreground="#999999"/>
                                                    <TextBlock Text=" | " FontSize="12" Foreground="#CCCCCC" Margin="5,0"/>
                                                    <TextBlock Text="👤" FontSize="12" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding CreatedBy}" 
                                                             FontSize="12" 
                                                             Foreground="#999999"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                                <Border Background="#E8F5E8" 
                                                        CornerRadius="4" 
                                                        Padding="8,4"
                                                        Margin="0,0,10,0">
                                                    <TextBlock Text="{Binding Status}" 
                                                             FontSize="11" 
                                                             Foreground="#2E7D32"
                                                             FontWeight="SemiBold"/>
                                                </Border>
                                                <TextBlock Text="📊" FontSize="20" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="4" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" 
                            Width="100" 
                            Height="4" 
                            Margin="0,0,0,10"/>
                <TextBlock Text="جاري التحميل..." 
                          Foreground="White" 
                          FontSize="16" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl> 