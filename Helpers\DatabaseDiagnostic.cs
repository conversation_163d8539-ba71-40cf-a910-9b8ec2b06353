using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using System.IO;
using System.Text;

namespace AqlanCenterProApp.Helpers;

/// <summary>
/// أداة تشخيص مبسطة لقاعدة البيانات
/// </summary>
public static class DatabaseDiagnostic
{
    /// <summary>
    /// تشخيص سريع لقاعدة البيانات
    /// </summary>
    public static async Task<(bool Success, string Message)> QuickDiagnosticAsync()
    {
        var report = new StringBuilder();
        
        try
        {
            // إنشاء مسار قاعدة البيانات
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            var dbPath = Path.Combine(dataDirectory, "AqlanCenter.db");
            
            report.AppendLine($"🔍 تشخيص قاعدة البيانات:");
            report.AppendLine($"📁 مسار التطبيق: {baseDirectory}");
            report.AppendLine($"🗄️ مسار قاعدة البيانات: {dbPath}");
            
            // التحقق من المجلد
            if (!Directory.Exists(dataDirectory))
            {
                report.AppendLine("⚠️ إنشاء مجلد Data...");
                Directory.CreateDirectory(dataDirectory);
                report.AppendLine("✅ تم إنشاء مجلد Data");
            }
            else
            {
                report.AppendLine("✅ مجلد Data موجود");
            }
            
            // التحقق من الصلاحيات
            try
            {
                var testFile = Path.Combine(dataDirectory, "test_permissions.tmp");
                await File.WriteAllTextAsync(testFile, "test");
                File.Delete(testFile);
                report.AppendLine("✅ صلاحيات الكتابة متوفرة");
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ مشكلة في صلاحيات الكتابة: {ex.Message}");
                return (false, report.ToString());
            }
            
            // اختبار إنشاء قاعدة البيانات
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);
            
            using var context = new AqlanCenterDbContext(optionsBuilder.Options);
            
            // محاولة إنشاء قاعدة البيانات
            report.AppendLine("🔧 إنشاء قاعدة البيانات...");
            await context.Database.EnsureCreatedAsync();
            report.AppendLine("✅ تم إنشاء قاعدة البيانات");
            
            // اختبار الاتصال
            report.AppendLine("🔗 اختبار الاتصال...");
            var canConnect = await context.Database.CanConnectAsync();
            if (!canConnect)
            {
                report.AppendLine("❌ فشل في الاتصال");
                return (false, report.ToString());
            }
            report.AppendLine("✅ الاتصال ناجح");
            
            // اختبار الجداول الأساسية
            report.AppendLine("📋 فحص الجداول الأساسية...");
            
            try
            {
                var usersCount = await context.Users.CountAsync();
                var rolesCount = await context.Roles.CountAsync();
                var patientsCount = await context.Patients.CountAsync();
                
                report.AppendLine($"✅ المستخدمين: {usersCount}");
                report.AppendLine($"✅ الأدوار: {rolesCount}");
                report.AppendLine($"✅ المرضى: {patientsCount}");
                
                // إذا لم تكن هناك بيانات أساسية، قم بإنشائها
                if (usersCount == 0 || rolesCount == 0)
                {
                    report.AppendLine("🔄 إنشاء البيانات الأساسية...");
                    await DatabaseInitializer.InitializeAsync(context);
                    report.AppendLine("✅ تم إنشاء البيانات الأساسية");
                }
                
                report.AppendLine("🎉 قاعدة البيانات جاهزة للاستخدام!");
                return (true, report.ToString());
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ خطأ في فحص الجداول: {ex.Message}");
                return (false, report.ToString());
            }
        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ عام: {ex.Message}");
            if (ex.InnerException != null)
            {
                report.AppendLine($"   التفاصيل: {ex.InnerException.Message}");
            }
            return (false, report.ToString());
        }
    }
    
    /// <summary>
    /// إعادة تعيين قاعدة البيانات بالكامل
    /// </summary>
    public static async Task<(bool Success, string Message)> ResetDatabaseAsync()
    {
        var report = new StringBuilder();
        
        try
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            var dbPath = Path.Combine(dataDirectory, "AqlanCenter.db");
            
            report.AppendLine("🔄 إعادة تعيين قاعدة البيانات...");
            
            // حذف الملفات الموجودة
            var filesToDelete = new[]
            {
                dbPath,
                dbPath + "-shm",
                dbPath + "-wal"
            };
            
            foreach (var file in filesToDelete)
            {
                if (File.Exists(file))
                {
                    File.Delete(file);
                    report.AppendLine($"🗑️ تم حذف {Path.GetFileName(file)}");
                }
            }
            
            // إنشاء قاعدة البيانات من جديد
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);
            
            using var context = new AqlanCenterDbContext(optionsBuilder.Options);
            
            await context.Database.EnsureCreatedAsync();
            report.AppendLine("✅ تم إنشاء قاعدة البيانات الجديدة");
            
            await DatabaseInitializer.InitializeAsync(context);
            report.AppendLine("✅ تم تهيئة البيانات الأولية");
            
            report.AppendLine("🎉 تم إعادة تعيين قاعدة البيانات بنجاح!");
            return (true, report.ToString());
        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ في إعادة التعيين: {ex.Message}");
            return (false, report.ToString());
        }
    }
    
    /// <summary>
    /// فحص صحة البيانات
    /// </summary>
    public static async Task<(bool Success, string Message)> ValidateDataAsync()
    {
        var report = new StringBuilder();
        
        try
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            var dbPath = Path.Combine(dataDirectory, "AqlanCenter.db");
            
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);
            
            using var context = new AqlanCenterDbContext(optionsBuilder.Options);
            
            report.AppendLine("🔍 فحص صحة البيانات:");
            
            // فحص البيانات الأساسية
            var usersCount = await context.Users.CountAsync();
            var rolesCount = await context.Roles.CountAsync();
            var adminUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "admin");
            
            if (usersCount == 0)
            {
                report.AppendLine("❌ لا توجد مستخدمين في النظام");
                return (false, report.ToString());
            }
            
            if (rolesCount == 0)
            {
                report.AppendLine("❌ لا توجد أدوار في النظام");
                return (false, report.ToString());
            }
            
            if (adminUser == null)
            {
                report.AppendLine("❌ مستخدم المدير غير موجود");
                return (false, report.ToString());
            }
            
            report.AppendLine($"✅ المستخدمين: {usersCount}");
            report.AppendLine($"✅ الأدوار: {rolesCount}");
            report.AppendLine($"✅ مستخدم المدير موجود: {adminUser.FullName}");
            
            // فحص الجداول الأخرى
            var doctorsCount = await context.Doctors.CountAsync();
            var patientsCount = await context.Patients.CountAsync();
            var prosthesisTypesCount = await context.ProsthesisTypes.CountAsync();
            
            report.AppendLine($"ℹ️ الأطباء: {doctorsCount}");
            report.AppendLine($"ℹ️ المرضى: {patientsCount}");
            report.AppendLine($"ℹ️ أنواع التركيبات: {prosthesisTypesCount}");
            
            report.AppendLine("🎉 البيانات صحيحة ومكتملة!");
            return (true, report.ToString());
        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ في فحص البيانات: {ex.Message}");
            return (false, report.ToString());
        }
    }
}
