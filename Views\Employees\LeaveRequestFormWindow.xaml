<Window x:Class="AqlanCenterProApp.Views.Employees.LeaveRequestFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نموذج طلب إجازة" Height="700" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="120"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="100"/>
        </Grid.RowDefinitions>

        <!-- Header with Logo and Center Name -->
        <Border Grid.Row="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Image Source="/Resources/logo.png" Width="80" Height="80" Margin="20" Grid.Column="0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="20">
                    <TextBlock Text="مركز عقيلان الطبي" FontSize="24" FontWeight="Bold" Foreground="#2196F3"/>
                    <TextBlock Text="Aqlan Medical Center" FontSize="16" Foreground="#666666"/>
                    <TextBlock Text="نموذج طلب إجازة" FontSize="18" FontWeight="SemiBold" Foreground="#333333" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Employee Information -->
                <GroupBox Header="معلومات الموظف" Margin="0,0,0,20" FontWeight="SemiBold">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Margin="5">
                            <TextBlock Text="اسم الموظف:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding EmployeeName}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Margin="5">
                            <TextBlock Text="الرقم الوظيفي:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding EmployeeId}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Margin="5">
                            <TextBlock Text="القسم:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding Department}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Margin="5">
                            <TextBlock Text="المنصب:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding Position}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="2" Margin="5">
                            <TextBlock Text="تاريخ الطلب:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding RequestDate}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Leave Request Details -->
                <GroupBox Header="تفاصيل طلب الإجازة" Margin="0,0,0,20" FontWeight="SemiBold">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Column="0" Grid.Row="0" Margin="5">
                            <TextBlock Text="نوع الإجازة:" FontWeight="SemiBold"/>
                            <ComboBox ItemsSource="{Binding LeaveTypes}" SelectedItem="{Binding SelectedLeaveType}" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="0" Margin="5">
                            <TextBlock Text="تاريخ بداية الإجازة:" FontWeight="SemiBold"/>
                            <DatePicker SelectedDate="{Binding StartDate}" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="1" Margin="5">
                            <TextBlock Text="تاريخ نهاية الإجازة:" FontWeight="SemiBold"/>
                            <DatePicker SelectedDate="{Binding EndDate}" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Grid.Row="1" Margin="5">
                            <TextBlock Text="عدد أيام الإجازة:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding DaysCount}" IsReadOnly="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2" Margin="5">
                            <TextBlock Text="سبب الإجازة:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding Reason}" Height="60" TextWrapping="Wrap" AcceptsReturn="True" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="3" Grid.ColumnSpan="2" Margin="5">
                            <TextBlock Text="عنوان الإقامة أثناء الإجازة:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding AddressDuringLeave}" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>

                        <StackPanel Grid.Column="0" Grid.Row="4" Grid.ColumnSpan="2" Margin="5">
                            <TextBlock Text="رقم الهاتف للتواصل:" FontWeight="SemiBold"/>
                            <TextBox Text="{Binding ContactPhone}" Margin="0,5,0,0" Padding="8"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Declaration -->
                <GroupBox Header="إقرار" Margin="0,0,0,20" FontWeight="SemiBold">
                    <StackPanel Margin="10">
                        <CheckBox Content="أقر بأن جميع المعلومات المذكورة أعلاه صحيحة وكاملة" 
                                  IsChecked="{Binding IsDeclarationAccepted}" Margin="0,5"/>
                        <CheckBox Content="أوافق على شروط وأحكام الإجازة" 
                                  IsChecked="{Binding IsTermsAccepted}" Margin="0,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="📄 طباعة النموذج" Width="150" Height="40" Margin="10" Background="#2196F3" Foreground="White" Command="{Binding PrintCommand}"/>
                    <Button Content="💾 حفظ كمسودة" Width="150" Height="40" Margin="10" Background="#4CAF50" Foreground="White" Command="{Binding SaveDraftCommand}"/>
                    <Button Content="📤 إرسال الطلب" Width="150" Height="40" Margin="10" Background="#FF9800" Foreground="White" Command="{Binding SubmitCommand}"/>
                    <Button Content="❌ إلغاء" Width="100" Height="40" Margin="10" Background="#F44336" Foreground="White" Click="Cancel_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer with Contact Information -->
        <Border Grid.Row="2" Background="#333333" BorderBrush="#DDDDDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="20">
                    <TextBlock Text="📍 العنوان:" FontWeight="SemiBold" Foreground="White"/>
                    <TextBlock Text="شارع الملك فهد، الرياض" Foreground="#CCCCCC" FontSize="12"/>
                </StackPanel>

                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="20">
                    <TextBlock Text="📞 الهاتف:" FontWeight="SemiBold" Foreground="White"/>
                    <TextBlock Text="+966-11-123-4567" Foreground="#CCCCCC" FontSize="12"/>
                </StackPanel>

                <StackPanel Grid.Column="2" VerticalAlignment="Center" Margin="20">
                    <TextBlock Text="📧 البريد الإلكتروني:" FontWeight="SemiBold" Foreground="White"/>
                    <TextBlock Text="<EMAIL>" Foreground="#CCCCCC" FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window> 