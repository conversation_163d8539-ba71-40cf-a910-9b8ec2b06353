using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.LabOrders;

namespace AqlanCenterProApp.Views.LabOrders
{
    /// <summary>
    /// Interaction logic for AddEditLabOrderView.xaml
    /// </summary>
    public partial class AddEditLabOrderView : Window
    {
        public AddEditLabOrderView(AddEditLabOrderViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;

            // Subscribe to events
            viewModel.LabOrderSaved += OnLabOrderSaved;
            viewModel.Cancelled += OnCancelled;
        }

        private void OnLabOrderSaved(object? sender, LabOrder labOrder)
        {
            DialogResult = true;
            Close();
        }

        private void OnCancelled(object? sender, System.EventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
} 