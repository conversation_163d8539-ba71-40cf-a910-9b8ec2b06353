using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Purchases
{
    public class AddEditPurchaseViewModel : BaseViewModel
    {
        private readonly IPurchaseService _purchaseService;
        private readonly ISupplierService _supplierService;
        private Purchase _purchase;
        private ObservableCollection<PurchaseItem> _purchaseItems;
        private PurchaseItem? _selectedPurchaseItem;
        private ObservableCollection<Supplier> _suppliers;
        private Supplier? _selectedSupplier;
        private bool _isEditMode;

        public event Action? SaveCompleted;
        public event Action? CancelRequested;

        public AddEditPurchaseViewModel(IPurchaseService purchaseService, ISupplierService supplierService, Purchase? purchase = null)
        {
            _purchaseService = purchaseService;
            _supplierService = supplierService;
            _purchase = purchase ?? new Purchase();
            _purchaseItems = new ObservableCollection<PurchaseItem>();
            _suppliers = new ObservableCollection<Supplier>();
            _isEditMode = purchase != null;

            SaveCommand = new RelayCommand(async () => await SavePurchaseAsync());
            CancelCommand = new RelayCommand(() => CancelRequested?.Invoke());
            AddItemCommand = new RelayCommand(() => AddPurchaseItem());
            RemoveItemCommand = new RelayCommand(() => RemovePurchaseItem(), () => SelectedPurchaseItem != null);
            LoadSuppliersCommand = new RelayCommand(async () => await LoadSuppliersAsync());

            // تحميل الموردين
            _ = LoadSuppliersAsync();

            // تحميل العناصر إذا كان في وضع التعديل
            if (_isEditMode && purchase?.PurchaseItems != null)
            {
                foreach (var item in purchase.PurchaseItems)
                {
                    _purchaseItems.Add(item);
                }
                _selectedSupplier = _suppliers.FirstOrDefault(s => s.Id == purchase.SupplierId);
            }
        }

        public Purchase Purchase
        {
            get => _purchase;
            set => SetProperty(ref _purchase, value);
        }

        public ObservableCollection<PurchaseItem> PurchaseItems
        {
            get => _purchaseItems;
            set => SetProperty(ref _purchaseItems, value);
        }

        public PurchaseItem? SelectedPurchaseItem
        {
            get => _selectedPurchaseItem;
            set
            {
                SetProperty(ref _selectedPurchaseItem, value);
                OnPropertyChanged(nameof(CanRemoveItem));
            }
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                SetProperty(ref _selectedSupplier, value);
                if (value != null)
                {
                    Purchase.SupplierId = value.Id;
                }
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public bool CanRemoveItem => SelectedPurchaseItem != null;

        public string WindowTitle => IsEditMode ? "تعديل مشترى" : "إضافة مشترى جديد";

        // Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand AddItemCommand { get; }
        public ICommand RemoveItemCommand { get; }
        public ICommand LoadSuppliersCommand { get; }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                IsBusy = true;
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                Suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    Suppliers.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تحميل الموردين: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddPurchaseItem()
        {
            var newItem = new PurchaseItem
            {
                Id = 0,
                PurchaseId = Purchase.Id,
                InventoryItemId = 0,
                Quantity = 1,
                UnitPrice = 0,
                TotalPrice = 0
            };
            PurchaseItems.Add(newItem);
            SelectedPurchaseItem = newItem;
        }

        private void RemovePurchaseItem()
        {
            if (SelectedPurchaseItem != null)
            {
                PurchaseItems.Remove(SelectedPurchaseItem);
                SelectedPurchaseItem = null;
            }
        }

        private async Task SavePurchaseAsync()
        {
            try
            {
                IsBusy = true;

                // التحقق من صحة البيانات
                if (SelectedSupplier == null)
                {
                    MessageHelper.ShowError("يرجى اختيار المورد");
                    return;
                }

                if (!PurchaseItems.Any())
                {
                    MessageHelper.ShowError("يرجى إضافة عنصر واحد على الأقل");
                    return;
                }

                // حساب المجموع
                Purchase.TotalAmount = PurchaseItems.Sum(item => item.TotalPrice);
                Purchase.PurchaseDate = DateTime.Now;
                Purchase.PurchaseStatus = "Pending";

                // حفظ المشتريات
                Purchase savedPurchase;
                if (IsEditMode)
                {
                    savedPurchase = await _purchaseService.UpdatePurchaseAsync(Purchase, PurchaseItems.ToList());
                }
                else
                {
                    savedPurchase = await _purchaseService.CreatePurchaseAsync(Purchase, PurchaseItems.ToList());
                }

                if (savedPurchase != null)
                {
                    MessageHelper.ShowSuccess(IsEditMode ? "تم تحديث المشتريات بنجاح" : "تم إضافة المشتريات بنجاح");
                    SaveCompleted?.Invoke();
                }
                else
                {
                    MessageHelper.ShowError("فشل في حفظ المشتريات");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في حفظ المشتريات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
} 