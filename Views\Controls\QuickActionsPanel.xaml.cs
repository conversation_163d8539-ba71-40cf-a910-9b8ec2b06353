using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.Views.Controls
{
    /// <summary>
    /// Interaction logic for QuickActionsPanel.xaml
    /// </summary>
    public partial class QuickActionsPanel : UserControl
    {
        public QuickActionsPanel()
        {
            InitializeComponent();
            DataContext = this;
            InitializeCommands();
        }

        #region Commands

        public ICommand AddPatientCommand { get; private set; }
        public ICommand AddAppointmentCommand { get; private set; }
        public ICommand AddInvoiceCommand { get; private set; }
        public ICommand AddSessionCommand { get; private set; }
        public ICommand ViewReportsCommand { get; private set; }
        public ICommand ManageInventoryCommand { get; private set; }
        public ICommand ManageEmployeesCommand { get; private set; }
        public ICommand OpenSettingsCommand { get; private set; }

        private void InitializeCommands()
        {
            AddPatientCommand = new RelayCommand(AddPatient);
            AddAppointmentCommand = new RelayCommand(AddAppointment);
            AddInvoiceCommand = new RelayCommand(AddInvoice);
            AddSessionCommand = new RelayCommand(AddSession);
            ViewReportsCommand = new RelayCommand(ViewReports);
            ManageInventoryCommand = new RelayCommand(ManageInventory);
            ManageEmployeesCommand = new RelayCommand(ManageEmployees);
            OpenSettingsCommand = new RelayCommand(OpenSettings);
        }

        #endregion

        #region Command Implementations

        private void AddPatient()
        {
            try
            {
                // إثارة حدث لطلب إضافة مريض جديد
                AddPatientRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إضافة المريض", ex.Message);
            }
        }

        private void AddAppointment()
        {
            try
            {
                // إثارة حدث لطلب إضافة موعد جديد
                AddAppointmentRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إضافة الموعد", ex.Message);
            }
        }

        private void AddInvoice()
        {
            try
            {
                // إثارة حدث لطلب إضافة فاتورة جديدة
                AddInvoiceRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إضافة الفاتورة", ex.Message);
            }
        }

        private void AddSession()
        {
            try
            {
                // إثارة حدث لطلب إضافة جلسة علاج جديدة
                AddSessionRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إضافة الجلسة", ex.Message);
            }
        }

        private void ViewReports()
        {
            try
            {
                // إثارة حدث لطلب عرض التقارير
                ViewReportsRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة التقارير", ex.Message);
            }
        }

        private void ManageInventory()
        {
            try
            {
                // إثارة حدث لطلب إدارة المخزون
                ManageInventoryRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إدارة المخزون", ex.Message);
            }
        }

        private void ManageEmployees()
        {
            try
            {
                // إثارة حدث لطلب إدارة الموظفين
                ManageEmployeesRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة إدارة الموظفين", ex.Message);
            }
        }

        private void OpenSettings()
        {
            try
            {
                // إثارة حدث لطلب فتح الإعدادات
                OpenSettingsRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError("خطأ في فتح نافذة الإعدادات", ex.Message);
            }
        }

        #endregion

        #region Helper Methods

        private void ShowError(string title, string message)
        {
            MessageBox.Show(
                $"{title}\n\nتفاصيل الخطأ:\n{message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }

        private void ShowSuccess(string message)
        {
            MessageBox.Show(
                message,
                "نجح",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        #endregion

        #region Events

        /// <summary>
        /// حدث طلب إضافة مريض جديد
        /// </summary>
        public event EventHandler AddPatientRequested;

        /// <summary>
        /// حدث طلب إضافة موعد جديد
        /// </summary>
        public event EventHandler AddAppointmentRequested;

        /// <summary>
        /// حدث طلب إضافة فاتورة جديدة
        /// </summary>
        public event EventHandler AddInvoiceRequested;

        /// <summary>
        /// حدث طلب إضافة جلسة علاج جديدة
        /// </summary>
        public event EventHandler AddSessionRequested;

        /// <summary>
        /// حدث طلب عرض التقارير
        /// </summary>
        public event EventHandler ViewReportsRequested;

        /// <summary>
        /// حدث طلب إدارة المخزون
        /// </summary>
        public event EventHandler ManageInventoryRequested;

        /// <summary>
        /// حدث طلب إدارة الموظفين
        /// </summary>
        public event EventHandler ManageEmployeesRequested;

        /// <summary>
        /// حدث طلب فتح الإعدادات
        /// </summary>
        public event EventHandler OpenSettingsRequested;

        #endregion

        #region Public Methods

        /// <summary>
        /// تفعيل أو تعطيل زر معين
        /// </summary>
        public void SetButtonEnabled(string buttonName, bool isEnabled)
        {
            try
            {
                var button = FindName($"{buttonName}Button") as Button;
                if (button != null)
                {
                    button.IsEnabled = isEnabled;
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إظهار رسالة للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل حالة الزر {buttonName}: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عداد التنبيهات لزر معين
        /// </summary>
        public void UpdateButtonBadge(string buttonName, int count)
        {
            try
            {
                // يمكن إضافة منطق لإظهار عدادات على الأزرار
                // مثل عدد المواعيد المعلقة أو الفواتير غير المدفوعة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الزر {buttonName}: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة تلميح مخصص لزر معين
        /// </summary>
        public void SetButtonTooltip(string buttonName, string tooltip)
        {
            try
            {
                var button = FindName($"{buttonName}Button") as Button;
                if (button != null)
                {
                    button.ToolTip = tooltip;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل تلميح الزر {buttonName}: {ex.Message}");
            }
        }

        #endregion
    }
}
