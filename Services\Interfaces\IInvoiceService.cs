using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الفواتير
    /// </summary>
    public interface IInvoiceService
    {
        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// جلب جميع الفواتير
        /// </summary>
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();

        /// <summary>
        /// جلب فاتورة بالمعرف
        /// </summary>
        Task<Invoice?> GetInvoiceByIdAsync(int id);

        /// <summary>
        /// جلب فاتورة برقم الفاتورة
        /// </summary>
        Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber);

        /// <summary>
        /// جلب فواتير مريض معين
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByPatientAsync(int patientId);

        /// <summary>
        /// إنشاء فاتورة جديدة
        /// </summary>
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// تحديث فاتورة
        /// </summary>
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// حذف فاتورة
        /// </summary>
        Task<bool> DeleteInvoiceAsync(int id);

        #endregion

        #region إدارة عناصر الفاتورة

        /// <summary>
        /// إضافة عنصر إلى فاتورة
        /// </summary>
        Task<InvoiceItem> AddInvoiceItemAsync(InvoiceItem item);

        /// <summary>
        /// تحديث عنصر فاتورة
        /// </summary>
        Task<InvoiceItem> UpdateInvoiceItemAsync(InvoiceItem item);

        /// <summary>
        /// حذف عنصر من فاتورة
        /// </summary>
        Task<bool> DeleteInvoiceItemAsync(int itemId);

        /// <summary>
        /// جلب عناصر فاتورة معينة
        /// </summary>
        Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId);

        #endregion

        #region حساب المبالغ

        /// <summary>
        /// حساب إجمالي الفاتورة
        /// </summary>
        Task<decimal> CalculateInvoiceTotalAsync(int invoiceId);

        /// <summary>
        /// تحديث مبالغ الفاتورة
        /// </summary>
        Task<bool> UpdateInvoiceAmountsAsync(int invoiceId);

        /// <summary>
        /// حساب الرصيد المتبقي للفاتورة
        /// </summary>
        Task<decimal> CalculateRemainingAmountAsync(int invoiceId);

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في الفواتير
        /// </summary>
        Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm);

        /// <summary>
        /// جلب الفواتير حسب التاريخ
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByDateAsync(DateTime date);

        /// <summary>
        /// جلب الفواتير في فترة زمنية
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب الفواتير حسب الحالة
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(string status);

        /// <summary>
        /// جلب الفواتير غير المدفوعة
        /// </summary>
        Task<IEnumerable<Invoice>> GetUnpaidInvoicesAsync();

        /// <summary>
        /// جلب الفواتير المتأخرة
        /// </summary>
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();

        #endregion

        #region الترقيم التلقائي

        /// <summary>
        /// الحصول على رقم الفاتورة التالي
        /// </summary>
        Task<string> GetNextInvoiceNumberAsync();

        /// <summary>
        /// التحقق من توفر رقم فاتورة
        /// </summary>
        Task<bool> IsInvoiceNumberAvailableAsync(string invoiceNumber);

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// جلب إحصائيات الفواتير
        /// </summary>
        Task<InvoiceStatistics> GetInvoiceStatisticsAsync();

        /// <summary>
        /// جلب إجمالي المبيعات في فترة زمنية
        /// </summary>
        Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إجمالي الديون
        /// </summary>
        Task<decimal> GetTotalDebtsAsync();

        /// <summary>
        /// جلب أفضل المرضى دفعاً
        /// </summary>
        Task<IEnumerable<Patient>> GetTopPayingPatientsAsync(int count = 10);

        #endregion

        #region الطباعة والتصدير

        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        Task<bool> PrintInvoiceAsync(int invoiceId);

        /// <summary>
        /// تصدير فاتورة إلى PDF
        /// </summary>
        Task<byte[]> ExportInvoiceToPdfAsync(int invoiceId);

        /// <summary>
        /// تصدير فاتورة إلى Excel
        /// </summary>
        Task<byte[]> ExportInvoiceToExcelAsync(int invoiceId);

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة بيانات الفاتورة
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// التحقق من صحة بيانات عنصر الفاتورة
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateInvoiceItemAsync(InvoiceItem item);

        #endregion
    }

    /// <summary>
    /// نموذج إحصائيات الفواتير
    /// </summary>
    public class InvoiceStatistics
    {
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int UnpaidInvoices { get; set; }
        public int OverdueInvoices { get; set; }
        public int CancelledInvoices { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal UnpaidAmount { get; set; }
        public decimal OverdueAmount { get; set; }
        public Dictionary<string, int> InvoicesByStatus { get; set; } = new();
        public Dictionary<string, decimal> SalesByMonth { get; set; } = new();
        public Dictionary<string, int> InvoicesByMonth { get; set; } = new();
    }
} 