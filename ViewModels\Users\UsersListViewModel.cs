using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Views.Users;
using Microsoft.Extensions.DependencyInjection;

namespace AqlanCenterProApp.ViewModels.Users
{
    public class UsersListViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;
        private readonly IActivityLogService _activityLogService;

        private ObservableCollection<User> _users;
        private User? _selectedUser;
        private string _searchTerm = string.Empty;
        private Role? _selectedRoleFilter;
        private bool _showActiveOnly = true;

        public UsersListViewModel(IUserService userService, IRoleService roleService, IActivityLogService activityLogService)
        {
            _userService = userService;
            _roleService = roleService;
            _activityLogService = activityLogService;

            Users = new ObservableCollection<User>();
            Roles = new ObservableCollection<Role>();

            LoadUsersCommand = new RelayCommand(async () => await LoadUsersAsync());
            SearchCommand = new RelayCommand(async () => await SearchUsersAsync());
            AddUserCommand = new RelayCommand(() => AddUser());
            EditUserCommand = new RelayCommand(() => EditUser(), () => CanEdit);
            DeleteUserCommand = new RelayCommand(async () => await DeleteUserAsync(), () => CanDelete);
            ActivateUserCommand = new RelayCommand(async () => await ActivateUserAsync(), () => CanActivate);
            DeactivateUserCommand = new RelayCommand(async () => await DeactivateUserAsync(), () => CanDeactivate);
            ChangePasswordCommand = new RelayCommand(() => ChangePassword(), () => CanEdit);
            ViewActivityLogsCommand = new RelayCommand(() => ViewActivityLogs(), () => CanViewLogs);
            ManagePermissionsCommand = new RelayCommand(() => ManagePermissions(), () => CanManagePermissions);
            ExportCommand = new RelayCommand(async () => await ExportUsersAsync());
            PrintCommand = new RelayCommand(async () => await PrintUsersAsync());

            _ = LoadInitialDataAsync();
        }

        public ObservableCollection<User> Users
        {
            get => _users;
            set => SetProperty(ref _users, value);
        }

        public User? SelectedUser
        {
            get => _selectedUser;
            set
            {
                SetProperty(ref _selectedUser, value);
                OnPropertyChanged(nameof(CanEdit));
                OnPropertyChanged(nameof(CanDelete));
                OnPropertyChanged(nameof(CanActivate));
                OnPropertyChanged(nameof(CanDeactivate));
                OnPropertyChanged(nameof(CanViewLogs));
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set => SetProperty(ref _searchTerm, value);
        }

        public Role? SelectedRoleFilter
        {
            get => _selectedRoleFilter;
            set
            {
                SetProperty(ref _selectedRoleFilter, value);
                _ = LoadUsersAsync();
            }
        }

        public bool ShowActiveOnly
        {
            get => _showActiveOnly;
            set
            {
                SetProperty(ref _showActiveOnly, value);
                _ = LoadUsersAsync();
            }
        }

        public ObservableCollection<Role> Roles { get; }

        public bool CanEdit => SelectedUser != null;
        public bool CanDelete => SelectedUser != null && SelectedUser.Id != 1; // Cannot delete admin
        public bool CanActivate => SelectedUser != null && !SelectedUser.IsActive;
        public bool CanDeactivate => SelectedUser != null && SelectedUser.IsActive && SelectedUser.Id != 1; // Cannot deactivate admin
        public bool CanViewLogs => SelectedUser != null;
        public bool CanManagePermissions => true; // All users can manage permissions

        public ICommand LoadUsersCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand { get; }
        public ICommand DeleteUserCommand { get; }
        public ICommand ActivateUserCommand { get; }
        public ICommand DeactivateUserCommand { get; }
        public ICommand ChangePasswordCommand { get; }
        public ICommand ViewActivityLogsCommand { get; }
        public ICommand ManagePermissionsCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }

        private async Task LoadInitialDataAsync()
        {
            IsBusy = true;
            try
            {
                var roles = await _roleService.GetActiveRolesAsync();
                Roles.Clear();
                foreach (var role in roles)
                {
                    Roles.Add(role);
                }

                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading initial data: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        public async Task LoadUsersAsync()
        {
            IsBusy = true;
            try
            {
                // تحميل بيانات وهمية مباشرة لتجنب مشاكل قاعدة البيانات
                await LoadSampleUsersAsync();

                // محاولة تحميل البيانات الحقيقية في الخلفية
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var users = await _userService.GetAllUsersAsync().ConfigureAwait(false);

                        // Apply filters
                        var filteredUsers = users.AsEnumerable();

                        if (ShowActiveOnly)
                            filteredUsers = filteredUsers.Where(u => u.IsActive);

                        if (SelectedRoleFilter != null)
                            filteredUsers = filteredUsers.Where(u => u.RoleId == SelectedRoleFilter.Id);

                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            Users.Clear();
                            foreach (var user in filteredUsers.OrderBy(u => u.FullName))
                            {
                                Users.Add(user);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الحقيقية: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المستخدمين: {ex.Message}");

                // تحميل بيانات وهمية في حالة الخطأ
                await LoadSampleUsersAsync();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadSampleUsersAsync()
        {
            try
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Users.Clear();
                    // إضافة مستخدم وهمي للاختبار
                    Users.Add(new User
                    {
                        Id = 1,
                        Username = "admin",
                        FullName = "مدير النظام",
                        Email = "<EMAIL>",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    });
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الوهمية: {ex.Message}");
            }
        }

        private async Task SearchUsersAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadUsersAsync();
                return;
            }

            IsBusy = true;
            try
            {
                var users = await _userService.SearchUsersAsync(SearchTerm);

                // Apply filters
                var filteredUsers = users.AsEnumerable();

                if (ShowActiveOnly)
                    filteredUsers = filteredUsers.Where(u => u.IsActive);

                if (SelectedRoleFilter != null)
                    filteredUsers = filteredUsers.Where(u => u.RoleId == SelectedRoleFilter.Id);

                Users.Clear();
                foreach (var user in filteredUsers.OrderBy(u => u.FullName))
                {
                    Users.Add(user);
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error searching users: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddUser()
        {
            // TODO: Open AddEditUserWindow
            System.Diagnostics.Debug.WriteLine("Add User clicked");
        }

        private void EditUser()
        {
            if (SelectedUser == null) return;

            // TODO: Open AddEditUserWindow with selected user
            System.Diagnostics.Debug.WriteLine($"Edit User clicked: {SelectedUser.FullName}");
        }

        private async Task DeleteUserAsync()
        {
            if (SelectedUser == null) return;

            // TODO: Show confirmation dialog
            try
            {
                var success = await _userService.DeleteUserAsync(SelectedUser.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Delete", "User", SelectedUser.Id, $"Deleted user: {SelectedUser.FullName}");
                    await LoadUsersAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error deleting user: {ex.Message}");
            }
        }

        private async Task ActivateUserAsync()
        {
            if (SelectedUser == null) return;

            try
            {
                var success = await _userService.ActivateUserAsync(SelectedUser.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Activate", "User", SelectedUser.Id, $"Activated user: {SelectedUser.FullName}");
                    await LoadUsersAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error activating user: {ex.Message}");
            }
        }

        private async Task DeactivateUserAsync()
        {
            if (SelectedUser == null) return;

            // TODO: Show confirmation dialog
            try
            {
                var success = await _userService.DeactivateUserAsync(SelectedUser.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Deactivate", "User", SelectedUser.Id, $"Deactivated user: {SelectedUser.FullName}");
                    await LoadUsersAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error deactivating user: {ex.Message}");
            }
        }

        private void ChangePassword()
        {
            if (SelectedUser == null) return;

            // TODO: Open ChangePasswordWindow
            System.Diagnostics.Debug.WriteLine($"Change Password clicked: {SelectedUser.FullName}");
        }

        private void ViewActivityLogs()
        {
            if (SelectedUser == null) return;

            // TODO: Open ActivityLogsWindow for selected user
            System.Diagnostics.Debug.WriteLine($"View Activity Logs clicked: {SelectedUser.FullName}");
        }

        private void ManagePermissions()
        {
            try
            {
                var permissionService = App.Services?.GetService<IPermissionService>();
                var userService = App.Services?.GetService<IUserService>();

                if (permissionService != null && userService != null)
                {
                    var permissionManagementViewModel = new PermissionManagementViewModel(permissionService, userService);
                    var permissionManagementView = new PermissionManagementView(permissionManagementViewModel);
                    permissionManagementView.Show();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PermissionService or UserService not found in DI container");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening permission management: {ex.Message}");
            }
        }

        private async Task ExportUsersAsync()
        {
            // TODO: Implement export functionality
            System.Diagnostics.Debug.WriteLine("Export Users clicked");
        }

        private async Task PrintUsersAsync()
        {
            // TODO: Implement print functionality
            System.Diagnostics.Debug.WriteLine("Print Users clicked");
        }
    }
}