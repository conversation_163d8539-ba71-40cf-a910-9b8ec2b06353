using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Invoices;

namespace AqlanCenterProApp.Views.Invoices
{
    /// <summary>
    /// Interaction logic for InvoicesView.xaml
    /// </summary>
    public partial class InvoicesView : UserControl
    {
        public InvoicesView()
        {
            InitializeComponent();
        }

        public InvoicesView(InvoicesListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += InvoicesView_Loaded;
        }

        private void InvoicesView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is InvoicesListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadInvoicesAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadInvoicesAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات الفواتير: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في InvoicesView_Loaded: {ex.Message}");
            }
        }
    }
}