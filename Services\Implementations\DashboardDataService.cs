using AqlanCenterProApp.Data;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AqlanCenterProApp.Services.Implementations
{
    public class DashboardDataService : IDashboardDataService
    {
        private readonly AqlanCenterDbContext _context;

        public DashboardDataService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<int> GetTotalPatientsAsync()
        {
            return await _context.Patients.CountAsync();
        }

        public async Task<int> GetTotalAppointmentsAsync()
        {
            var today = DateTime.Today;
            return await _context.Appointments.CountAsync(a => a.AppointmentDateTime.Date == today);
        }

        public async Task<decimal> GetTotalRevenueAsync()
        {
            var today = DateTime.Today;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            return await _context.Receipts
                .Where(r => r.ReceiptDate >= startOfMonth)
                .SumAsync(r => r.Amount);
        }

        public async Task<int> GetPendingLabOrdersAsync()
        {
            return await _context.LabOrders.CountAsync(o => o.Status != "Completed");
        }

        public async Task<int> GetNewPatientsThisMonthAsync()
        {
            var today = DateTime.Today;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            return await _context.Patients.CountAsync(p => p.CreatedAt >= startOfMonth);
        }

        public async Task<decimal> GetTodayRevenueAsync()
        {
            var today = DateTime.Today;
            return await _context.Receipts
                .Where(r => r.ReceiptDate.Date == today)
                .SumAsync(r => r.Amount);
        }

        public async Task<int> GetLowStockItemsAsync()
        {
            return await _context.InventoryItems.CountAsync(i => i.CurrentQuantity <= i.MinimumQuantity);
        }

        public async Task<int> GetTotalDoctorsAsync()
        {
            return await _context.Doctors.CountAsync();
        }
    }
}