using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج سند الصرف لمصروفات العيادة
    /// </summary>
    public class PaymentVoucher : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        public DateTime VoucherDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(100)]
        public string BeneficiaryName { get; set; } = string.Empty; // اسم الجهة المستفيدة

        [StringLength(50)]
        public string? BeneficiaryType { get; set; } = "أخرى"; // موظف، معمل، مورد، أخرى

        public int? EmployeeId { get; set; } // إذا كان المستفيد موظف

        public int? SupplierId { get; set; } // إذا كان المستفيد مورد

        public int? LabId { get; set; } // إذا كان المستفيد معمل

        public int? DoctorId { get; set; } // إذا كان المستفيد طبيب

        [Required]
        [StringLength(100)]
        public string ExpenseType { get; set; } = string.Empty; // نوع المصروف

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty; // وصف المصروف

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [StringLength(20)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، تحويل، شيك

        [StringLength(100)]
        public string? ReferenceNumber { get; set; } // رقم المرجع (رقم الشيك، رقم التحويل)

        [StringLength(20)]
        public string Status { get; set; } = "مكتمل"; // مكتمل، معلق، ملغي

        [StringLength(500)]
        public new string? Notes { get; set; }

        [StringLength(100)]
        public string? IssuedBy { get; set; } // من أصدر السند

        [StringLength(100)]
        public string? ApprovedBy { get; set; } // من وافق على السند

        public DateTime? ApprovalDate { get; set; }

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee? Employee { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("LabId")]
        public virtual Lab? Lab { get; set; }
    }
}