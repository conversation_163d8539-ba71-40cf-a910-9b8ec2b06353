using System.Collections.ObjectModel;
using System.ComponentModel;
using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeDocumentsViewModel : INotifyPropertyChanged
    {
        private ObservableCollection<EmployeeDocument> _documentRecords = new ObservableCollection<EmployeeDocument>();
        public ObservableCollection<EmployeeDocument> DocumentRecords
        {
            get => _documentRecords;
            set { _documentRecords = value; OnPropertyChanged(nameof(DocumentRecords)); }
        }

        public EmployeeDocumentsViewModel(Employee employee)
        {
            // Load documents for the given employee
            if (employee?.Documents != null)
                DocumentRecords = new ObservableCollection<EmployeeDocument>(employee.Documents);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 