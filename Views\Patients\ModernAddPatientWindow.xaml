<Window x:Class="AqlanCenterProApp.Views.Patients.ModernAddPatientWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مريض جديد"
        Height="750"
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="70"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="#4A90E2"
                CornerRadius="0">
            <TextBlock Text="إضافة مريض جديد"
                       FontSize="20"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        </Border>

        <!-- Patient Info Header -->
        <Border Grid.Row="1"
                Background="#E8E8E8"
                BorderBrush="#D0D0D0"
                BorderThickness="0,1,0,1">
            <StackPanel Orientation="Vertical"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <TextBlock Text="معلومات المريض"
                           FontSize="14"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"/>
                <TextBlock x:Name="PatientInfoSubtitle"
                           Text="مريض جديد - رقم الملف: 8500"
                           FontSize="11"
                           Foreground="#666666"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="2"
                      VerticalScrollBarVisibility="Auto"
                      Margin="20">
            <StackPanel>

                <!-- الاسم الكامل -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="الاسم الكامل:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1"
                             x:Name="FullNameTextBox"
                             Height="35"
                             Padding="8"
                             BorderBrush="#CCCCCC"
                             BorderThickness="1"/>
                </Grid>

                <!-- رقم الملف -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="رقم الملف:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1"
                             x:Name="FileNumberTextBox"
                             Text="8500"
                             Height="35"
                             Padding="8"
                             BorderBrush="#CCCCCC"
                             BorderThickness="1"
                             IsReadOnly="True"
                             Background="#F0F0F0"/>
                </Grid>

                <!-- الجنس -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="الجنس:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="1"
                              x:Name="GenderComboBox"
                              Height="35"
                              Padding="8"
                              BorderBrush="#CCCCCC"
                              BorderThickness="1"
                              SelectedIndex="0">
                        <ComboBoxItem Content="ذكر"/>
                        <ComboBoxItem Content="أنثى"/>
                    </ComboBox>
                </Grid>

                <!-- تاريخ الميلاد -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="تاريخ الميلاد:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <DatePicker Grid.Column="1"
                                x:Name="DateOfBirthPicker"
                                Height="35"
                                BorderBrush="#CCCCCC"
                                BorderThickness="1"
                                SelectedDate="{x:Static sys:DateTime.Now}"
                                xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </Grid>

                <!-- رقم الهاتف -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="رقم الهاتف:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1"
                             x:Name="PhoneTextBox"
                             Height="35"
                             Padding="8"
                             BorderBrush="#CCCCCC"
                             BorderThickness="1"/>
                </Grid>

                <!-- العنوان -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="العنوان:"
                               FontWeight="Bold"
                               VerticalAlignment="Top"
                               HorizontalAlignment="Right"
                               Margin="0,8,10,0"/>
                    <TextBox Grid.Column="1"
                             x:Name="AddressTextBox"
                             Height="60"
                             Padding="8"
                             BorderBrush="#CCCCCC"
                             BorderThickness="1"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"/>
                </Grid>

                <!-- تصنيف المريض -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="تصنيف المريض:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="1"
                              x:Name="CategoryComboBox"
                              Height="35"
                              Padding="8"
                              BorderBrush="#CCCCCC"
                              BorderThickness="1"
                              SelectedIndex="0">
                        <ComboBoxItem Content="جديد"/>
                        <ComboBoxItem Content="متابعة"/>
                        <ComboBoxItem Content="VIP"/>
                        <ComboBoxItem Content="طارئ"/>
                    </ComboBox>
                </Grid>

                <!-- مبلغ المعاينة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               Text="مبلغ المعاينة:"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Right"
                               Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1"
                             x:Name="ConsultationFeeTextBox"
                             Text="50.00"
                             Height="35"
                             Padding="8"
                             BorderBrush="#CCCCCC"
                             BorderThickness="1"/>
                </Grid>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="3"
                Background="White"
                BorderBrush="#D0D0D0"
                BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <Button x:Name="SaveButton"
                        Content="حفظ المريض"
                        Width="120"
                        Height="40"
                        Margin="0,0,10,0"
                        Background="#5CB85C"
                        Foreground="White"
                        BorderThickness="0"
                        FontWeight="Bold"
                        Click="SaveButton_Click"/>
                <Button x:Name="PrintReceiptButton"
                        Content="طباعة سند المعاينة"
                        Width="140"
                        Height="40"
                        Margin="0,0,10,0"
                        Background="#17A2B8"
                        Foreground="White"
                        BorderThickness="0"
                        FontWeight="Bold"
                        Click="PrintReceiptButton_Click"
                        IsEnabled="False"/>
                <Button x:Name="CancelButton"
                        Content="إلغاء"
                        Width="120"
                        Height="40"
                        Background="#D9534F"
                        Foreground="White"
                        BorderThickness="0"
                        FontWeight="Bold"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
