<Window x:Class="AqlanCenterProApp.Views.Receipts.AddEditReceiptView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
        xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <Window.Resources>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <controls:HeaderControl Grid.Row="0"/>
        
        <!-- المحتوى الرئيسي مع ScrollViewer -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly">
            <StackPanel>
                <Border Background="White" Margin="20,20,20,10" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,20,0">
                            <TextBlock Text="المريض:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Patients}" SelectedItem="{Binding SelectedPatient}" DisplayMemberPath="FullName" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="0">
                            <TextBlock Text="رقم السند:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Receipt.ReceiptNumber}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1" IsReadOnly="True"/>
                        </StackPanel>
                        <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,20,20,0">
                            <TextBlock Text="تاريخ السند:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding Receipt.ReceiptDate}" Height="35" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="1">
                            <TextBlock Text="المبلغ:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Receipt.Amount}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="0" Grid.Row="2" Margin="0,20,20,0">
                            <TextBlock Text="الغرض من الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Receipt.Purpose}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="2">
                            <TextBlock Text="طريقة الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding PaymentMethods}" SelectedItem="{Binding Receipt.PaymentMethod}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="0" Grid.Row="3" Margin="0,20,20,0">
                            <TextBlock Text="الحالة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Receipt.Status}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1" IsReadOnly="True"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="3">
                            <TextBlock Text="المستلم:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Receipt.ReceivedBy}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <Button Content="{Binding SaveButtonText}" Command="{Binding SaveCommand}" Style="{StaticResource ActionButtonStyle}" Background="#4CAF50" Width="100"/>
                    <Button Content="إلغاء" Command="{Binding CancelCommand}" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Width="100"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
        
        <controls:FooterControl Grid.Row="2"/>
        <Grid Grid.Row="0" Grid.RowSpan="3" Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="10" Margin="0,0,0,10"/>
                <TextBlock Text="جاري الحفظ..." Foreground="White" HorizontalAlignment="Center" FontSize="16"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 