using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Suppliers
{
    public class AddEditSupplierViewModel : BaseViewModel
    {
        private readonly ISupplierService _supplierService;
        private Supplier _supplier;
        private bool _isEditMode;

        public AddEditSupplierViewModel(ISupplierService supplierService, Supplier? supplier = null)
        {
            _supplierService = supplierService;
            _supplier = supplier ?? new Supplier();
            _isEditMode = supplier != null;

            SaveCommand = new RelayCommand(async () => await SaveAsync());
            CancelCommand = new RelayCommand(() => Cancel());
            ValidateCommand = new RelayCommand(() => Validate());

            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                _supplier.IsActive = true;
            }
        }

        public Supplier Supplier
        {
            get => _supplier;
            set => SetProperty(ref _supplier, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public string WindowTitle => IsEditMode ? "تعديل المورد" : "إضافة مورد جديد";
        public string SaveButtonText => IsEditMode ? "تحديث" : "حفظ";

        // Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ValidateCommand { get; }

        private async Task SaveAsync()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                IsBusy = true;

                if (IsEditMode)
                {
                    await _supplierService.UpdateSupplierAsync(Supplier);
                    MessageHelper.ShowSuccess("تم تحديث بيانات المورد بنجاح");
                }
                else
                {
                    await _supplierService.CreateSupplierAsync(Supplier);
                    MessageHelper.ShowSuccess("تم إضافة المورد بنجاح");
                }

                // إغلاق النافذة أو العودة للقائمة
                OnSaveCompleted();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في حفظ بيانات المورد: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void Cancel()
        {
            OnCancelRequested();
        }

        private bool Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Supplier.Name))
            {
                errors.Add("اسم المورد مطلوب");
            }

            if (string.IsNullOrWhiteSpace(Supplier.Phone) && string.IsNullOrWhiteSpace(Supplier.Mobile))
            {
                errors.Add("يجب إدخال رقم هاتف واحد على الأقل");
            }

            if (!string.IsNullOrWhiteSpace(Supplier.Email) && !IsValidEmail(Supplier.Email))
            {
                errors.Add("البريد الإلكتروني غير صحيح");
            }

            if (!string.IsNullOrWhiteSpace(Supplier.TaxNumber))
            {
                // التحقق من عدم تكرار الرقم الضريبي
                var existingSupplier = _supplierService.GetSupplierByTaxNumberAsync(Supplier.TaxNumber).Result;
                if (existingSupplier != null && existingSupplier.Id != Supplier.Id)
                {
                    errors.Add("الرقم الضريبي مستخدم بالفعل");
                }
            }

            if (errors.Any())
            {
                MessageHelper.ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // Events
        public event Action? SaveCompleted;
        public event Action? CancelRequested;

        protected virtual void OnSaveCompleted()
        {
            SaveCompleted?.Invoke();
        }

        protected virtual void OnCancelRequested()
        {
            CancelRequested?.Invoke();
        }
    }
} 