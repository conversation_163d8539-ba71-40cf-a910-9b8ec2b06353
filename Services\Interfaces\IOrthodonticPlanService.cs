using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة خطط علاج التقويم
    /// </summary>
    public interface IOrthodonticPlanService
    {
        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// جلب جميع خطط التقويم
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetAllPlansAsync();

        /// <summary>
        /// جلب خطة تقويم بالمعرف
        /// </summary>
        Task<OrthodonticPlan?> GetPlanByIdAsync(int id);

        /// <summary>
        /// جلب خطط تقويم مريض معين
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetPlansByPatientAsync(int patientId);

        /// <summary>
        /// جلب خطط تقويم طبيب معين
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetPlansByDoctorAsync(int doctorId);

        /// <summary>
        /// إنشاء خطة تقويم جديدة
        /// </summary>
        Task<OrthodonticPlan> CreatePlanAsync(OrthodonticPlan plan);

        /// <summary>
        /// تحديث خطة تقويم
        /// </summary>
        Task<OrthodonticPlan> UpdatePlanAsync(OrthodonticPlan plan);

        /// <summary>
        /// حذف خطة تقويم
        /// </summary>
        Task<bool> DeletePlanAsync(int id);

        #endregion

        #region إدارة المواعيد التلقائية

        /// <summary>
        /// إنشاء مواعيد تلقائية لخطة تقويم
        /// </summary>
        Task<bool> GenerateAppointmentsAsync(int planId);

        /// <summary>
        /// إنشاء موعد واحد لخطة تقويم
        /// </summary>
        Task<Appointment?> CreateNextAppointmentAsync(int planId);

        /// <summary>
        /// تحديث حالة خطة التقويم بناءً على المواعيد المكتملة
        /// </summary>
        Task UpdatePlanProgressAsync(int planId);

        /// <summary>
        /// إعادة جدولة جميع المواعيد المتبقية لخطة تقويم
        /// </summary>
        Task<bool> RescheduleRemainingAppointmentsAsync(int planId, DateTime newStartDate);

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في خطط التقويم
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> SearchPlansAsync(string searchTerm);

        /// <summary>
        /// جلب خطط التقويم النشطة
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetActivePlansAsync();

        /// <summary>
        /// جلب خطط التقويم المكتملة
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetCompletedPlansAsync();

        /// <summary>
        /// جلب خطط التقويم المتأخرة
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetOverduePlansAsync();

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// جلب إحصائيات خطط التقويم
        /// </summary>
        Task<OrthodonticStatistics> GetStatisticsAsync();

        /// <summary>
        /// جلب خطط التقويم التي تحتاج متابعة
        /// </summary>
        Task<IEnumerable<OrthodonticPlan>> GetPlansNeedingFollowUpAsync();

        /// <summary>
        /// جلب المرضى المتخلفين عن جلسات التقويم
        /// </summary>
        Task<IEnumerable<Patient>> GetPatientsWithMissedOrthodonticSessionsAsync();

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من توفر الطبيب في التاريخ والوقت المحددين
        /// </summary>
        Task<bool> IsDoctorAvailableAsync(int doctorId, DateTime dateTime, int durationMinutes);

        /// <summary>
        /// التحقق من صحة بيانات خطة التقويم
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidatePlanAsync(OrthodonticPlan plan);

        #endregion
    }

    /// <summary>
    /// نموذج إحصائيات خطط التقويم
    /// </summary>
    public class OrthodonticStatistics
    {
        public int TotalPlans { get; set; }
        public int ActivePlans { get; set; }
        public int CompletedPlans { get; set; }
        public int OverduePlans { get; set; }
        public int TotalSessions { get; set; }
        public int CompletedSessions { get; set; }
        public int MissedSessions { get; set; }
        public double AverageProgressPercentage { get; set; }
        public int PatientsWithActivePlans { get; set; }
        public Dictionary<string, int> PlansByStatus { get; set; } = new();
        public Dictionary<string, int> SessionsByMonth { get; set; } = new();
    }
} 