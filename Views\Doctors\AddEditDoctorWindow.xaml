<Window x:Class="AqlanCenterProApp.Views.Doctors.AddEditDoctorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل طبيب"
        Height="700"
        Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5">

        <Window.Resources>
                <Style TargetType="TextBox">
                        <Setter Property="Padding"
                                Value="10"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="BorderBrush"
                                Value="#DDD"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                </Style>

                <Style TargetType="ComboBox">
                        <Setter Property="Padding"
                                Value="10"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="BorderBrush"
                                Value="#DDD"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                </Style>

                <Style TargetType="DatePicker">
                        <Setter Property="Padding"
                                Value="10"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="BorderBrush"
                                Value="#DDD"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                        <Setter Property="Language"
                                Value="ar-SA"/>
                        <Setter Property="FlowDirection"
                                Value="RightToLeft"/>
                </Style>

                <Style TargetType="Label">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Foreground"
                                Value="#333"/>
                        <Setter Property="Margin"
                                Value="0,10,0,5"/>
                </Style>
        </Window.Resources>

        <Grid Margin="20">
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- العنوان -->
                <TextBlock Grid.Row="0"
                           Text="{Binding WindowTitle}"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="#2196F3"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <!-- محتوى النموذج -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                                <!-- المعلومات الأساسية -->
                                <GroupBox Header="المعلومات الأساسية"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="#4CAF50"
                                          Margin="0,0,0,20">
                                        <Grid Margin="10">
                                                <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="20"/>
                                                        <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- الاسم الكامل -->
                                                <Label Grid.Row="0"
                                                       Grid.Column="0"
                                                       Content="الاسم الكامل *"/>
                                                <TextBox Grid.Row="0"
                                                         Grid.Column="0"
                                                         Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                                                         Margin="0,35,0,5"/>

                                                <!-- التخصص -->
                                                <Label Grid.Row="0"
                                                       Grid.Column="2"
                                                       Content="التخصص *"/>
                                                <ComboBox Grid.Row="0"
                                                          Grid.Column="2"
                                                          SelectedValue="{Binding Specialization, UpdateSourceTrigger=PropertyChanged}"
                                                          IsEditable="True"
                                                          Margin="0,35,0,5">
                                                        <ComboBoxItem Content="طب أسنان عام"/>
                                                        <ComboBoxItem Content="تقويم الأسنان"/>
                                                        <ComboBoxItem Content="جراحة الفم والأسنان"/>
                                                        <ComboBoxItem Content="طب أسنان الأطفال"/>
                                                        <ComboBoxItem Content="تركيبات الأسنان"/>
                                                        <ComboBoxItem Content="علاج الجذور"/>
                                                        <ComboBoxItem Content="أمراض اللثة"/>
                                                        <ComboBoxItem Content="طب الأسنان التجميلي"/>
                                                        <ComboBoxItem Content="زراعة الأسنان"/>
                                                        <ComboBoxItem Content="أشعة الأسنان"/>
                                                        <ComboBoxItem Content="طب الفم"/>
                                                        <ComboBoxItem Content="تخدير الأسنان"/>
                                                </ComboBox>

                                                <!-- رقم الهاتف -->
                                                <Label Grid.Row="1"
                                                       Grid.Column="0"
                                                       Content="رقم الهاتف *"/>
                                                <TextBox Grid.Row="1"
                                                         Grid.Column="0"
                                                         Text="{Binding Mobile, UpdateSourceTrigger=PropertyChanged}"
                                                         Margin="0,35,0,5"/>

                                                <!-- البريد الإلكتروني -->
                                                <Label Grid.Row="1"
                                                       Grid.Column="2"
                                                       Content="البريد الإلكتروني"/>
                                                <TextBox Grid.Row="1"
                                                         Grid.Column="2"
                                                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                                                         Margin="0,35,0,5"/>

                                                <!-- الجنس -->
                                                <Label Grid.Row="2"
                                                       Grid.Column="0"
                                                       Content="الجنس"/>
                                                <ComboBox Grid.Row="2"
                                                          Grid.Column="0"
                                                          Text="{Binding Gender, UpdateSourceTrigger=PropertyChanged}"
                                                          Margin="0,35,0,5">
                                                        <ComboBoxItem Content="ذكر"/>
                                                        <ComboBoxItem Content="أنثى"/>
                                                </ComboBox>

                                                <!-- الجنسية -->
                                                <Label Grid.Row="2"
                                                       Grid.Column="2"
                                                       Content="الجنسية"/>
                                                <TextBox Grid.Row="2"
                                                         Grid.Column="2"
                                                         Text="{Binding Nationality, UpdateSourceTrigger=PropertyChanged}"
                                                         Margin="0,35,0,5"/>

                                                <!-- تاريخ الانضمام -->
                                                <Label Grid.Row="3"
                                                       Grid.Column="0"
                                                       Content="تاريخ الانضمام"/>
                                                <DatePicker Grid.Row="3"
                                                            Grid.Column="0"
                                                            SelectedDate="{Binding JoinDate, UpdateSourceTrigger=PropertyChanged}"
                                                            Margin="0,35,0,5"/>

                                                <!-- الحالة -->
                                                <Label Grid.Row="3"
                                                       Grid.Column="2"
                                                       Content="الحالة"/>
                                                <ComboBox Grid.Row="3"
                                                          Grid.Column="2"
                                                          Text="{Binding Status, UpdateSourceTrigger=PropertyChanged}"
                                                          Margin="0,35,0,5">
                                                        <ComboBoxItem Content="نشط"/>
                                                        <ComboBoxItem Content="غير نشط"/>
                                                        <ComboBoxItem Content="في إجازة"/>
                                                        <ComboBoxItem Content="منتهي العقد"/>
                                                </ComboBox>
                                        </Grid>
                                </GroupBox>

                                <!-- معلومات العمل -->
                                <GroupBox Header="معلومات العمل"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="#FF9800"
                                          Margin="0,0,0,20">
                                        <Grid Margin="10">
                                                <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="20"/>
                                                        <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- نوع العقد -->
                                                <Label Grid.Row="0"
                                                       Grid.Column="0"
                                                       Content="نوع العقد"/>
                                                <ComboBox Grid.Row="0"
                                                          Grid.Column="0"
                                                          Text="{Binding ContractType, UpdateSourceTrigger=PropertyChanged}">
                                                        <ComboBoxItem Content="دائم"/>
                                                        <ComboBoxItem Content="مؤقت"/>
                                                        <ComboBoxItem Content="دوام جزئي"/>
                                                        <ComboBoxItem Content="استشاري"/>
                                                </ComboBox>

                                                <!-- نسبة العمولة -->
                                                <Label Grid.Row="0"
                                                       Grid.Column="2"
                                                       Content="نسبة العمولة"/>
                                                <StackPanel Grid.Row="0"
                                                            Grid.Column="2"
                                                            Orientation="Horizontal"
                                                            Margin="0,30,0,5">
                                                        <TextBox Text="{Binding CommissionPercentage, UpdateSourceTrigger=PropertyChanged}"
                                                                 Width="120"
                                                                 HorizontalAlignment="Right"/>
                                                        <ComboBox SelectedValue="{Binding CommissionCurrency, UpdateSourceTrigger=PropertyChanged}"
                                                                  Width="80"
                                                                  Margin="5,0,0,0">
                                                                <ComboBoxItem Content="ر.ي"/>
                                                                <ComboBoxItem Content="ر.س"/>
                                                                <ComboBoxItem Content="$"/>
                                                        </ComboBox>
                                                </StackPanel>

                                                <!-- الراتب الثابت -->
                                                <Label Grid.Row="1"
                                                       Grid.Column="0"
                                                       Content="الراتب الثابت"/>
                                                <StackPanel Grid.Row="1"
                                                            Grid.Column="0"
                                                            Orientation="Horizontal"
                                                            Margin="0,30,0,5">
                                                        <TextBox Text="{Binding FixedSalary, UpdateSourceTrigger=PropertyChanged}"
                                                                 Width="120"
                                                                 HorizontalAlignment="Right"/>
                                                        <ComboBox SelectedValue="{Binding SalaryCurrency, UpdateSourceTrigger=PropertyChanged}"
                                                                  Width="80"
                                                                  Margin="5,0,0,0">
                                                                <ComboBoxItem Content="ر.ي"/>
                                                                <ComboBoxItem Content="ر.س"/>
                                                                <ComboBoxItem Content="$"/>
                                                        </ComboBox>
                                                </StackPanel>

                                                <!-- رقم الترخيص -->
                                                <Label Grid.Row="1"
                                                       Grid.Column="2"
                                                       Content="رقم الترخيص"/>
                                                <TextBox Grid.Row="1"
                                                         Grid.Column="2"
                                                         Text="{Binding LicenseNumber, UpdateSourceTrigger=PropertyChanged}"/>

                                                <!-- تاريخ انتهاء الترخيص -->
                                                <Label Grid.Row="2"
                                                       Grid.Column="0"
                                                       Content="تاريخ انتهاء الترخيص"/>
                                                <DatePicker Grid.Row="2"
                                                            Grid.Column="0"
                                                            SelectedDate="{Binding LicenseExpiryDate, UpdateSourceTrigger=PropertyChanged}"/>

                                                <!-- متاح للمواعيد -->
                                                <StackPanel Grid.Row="2"
                                                            Grid.Column="2"
                                                            Orientation="Horizontal"
                                                            Margin="0,35,0,5">
                                                        <CheckBox IsChecked="{Binding IsAvailableForAppointments, UpdateSourceTrigger=PropertyChanged}"
                                                                  VerticalAlignment="Center"
                                                                  Margin="0,0,10,0"/>
                                                        <TextBlock Text="متاح للمواعيد"
                                                                   VerticalAlignment="Center"
                                                                   FontSize="14"
                                                                   FontWeight="Bold"/>
                                                </StackPanel>
                                        </Grid>
                                </GroupBox>

                                <!-- المؤهلات والملاحظات -->
                                <GroupBox Header="المؤهلات والملاحظات"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="#9C27B0">
                                        <Grid Margin="10">
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- المؤهلات -->
                                                <Label Grid.Row="0"
                                                       Content="المؤهلات"/>
                                                <TextBox Grid.Row="0"
                                                         Text="{Binding Qualifications, UpdateSourceTrigger=PropertyChanged}"
                                                         Height="80"
                                                         TextWrapping="Wrap"
                                                         AcceptsReturn="True"
                                                         VerticalScrollBarVisibility="Auto"
                                                         Margin="0,30,0,10"/>

                                                <!-- الملاحظات -->
                                                <Label Grid.Row="1"
                                                       Content="الملاحظات"/>
                                                <TextBox Grid.Row="1"
                                                         Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                                         Height="80"
                                                         TextWrapping="Wrap"
                                                         AcceptsReturn="True"
                                                         VerticalScrollBarVisibility="Auto"
                                                         Margin="0,30,0,10"/>
                                        </Grid>
                                </GroupBox>
                        </StackPanel>
                </ScrollViewer>

                <!-- الأزرار -->
                <StackPanel Grid.Row="2"
                            Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,20,0,0">
                        <Button Content="💾 حفظ"
                                Command="{Binding SaveCommand}"
                                Background="#4CAF50"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="20,10"
                                Margin="10,0"
                                FontWeight="Bold"
                                FontSize="14"
                                MinWidth="120"/>

                        <Button Content="❌ إلغاء"
                                Command="{Binding CancelCommand}"
                                Background="#F44336"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="20,10"
                                Margin="10,0"
                                FontWeight="Bold"
                                FontSize="14"
                                MinWidth="120"/>
                </StackPanel>
        </Grid>
</Window>
