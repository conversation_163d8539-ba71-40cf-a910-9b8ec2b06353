using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Doctors;

namespace AqlanCenterProApp.Views.Doctors
{
    /// <summary>
    /// Interaction logic for DoctorStatisticsWindow.xaml
    /// </summary>
    public partial class DoctorStatisticsWindow : Window
    {
        private DoctorStatisticsViewModel _viewModel;

        public DoctorStatisticsWindow(Doctor doctor)
        {
            InitializeComponent();

            try
            {
                // إنشاء ViewModel
                _viewModel = new DoctorStatisticsViewModel(doctor);
                DataContext = _viewModel;

                // ربط الأحداث
                _viewModel.CloseRequested += (sender, result) =>
                {
                    DialogResult = result;
                    Close();
                };

                // تحميل الرسوم البيانية
                LoadCharts();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                Close();
            }
        }

        /// <summary>
        /// تحميل الرسوم البيانية
        /// </summary>
        private void LoadCharts()
        {
            try
            {
                LoadMonthlyEarningsChart();
                LoadMonthlySessionsChart();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل الرسوم البيانية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل رسم الأرباح الشهرية
        /// </summary>
        private void LoadMonthlyEarningsChart()
        {
            MonthlyEarningsPanel.Children.Clear();

            if (_viewModel.MonthlyEarnings?.Any() == true)
            {
                var maxValue = _viewModel.MonthlyEarnings.Max(x => x.Value);

                foreach (var item in _viewModel.MonthlyEarnings)
                {
                    var panel = new StackPanel 
                    { 
                        Orientation = Orientation.Horizontal, 
                        Margin = new Thickness(0, 0, 0, 5) 
                    };

                    // اسم الشهر
                    var monthLabel = new TextBlock
                    {
                        Text = $"{item.Month}:",
                        Width = 80,
                        FontWeight = FontWeights.SemiBold,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    panel.Children.Add(monthLabel);

                    // شريط التقدم
                    var progressBar = new ProgressBar
                    {
                        Width = 200,
                        Height = 20,
                        Maximum = (double)maxValue,
                        Value = (double)item.Value,
                        Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)), // #FF9800
                        Margin = new Thickness(5, 0, 5, 0)
                    };
                    panel.Children.Add(progressBar);

                    // القيمة
                    var valueLabel = new TextBlock
                    {
                        Text = $"{item.Value:N0} ريال",
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    panel.Children.Add(valueLabel);

                    MonthlyEarningsPanel.Children.Add(panel);
                }
            }
            else
            {
                var noDataLabel = new TextBlock
                {
                    Text = "لا توجد بيانات متاحة",
                    FontStyle = FontStyles.Italic,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                MonthlyEarningsPanel.Children.Add(noDataLabel);
            }
        }

        /// <summary>
        /// تحميل رسم الجلسات الشهرية
        /// </summary>
        private void LoadMonthlySessionsChart()
        {
            MonthlySessionsPanel.Children.Clear();

            if (_viewModel.MonthlySessions?.Any() == true)
            {
                var maxValue = _viewModel.MonthlySessions.Max(x => x.Value);

                foreach (var item in _viewModel.MonthlySessions)
                {
                    var panel = new StackPanel 
                    { 
                        Orientation = Orientation.Horizontal, 
                        Margin = new Thickness(0, 0, 0, 5) 
                    };

                    // اسم الشهر
                    var monthLabel = new TextBlock
                    {
                        Text = $"{item.Month}:",
                        Width = 80,
                        FontWeight = FontWeights.SemiBold,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    panel.Children.Add(monthLabel);

                    // شريط التقدم
                    var progressBar = new ProgressBar
                    {
                        Width = 200,
                        Height = 20,
                        Maximum = (double)maxValue,
                        Value = (double)item.Value,
                        Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)), // #4CAF50
                        Margin = new Thickness(5, 0, 5, 0)
                    };
                    panel.Children.Add(progressBar);

                    // القيمة
                    var valueLabel = new TextBlock
                    {
                        Text = $"{item.Value} جلسة",
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    panel.Children.Add(valueLabel);

                    MonthlySessionsPanel.Children.Add(panel);
                }
            }
            else
            {
                var noDataLabel = new TextBlock
                {
                    Text = "لا توجد بيانات متاحة",
                    FontStyle = FontStyles.Italic,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                MonthlySessionsPanel.Children.Add(noDataLabel);
            }
        }
    }
}
