using System.Collections.ObjectModel;
using System.Threading.Tasks;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeAttendanceViewModel
    {
        private readonly IEmployeeService _employeeService;
        public ObservableCollection<EmployeeAttendance> AttendanceRecords { get; set; } = new();
        public Employee Employee { get; }

        public EmployeeAttendanceViewModel(IEmployeeService employeeService, Employee employee)
        {
            _employeeService = employeeService;
            Employee = employee;
            _ = LoadAttendanceAsync();
        }

        private async Task LoadAttendanceAsync()
        {
            var startDate = DateTime.Today.AddYears(-1);
            var endDate = DateTime.Today;
            var records = await _employeeService.GetEmployeeAttendanceAsync(Employee.EmployeeId, startDate, endDate);
            AttendanceRecords.Clear();
            foreach (var record in records)
                AttendanceRecords.Add(record);
        }
    }
} 