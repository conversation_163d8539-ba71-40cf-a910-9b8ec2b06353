using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.LabOrders
{
    public class AddEditLabOrderViewModel : BaseViewModel
    {
        private readonly ILabOrderService _labOrderService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;
        private readonly ILabService _labService;
        private readonly IShadeService _shadeService;
        private readonly IAppointmentService _appointmentService;

        private LabOrder _labOrder = new();
        private Patient? _selectedPatient;
        private Doctor? _selectedDoctor;
        private Lab? _selectedLab;
        private Shade? _selectedShade;
        private Appointment? _selectedAppointment;
        private ObservableCollection<Patient> _patients = new();
        private ObservableCollection<Doctor> _doctors = new();
        private ObservableCollection<Lab> _labs = new();
        private ObservableCollection<Shade> _shades = new();
        private ObservableCollection<Appointment> _appointments = new();
        private string _attachmentPath = string.Empty;
        private bool _isEditMode;

        public AddEditLabOrderViewModel(
            ILabOrderService labOrderService,
            IPatientService patientService,
            IDoctorService doctorService,
            ILabService labService,
            IShadeService shadeService,
            IAppointmentService appointmentService)
        {
            _labOrderService = labOrderService;
            _patientService = patientService;
            _doctorService = doctorService;
            _labService = labService;
            _shadeService = shadeService;
            _appointmentService = appointmentService;

            InitializeCommands();
            _ = LoadDataAsync();
        }

        public AddEditLabOrderViewModel(
            LabOrder labOrder,
            ILabOrderService labOrderService,
            IPatientService patientService,
            IDoctorService doctorService,
            ILabService labService,
            IShadeService shadeService,
            IAppointmentService appointmentService)
        {
            _labOrderService = labOrderService;
            _patientService = patientService;
            _doctorService = doctorService;
            _labService = labService;
            _shadeService = shadeService;
            _appointmentService = appointmentService;

            LabOrder = labOrder;
            IsEditMode = true;

            InitializeCommands();
            _ = LoadDataAsync();
        }

        #region Properties

        public LabOrder LabOrder
        {
            get => _labOrder;
            set => SetProperty(ref _labOrder, value);
        }

        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set
            {
                if (SetProperty(ref _selectedPatient, value))
                {
                    LoadPatientAppointmentsAsync();
                }
            }
        }

        public Doctor? SelectedDoctor
        {
            get => _selectedDoctor;
            set => SetProperty(ref _selectedDoctor, value);
        }

        public Lab? SelectedLab
        {
            get => _selectedLab;
            set => SetProperty(ref _selectedLab, value);
        }

        public Shade? SelectedShade
        {
            get => _selectedShade;
            set => SetProperty(ref _selectedShade, value);
        }

        public Appointment? SelectedAppointment
        {
            get => _selectedAppointment;
            set => SetProperty(ref _selectedAppointment, value);
        }

        public ObservableCollection<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }

        public ObservableCollection<Doctor> Doctors
        {
            get => _doctors;
            set => SetProperty(ref _doctors, value);
        }

        public ObservableCollection<Lab> Labs
        {
            get => _labs;
            set => SetProperty(ref _labs, value);
        }

        public ObservableCollection<Shade> Shades
        {
            get => _shades;
            set => SetProperty(ref _shades, value);
        }

        public ObservableCollection<Appointment> Appointments
        {
            get => _appointments;
            set => SetProperty(ref _appointments, value);
        }

        public string AttachmentPath
        {
            get => _attachmentPath;
            set => SetProperty(ref _attachmentPath, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public string WindowTitle => IsEditMode ? "تعديل طلب معمل" : "إضافة طلب معمل جديد";

        public event EventHandler<LabOrder>? LabOrderSaved;
        public event EventHandler? Cancelled;

        #endregion

        #region Commands

        public RelayCommand SaveCommand { get; private set; } = null!;
        public RelayCommand CancelCommand { get; private set; } = null!;
        public RelayCommand LoadPatientsCommand { get; private set; } = null!;
        public RelayCommand LoadDoctorsCommand { get; private set; } = null!;
        public RelayCommand LoadLabsCommand { get; private set; } = null!;
        public RelayCommand LoadShadesCommand { get; private set; } = null!;
        public RelayCommand LoadAppointmentsCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(async _ => await SaveAsync(), _ => CanSave());
            CancelCommand = new RelayCommand(_ => Cancel());
            LoadPatientsCommand = new RelayCommand(async _ => await LoadPatientsAsync());
            LoadDoctorsCommand = new RelayCommand(async _ => await LoadDoctorsAsync());
            LoadLabsCommand = new RelayCommand(async _ => await LoadLabsAsync());
            LoadShadesCommand = new RelayCommand(async _ => await LoadShadesAsync());
            LoadAppointmentsCommand = new RelayCommand(async _ => await LoadAppointmentsAsync());
        }

        #endregion

        #region Methods

        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                await Task.WhenAll(
                    LoadPatientsAsync(),
                    LoadDoctorsAsync(),
                    LoadLabsAsync(),
                    LoadShadesAsync(),
                    LoadAppointmentsAsync()
                );

                if (IsEditMode)
                {
                    // Set selected items based on LabOrder data
                    SelectedPatient = Patients.FirstOrDefault(p => p.Id == LabOrder.PatientId);
                    SelectedDoctor = Doctors.FirstOrDefault(d => d.Id == LabOrder.DoctorId);
                    SelectedLab = Labs.FirstOrDefault(l => l.LabId == LabOrder.LabId);
                    SelectedShade = Shades.FirstOrDefault(s => s.ShadeId == LabOrder.ShadeId);
                    SelectedAppointment = Appointments.FirstOrDefault(a => a.Id == LabOrder.AppointmentId);
                }
            }, "جاري تحميل البيانات...");
        }

        private async Task LoadPatientsAsync()
        {
            var patients = await _patientService.GetAllPatientsAsync();
            Patients.Clear();
            foreach (var patient in patients)
            {
                Patients.Add(patient);
            }
        }

        private async Task LoadDoctorsAsync()
        {
            var doctors = await _doctorService.GetAllDoctorsAsync();
            Doctors.Clear();
            foreach (var doctor in doctors)
            {
                Doctors.Add(doctor);
            }
        }

        private async Task LoadLabsAsync()
        {
            var labs = await _labService.GetActiveLabsAsync();
            Labs.Clear();
            foreach (var lab in labs)
            {
                Labs.Add(lab);
            }
        }

        private async Task LoadShadesAsync()
        {
            var shades = await _shadeService.GetAllShadesAsync();
            Shades.Clear();
            foreach (var shade in shades)
            {
                Shades.Add(shade);
            }
        }

        private async Task LoadAppointmentsAsync()
        {
            var appointments = await _appointmentService.GetAllAppointmentsAsync();
            Appointments.Clear();
            foreach (var appointment in appointments)
            {
                Appointments.Add(appointment);
            }
        }

        private async Task LoadPatientAppointmentsAsync()
        {
            if (SelectedPatient != null)
            {
                var appointments = await _appointmentService.GetAppointmentsByPatientAsync(SelectedPatient.Id);
                Appointments.Clear();
                foreach (var appointment in appointments)
                {
                    Appointments.Add(appointment);
                }
            }
        }

        private async Task SaveAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Update LabOrder properties
                LabOrder.PatientId = SelectedPatient!.Id;
                LabOrder.DoctorId = SelectedDoctor!.Id;
                LabOrder.LabId = SelectedLab!.LabId;
                LabOrder.ShadeId = SelectedShade?.ShadeId;
                LabOrder.AppointmentId = SelectedAppointment?.Id;

                if (IsEditMode)
                {
                    await _labOrderService.UpdateLabOrderAsync(LabOrder);
                }
                else
                {
                    await _labOrderService.AddLabOrderAsync(LabOrder);
                }

                // Notify parent window
                LabOrderSaved?.Invoke(this, LabOrder);
            }, IsEditMode ? "جاري تحديث طلب المختبر..." : "جاري إضافة طلب المختبر...");
        }

        private void Cancel()
        {
            Cancelled?.Invoke(this, EventArgs.Empty);
        }

        private void AddAttachment()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر المرفق",
                Filter = "جميع الملفات|*.*|الصور|*.jpg;*.jpeg;*.png;*.gif|مستندات PDF|*.pdf",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
                LabOrder.AttachmentPath = AttachmentPath;
            }
        }

        private async Task PrintAsync()
        {
            try
            {
                if (LabOrder.LabOrderId > 0)
                {
                    await _labOrderService.PrintLabOrderAsync(LabOrder.LabOrderId);
                    MessageBox.Show("تم إرسال الطلب للطباعة", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("يجب حفظ الطلب أولاً قبل الطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SendWhatsAppAsync()
        {
            try
            {
                if (SelectedLab == null)
                {
                    MessageBox.Show("يجب اختيار المعمل أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrEmpty(SelectedLab.WhatsApp))
                {
                    MessageBox.Show("لا يوجد رقم واتساب للمعمل المحدد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var message = $"طلب معمل جديد\nرقم الطلب: {LabOrder.OrderNumber}\nالمريض: {SelectedPatient?.FullName}\nنوع العمل: {LabOrder.WorkType}\nعدد القطع: {LabOrder.PiecesCount}";

                await _labOrderService.SendWhatsAppNotificationAsync(LabOrder.LabOrderId, message);
                MessageBox.Show("تم إرسال الرسالة عبر الواتساب", "إرسال", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddNewPatient()
        {
            // Implementation for adding new patient
            MessageBox.Show("سيتم إضافة ميزة إضافة مريض جديد قريباً", "ميزة جديدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddNewLab()
        {
            // Implementation for adding new lab
            MessageBox.Show("سيتم إضافة ميزة إضافة معمل جديد قريباً", "ميزة جديدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddNewShade()
        {
            // Implementation for adding new shade
            MessageBox.Show("سيتم إضافة ميزة إضافة درجة لون جديدة قريباً", "ميزة جديدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private bool ValidateLabOrder()
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(LabOrder);

            if (!Validator.TryValidateObject(LabOrder, validationContext, validationResults, true))
            {
                var errors = string.Join("\n", validationResults.Select(v => v.ErrorMessage));
                MessageBox.Show($"أخطاء في البيانات:\n{errors}", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SelectedPatient == null)
            {
                MessageBox.Show("يجب اختيار المريض", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SelectedDoctor == null)
            {
                MessageBox.Show("يجب اختيار الطبيب", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (SelectedLab == null)
            {
                MessageBox.Show("يجب اختيار المعمل", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private bool CanSave()
        {
            return SelectedPatient != null &&
                   SelectedDoctor != null &&
                   SelectedLab != null &&
                   !string.IsNullOrWhiteSpace(LabOrder.WorkType) &&
                   LabOrder.PiecesCount > 0;
        }

        #endregion
    }
} 