using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.Roles
{
    public class RolesListViewModel : BaseViewModel
    {
        private readonly IRoleService _roleService;
        private readonly IActivityLogService _activityLogService;

        private ObservableCollection<Role> _roles;
        private Role? _selectedRole;
        private string _searchTerm = string.Empty;
        private bool _showActiveOnly = true;

        public RolesListViewModel(IRoleService roleService, IActivityLogService activityLogService)
        {
            _roleService = roleService;
            _activityLogService = activityLogService;

            Roles = new ObservableCollection<Role>();

            LoadRolesCommand = new RelayCommand(async () => await LoadRolesAsync());
            SearchCommand = new RelayCommand(async () => await SearchRolesAsync());
            AddRoleCommand = new RelayCommand(() => AddRole());
            EditRoleCommand = new RelayCommand(() => EditRole(), () => CanEdit);
            DeleteRoleCommand = new RelayCommand(async () => await DeleteRoleAsync(), () => CanDelete);
            ActivateRoleCommand = new RelayCommand(async () => await ActivateRoleAsync(), () => CanActivate);
            DeactivateRoleCommand = new RelayCommand(async () => await DeactivateRoleAsync(), () => CanDeactivate);
            ManagePermissionsCommand = new RelayCommand(() => ManagePermissions(), () => CanEdit);
            ViewUsersInRoleCommand = new RelayCommand(() => ViewUsersInRole(), () => CanViewUsers);
            ExportCommand = new RelayCommand(async () => await ExportRolesAsync());
            PrintCommand = new RelayCommand(async () => await PrintRolesAsync());

            _ = LoadRolesAsync();
        }

        public ObservableCollection<Role> Roles
        {
            get => _roles;
            set => SetProperty(ref _roles, value);
        }

        public Role? SelectedRole
        {
            get => _selectedRole;
            set
            {
                SetProperty(ref _selectedRole, value);
                OnPropertyChanged(nameof(CanEdit));
                OnPropertyChanged(nameof(CanDelete));
                OnPropertyChanged(nameof(CanActivate));
                OnPropertyChanged(nameof(CanDeactivate));
                OnPropertyChanged(nameof(CanViewUsers));
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set => SetProperty(ref _searchTerm, value);
        }

        public bool ShowActiveOnly
        {
            get => _showActiveOnly;
            set
            {
                SetProperty(ref _showActiveOnly, value);
                _ = LoadRolesAsync();
            }
        }

        public bool CanEdit => SelectedRole != null;
        public bool CanDelete => SelectedRole != null && SelectedRole.Id != 1; // Cannot delete admin role
        public bool CanActivate => SelectedRole != null && !SelectedRole.IsActive;
        public bool CanDeactivate => SelectedRole != null && SelectedRole.IsActive && SelectedRole.Id != 1; // Cannot deactivate admin role
        public bool CanViewUsers => SelectedRole != null;

        public ICommand LoadRolesCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand AddRoleCommand { get; }
        public ICommand EditRoleCommand { get; }
        public ICommand DeleteRoleCommand { get; }
        public ICommand ActivateRoleCommand { get; }
        public ICommand DeactivateRoleCommand { get; }
        public ICommand ManagePermissionsCommand { get; }
        public ICommand ViewUsersInRoleCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }

        private async Task LoadRolesAsync()
        {
            IsBusy = true;
            try
            {
                var roles = await _roleService.GetAllRolesAsync();
                
                // Apply filters
                var filteredRoles = roles.AsEnumerable();
                
                if (ShowActiveOnly)
                    filteredRoles = filteredRoles.Where(r => r.IsActive);

                Roles.Clear();
                foreach (var role in filteredRoles.OrderBy(r => r.RoleName))
                {
                    Roles.Add(role);
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading roles: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchRolesAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadRolesAsync();
                return;
            }

            IsBusy = true;
            try
            {
                var allRoles = await _roleService.GetAllRolesAsync();
                var searchResults = allRoles.Where(r => 
                    r.RoleName.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (r.Description?.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
                
                // Apply filters
                var filteredRoles = searchResults.AsEnumerable();
                
                if (ShowActiveOnly)
                    filteredRoles = filteredRoles.Where(r => r.IsActive);

                Roles.Clear();
                foreach (var role in filteredRoles.OrderBy(r => r.RoleName))
                {
                    Roles.Add(role);
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error searching roles: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddRole()
        {
            // TODO: Open AddEditRoleWindow
            System.Diagnostics.Debug.WriteLine("Add Role clicked");
        }

        private void EditRole()
        {
            if (SelectedRole == null) return;
            
            // TODO: Open AddEditRoleWindow with selected role
            System.Diagnostics.Debug.WriteLine($"Edit Role clicked: {SelectedRole.RoleName}");
        }

        private async Task DeleteRoleAsync()
        {
            if (SelectedRole == null) return;

            // TODO: Show confirmation dialog
            try
            {
                var success = await _roleService.DeleteRoleAsync(SelectedRole.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Delete", "Role", SelectedRole.Id, $"Deleted role: {SelectedRole.RoleName}");
                    await LoadRolesAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error deleting role: {ex.Message}");
            }
        }

        private async Task ActivateRoleAsync()
        {
            if (SelectedRole == null) return;

            try
            {
                var success = await _roleService.ActivateRoleAsync(SelectedRole.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Activate", "Role", SelectedRole.Id, $"Activated role: {SelectedRole.RoleName}");
                    await LoadRolesAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error activating role: {ex.Message}");
            }
        }

        private async Task DeactivateRoleAsync()
        {
            if (SelectedRole == null) return;

            // TODO: Show confirmation dialog
            try
            {
                var success = await _roleService.DeactivateRoleAsync(SelectedRole.Id);
                if (success)
                {
                    await _activityLogService.LogActivityAsync(1, "Deactivate", "Role", SelectedRole.Id, $"Deactivated role: {SelectedRole.RoleName}");
                    await LoadRolesAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error deactivating role: {ex.Message}");
            }
        }

        private void ManagePermissions()
        {
            if (SelectedRole == null) return;
            
            // TODO: Open RolePermissionsWindow
            System.Diagnostics.Debug.WriteLine($"Manage Permissions clicked: {SelectedRole.RoleName}");
        }

        private void ViewUsersInRole()
        {
            if (SelectedRole == null) return;
            
            // TODO: Open UsersInRoleWindow
            System.Diagnostics.Debug.WriteLine($"View Users in Role clicked: {SelectedRole.RoleName}");
        }

        private async Task ExportRolesAsync()
        {
            // TODO: Implement export functionality
            System.Diagnostics.Debug.WriteLine("Export Roles clicked");
        }

        private async Task PrintRolesAsync()
        {
            // TODO: Implement print functionality
            System.Diagnostics.Debug.WriteLine("Print Roles clicked");
        }
    }
} 