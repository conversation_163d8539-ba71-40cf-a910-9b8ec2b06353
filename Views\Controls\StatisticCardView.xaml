<UserControl x:Class="AqlanCenterProApp.Views.Controls.StatisticCardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Controls"
             mc:Ignorable="d"
             d:DesignHeight="120" d:DesignWidth="220"
             x:Name="Root">
    <Border CornerRadius="10" Padding="15" Background="{Binding ElementName=Root, Path=BackgroundColor}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Text="{Binding ElementName=Root, Path=Title}" FontWeight="Bold" FontSize="16" Foreground="White" />

            <Grid Grid.Row="1" VerticalAlignment="Bottom">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Text="{Binding ElementName=Root, Path=Value}" FontSize="24" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>

                <Path Data="{Binding ElementName=Root, Path=Icon}" Stretch="Uniform" Width="40" Height="40" Fill="White" Grid.Column="1" HorizontalAlignment="Right"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>