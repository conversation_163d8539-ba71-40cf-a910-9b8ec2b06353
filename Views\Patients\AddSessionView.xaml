<Window x:Class="AqlanCenterProApp.Views.Patients.AddSessionView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة جلسة جديدة"
        Height="600"
        Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style TargetType="Label">
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="FontWeight"
                    Value="Bold"/>
            <Setter Property="Foreground"
                    Value="#2c3e50"/>
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="Padding"
                    Value="8"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#bdc3c7"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="Padding"
                    Value="8"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#bdc3c7"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <Style TargetType="DatePicker">
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="Padding"
                    Value="8"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#bdc3c7"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <Style TargetType="Button">
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontWeight"
                    Value="Bold"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0"
                Background="#3498db"
                CornerRadius="5"
                Margin="0,0,0,20">
            <TextBlock Text="إضافة جلسة جديدة"
                       FontSize="18"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       Padding="20,15"/>
        </Border>

        <!-- معلومات المريض -->
        <Border Grid.Row="0"
                Background="#ecf0f1"
                CornerRadius="5"
                Margin="0,60,0,10">
            <StackPanel Margin="15">
                <TextBlock Text="معلومات المريض"
                        FontWeight="Bold"
                        FontSize="16"
                        Margin="0,0,0,10"/>
                <TextBlock Text="{Binding Patient.FullName}"
                        FontSize="14"
                        Margin="0,2"/>
                <TextBlock Text="{Binding Patient.FileNumber, StringFormat='رقم الملف: {0}'}"
                        FontSize="12"
                        Foreground="#7f8c8d"/>
            </StackPanel>
        </Border>

        <!-- نموذج الجلسة -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly"
                      ScrollViewer.CanContentScroll="False">
            <StackPanel>
                <!-- تاريخ الجلسة -->
                <Label Content="تاريخ الجلسة:"/>
                <DatePicker SelectedDate="{Binding SessionDate}"/>

                <!-- نوع العلاج -->
                <Label Content="نوع العلاج:"/>
                <ComboBox ItemsSource="{Binding TreatmentTypes}"
                          SelectedItem="{Binding TreatmentType}"
                          IsEditable="True"/>

                <!-- المبلغ -->
                <Label Content="المبلغ:"/>
                <TextBox Text="{Binding Amount, StringFormat=N2}"
                         InputScope="Number"/>

                <!-- حالة الجلسة -->
                <Label Content="حالة الجلسة:"/>
                <ComboBox ItemsSource="{Binding SessionStatuses}"
                          SelectedItem="{Binding SessionStatus}"/>

                <!-- حالة الدفع -->
                <Label Content="حالة الدفع:"/>
                <ComboBox ItemsSource="{Binding PaymentStatuses}"
                          SelectedItem="{Binding PaymentStatus}"/>

                <!-- الملاحظات -->
                <Label Content="ملاحظات:"/>
                <TextBox Text="{Binding Notes}"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2"
                Orientation="Horizontal"
                HorizontalAlignment="Center"
                Margin="0,20,0,0">
            <Button Content="حفظ الجلسة"
                    Command="{Binding SaveSessionCommand}"
                    Background="#27ae60"
                    Foreground="White"
                    MinWidth="120"/>

            <Button Content="إلغاء"
                    Command="{Binding CancelCommand}"
                    Background="#e74c3c"
                    Foreground="White"
                    MinWidth="120"/>
        </StackPanel>
    </Grid>
</Window> 