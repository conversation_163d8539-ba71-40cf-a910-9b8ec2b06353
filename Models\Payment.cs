using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class Payment : BaseEntity
    {
        [Required]
        public int PatientId { get; set; }
        
        public int? SessionId { get; set; }
        
        public int? InvoiceId { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;
        
        [StringLength(20)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، بطاقة، تحويل
        
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        
        [StringLength(20)]
        public string PaymentType { get; set; } = "دفع"; // دفع، استرداد
        
        [StringLength(20)]
        public string Status { get; set; } = "مكتمل"; // مكتمل، معلق، ملغي
        
        [StringLength(100)]
        public string? ReferenceNumber { get; set; }
        
        [StringLength(500)]
        public string? PaymentNotes { get; set; }
        
        [StringLength(100)]
        public string? ReceivedBy { get; set; }
        
        // Navigation Properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;
        
        [ForeignKey("SessionId")]
        public virtual Session? Session { get; set; }
        
        [ForeignKey("InvoiceId")]
        public virtual Invoice? Invoice { get; set; }
    }
}
