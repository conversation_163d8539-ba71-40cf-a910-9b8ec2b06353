<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeAttendanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="سجلات حضور الموظف" Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="سجلات حضور الموظف" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        <DataGrid x:Name="AttendanceDataGrid"
                  ItemsSource="{Binding AttendanceRecords}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Margin="20"
                  RowHeight="32">
            <DataGrid.Columns>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding AttendanceDate, StringFormat=dd/MM/yyyy}" Width="*"/>
                <DataGridTextColumn Header="وقت الدخول" Binding="{Binding CheckInTime}" Width="*"/>
                <DataGridTextColumn Header="وقت الخروج" Binding="{Binding CheckOutTime}" Width="*"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding AttendanceStatus}" Width="*"/>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>
        <Button Content="إغلاق" Width="100" Height="36" Margin="20" DockPanel.Dock="Bottom" HorizontalAlignment="Center" Click="Close_Click"/>
    </DockPanel>
</Window> 