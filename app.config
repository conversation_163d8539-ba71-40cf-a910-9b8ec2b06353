<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <!-- تعطيل BinaryFormatter لحل مشاكل .NET 8 -->
    <AppContextSwitchOverrides value="System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false;System.Runtime.Serialization.DisallowBinaryFormatterSerialization=true" />
    
    <!-- إعدادات WPF لحل مشاكل التسلسل -->
    <AppContextSwitchOverrides value="System.Windows.Markup.DoNotUseSha256ForMarkupCompilerChecksumAlgorithm=true" />
    
    <!-- تحسين أداء .NET 8 -->
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
  </runtime>
  
  <appSettings>
    <!-- إعدادات التطبيق -->
    <add key="EnableBinaryFormatterSerialization" value="false" />
    <add key="UseJsonSerialization" value="true" />
  </appSettings>
</configuration>
