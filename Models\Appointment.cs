using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class Appointment : BaseEntity
    {
        [Key]
        [Column("AppointmentId")]
        public new int Id { get; set; }

        [Required]
        public int PatientId { get; set; }
        public virtual Patient Patient { get; set; } = null!;

        [Required]
        public int DoctorId { get; set; }
        public virtual Doctor Doctor { get; set; } = null!;

        [Required]
        [StringLength(50)]
        public string ServiceType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string AppointmentType { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "مجدول";

        [Required]
        [Column("Date")]
        public DateTime AppointmentDate { get; set; }

        [Required]
        [Column("Time")]
        public TimeSpan AppointmentTime { get; set; }

        [NotMapped]
        public DateTime AppointmentDateTime
        {
            get => AppointmentDate.Date + AppointmentTime;
            set
            {
                AppointmentDate = value.Date;
                AppointmentTime = value.TimeOfDay;
            }
        }

        [Required]
        public int DurationMinutes { get; set; } = 30;

        [StringLength(500)]
        public new string? Notes { get; set; }

        [StringLength(20)]
        public string AttendanceStatus { get; set; } = "لم يحضر";

        [StringLength(200)]
        public string? CancellationReason { get; set; }

        public DateTime? CompletionDate { get; set; }

        public bool IsOrthodonticPlan { get; set; } = false;
        public bool FollowUpFlag { get; set; } = false;

        // Navigation Properties
        [Column("OrthodonticPlanPlanId")]
        public int? OrthodonticPlanId { get; set; }
        public virtual OrthodonticPlan? OrthodonticPlan { get; set; }

        public decimal Cost { get; set; }

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
    }
}
