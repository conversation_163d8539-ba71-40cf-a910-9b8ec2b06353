namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IDoctorService
    {
        // Define required doctor-related methods here
        // Example methods (to be implemented based on actual requirements):
        // Task<IEnumerable<Doctor>> GetAllDoctorsAsync();
        // Task<Doctor> GetDoctorByIdAsync(int id);
        // Task CreateDoctorAsync(Doctor doctor);
        // Task UpdateDoctorAsync(Doctor doctor);
        // Task DeleteDoctorAsync(int id);
    }
}