using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IDoctorService
    {
        // Basic CRUD operations
        Task<IEnumerable<Doctor>> GetAllDoctorsAsync();
        Task<IEnumerable<Doctor>> GetActiveDoctorsAsync();
        Task<Doctor?> GetDoctorByIdAsync(int id);
        Task<IEnumerable<Doctor>> SearchDoctorsAsync(string searchTerm);
        Task<IEnumerable<Doctor>> GetDoctorsBySpecializationAsync(string specialization);
        Task<IEnumerable<Doctor>> GetDoctorsByStatusAsync(string status);
        Task<Doctor> AddDoctorAsync(Doctor doctor, string currentUser);
        Task<Doctor> UpdateDoctorAsync(Doctor doctor, string currentUser);
        Task<bool> DeleteDoctorAsync(int id, string currentUser);
        Task<bool> ToggleDoctorStatusAsync(int id, string currentUser);

        // Statistics and reporting
        Task<object> GetDoctorStatisticsAsync(int doctorId);
        Task<object> GetGeneralStatisticsAsync();
        Task<bool> UpdateDoctorRatingAsync(int doctorId, decimal rating);
        Task<IEnumerable<Doctor>> GetAvailableDoctorsAsync();
        Task<IEnumerable<Doctor>> GetDoctorsWithExpiringLicensesAsync(int daysBeforeExpiry);
        Task<IEnumerable<Doctor>> GetDoctorsWithExpiringContractsAsync(int daysBeforeExpiry);
        Task<IEnumerable<Doctor>> GetTopRatedDoctorsAsync(int count);
        Task<bool> UpdateDoctorStatisticsAsync(int doctorId);

        // Count methods for dashboard
        Task<int> GetDoctorCountAsync();
    }
}