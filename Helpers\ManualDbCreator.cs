using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using System.IO;

namespace AqlanCenterProApp.Helpers;

/// <summary>
/// أداة إنشاء قاعدة البيانات يدوياً
/// </summary>
public static class ManualDbCreator
{
    /// <summary>
    /// إنشاء قاعدة البيانات يدوياً مع معالجة شاملة للأخطاء
    /// </summary>
    public static async Task<(bool Success, string Message)> CreateDatabaseManuallyAsync()
    {
        try
        {
            // إنشاء مسار قاعدة البيانات
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
            var dbPath = @"D:\AqlanCenterProApp\AqlanCenterDatabase.db";

            var message = $"🔧 إنشاء قاعدة البيانات يدوياً...\n";
            message += $"📁 مجلد التطبيق: {baseDirectory}\n";
            message += $"🗄️ مسار قاعدة البيانات: {dbPath}\n\n";

            // التأكد من وجود المجلد الرئيسي
            var projectDirectory = Path.GetDirectoryName(dbPath);
            if (!string.IsNullOrEmpty(projectDirectory) && !Directory.Exists(projectDirectory))
            {
                Directory.CreateDirectory(projectDirectory);
                message += "✅ تم إنشاء المجلد الرئيسي\n";
            }
            else
            {
                message += "✅ المجلد الرئيسي موجود\n";
            }

            // حذف قاعدة البيانات الموجودة إن وجدت
            if (File.Exists(dbPath))
            {
                File.Delete(dbPath);
                message += "🗑️ تم حذف قاعدة البيانات القديمة\n";
            }

            // حذف ملفات SQLite المساعدة
            var shmFile = dbPath + "-shm";
            var walFile = dbPath + "-wal";
            if (File.Exists(shmFile)) File.Delete(shmFile);
            if (File.Exists(walFile)) File.Delete(walFile);

            // إنشاء قاعدة البيانات الجديدة
            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);

            using var context = new AqlanCenterDbContext(optionsBuilder.Options);

            // إنشاء قاعدة البيانات
            message += "🔧 إنشاء هيكل قاعدة البيانات...\n";
            await context.Database.EnsureCreatedAsync();
            message += "✅ تم إنشاء هيكل قاعدة البيانات\n";

            // التحقق من إنشاء الملف
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                message += $"✅ تم إنشاء ملف قاعدة البيانات ({fileInfo.Length} bytes)\n";
            }
            else
            {
                message += "❌ فشل في إنشاء ملف قاعدة البيانات\n";
                return (false, message);
            }

            // اختبار الاتصال
            message += "🔗 اختبار الاتصال...\n";
            var canConnect = await context.Database.CanConnectAsync();
            if (!canConnect)
            {
                message += "❌ فشل في الاتصال بقاعدة البيانات\n";
                return (false, message);
            }
            message += "✅ تم الاتصال بنجاح\n";

            // تهيئة البيانات الأولية
            message += "🔄 تهيئة البيانات الأولية...\n";
            await DatabaseInitializer.InitializeAsync(context);
            message += "✅ تم تهيئة البيانات الأولية\n";

            // التحقق من البيانات
            var usersCount = await context.Users.CountAsync();
            var rolesCount = await context.Roles.CountAsync();
            var doctorsCount = await context.Doctors.CountAsync();
            var patientsCount = await context.Patients.CountAsync();
            var prosthesisTypesCount = await context.ProsthesisTypes.CountAsync();

            message += "\n📊 إحصائيات البيانات:\n";
            message += $"   👥 المستخدمين: {usersCount}\n";
            message += $"   🔐 الأدوار: {rolesCount}\n";
            message += $"   👨‍⚕️ الأطباء: {doctorsCount}\n";
            message += $"   🏥 المرضى: {patientsCount}\n";
            message += $"   🦷 أنواع التركيبات: {prosthesisTypesCount}\n";

            if (usersCount > 0 && rolesCount > 0)
            {
                message += "\n🎉 تم إنشاء قاعدة البيانات بنجاح مع جميع البيانات الأولية!";
                return (true, message);
            }
            else
            {
                message += "\n⚠️ تم إنشاء قاعدة البيانات ولكن البيانات الأولية ناقصة";
                return (false, message);
            }
        }
        catch (Exception ex)
        {
            var errorMessage = $"❌ خطأ في إنشاء قاعدة البيانات:\n{ex.Message}";
            if (ex.InnerException != null)
            {
                errorMessage += $"\nالتفاصيل: {ex.InnerException.Message}";
            }
            return (false, errorMessage);
        }
    }

    /// <summary>
    /// اختبار سريع لقاعدة البيانات
    /// </summary>
    public static async Task<(bool Success, string Message)> QuickTestAsync()
    {
        try
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var projectRoot = Directory.GetParent(baseDirectory)!.Parent!.Parent!.Parent!.FullName;
            var dbPath = @"D:\AqlanCenterProApp\AqlanCenterDatabase.db";

            if (!File.Exists(dbPath))
            {
                return (false, "❌ ملف قاعدة البيانات غير موجود");
            }

            var connectionString = $"Data Source={dbPath}";
            var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
            optionsBuilder.UseSqlite(connectionString);

            using var context = new AqlanCenterDbContext(optionsBuilder.Options);

            var canConnect = await context.Database.CanConnectAsync();
            if (!canConnect)
            {
                return (false, "❌ فشل في الاتصال بقاعدة البيانات");
            }

            var usersCount = await context.Users.CountAsync();
            var rolesCount = await context.Roles.CountAsync();

            if (usersCount == 0 || rolesCount == 0)
            {
                return (false, "❌ البيانات الأساسية مفقودة");
            }

            return (true, $"✅ قاعدة البيانات تعمل بشكل صحيح ({usersCount} مستخدمين، {rolesCount} أدوار)");
        }
        catch (Exception ex)
        {
            return (false, $"❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
        }
    }
}
