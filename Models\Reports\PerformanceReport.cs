using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// تقرير الأداء
    /// </summary>
    public class PerformanceReport : ReportBase
    {
        public PerformanceReport()
        {
            Type = ReportType.PerformanceReport;
        }

        // إحصائيات عامة
        public int TotalDoctors { get; set; }
        public int TotalEmployees { get; set; }
        public double OverallSatisfactionRate { get; set; }
        public double AverageResponseTime { get; set; }

        // أداء الأطباء
        public List<DoctorPerformance> DoctorPerformances { get; set; } = new();

        // أداء الموظفين
        public List<EmployeePerformance> EmployeePerformances { get; set; } = new();

        // تقييمات المرضى
        public List<PatientRating> PatientRatings { get; set; } = new();

        // مؤشرات الأداء الرئيسية
        public List<KPIMetric> KPIMetrics { get; set; } = new();

        // مقارنة الأداء
        public List<PerformanceComparison> PerformanceComparisons { get; set; } = new();
    }

    /// <summary>
    /// أداء الطبيب
    /// </summary>
    public class DoctorPerformance
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string Specialty { get; set; } = string.Empty;
        public int TotalPatients { get; set; }
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public double CompletionRate { get; set; }
        public double AverageRating { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageRevenuePerPatient { get; set; }
        public double PatientSatisfactionRate { get; set; }
        public int OnTimeAppointments { get; set; }
        public double PunctualityRate { get; set; }
        public int TreatmentSuccessRate { get; set; }
    }

    /// <summary>
    /// أداء الموظف
    /// </summary>
    public class EmployeePerformance
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public int WorkDays { get; set; }
        public int AbsentDays { get; set; }
        public double AttendanceRate { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksAssigned { get; set; }
        public double TaskCompletionRate { get; set; }
        public double AverageRating { get; set; }
        public int OvertimeHours { get; set; }
        public double EfficiencyScore { get; set; }
    }

    /// <summary>
    /// تقييم المريض
    /// </summary>
    public class PatientRating
    {
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public double OverallRating { get; set; }
        public double ServiceRating { get; set; }
        public double CleanlinessRating { get; set; }
        public double WaitingTimeRating { get; set; }
        public double CommunicationRating { get; set; }
        public string? Comments { get; set; }
        public DateTime RatingDate { get; set; }
    }

    /// <summary>
    /// مؤشر الأداء الرئيسي
    /// </summary>
    public class KPIMetric
    {
        public string MetricName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public double CurrentValue { get; set; }
        public double TargetValue { get; set; }
        public double PreviousValue { get; set; }
        public double Improvement { get; set; }
        public string Unit { get; set; } = string.Empty;
        public bool IsPositive { get; set; }
        public string Status { get; set; } = string.Empty; // Excellent, Good, Fair, Poor
    }

    /// <summary>
    /// مقارنة الأداء
    /// </summary>
    public class PerformanceComparison
    {
        public string MetricName { get; set; } = string.Empty;
        public double CurrentPeriod { get; set; }
        public double PreviousPeriod { get; set; }
        public double Change { get; set; }
        public double ChangePercentage { get; set; }
        public bool IsImprovement { get; set; }
        public string Trend { get; set; } = string.Empty; // Up, Down, Stable
    }
} 