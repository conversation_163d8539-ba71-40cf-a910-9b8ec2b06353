using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.IO;

namespace AqlanCenterProApp.Services.Implementations
{
    public class SettingsService : ISettingsService
    {
        private readonly AqlanCenterDbContext _context;
        private readonly ILogger<SettingsService> _logger;

        public SettingsService(AqlanCenterDbContext context, ILogger<SettingsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region إعدادات العيادة
        public async Task<ClinicSettings> GetClinicSettingsAsync()
        {
            try
            {
                var settings = await _context.ClinicSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    settings = new ClinicSettings
                    {
                        ClinicName = "مركز عقيلان الطبي",
                        DefaultLanguage = "ar"
                    };
                    _context.ClinicSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات العيادة");
                throw;
            }
        }

        public async Task<bool> UpdateClinicSettingsAsync(ClinicSettings settings)
        {
            try
            {
                var existing = await _context.ClinicSettings.FirstOrDefaultAsync();
                if (existing != null)
                {
                    _context.Entry(existing).CurrentValues.SetValues(settings);
                }
                else
                {
                    _context.ClinicSettings.Add(settings);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات العيادة");
                return false;
            }
        }
        #endregion

        #region الإعدادات العامة
        public async Task<SystemSettings> GetSystemSettingsAsync()
        {
            try
            {
                var settings = await _context.SystemSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    settings = new SystemSettings();
                    _context.SystemSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإعدادات العامة");
                throw;
            }
        }

        public async Task<bool> UpdateSystemSettingsAsync(SystemSettings settings)
        {
            try
            {
                var existing = await _context.SystemSettings.FirstOrDefaultAsync();
                if (existing != null)
                {
                    _context.Entry(existing).CurrentValues.SetValues(settings);
                }
                else
                {
                    _context.SystemSettings.Add(settings);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الإعدادات العامة");
                return false;
            }
        }
        #endregion

        #region إعدادات الإشعارات
        public async Task<NotificationSettings> GetNotificationSettingsAsync()
        {
            try
            {
                var settings = await _context.NotificationSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    settings = new NotificationSettings();
                    _context.NotificationSettings.Add(settings);
                    await _context.SaveChangesAsync();
                }
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات الإشعارات");
                throw;
            }
        }

        public async Task<bool> UpdateNotificationSettingsAsync(NotificationSettings settings)
        {
            try
            {
                var existing = await _context.NotificationSettings.FirstOrDefaultAsync();
                if (existing != null)
                {
                    _context.Entry(existing).CurrentValues.SetValues(settings);
                }
                else
                {
                    _context.NotificationSettings.Add(settings);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات الإشعارات");
                return false;
            }
        }
        #endregion

        #region القوائم المساعدة
        public async Task<List<SystemLookup>> GetLookupsByTypeAsync(string lookupType)
        {
            try
            {
                return await _context.SystemLookups
                    .Where(l => l.LookupType == lookupType && l.IsActive && !l.IsDeleted)
                    .OrderBy(l => l.SortOrder)
                    .ThenBy(l => l.Name)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب القوائم المساعدة");
                return new List<SystemLookup>();
            }
        }

        public async Task<SystemLookup?> GetLookupByIdAsync(int id)
        {
            try
            {
                return await _context.SystemLookups
                    .FirstOrDefaultAsync(l => l.Id == id && !l.IsDeleted)
                    .ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عنصر القائمة");
                return null;
            }
        }

        public async Task<bool> AddLookupAsync(SystemLookup lookup)
        {
            try
            {
                _context.SystemLookups.Add(lookup);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة عنصر القائمة");
                return false;
            }
        }

        public async Task<bool> UpdateLookupAsync(SystemLookup lookup)
        {
            try
            {
                var existing = await _context.SystemLookups.FindAsync(lookup.Id);
                if (existing != null)
                {
                    _context.Entry(existing).CurrentValues.SetValues(lookup);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عنصر القائمة");
                return false;
            }
        }

        public async Task<bool> DeleteLookupAsync(int id)
        {
            try
            {
                var lookup = await _context.SystemLookups.FindAsync(id);
                if (lookup != null)
                {
                    lookup.IsDeleted = true;
                    lookup.DeletedAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف عنصر القائمة");
                return false;
            }
        }

        public async Task<bool> ReorderLookupsAsync(List<int> lookupIds)
        {
            try
            {
                for (int i = 0; i < lookupIds.Count; i++)
                {
                    var lookup = await _context.SystemLookups.FindAsync(lookupIds[i]);
                    if (lookup != null)
                    {
                        lookup.SortOrder = i;
                    }
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة ترتيب القوائم");
                return false;
            }
        }
        #endregion

        #region استيراد/تصدير الإعدادات
        public async Task<bool> ExportSettingsAsync(string filePath)
        {
            try
            {
                var settingsProfile = new
                {
                    ClinicSettings = await GetClinicSettingsAsync(),
                    SystemSettings = await GetSystemSettingsAsync(),
                    NotificationSettings = await GetNotificationSettingsAsync(),
                    Lookups = await _context.SystemLookups.Where(l => !l.IsDeleted).ToListAsync(),
                    ExportDate = DateTime.Now
                };

                var json = JsonSerializer.Serialize(settingsProfile, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الإعدادات");
                return false;
            }
        }

        public async Task<bool> ImportSettingsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath);
                var settingsProfile = JsonSerializer.Deserialize<dynamic>(json);

                // تطبيق الإعدادات المستوردة
                // سيتم تنفيذ هذا لاحقاً

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استيراد الإعدادات");
                return false;
            }
        }

        public async Task<string> GetSettingsProfileAsync()
        {
            try
            {
                var settingsProfile = new
                {
                    ClinicSettings = await GetClinicSettingsAsync(),
                    SystemSettings = await GetSystemSettingsAsync(),
                    NotificationSettings = await GetNotificationSettingsAsync(),
                    ExportDate = DateTime.Now
                };

                return JsonSerializer.Serialize(settingsProfile, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب ملف الإعدادات");
                return string.Empty;
            }
        }
        #endregion

        #region التحقق من كلمة المرور
        public async Task<bool> ValidateSettingsPasswordAsync(string password)
        {
            try
            {
                // يمكن إضافة منطق التحقق من كلمة المرور هنا
                // حالياً نرجع true للتبسيط
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من كلمة المرور");
                return false;
            }
        }
        #endregion

        #region تطبيق الإعدادات
        public async Task<bool> ApplySettingsAsync()
        {
            try
            {
                // تطبيق الإعدادات على النظام
                // سيتم تنفيذ هذا لاحقاً
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تطبيق الإعدادات");
                return false;
            }
        }
        #endregion

        #region إعادة تعيين الإعدادات
        public async Task<bool> ResetToDefaultsAsync()
        {
            try
            {
                // إعادة تعيين الإعدادات إلى القيم الافتراضية
                // سيتم تنفيذ هذا لاحقاً
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                return false;
            }
        }
        #endregion

        #region التحقق من التحديثات
        public async Task<bool> CheckForUpdatesAsync()
        {
            try
            {
                // التحقق من وجود تحديثات جديدة
                // سيتم تنفيذ هذا لاحقاً
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من التحديثات");
                return false;
            }
        }

        public async Task<bool> DownloadUpdateAsync(string updateUrl)
        {
            try
            {
                // تحميل التحديث
                // سيتم تنفيذ هذا لاحقاً
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل التحديث");
                return false;
            }
        }
        #endregion

        #region إعدادات النسخ الاحتياطي
        public async Task<string> GetBackupPathAsync()
        {
            try
            {
                var settings = await GetSystemSettingsAsync();
                return settings.BackupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مسار النسخ الاحتياطي");
                return "Backups";
            }
        }

        public async Task<bool> SetBackupPathAsync(string path)
        {
            try
            {
                var settings = await GetSystemSettingsAsync();
                settings.BackupPath = path;
                return await UpdateSystemSettingsAsync(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تعيين مسار النسخ الاحتياطي");
                return false;
            }
        }

        public async Task<bool> TestBackupPathAsync(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }

                var testFile = Path.Combine(path, "test.txt");
                await File.WriteAllTextAsync(testFile, "test");
                File.Delete(testFile);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اختبار مسار النسخ الاحتياطي");
                return false;
            }
        }
        #endregion
    }
}