using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الأطباء
    /// </summary>
    public interface IDoctorService
    {
        /// <summary>
        /// الحصول على جميع الأطباء
        /// </summary>
        Task<List<Doctor>> GetAllDoctorsAsync();

        /// <summary>
        /// الحصول على الأطباء النشطين فقط
        /// </summary>
        Task<List<Doctor>> GetActiveDoctorsAsync();

        /// <summary>
        /// الحصول على طبيب بالمعرف
        /// </summary>
        Task<Doctor?> GetDoctorByIdAsync(int doctorId);

        /// <summary>
        /// البحث عن الأطباء
        /// </summary>
        Task<List<Doctor>> SearchDoctorsAsync(string searchTerm);

        /// <summary>
        /// تصفية الأطباء حسب التخصص
        /// </summary>
        Task<List<Doctor>> GetDoctorsBySpecializationAsync(string specialization);

        /// <summary>
        /// تصفية الأطباء حسب الحالة
        /// </summary>
        Task<List<Doctor>> GetDoctorsByStatusAsync(string status);

        /// <summary>
        /// إضافة طبيب جديد
        /// </summary>
        Task<bool> AddDoctorAsync(Doctor doctor, string createdBy);

        /// <summary>
        /// تحديث بيانات طبيب
        /// </summary>
        Task<bool> UpdateDoctorAsync(Doctor doctor, string updatedBy);

        /// <summary>
        /// حذف طبيب
        /// </summary>
        Task<bool> DeleteDoctorAsync(int doctorId, string deletedBy);

        /// <summary>
        /// تفعيل/تعطيل طبيب
        /// </summary>
        Task<bool> ToggleDoctorStatusAsync(int doctorId, string updatedBy);

        /// <summary>
        /// الحصول على إحصائيات الطبيب
        /// </summary>
        Task<DoctorStatistics> GetDoctorStatisticsAsync(int doctorId);

        /// <summary>
        /// الحصول على إحصائيات عامة للأطباء
        /// </summary>
        Task<GeneralDoctorStatistics> GetGeneralStatisticsAsync();

        /// <summary>
        /// تحديث تقييم الطبيب
        /// </summary>
        Task<bool> UpdateDoctorRatingAsync(int doctorId, decimal rating);

        /// <summary>
        /// الحصول على الأطباء المتاحين للمواعيد
        /// </summary>
        Task<List<Doctor>> GetAvailableDoctorsAsync();

        /// <summary>
        /// التحقق من انتهاء تراخيص الأطباء
        /// </summary>
        Task<List<Doctor>> GetDoctorsWithExpiringLicensesAsync(int daysBeforeExpiry = 30);

        /// <summary>
        /// التحقق من انتهاء عقود الأطباء
        /// </summary>
        Task<List<Doctor>> GetDoctorsWithExpiringContractsAsync(int daysBeforeExpiry = 30);

        /// <summary>
        /// الحصول على أفضل الأطباء حسب التقييم
        /// </summary>
        Task<List<Doctor>> GetTopRatedDoctorsAsync(int count = 10);

        /// <summary>
        /// تحديث إحصائيات الطبيب
        /// </summary>
        Task<bool> UpdateDoctorStatisticsAsync(int doctorId);
    }

    /// <summary>
    /// إحصائيات الطبيب الفردية
    /// </summary>
    public class DoctorStatistics
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int TotalPatients { get; set; }
        public int TotalSessions { get; set; }
        public int CompletedSessions { get; set; }
        public int PendingSessions { get; set; }
        public decimal TotalEarnings { get; set; }
        public decimal AverageRating { get; set; }
        public int TotalRatings { get; set; }
        public DateTime LastSessionDate { get; set; }
        public int SessionsThisMonth { get; set; }
        public decimal EarningsThisMonth { get; set; }
    }

    /// <summary>
    /// الإحصائيات العامة للأطباء
    /// </summary>
    public class GeneralDoctorStatistics
    {
        public int TotalDoctors { get; set; }
        public int ActiveDoctors { get; set; }
        public int InactiveDoctors { get; set; }
        public int OnLeaveDoctors { get; set; }
        public int FullTimeDoctors { get; set; }
        public int PartTimeDoctors { get; set; }
        public int CommissionBasedDoctors { get; set; }
        public decimal AverageRating { get; set; }
        public int DoctorsWithExpiringLicenses { get; set; }
        public int DoctorsWithExpiringContracts { get; set; }
        public Dictionary<string, int> DoctorsBySpecialization { get; set; } = new();
    }
}
