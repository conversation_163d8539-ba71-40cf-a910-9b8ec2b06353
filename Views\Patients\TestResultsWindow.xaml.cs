using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class TestResultsWindow : Window
    {
        public ObservableCollection<TestResult> TestResults { get; set; }

        public TestResultsWindow()
        {
            InitializeComponent();
            TestResults = new ObservableCollection<TestResult>();
            LoadSampleData();
            TestResultsDataGrid.ItemsSource = TestResults;
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للعرض
            TestResults.Add(new TestResult
            {
                Id = 1,
                PatientName = "أحمد محمد علي",
                TestType = "أشعة بانوراما",
                TestDate = DateTime.Now.AddDays(-7),
                ResultDate = DateTime.Now.AddDays(-5),
                Result = "لا توجد مشاكل واضحة، أسنان سليمة بشكل عام",
                RequestingDoctor = "د. سارة أحمد",
                Laboratory = "مختبر الأشعة المتقدم",
                Status = "مكتمل"
            });

            TestResults.Add(new TestResult
            {
                Id = 2,
                PatientName = "فاطمة حسن محمد",
                TestType = "فحص دم",
                TestDate = DateTime.Now.AddDays(-10),
                ResultDate = DateTime.Now.AddDays(-8),
                Result = "مستوى السكر طبيعي، لا توجد علامات التهاب",
                RequestingDoctor = "د. محمد عبدالله",
                Laboratory = "مختبر التحاليل الطبية",
                Status = "مكتمل"
            });

            TestResults.Add(new TestResult
            {
                Id = 3,
                PatientName = "خالد عبدالرحمن",
                TestType = "أشعة سينية",
                TestDate = DateTime.Now.AddDays(-3),
                ResultDate = DateTime.Now.AddDays(-1),
                Result = "كسر بسيط في جذر السن، يحتاج لعلاج",
                RequestingDoctor = "د. أمل سالم",
                Laboratory = "مختبر الأشعة المتقدم",
                Status = "مكتمل"
            });

            TestResults.Add(new TestResult
            {
                Id = 4,
                PatientName = "مريم أحمد سالم",
                TestType = "مزرعة بكتيريا",
                TestDate = DateTime.Now.AddDays(-2),
                ResultDate = null,
                Result = "قيد الانتظار",
                RequestingDoctor = "د. سارة أحمد",
                Laboratory = "مختبر الميكروبيولوجي",
                Status = "قيد الانتظار"
            });
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق البحث والفلاتر", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is TestResult testResult)
            {
                ShowTestResultDetails(testResult);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is TestResult testResult)
            {
                PrintTestResult(testResult);
            }
        }

        private void AttachmentsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is TestResult testResult)
            {
                ShowAttachments(testResult);
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is TestResult testResult)
            {
                EditTestResult(testResult);
            }
        }

        private void ViewDetailsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                ShowTestResultDetails(testResult);
            }
        }

        private void PrintMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                PrintTestResult(testResult);
            }
        }

        private void AttachmentsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                ShowAttachments(testResult);
            }
        }

        private void EditMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                EditTestResult(testResult);
            }
        }

        private void CopyMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                var testInfo = $"نتيجة الفحص رقم: {testResult.Id}\n" +
                              $"المريض: {testResult.PatientName}\n" +
                              $"نوع الفحص: {testResult.TestType}\n" +
                              $"تاريخ الفحص: {testResult.TestDate:dd/MM/yyyy}\n" +
                              $"النتيجة: {testResult.Result}\n" +
                              $"الطبيب المطلوب: {testResult.RequestingDoctor}";
                
                Clipboard.SetText(testInfo);
                MessageBox.Show("تم نسخ معلومات نتيجة الفحص", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير وظيفة التصدير قريباً", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (TestResultsDataGrid.SelectedItem is TestResult testResult)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف نتيجة الفحص للمريض {testResult.PatientName}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    TestResults.Remove(testResult);
                    MessageBox.Show("تم حذف نتيجة الفحص بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void AddTestResultButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة نتيجة فحص جديدة", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportAllButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير جميع نتائج الفحوصات", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void StatisticsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم عرض إحصائيات الفحوصات", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ShowTestResultDetails(TestResult testResult)
        {
            var details = $"تفاصيل نتيجة الفحص\n\n" +
                         $"رقم الفحص: {testResult.Id}\n" +
                         $"اسم المريض: {testResult.PatientName}\n" +
                         $"نوع الفحص: {testResult.TestType}\n" +
                         $"تاريخ الفحص: {testResult.TestDate:dd/MM/yyyy}\n" +
                         $"تاريخ النتيجة: {(testResult.ResultDate?.ToString("dd/MM/yyyy") ?? "لم تصدر بعد")}\n" +
                         $"النتيجة: {testResult.Result}\n" +
                         $"الطبيب المطلوب: {testResult.RequestingDoctor}\n" +
                         $"المختبر: {testResult.Laboratory}\n" +
                         $"الحالة: {testResult.Status}";

            MessageBox.Show(details, "تفاصيل نتيجة الفحص", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditTestResult(TestResult testResult)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل نتيجة الفحص للمريض: {testResult.PatientName}", 
                "تعديل نتيجة الفحص", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintTestResult(TestResult testResult)
        {
            MessageBox.Show($"سيتم طباعة نتيجة الفحص للمريض: {testResult.PatientName}", 
                "طباعة نتيجة الفحص", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowAttachments(TestResult testResult)
        {
            MessageBox.Show($"سيتم عرض المرفقات لنتيجة الفحص للمريض: {testResult.PatientName}", 
                "المرفقات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // نموذج نتيجة الفحص
    public class TestResult
    {
        public int Id { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string TestType { get; set; } = string.Empty;
        public DateTime TestDate { get; set; }
        public DateTime? ResultDate { get; set; }
        public string Result { get; set; } = string.Empty;
        public string RequestingDoctor { get; set; } = string.Empty;
        public string Laboratory { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}
