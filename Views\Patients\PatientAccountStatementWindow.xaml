<Window x:Class="AqlanCenterProApp.Views.Patients.PatientAccountStatementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="كشف حساب المريض" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" Background="#F8F9FA" FlowDirection="RightToLeft">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
            <TextBlock Text="كشف حساب المريض" FontSize="28" FontWeight="Bold" Foreground="#4A90E2" Margin="0,0,10,0"/>
            <TextBlock x:Name="PatientNameText" FontSize="20" FontWeight="Bold" Foreground="#333"/>
        </StackPanel>

        <!-- DataGrid -->
        <DataGrid x:Name="ReceiptsDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True"
                  ItemsSource="{Binding Receipts}" Margin="0,0,0,10" RowHeight="32" HeadersVisibility="Column"
                  CanUserAddRows="False" EnableRowVirtualization="True" EnableColumnVirtualization="True">
            <DataGrid.Columns>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding ReceiptDate, StringFormat=yyyy/MM/dd}" Width="120"/>
                <DataGridTextColumn Header="النوع" Binding="{Binding Purpose}" Width="120"/>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="100"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                <DataGridTextColumn Header="المستخدم" Binding="{Binding IssuedBy}" Width="100"/>
                <DataGridTemplateColumn Header="إلغاء" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="❌ إلغاء" Click="CancelReceipt_Click" Visibility="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=DataContext.CanCancelReceipts, Converter={StaticResource BoolToVisibilityConverter}}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Footer: الرصيد وزر الطباعة -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
            <TextBlock Text="الرصيد الحالي: " FontSize="18" FontWeight="Bold" Foreground="#333"/>
            <TextBlock x:Name="BalanceText" FontSize="18" FontWeight="Bold" Foreground="#28A745" Margin="5,0,0,0"/>
            <Button Content="🖨️ طباعة كشف الحساب" Click="PrintButton_Click" Margin="30,0,0,0" Padding="15,5" Background="#4A90E2" Foreground="White" FontWeight="Bold"/>
        </StackPanel>
    </Grid>
</Window> 