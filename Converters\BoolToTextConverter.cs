using System;
using System.Globalization;
using System.Windows.Data;

namespace AqlanCenterProApp.Converters
{
    /// <summary>
    /// Converter للتحويل من Boolean إلى نص
    /// </summary>
    public class BoolToTextConverter : IValueConverter
    {
        public string TrueText { get; set; } = "نعم";
        public string FalseText { get; set; } = "لا";

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? TrueText : FalseText;
            }
            return FalseText;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Equals(TrueText, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }
    }
} 