using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface ILabService
    {
        Task<IEnumerable<Lab>> GetAllLabsAsync();
        Task<IEnumerable<Lab>> GetActiveLabsAsync();
        Task<Lab?> GetLabByIdAsync(int id);
        Task<Lab> AddLabAsync(Lab lab);
        Task<Lab> UpdateLabAsync(Lab lab);
        Task<bool> DeleteLabAsync(int id);
        Task<bool> LabExistsAsync(int id);
        Task<IEnumerable<Lab>> SearchLabsAsync(string searchTerm);
        Task<Lab> GetLabByNameAsync(string name);
        Task<IEnumerable<Lab>> GetLabsByPerformanceAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetLabAverageRatingAsync(int labId);
        Task<int> GetLabOrdersCountAsync(int labId, DateTime startDate, DateTime endDate);
        Task<int> GetLabOverdueOrdersCountAsync(int labId);
    }
} 