using System.Collections.ObjectModel;
using System.ComponentModel;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeLeavesViewModel : INotifyPropertyChanged
    {
        private ObservableCollection<EmployeeLeave> _leaveRecords = new ObservableCollection<EmployeeLeave>();
        public ObservableCollection<EmployeeLeave> LeaveRecords
        {
            get => _leaveRecords;
            set { _leaveRecords = value; OnPropertyChanged(nameof(LeaveRecords)); }
        }

        public EmployeeLeavesViewModel(Employee employee)
        {
            // Load leaves for the given employee
            LeaveRecords = new ObservableCollection<EmployeeLeave>(employee.Leaves ?? new List<EmployeeLeave>());
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 