using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace AqlanCenterProApp.Converters
{
    /// <summary>
    /// محول لتحويل حالات المشتريات إلى ألوان
    /// </summary>
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "Pending" => new SolidColorBrush(Colors.Orange),
                    "Received" => new SolidColorBrush(Colors.Green),
                    "Cancelled" => new SolidColorBrush(Colors.Red),
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 