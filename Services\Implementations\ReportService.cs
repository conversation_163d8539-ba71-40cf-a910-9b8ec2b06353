using AqlanCenterProApp.Models.Reports;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.IO;
using System.Collections.Generic;
using System.Linq;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة التقارير
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly List<ReportBase> _savedReports = new();
        private int _nextReportId = 1;
        private readonly AqlanCenterDbContext _context;

        public ReportService(AqlanCenterDbContext context)
        {
            _context = context;
            // إضافة بعض التقارير التجريبية
            InitializeSampleReports();
        }

        #region تقارير المرضى
        public async Task<PatientReport> GeneratePatientReportAsync()
        {
            var patients = await _context.Patients
                .Include(p => p.Sessions)
                .Include(p => p.Payments)
                .Include(p => p.Appointments)
                .ToListAsync();

            var totalPatients = patients.Count;
            var newPatients = patients.Count(p => p.RegistrationDate >= DateTime.Today.AddMonths(-1));
            var activePatients = patients.Count(p => p.IsActive);
            var inactivePatients = patients.Count(p => !p.IsActive);
            var malePatients = patients.Count(p => p.Gender == "ذكر");
            var femalePatients = patients.Count(p => p.Gender == "أنثى");
            
            // حساب الفئات العمرية
            var childrenCount = patients.Count(p => p.Age.HasValue && p.Age.Value <= 12);
            var teenagersCount = patients.Count(p => p.Age.HasValue && p.Age.Value >= 13 && p.Age.Value <= 19);
            var adultsCount = patients.Count(p => p.Age.HasValue && p.Age.Value >= 20 && p.Age.Value <= 59);
            var elderlyCount = patients.Count(p => p.Age.HasValue && p.Age.Value >= 60);

            // حساب متوسط العمر
            var patientsWithAge = patients.Where(p => p.Age.HasValue).ToList();
            var averageAge = patientsWithAge.Any() ? patientsWithAge.Average(p => p.Age.Value) : 0;

            // المرضى الأكثر نشاطاً (حسب عدد الجلسات)
            var topActivePatients = patients
                .OrderByDescending(p => p.Sessions.Count)
                .Take(10)
                .Select(p => new PatientActivity
                {
                    PatientId = p.Id,
                    PatientName = p.FullName,
                    AppointmentsCount = p.Appointments.Count,
                    TreatmentsCount = p.Sessions.Count,
                    TotalPaid = p.Payments.Sum(pay => pay.Amount),
                    LastVisit = p.Sessions.Any() ? p.Sessions.Max(s => s.SessionDate) : p.RegistrationDate
                })
                .ToList();

            // المرضى الجدد حسب الشهر
            var newPatientsByMonth = patients
                .Where(p => p.RegistrationDate >= DateTime.Today.AddMonths(-6))
                .GroupBy(p => new { p.RegistrationDate.Year, p.RegistrationDate.Month })
                .OrderBy(g => g.Key.Year)
                .ThenBy(g => g.Key.Month)
                .Select(g => new MonthlyPatientCount
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                    Count = g.Count()
                })
                .ToList();

            return new PatientReport
            {
                TotalPatients = totalPatients,
                NewPatients = newPatients,
                ActivePatients = activePatients,
                InactivePatients = inactivePatients,
                MalePatients = malePatients,
                FemalePatients = femalePatients,
                ChildrenCount = childrenCount,
                TeenagersCount = teenagersCount,
                AdultsCount = adultsCount,
                ElderlyCount = elderlyCount,
                AverageAge = averageAge,
                TopActivePatients = topActivePatients,
                NewPatientsByMonth = newPatientsByMonth
            };
        }

        private int CalculateAge(DateTime birthDate, DateTime currentDate)
        {
            var age = currentDate.Year - birthDate.Year;
            if (birthDate.Date > currentDate.AddYears(-age)) age--;
            return age;
        }

        public async Task<List<PatientActivity>> GetTopActivePatientsAsync(DateTime startDate, DateTime endDate, int count = 10)
        {
            var patientActivities = await _context.Patients
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .Select(p => new PatientActivity
                {
                    PatientId = p.Id,
                    PatientName = $"{p.FullName}",
                    AppointmentsCount = _context.Appointments.Count(a => a.PatientId == p.Id),
                    TreatmentsCount = _context.Sessions.Count(s => s.PatientId == p.Id),
                    TotalPaid = _context.Receipts.Where(r => r.PatientId == p.Id).Sum(r => r.Amount),
                    LastVisit = _context.Appointments
                        .Where(a => a.PatientId == p.Id)
                        .OrderByDescending(a => a.AppointmentDate)
                        .Select(a => a.AppointmentDate)
                        .FirstOrDefault()
                })
                .OrderByDescending(pa => pa.AppointmentsCount)
                .Take(count)
                .ToListAsync();

            return patientActivities;
        }

        public async Task<List<MonthlyPatientCount>> GetNewPatientsByMonthAsync(int year)
        {
            var monthlyCounts = await _context.Patients
                .Where(p => p.CreatedAt.Year == year)
                .GroupBy(p => p.CreatedAt.Month)
                .Select(g => new MonthlyPatientCount
                {
                    Year = year,
                    Month = g.Key,
                    MonthName = GetMonthName(g.Key),
                    Count = g.Count()
                })
                .OrderBy(mc => mc.Month)
                .ToListAsync();

            // إضافة الأشهر الفارغة
            for (int month = 1; month <= 12; month++)
            {
                if (!monthlyCounts.Any(mc => mc.Month == month))
                {
                    monthlyCounts.Add(new MonthlyPatientCount
                    {
                        Year = year,
                        Month = month,
                        MonthName = GetMonthName(month),
                        Count = 0
                    });
                }
            }

            return monthlyCounts.OrderBy(mc => mc.Month).ToList();
        }

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
        #endregion

        #region تقارير المواعيد
        public async Task<AppointmentReport> GenerateAppointmentReportAsync()
        {
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Doctor)
                .ToListAsync();

            var totalAppointments = appointments.Count;
            var completedAppointments = appointments.Count(a => a.Status == "مكتمل");
            var cancelledAppointments = appointments.Count(a => a.Status == "ملغي");
            var rescheduledAppointments = appointments.Count(a => a.Status == "مؤجل");
            var noShowAppointments = appointments.Count(a => a.Status == "لم يحضر");

            // حساب النسب
            var attendanceRate = totalAppointments > 0 ? (double)completedAppointments / totalAppointments * 100 : 0;
            var cancellationRate = totalAppointments > 0 ? (double)cancelledAppointments / totalAppointments * 100 : 0;
            var noShowRate = totalAppointments > 0 ? (double)noShowAppointments / totalAppointments * 100 : 0;

            // إحصائيات حسب الطبيب
            var doctorStats = appointments
                .GroupBy(a => a.Doctor)
                .Select(g => new DoctorAppointmentStats
                {
                    DoctorId = g.Key?.Id ?? 0,
                    DoctorName = g.Key?.FullName ?? "غير محدد",
                    TotalAppointments = g.Count(),
                    CompletedAppointments = g.Count(a => a.Status == "مكتمل"),
                    CancelledAppointments = g.Count(a => a.Status == "ملغي"),
                    CompletionRate = g.Count() > 0 ? (double)g.Count(a => a.Status == "مكتمل") / g.Count() * 100 : 0,
                    AverageRating = 4.5 // افتراضي
                })
                .ToList();

            // إحصائيات حسب الشهر
            var monthlyStats = appointments
                .Where(a => a.AppointmentDate >= DateTime.Today.AddMonths(-6))
                .GroupBy(a => new { a.AppointmentDate.Year, a.AppointmentDate.Month })
                .OrderBy(g => g.Key.Year)
                .ThenBy(g => g.Key.Month)
                .Select(g => new MonthlyAppointmentCount
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                    TotalCount = g.Count(),
                    CompletedCount = g.Count(a => a.Status == "مكتمل"),
                    CancelledCount = g.Count(a => a.Status == "ملغي")
                })
                .ToList();

            // إحصائيات الوقت
            var morningAppointments = appointments.Count(a => a.AppointmentDate.Hour >= 8 && a.AppointmentDate.Hour < 12);
            var afternoonAppointments = appointments.Count(a => a.AppointmentDate.Hour >= 12 && a.AppointmentDate.Hour < 17);
            var eveningAppointments = appointments.Count(a => a.AppointmentDate.Hour >= 17 && a.AppointmentDate.Hour < 21);

            return new AppointmentReport
            {
                TotalAppointments = totalAppointments,
                CompletedAppointments = completedAppointments,
                CancelledAppointments = cancelledAppointments,
                RescheduledAppointments = rescheduledAppointments,
                NoShowAppointments = noShowAppointments,
                AttendanceRate = attendanceRate,
                CancellationRate = cancellationRate,
                NoShowRate = noShowRate,
                DoctorStats = doctorStats,
                MonthlyStats = monthlyStats,
                MorningAppointments = morningAppointments,
                AfternoonAppointments = afternoonAppointments,
                EveningAppointments = eveningAppointments,
                AverageAppointmentDuration = 45.0,
                DelayedAppointments = 0,
                AverageDelayMinutes = 0
            };
        }

        public async Task<List<DoctorAppointmentStats>> GetDoctorAppointmentStatsAsync(DateTime startDate, DateTime endDate)
        {
            var doctorStats = await _context.Doctors
                .Select(d => new DoctorAppointmentStats
                {
                    DoctorId = d.Id,
                    DoctorName = $"د. {d.FullName}",
                    TotalAppointments = _context.Appointments.Count(a => a.DoctorId == d.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate),
                    CompletedAppointments = _context.Appointments.Count(a => a.DoctorId == d.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate && a.Status == "مكتمل"),
                    CancelledAppointments = _context.Appointments.Count(a => a.DoctorId == d.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate && a.Status == "ملغي"),
                    CompletionRate = 0, // سيتم حسابه أدناه
                    AverageRating = 4.5 // افتراضي
                })
                .Where(ds => ds.TotalAppointments > 0)
                .ToListAsync();

            // حساب نسبة الإكمال
            foreach (var stat in doctorStats)
            {
                if (stat.TotalAppointments > 0)
                {
                    stat.CompletionRate = (double)stat.CompletedAppointments / stat.TotalAppointments * 100;
                }
            }

            return doctorStats.OrderByDescending(ds => ds.TotalAppointments).ToList();
        }

        public async Task<List<PatientCommitment>> GetPatientCommitmentAsync(DateTime startDate, DateTime endDate, bool topCommitted = true)
        {
            var patientCommitments = await _context.Patients
                .Select(p => new PatientCommitment
                {
                    PatientId = p.Id,
                    PatientName = $"{p.FullName}",
                    TotalAppointments = _context.Appointments.Count(a => a.PatientId == p.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate),
                    AttendedAppointments = _context.Appointments.Count(a => a.PatientId == p.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate && a.Status == "مكتمل"),
                    MissedAppointments = _context.Appointments.Count(a => a.PatientId == p.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate && a.Status == "لم يحضر"),
                    AttendanceRate = 0, // سيتم حسابه أدناه
                    ConsecutiveMissed = 0 // سيتم حسابه لاحقاً
                })
                .Where(pc => pc.TotalAppointments > 0)
                .ToListAsync();

            // حساب معدل الحضور
            foreach (var commitment in patientCommitments)
            {
                if (commitment.TotalAppointments > 0)
                {
                    commitment.AttendanceRate = (double)commitment.AttendedAppointments / commitment.TotalAppointments * 100;
                }
            }

            // ترتيب حسب معدل الحضور
            return topCommitted 
                ? patientCommitments.OrderByDescending(pc => pc.AttendanceRate).Take(10).ToList()
                : patientCommitments.OrderBy(pc => pc.AttendanceRate).Take(10).ToList();
        }
        #endregion

        #region التقارير المالية
        public async Task<FinancialReport> GenerateFinancialReportAsync()
        {
            var receipts = await _context.Receipts.ToListAsync();
            var invoices = await _context.Invoices.ToListAsync();
            var payments = await _context.PaymentVouchers.ToListAsync();

            var totalRevenue = receipts.Sum(r => r.Amount);
            var totalExpenses = payments.Sum(p => p.Amount);
            var netProfit = totalRevenue - totalExpenses;

            // إحصائيات الإيرادات حسب طريقة الدفع
            var cashRevenue = receipts.Where(r => r.PaymentMethod == "نقدي").Sum(r => r.Amount);
            var cardRevenue = receipts.Where(r => r.PaymentMethod == "بطاقة ائتمان").Sum(r => r.Amount);
            var insuranceRevenue = receipts.Where(r => r.PaymentMethod == "تأمين").Sum(r => r.Amount);
            var otherRevenue = receipts.Where(r => !new[] { "نقدي", "بطاقة ائتمان", "تأمين" }.Contains(r.PaymentMethod)).Sum(r => r.Amount);

            // إحصائيات المصروفات حسب الفئة
            var salariesExpenses = payments.Where(p => p.ExpenseType == "رواتب").Sum(p => p.Amount);
            var rentExpenses = payments.Where(p => p.ExpenseType == "إيجار").Sum(p => p.Amount);
            var utilitiesExpenses = payments.Where(p => p.ExpenseType == "مرافق").Sum(p => p.Amount);
            var suppliesExpenses = payments.Where(p => p.ExpenseType == "مستلزمات").Sum(p => p.Amount);
            var labExpenses = payments.Where(p => p.ExpenseType == "مختبر").Sum(p => p.Amount);
            var marketingExpenses = payments.Where(p => p.ExpenseType == "إعلانات").Sum(p => p.Amount);
            var otherExpenses = payments.Where(p => !new[] { "رواتب", "إيجار", "مرافق", "مستلزمات", "مختبر", "إعلانات" }.Contains(p.ExpenseType)).Sum(p => p.Amount);

            // حساب الربح
            var grossProfit = totalRevenue - totalExpenses;
            var profitMargin = totalRevenue > 0 ? (double)netProfit / (double)totalRevenue * 100 : 0;

            // المستحقات
            var totalReceivables = invoices.Where(i => i.InvoiceStatus == "غير مدفوع").Sum(i => i.TotalAmount);
            var overdueReceivables = invoices.Where(i => i.InvoiceStatus == "غير مدفوع" && i.DueDate < DateTime.Now).Sum(i => i.TotalAmount);
            var overdueInvoicesCount = invoices.Count(i => i.InvoiceStatus == "غير مدفوع" && i.DueDate < DateTime.Now);

            // الإيرادات الشهرية
            var monthlyRevenues = receipts
                .Where(r => r.ReceiptDate >= DateTime.Today.AddMonths(-6))
                .GroupBy(r => new { r.ReceiptDate.Year, r.ReceiptDate.Month })
                .OrderBy(g => g.Key.Year)
                .ThenBy(g => g.Key.Month)
                .Select(g => new MonthlyRevenue
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = $"{g.Key.Year}-{g.Key.Month:D2}",
                    Revenue = g.Sum(r => r.Amount),
                    Expenses = 0, // سيتم حسابها لاحقاً
                    Profit = g.Sum(r => r.Amount),
                    PatientsCount = g.Select(r => r.PatientId).Distinct().Count()
                })
                .ToList();

            // حساب المصروفات الشهرية
            var monthlyExpenses = await _context.PaymentVouchers
                .Where(p => p.VoucherDate.Year == DateTime.Now.Year)
                .GroupBy(p => p.VoucherDate.Month)
                .Select(g => new { Month = g.Key, Expenses = g.Sum(p => p.Amount) })
                .ToListAsync();

            // دمج البيانات وحساب الأرباح
            foreach (var revenue in monthlyRevenues)
            {
                var expenses = monthlyExpenses.FirstOrDefault(e => e.Month == revenue.Month)?.Expenses ?? 0;
                revenue.Expenses = expenses;
                revenue.Profit = revenue.Revenue - revenue.Expenses;
            }

            // إضافة الأشهر الفارغة
            for (int month = 1; month <= 12; month++)
            {
                if (!monthlyRevenues.Any(mr => mr.Month == month))
                {
                    monthlyRevenues.Add(new MonthlyRevenue
                    {
                        Year = DateTime.Now.Year,
                        Month = month,
                        MonthName = GetMonthName(month),
                        Revenue = 0,
                        Expenses = 0,
                        Profit = 0,
                        PatientsCount = 0
                    });
                }
            }

            return new FinancialReport
            {
                TotalRevenue = totalRevenue,
                CashRevenue = cashRevenue,
                CardRevenue = cardRevenue,
                InsuranceRevenue = insuranceRevenue,
                OtherRevenue = otherRevenue,
                TotalExpenses = totalExpenses,
                SalariesExpenses = salariesExpenses,
                RentExpenses = rentExpenses,
                UtilitiesExpenses = utilitiesExpenses,
                SuppliesExpenses = suppliesExpenses,
                LabExpenses = labExpenses,
                MarketingExpenses = marketingExpenses,
                OtherExpenses = otherExpenses,
                GrossProfit = grossProfit,
                NetProfit = netProfit,
                ProfitMargin = profitMargin,
                TotalReceivables = totalReceivables,
                OverdueReceivables = overdueReceivables,
                OverdueInvoicesCount = overdueInvoicesCount,
                MonthlyRevenues = monthlyRevenues
            };
        }

        public async Task<List<DoctorRevenue>> GetDoctorRevenuesAsync(DateTime startDate, DateTime endDate)
        {
            var doctorRevenues = await _context.Doctors
                .Select(d => new DoctorRevenue
                {
                    DoctorId = d.Id,
                    DoctorName = $"د. {d.FullName}",
                    TotalRevenue = 0, // سيتم حسابه أدناه
                    PatientsCount = _context.Appointments
                        .Where(a => a.DoctorId == d.Id)
                        .Select(a => a.PatientId)
                        .Distinct()
                        .Count(),
                    AppointmentsCount = _context.Appointments
                        .Count(a => a.DoctorId == d.Id && a.AppointmentDate >= startDate && a.AppointmentDate <= endDate),
                    AverageRevenuePerPatient = 0, // سيتم حسابه أدناه
                    AverageRevenuePerAppointment = 0 // سيتم حسابه أدناه
                })
                .Where(dr => dr.TotalRevenue > 0)
                .ToListAsync();

            // حساب المتوسطات
            foreach (var revenue in doctorRevenues)
            {
                if (revenue.PatientsCount > 0)
                    revenue.AverageRevenuePerPatient = revenue.TotalRevenue / revenue.PatientsCount;
                
                if (revenue.AppointmentsCount > 0)
                    revenue.AverageRevenuePerAppointment = revenue.TotalRevenue / revenue.AppointmentsCount;
            }

            return doctorRevenues.OrderByDescending(dr => dr.TotalRevenue).ToList();
        }

        public async Task<List<ServiceRevenue>> GetServiceRevenuesAsync(DateTime startDate, DateTime endDate)
        {
            await Task.Delay(300);

            return new List<ServiceRevenue>
            {
                new() { ServiceName = "تقويم الأسنان", TotalRevenue = 15000, ServiceCount = 30, AveragePrice = 500, RevenuePercentage = 30.0 },
                new() { ServiceName = "تنظيف الأسنان", TotalRevenue = 10000, ServiceCount = 100, AveragePrice = 100, RevenuePercentage = 20.0 },
                new() { ServiceName = "حشو الأسنان", TotalRevenue = 8000, ServiceCount = 40, AveragePrice = 200, RevenuePercentage = 16.0 },
                new() { ServiceName = "خلع الأسنان", TotalRevenue = 6000, ServiceCount = 20, AveragePrice = 300, RevenuePercentage = 12.0 },
                new() { ServiceName = "خدمات أخرى", TotalRevenue = 11000, ServiceCount = 55, AveragePrice = 200, RevenuePercentage = 22.0 }
            };
        }

        public async Task<List<MonthlyRevenue>> GetMonthlyRevenuesAsync(int year)
        {
            var monthlyRevenues = await _context.Receipts
                .Where(r => r.ReceiptDate.Year == year)
                .GroupBy(r => r.ReceiptDate.Month)
                .Select(g => new MonthlyRevenue
                {
                    Year = year,
                    Month = g.Key,
                    MonthName = GetMonthName(g.Key),
                    Revenue = g.Sum(r => r.Amount),
                    Expenses = 0, // سيتم حسابه أدناه
                    Profit = 0, // سيتم حسابه أدناه
                    PatientsCount = g.Count()
                })
                .ToListAsync();

            // حساب المصروفات الشهرية
            var monthlyExpenses = await _context.PaymentVouchers
                .Where(p => p.VoucherDate.Year == year)
                .GroupBy(p => p.VoucherDate.Month)
                .Select(g => new { Month = g.Key, Expenses = g.Sum(p => p.Amount) })
                .ToListAsync();

            // دمج البيانات وحساب الأرباح
            foreach (var revenue in monthlyRevenues)
            {
                var expenses = monthlyExpenses.FirstOrDefault(e => e.Month == revenue.Month)?.Expenses ?? 0;
                revenue.Expenses = expenses;
                revenue.Profit = revenue.Revenue - revenue.Expenses;
            }

            // إضافة الأشهر الفارغة
            for (int month = 1; month <= 12; month++)
            {
                if (!monthlyRevenues.Any(mr => mr.Month == month))
                {
                    monthlyRevenues.Add(new MonthlyRevenue
                    {
                        Year = year,
                        Month = month,
                        MonthName = GetMonthName(month),
                        Revenue = 0,
                        Expenses = 0,
                        Profit = 0,
                        PatientsCount = 0
                    });
                }
            }

            return monthlyRevenues.OrderBy(mr => mr.Month).ToList();
        }

        public async Task<List<DailyRevenue>> GetDailyRevenuesAsync(DateTime startDate, DateTime endDate)
        {
            await Task.Delay(300);

            var dailyRevenues = new List<DailyRevenue>();
            var currentDate = startDate;

            while (currentDate <= endDate)
            {
                var random = new Random(currentDate.GetHashCode());
                var revenue = random.Next(1500, 3000);
                var expenses = random.Next(800, 1500);
                var profit = revenue - expenses;
                var patientsCount = random.Next(5, 15);
                var appointmentsCount = random.Next(8, 20);

                dailyRevenues.Add(new DailyRevenue
                {
                    Date = currentDate,
                    DayName = currentDate.ToString("dddd"),
                    Revenue = revenue,
                    Expenses = expenses,
                    Profit = profit,
                    PatientsCount = patientsCount,
                    AppointmentsCount = appointmentsCount
                });

                currentDate = currentDate.AddDays(1);
            }

            return dailyRevenues;
        }
        #endregion

        #region تقارير المخزون
        public async Task<InventoryReport> GenerateInventoryReportAsync()
        {
            var items = await _context.InventoryItems.ToListAsync();

            var totalItems = items.Count;
            var activeItems = items.Count(i => i.IsActive);
            var inactiveItems = items.Count(i => !i.IsActive);
            var lowStockItems = items.Count(i => i.CurrentQuantity <= i.MinimumQuantity);
            var outOfStockItems = items.Count(i => i.CurrentQuantity == 0);
            var expiringItems = items.Count(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value <= DateTime.Today.AddMonths(3));

            // إحصائيات القيمة
            var totalInventoryValue = items.Sum(i => i.CurrentQuantity * i.AverageCost);
            var lowStockValue = items.Where(i => i.CurrentQuantity <= i.MinimumQuantity).Sum(i => i.CurrentQuantity * i.AverageCost);
            var expiringValue = items.Where(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value <= DateTime.Today.AddMonths(3)).Sum(i => i.CurrentQuantity * i.AverageCost);

            // العناصر منخفضة المخزون
            var lowStockItemsList = items
                .Where(i => i.CurrentQuantity <= i.MinimumQuantity)
                .Select(i => new LowStockItem
                {
                    ItemId = i.Id,
                    ItemName = i.Name,
                    Category = i.Category,
                    CurrentStock = (int)i.CurrentQuantity,
                    MinimumStock = (int)i.MinimumQuantity,
                    ReorderQuantity = (int)(i.MinimumQuantity * 2),
                    UnitPrice = i.AverageCost,
                    TotalValue = i.CurrentQuantity * i.AverageCost,
                    DaysUntilStockout = 30 // افتراضي
                })
                .ToList();

            // العناصر منتهية الصلاحية قريباً
            var expiringItemsList = items
                .Where(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value <= DateTime.Today.AddMonths(3))
                .Select(i => new ExpiringItem
                {
                    ItemId = i.Id,
                    ItemName = i.Name,
                    Category = i.Category,
                    CurrentStock = (int)i.CurrentQuantity,
                    ExpiryDate = i.ExpiryDate.Value,
                    DaysUntilExpiry = (i.ExpiryDate.Value - DateTime.Today).Days,
                    UnitPrice = i.AverageCost,
                    TotalValue = i.CurrentQuantity * i.AverageCost
                })
                .ToList();

            return new InventoryReport
            {
                TotalItems = totalItems,
                ActiveItems = activeItems,
                InactiveItems = inactiveItems,
                LowStockItems = lowStockItems,
                OutOfStockItems = outOfStockItems,
                ExpiringItems = expiringItems,
                TotalInventoryValue = totalInventoryValue,
                LowStockValue = lowStockValue,
                ExpiringValue = expiringValue,
                LowStockItemsList = lowStockItemsList,
                ExpiringItemsList = expiringItemsList
            };
        }

        public async Task<List<LowStockItem>> GetLowStockItemsAsync()
        {
            var lowStockItems = await _context.InventoryItems
                .Where(i => i.CurrentQuantity <= i.MinimumQuantity && i.CurrentQuantity > 0)
                .Select(i => new LowStockItem
                {
                    ItemId = i.Id,
                    ItemName = i.Name,
                    Category = i.Category,
                    CurrentStock = (int)i.CurrentQuantity,
                    MinimumStock = (int)i.MinimumQuantity,
                    ReorderQuantity = 0,
                    UnitPrice = i.AverageCost,
                    TotalValue = i.CurrentQuantity * i.AverageCost,
                    DaysUntilStockout = CalculateDaysUntilStockout((int)i.CurrentQuantity, (int)i.MinimumQuantity)
                })
                .OrderBy(i => i.DaysUntilStockout)
                .ToListAsync();

            return lowStockItems;
        }

        private int CalculateDaysUntilStockout(int currentStock, int minimumStock)
        {
            // حساب تقريبي للأيام حتى نفاد المخزون
            // يمكن تحسين هذا الحساب بناءً على معدل الاستهلاك الفعلي
            return currentStock > 0 ? currentStock * 2 : 0; // افتراضي: كل وحدة تكفي ليومين
        }

        public async Task<List<ExpiringItem>> GetExpiringItemsAsync(int daysThreshold = 30)
        {
            var expiringItems = await _context.InventoryItems
                .Where(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value <= DateTime.Now.AddDays(daysThreshold))
                .Select(i => new ExpiringItem
                {
                    ItemId = i.Id,
                    ItemName = i.Name,
                    Category = i.Category,
                    CurrentStock = (int)i.CurrentQuantity,
                    ExpiryDate = i.ExpiryDate.Value,
                    DaysUntilExpiry = (i.ExpiryDate.Value - DateTime.Now).Days,
                    UnitPrice = i.AverageCost,
                    TotalValue = i.CurrentQuantity * i.AverageCost
                })
                .OrderBy(i => i.DaysUntilExpiry)
                .ToListAsync();

            return expiringItems;
        }
        #endregion

        #region تقارير الأداء
        public async Task<PerformanceReport> GeneratePerformanceReportAsync()
        {
            var doctors = await _context.Doctors.ToListAsync();
            var employees = await _context.Employees.ToListAsync();
            var appointments = await _context.Appointments.Include(a => a.Doctor).ToListAsync();

            var totalDoctors = doctors.Count;
            var totalEmployees = employees.Count;

            // أداء الأطباء
            var doctorPerformances = doctors.Select(d => new DoctorPerformance
            {
                DoctorId = d.Id,
                DoctorName = d.FullName,
                Specialty = d.Specialization,
                TotalPatients = appointments.Where(a => a.DoctorId == d.Id).Select(a => a.PatientId).Distinct().Count(),
                TotalAppointments = appointments.Count(a => a.DoctorId == d.Id),
                CompletedAppointments = appointments.Count(a => a.DoctorId == d.Id && a.Status == "مكتمل"),
                CompletionRate = appointments.Count(a => a.DoctorId == d.Id) > 0 ? 
                    (double)appointments.Count(a => a.DoctorId == d.Id && a.Status == "مكتمل") / appointments.Count(a => a.DoctorId == d.Id) * 100 : 0,
                AverageRating = 4.5, // افتراضي
                TotalRevenue = 0, // سيتم حسابها لاحقاً
                AverageRevenuePerPatient = 0,
                PatientSatisfactionRate = 85.0, // افتراضي
                OnTimeAppointments = appointments.Count(a => a.DoctorId == d.Id && a.Status == "مكتمل"),
                PunctualityRate = 90.0, // افتراضي
                TreatmentSuccessRate = 95 // افتراضي
            }).ToList();

            // أداء الموظفين
            var employeePerformances = employees.Select(e => new EmployeePerformance
            {
                EmployeeId = e.Id,
                EmployeeName = e.FullName,
                Position = e.Position,
                Department = e.Department,
                WorkDays = 22, // افتراضي
                AbsentDays = 0, // افتراضي
                AttendanceRate = 95.0, // افتراضي
                TasksCompleted = 50, // افتراضي
                TasksAssigned = 55, // افتراضي
                TaskCompletionRate = 90.9, // افتراضي
                AverageRating = 4.2, // افتراضي
                OvertimeHours = 10, // افتراضي
                EfficiencyScore = 85.0 // افتراضي
            }).ToList();

            return new PerformanceReport
            {
                TotalDoctors = totalDoctors,
                TotalEmployees = totalEmployees,
                OverallSatisfactionRate = 4.3, // افتراضي
                AverageResponseTime = 2.5, // افتراضي
                DoctorPerformances = doctorPerformances,
                EmployeePerformances = employeePerformances
            };
        }

        public async Task<List<DoctorPerformance>> GetDoctorPerformanceAsync(DateTime startDate, DateTime endDate)
        {
            await Task.Delay(500);

            return new List<DoctorPerformance>
            {
                new() { DoctorId = 1, DoctorName = "د. أحمد محمد", Specialty = "تقويم الأسنان", TotalPatients = 40, TotalAppointments = 80, CompletedAppointments = 75, CompletionRate = 93.75, AverageRating = 4.8, TotalRevenue = 20000, AverageRevenuePerPatient = 500, PatientSatisfactionRate = 95.0, OnTimeAppointments = 70, PunctualityRate = 87.5, TreatmentSuccessRate = 92 },
                new() { DoctorId = 2, DoctorName = "د. فاطمة علي", Specialty = "طب الأسنان العام", TotalPatients = 35, TotalAppointments = 70, CompletedAppointments = 65, CompletionRate = 92.86, AverageRating = 4.6, TotalRevenue = 18000, AverageRevenuePerPatient = 514, PatientSatisfactionRate = 92.0, OnTimeAppointments = 60, PunctualityRate = 85.7, TreatmentSuccessRate = 89 },
                new() { DoctorId = 3, DoctorName = "د. محمد حسن", Specialty = "جراحة الفم", TotalPatients = 25, TotalAppointments = 50, CompletedAppointments = 45, CompletionRate = 90.0, AverageRating = 4.7, TotalRevenue = 12000, AverageRevenuePerPatient = 480, PatientSatisfactionRate = 90.0, OnTimeAppointments = 40, PunctualityRate = 80.0, TreatmentSuccessRate = 88 }
            };
        }

        public async Task<List<EmployeePerformance>> GetEmployeePerformanceAsync(DateTime startDate, DateTime endDate)
        {
            await Task.Delay(500);

            return new List<EmployeePerformance>
            {
                new() { EmployeeId = 1, EmployeeName = "سارة أحمد", Position = "ممرضة", Department = "التمريض", WorkDays = 22, AbsentDays = 1, AttendanceRate = 95.45, TasksCompleted = 45, TasksAssigned = 50, TaskCompletionRate = 90.0, AverageRating = 4.5, OvertimeHours = 8, EfficiencyScore = 88.0 },
                new() { EmployeeId = 2, EmployeeName = "علي محمد", Position = "مساعد طبيب", Department = "التمريض", WorkDays = 21, AbsentDays = 2, AttendanceRate = 90.48, TasksCompleted = 40, TasksAssigned = 45, TaskCompletionRate = 88.89, AverageRating = 4.3, OvertimeHours = 6, EfficiencyScore = 85.0 },
                new() { EmployeeId = 3, EmployeeName = "فاطمة حسن", Position = "استقبال", Department = "الإدارة", WorkDays = 23, AbsentDays = 0, AttendanceRate = 100.0, TasksCompleted = 60, TasksAssigned = 60, TaskCompletionRate = 100.0, AverageRating = 4.7, OvertimeHours = 4, EfficiencyScore = 95.0 }
            };
        }
        #endregion

        #region إدارة التقارير
        public async Task<List<ReportBase>> GetSavedReportsAsync(ReportType? type = null)
        {
            await Task.Delay(300);

            var reports = _savedReports.AsEnumerable();

            if (type.HasValue)
            {
                reports = reports.Where(r => r.Type == type.Value);
            }

            return reports.OrderByDescending(r => r.CreatedAt).ToList();
        }

        public async Task<ReportBase?> GetReportByIdAsync(int reportId)
        {
            await Task.Delay(100);
            return _savedReports.FirstOrDefault(r => r.Id == reportId);
        }

        public async Task<int> SaveReportAsync(ReportBase report)
        {
            await Task.Delay(500);

            if (report.Id == 0)
            {
                report.Id = _nextReportId++;
                report.CreatedAt = DateTime.Now;
                _savedReports.Add(report);
            }
            else
            {
                var existingReport = _savedReports.FirstOrDefault(r => r.Id == report.Id);
                if (existingReport != null)
                {
                    var index = _savedReports.IndexOf(existingReport);
                    _savedReports[index] = report;
                }
            }

            return report.Id;
        }

        public async Task<bool> DeleteReportAsync(int reportId)
        {
            await Task.Delay(300);

            var report = _savedReports.FirstOrDefault(r => r.Id == reportId);
            if (report != null)
            {
                _savedReports.Remove(report);
                return true;
            }

            return false;
        }

        public async Task<bool> ExportReportAsync(int reportId, string format, string filePath)
        {
            await Task.Delay(1000);

            var report = await GetReportByIdAsync(reportId);
            if (report == null) return false;

            // محاكاة التصدير
            var reportData = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, reportData);

            return true;
        }

        public async Task<byte[]> GenerateReportPdfAsync(ReportBase report)
        {
            await Task.Delay(2000);

            // محاكاة إنشاء PDF
            var reportData = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
            return System.Text.Encoding.UTF8.GetBytes(reportData);
        }

        public async Task<byte[]> GenerateReportExcelAsync(ReportBase report)
        {
            await Task.Delay(2000);

            // محاكاة إنشاء Excel
            var reportData = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
            return System.Text.Encoding.UTF8.GetBytes(reportData);
        }
        #endregion

        #region Private Methods
        private void InitializeSampleReports()
        {
            var sampleReports = new List<ReportBase>
            {
                new PatientReport
                {
                    Id = 1,
                    Title = "تقرير المرضى - يناير 2024",
                    Description = "إحصائيات المرضى لشهر يناير",
                    Type = ReportType.PatientReport,
                    Status = ReportStatus.Generated,
                    CreatedAt = DateTime.Now.AddDays(-30),
                    CreatedBy = "النظام"
                },
                new AppointmentReport
                {
                    Id = 2,
                    Title = "تقرير المواعيد - يناير 2024",
                    Description = "إحصائيات المواعيد لشهر يناير",
                    Type = ReportType.AppointmentReport,
                    Status = ReportStatus.Generated,
                    CreatedAt = DateTime.Now.AddDays(-25),
                    CreatedBy = "النظام"
                },
                new FinancialReport
                {
                    Id = 3,
                    Title = "التقرير المالي - يناير 2024",
                    Description = "الإيرادات والمصروفات لشهر يناير",
                    Type = ReportType.FinancialReport,
                    Status = ReportStatus.Generated,
                    CreatedAt = DateTime.Now.AddDays(-20),
                    CreatedBy = "النظام"
                }
            };

            foreach (var report in sampleReports)
            {
                _savedReports.Add(report);
            }

            _nextReportId = 4;
        }
        #endregion
    }
} 