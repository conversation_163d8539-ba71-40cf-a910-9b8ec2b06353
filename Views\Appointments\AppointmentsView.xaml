<UserControl x:Class="AqlanCenterProApp.Views.Appointments.AppointmentsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AqlanCenterProApp.Views.Appointments"
        xmlns:vm="clr-namespace:AqlanCenterProApp.ViewModels.Appointments"
        mc:Ignorable="d"
        FlowDirection="RightToLeft"
        Background="#FAFAFA">
    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  PanningMode="VerticalOnly">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20" 
                    Effect="{StaticResource CardShadow}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title and Search -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="📅" FontSize="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إدارة المواعيد" FontSize="24" FontWeight="Bold" Foreground="#2D3E50"/>
                            <TextBlock Text="إدارة وتنظيم جميع مواعيد المرضى" FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Search and Actions -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBox Width="250" Height="36" Margin="0,0,10,0" VerticalAlignment="Center" 
                                 Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBox}"
                                 Tag="بحث بالاسم أو الطبيب أو الملاحظة..."/>
                        <Button Content="🔍" Command="{Binding SearchCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="بحث"/>
                        <Button Content="🔄" Command="{Binding RefreshCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="تحديث" Margin="5,0,0,0"/>
                        <Button Content="��" Command="{Binding CalendarCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="تقويم المواعيد" Margin="5,0,0,0"/>
                        <Button Content="➕ إضافة موعد" Command="{Binding AddCommand}" Height="36" Width="120" 
                                Style="{StaticResource PrimaryButton}" Margin="10,0,0,0"/>
                        <Button Content="�� خطة تقويم" Command="{Binding AddOrthodonticPlanCommand}" Height="36" Width="120" 
                                Style="{StaticResource PrimaryButton}" Margin="10,0,0,0" Background="#8E44AD"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- DataGrid -->
            <Border Grid.Row="1" Background="White" CornerRadius="8" Margin="0,0,0,20" 
                    Effect="{StaticResource CardShadow}">
                <DataGrid ItemsSource="{Binding Appointments}" 
                          SelectedItem="{Binding SelectedAppointment, Mode=TwoWay}"
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          IsReadOnly="True" 
                          RowHeight="45"
                          HeadersVisibility="Column" 
                          FontSize="14" 
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal" 
                          Style="{StaticResource ModernDataGrid}"
                          AlternatingRowBackground="#F8F9FA">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المريض" Binding="{Binding Patient.FullName}" Width="150"/>
                        <DataGridTextColumn Header="الطبيب" Binding="{Binding Doctor.FullName}" Width="150"/>
                        <DataGridTextColumn Header="الخدمة" Binding="{Binding ServiceType}" Width="120"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding AppointmentType}" Width="100"/>
                        <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding AppointmentDateTime, StringFormat=yyyy/MM/dd HH:mm}" Width="140"/>
                        <DataGridTextColumn Header="المدة" Binding="{Binding DurationMinutes, StringFormat={}{0} دقيقة}" Width="80"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Status}" Value="مجدول">
                                            <Setter Property="Foreground" Value="#27AE60"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="مكتمل">
                                            <Setter Property="Foreground" Value="#2980B9"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="ملغي">
                                            <Setter Property="Foreground" Value="#E74C3C"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="لم يحضر">
                                            <Setter Property="Foreground" Value="#F39C12"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- Footer/Actions -->
            <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="15" 
                    Effect="{StaticResource CardShadow}">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="✅ إكمال" Command="{Binding CompleteCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="❌ إلغاء" Command="{Binding CancelCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="🚫 لم يحضر" Command="{Binding NoShowCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="🔄 إعادة جدولة" Command="{Binding RescheduleCommand}" Height="36" Width="120" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="✏️ تعديل" Command="{Binding EditCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="🗑️ حذف" Command="{Binding DeleteCommand}" Height="36" Width="100" 
                            Style="{StaticResource DangerButton}" Margin="0,0,10,0"/>
                    <Button Content="👁️ تفاصيل" Command="{Binding DetailsCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}"/>
                </StackPanel>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl> 