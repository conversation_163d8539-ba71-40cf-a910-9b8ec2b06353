﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AqlanCenterProApp.Views;
using AqlanCenterProApp.Views.Patients;
using AqlanCenterProApp.Views.Doctors;
using AqlanCenterProApp.Views.Employees;
using AqlanCenterProApp.Views.Appointments;
using AqlanCenterProApp.Views.Labs;
using AqlanCenterProApp.Views.LabOrders;
using AqlanCenterProApp.Views.Purchases;
using AqlanCenterProApp.Views.Suppliers;
using AqlanCenterProApp.Views.Inventory;
using AqlanCenterProApp.ViewModels.Patients;
using AqlanCenterProApp.ViewModels.Doctors;
using AqlanCenterProApp.ViewModels.Employees;
using AqlanCenterProApp.ViewModels.Appointments;
using AqlanCenterProApp.ViewModels.Labs;
using AqlanCenterProApp.ViewModels.LabOrders;
using AqlanCenterProApp.ViewModels.Purchases;
using AqlanCenterProApp.ViewModels.Suppliers;
using AqlanCenterProApp.ViewModels.Inventory;
using AqlanCenterProApp.ViewModels.Users;
using AqlanCenterProApp.ViewModels.Roles;
using AqlanCenterProApp.Views.Users;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AqlanCenterProApp.Helpers;
using AqlanCenterProApp.Views.Settings;
using AqlanCenterProApp.Views.Dashboard;
using AqlanCenterProApp.ViewModels.Dashboard;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AqlanCenterProApp.Views.Invoices;
using AqlanCenterProApp.Views.Receipts;
using AqlanCenterProApp.Views.PaymentVouchers;
using AqlanCenterProApp.Views.AccountStatements;
using AqlanCenterProApp.Views.Reports;
using AqlanCenterProApp.ViewModels.Invoices;
using AqlanCenterProApp.ViewModels.Receipts;
using AqlanCenterProApp.ViewModels.PaymentVouchers;
using AqlanCenterProApp.ViewModels.AccountStatements;
using AqlanCenterProApp.ViewModels.Reports;
using AqlanCenterProApp.Views.Roles;
using System.Threading;

namespace AqlanCenterProApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private Dictionary<string, (string Title, UserControl? Control)> _pages = new();
    private readonly IServiceProvider _serviceProvider;
    private readonly ILoggingService _loggingService;

    static MainWindow()
    {
        // تعطيل BinaryFormatter في WPF
        try
        {
            AppContext.SetSwitch("System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization", false);
            AppContext.SetSwitch("System.Windows.Markup.DoNotUseSha256ForMarkupCompilerChecksumAlgorithm", true);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ تحذير: لا يمكن تعطيل BinaryFormatter: {ex.Message}");
        }
    }

    public MainWindow(IServiceProvider serviceProvider)
    {
        try
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _loggingService = _serviceProvider.GetRequiredService<ILoggingService>();

            // معالجة أخطاء التسلسل قبل تهيئة المكونات
            try
            {
                InitializeComponent();
            }
            catch (Exception initEx) when (IsSerializationError(initEx))
            {
                Console.WriteLine($"🔧 معالجة خطأ تسلسل في تهيئة المكونات: {initEx.Message}");

                // تعطيل BinaryFormatter وإعادة المحاولة
                AppContext.SetSwitch("System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization", false);
                InitializeComponent();
            }

            InitializePages();

            LoadDefaultPage();

            // ربط أحداث السايدبار والهيدر
            SidebarControl.MenuItemSelected += OnMenuItemSelected;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة النافذة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
        }
    }

    private void InitializePages()
    {
        _pages = new Dictionary<string, (string Title, UserControl? Control)>
        {
            { "Dashboard", ("لوحة التحكم الرئيسية", null) },
            { "Patients", ("إدارة المرضى", null) },
            { "Doctors", ("إدارة الأطباء", null) },
            { "Employees", ("إدارة الموظفين", null) },
            { "Appointments", ("إدارة المواعيد", null) },
            { "Invoices", ("الفواتير والمدفوعات", null) },
            { "Labs", ("المعامل والمختبرات", null) },
            { "Inventory", ("المشتريات والمخزون", null) },
            { "Purchases", ("المشتريات", null) },
            { "Suppliers", ("الموردين", null) },
            { "Reports", ("التقارير", null) },
            { "Users", ("المستخدمين والصلاحيات", null) },
            { "Backup", ("النسخ الاحتياطي", null) },
            { "Settings", ("الإعدادات", null) },
            { "Receipts", ("الإيصالات", null) },
            { "PaymentVouchers", ("سندات الصرف", null) },
            { "AccountStatements", ("كشف الحساب", null) },
            { "LabOrders", ("طلبات المعمل", null) },
            { "OrthodonticPlans", ("مخططات تقويم الأسنان", null) },
            { "Shades", ("الألوان والظلال", null) },
            { "AddPatient", ("إضافة مريض جديد", null) },
            { "SearchPatients", ("البحث في المرضى", null) },
            { "MedicalRecords", ("السجلات الطبية", null) },
            { "AddDoctor", ("إضافة طبيب جديد", null) },
            { "DoctorSchedule", ("جدول الأطباء", null) },
            { "AddAppointment", ("إضافة موعد جديد", null) },
            { "AppointmentCalendar", ("جدول المواعيد", null) },
            { "EmployeeAttendance", ("الحضور والانصراف", null) },
            { "EmployeeLeaves", ("الإجازات", null) },
            { "EmployeeSalaries", ("الرواتب", null) },
            { "EmployeeReports", ("تقارير الموظفين", null) },
            { "FinancialReports", ("التقارير المالية", null) },
            { "PatientReports", ("تقارير المرضى", null) },
            { "AppointmentReports", ("تقارير المواعيد", null) },
            { "InventoryReports", ("تقارير المخزون", null) },
            { "Roles", ("إدارة الصلاحيات", null) },
            { "ActivityLog", ("سجل النشاط", null) },
            { "Restore", ("استعادة نسخة احتياطية", null) },
            { "BackupManagement", ("إدارة النسخ الاحتياطية", null) },
            { "PrintSettings", ("إعدادات الطباعة", null) },
            { "NotificationSettings", ("إعدادات الإشعارات", null) },
            { "ReportsMain", ("التقارير الرئيسية", null) },
            { "PerformanceReport", ("تقرير الأداء", null) }
        };
    }

    private async void LoadDefaultPage()
    {
        try
        {
            await _loggingService.LogInfoAsync("بدء تحميل الداشبورد الافتراضي", "MainWindow");
            MainContentControl.Content = await CreateDashboardContentAsync();
            await _loggingService.LogInfoAsync("تم تحميل الداشبورد بنجاح", "MainWindow");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync(ex, "خطأ في تحميل الداشبورد الافتراضي", "MainWindow");

            MessageBox.Show($"خطأ في تحميل الداشبورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

            // في حالة الخطأ، عرض رسالة بسيطة
            var errorText = new TextBlock
            {
                Text = "خطأ في تحميل الداشبورد",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Colors.Red)
            };

            MainContentControl.Content = errorText;
        }
    }

    private async Task<UserControl> CreateDashboardContentAsync()
    {
        try
        {
            await _loggingService.LogInfoAsync("بدء إنشاء محتوى الداشبورد", "MainWindow");

            // التحقق من صحة قاعدة البيانات أولاً
            var dbValidation = DatabasePathHelper.ValidateDatabasePath();
            if (!string.IsNullOrEmpty(dbValidation))
            {
                await _loggingService.LogErrorAsync(new Exception(dbValidation), "خطأ في التحقق من قاعدة البيانات", "MainWindow");
                return CreateErrorControl($"خطأ في قاعدة البيانات: {dbValidation}");
            }

            if (!DatabasePathHelper.DatabaseExists())
            {
                await _loggingService.LogWarningAsync("قاعدة البيانات غير موجودة، محاولة إنشائها", "MainWindow");

                try
                {
                    var created = await DatabaseManager.CreateUnifiedDatabaseAsync();
                    if (!created)
                    {
                        return CreateErrorControl("فشل في إنشاء قاعدة البيانات");
                    }
                    await _loggingService.LogInfoAsync("تم إنشاء قاعدة البيانات بنجاح", "MainWindow");
                }
                catch (Exception dbEx)
                {
                    await _loggingService.LogErrorAsync(dbEx, "خطأ في إنشاء قاعدة البيانات", "MainWindow");
                    return CreateErrorControl($"خطأ في إنشاء قاعدة البيانات: {dbEx.Message}");
                }
            }

            var viewModel = _serviceProvider.GetRequiredService<DashboardViewModel>();
            var dashboardView = new DashboardView(viewModel);

            await _loggingService.LogInfoAsync("تم إنشاء محتوى الداشبورد بنجاح", "MainWindow");
            return dashboardView;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync(ex, "خطأ في إنشاء محتوى الداشبورد", "MainWindow");

            MessageBox.Show($"خطأ في إنشاء الداشبورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            return CreateErrorControl($"خطأ في تحميل الداشبورد: {ex.Message}");
        }
    }

    private UserControl CreateErrorControl(string errorMessage)
    {
        var errorControl = new UserControl();
        var errorText = new TextBlock
        {
            Text = errorMessage,
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            Foreground = new SolidColorBrush(Colors.Red),
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(20)
        };
        errorControl.Content = errorText;
        return errorControl;
    }

    private async void OnMenuItemSelected(object? sender, string menuItem)
    {
        await NavigateToPageAsync(menuItem);
    }

    private async Task NavigateToPageAsync(string pageKey)
    {
        try
        {
            await _loggingService.LogUserInteractionAsync($"النقر على الوحدة: {pageKey}", "Navigation");

            // معالجة خاصة للداشبورد
            if (pageKey == "Dashboard")
            {
                LoadDefaultPage();
                return;
            }

            if (_pages.TryGetValue(pageKey, out var page))
            {
                // معالجة الداشبورد
                if (pageKey == "Dashboard")
                    MainContentControl.Content = await CreateDashboardContentAsync();
                // التقارير الموحدة
                else if (pageKey == "ReportsMain")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(), "ReportsMain");
                else if (pageKey == "PatientReports")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(Models.Reports.ReportType.PatientReport), "PatientReports");
                else if (pageKey == "AppointmentReports")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(Models.Reports.ReportType.AppointmentReport), "AppointmentReports");
                else if (pageKey == "FinancialReports")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(Models.Reports.ReportType.FinancialReport), "FinancialReports");
                else if (pageKey == "InventoryReports")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(Models.Reports.ReportType.InventoryReport), "InventoryReports");
                else if (pageKey == "PerformanceReport")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateReportsMainContent(Models.Reports.ReportType.PerformanceReport), "PerformanceReport");
                // باقي الصفحات كما هي
                else if (pageKey == "Patients")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreatePatientsListContent(), "Patients");
                else if (pageKey == "Doctors")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateDoctorsListContent(), "Doctors");
                else if (pageKey == "Employees")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateEmployeesListContent(), "Employees");
                else if (pageKey == "Appointments")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateAppointmentsListContent(), "Appointments");
                else if (pageKey == "Invoices")
                    MainContentControl.Content = await CreateContentSafelyAsync(() => CreateInvoicesListContent(), "Invoices");
                else if (pageKey == "Receipts")
                    MainContentControl.Content = CreateReceiptsListContent();
                else if (pageKey == "PaymentVouchers")
                    MainContentControl.Content = CreatePaymentVouchersListContent();
                else if (pageKey == "AccountStatements")
                    MainContentControl.Content = CreateAccountStatementsListContent();
                else if (pageKey == "Labs")
                    MainContentControl.Content = CreateLabsListContent();
                else if (pageKey == "LabOrders")
                    MainContentControl.Content = CreateLabOrdersListContent();
                else if (pageKey == "Inventory")
                    MainContentControl.Content = CreateInventoryContent();
                else if (pageKey == "Purchases")
                    MainContentControl.Content = CreatePurchasesListContent();
                else if (pageKey == "Suppliers")
                    MainContentControl.Content = CreateSuppliersListContent();
                else if (pageKey == "OrthodonticPlans")
                    MainContentControl.Content = CreateOrthodonticPlansContent();
                else if (pageKey == "Shades")
                    MainContentControl.Content = CreateShadesContent();
                else if (pageKey == "AddPatient")
                    ShowAddPatientDialog();
                else if (pageKey == "SearchPatients")
                    MainContentControl.Content = CreatePatientsSearchContent();
                else if (pageKey == "MedicalRecords")
                    MainContentControl.Content = CreateMedicalRecordsContent();
                else if (pageKey == "AddDoctor")
                    ShowAddDoctorDialog();
                else if (pageKey == "DoctorSchedule")
                    MainContentControl.Content = CreateDoctorScheduleContent();
                else if (pageKey == "AddAppointment")
                    ShowAddAppointmentDialog();
                else if (pageKey == "AppointmentCalendar")
                    MainContentControl.Content = CreateAppointmentCalendarContent();
                else if (pageKey == "EmployeeAttendance")
                    MainContentControl.Content = CreateEmployeeAttendanceContent();
                else if (pageKey == "EmployeeLeaves")
                    MainContentControl.Content = CreateEmployeeLeavesContent();
                else if (pageKey == "EmployeeSalaries")
                    MainContentControl.Content = CreateEmployeeSalariesContent();
                else if (pageKey == "EmployeeReports")
                    MainContentControl.Content = CreateEmployeeReportsContent();
                else if (pageKey == "Users")
                    MainContentControl.Content = CreateUsersContent();
                else if (pageKey == "Roles")
                    MainContentControl.Content = CreateRolesContent();
                else if (pageKey == "ActivityLog")
                    MainContentControl.Content = CreateActivityLogContent();
                else if (pageKey == "Restore")
                    MainContentControl.Content = CreateRestoreContent();
                else if (pageKey == "Backup")
                    MainContentControl.Content = CreateBackupManagementContent();
                else if (pageKey == "BackupManagement")
                    MainContentControl.Content = CreateBackupManagementContent();
                else if (pageKey == "PrintSettings")
                    MainContentControl.Content = CreatePrintSettingsContent();
                else if (pageKey == "NotificationSettings")
                    MainContentControl.Content = CreateNotificationSettingsContent();
                else if (pageKey == "Settings")
                    MainContentControl.Content = CreateSettingsContent();
                else if (page.Control != null)
                    MainContentControl.Content = page.Control;
                else
                    MainContentControl.Content = CreatePlaceholderControl(page.Title);
            }

            await _loggingService.LogInfoAsync($"تم التنقل بنجاح إلى الوحدة: {pageKey}", "Navigation");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync(ex, $"خطأ في التنقل إلى الوحدة: {pageKey}", "Navigation");

            MessageBox.Show($"خطأ في تحميل الوحدة {pageKey}: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            MainContentControl.Content = CreateErrorControl($"خطأ في تحميل الوحدة: {ex.Message}");
        }
    }

    private async Task<UserControl> CreateContentSafelyAsync(Func<UserControl> createContentFunc, string moduleName)
    {
        try
        {
            await _loggingService.LogInfoAsync($"بدء إنشاء محتوى الوحدة: {moduleName}", "ModuleCreation");

            var operationId = await _loggingService.LogOperationStartAsync($"إنشاء محتوى {moduleName}", "ModuleCreation");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            var content = createContentFunc();

            stopwatch.Stop();
            await _loggingService.LogOperationEndAsync(operationId, true);
            await _loggingService.LogPerformanceAsync($"إنشاء محتوى {moduleName}", stopwatch.Elapsed, "ModuleCreation");

            return content;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync(ex, $"خطأ في إنشاء محتوى الوحدة: {moduleName}", "ModuleCreation");
            return CreateErrorControl($"خطأ في تحميل وحدة {moduleName}: {ex.Message}");
        }
    }

    private UserControl CreatePlaceholderControl(string moduleName)
    {
        var placeholderControl = new UserControl();
        var stackPanel = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var titleText = new TextBlock
        {
            Text = moduleName,
            FontFamily = (System.Windows.Media.FontFamily)FindResource("ArabicFontFamily"),
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            TextAlignment = TextAlignment.Center,
            Foreground = (System.Windows.Media.Brush)FindResource("PrimaryBlueBrush"),
            Margin = new Thickness(0, 0, 0, 20)
        };

        var messageText = new TextBlock
        {
            Text = "هذه الوحدة قيد التطوير\nسيتم إضافتها في المراحل القادمة",
            FontFamily = (System.Windows.Media.FontFamily)FindResource("ArabicFontFamily"),
            FontSize = 16,
            TextAlignment = TextAlignment.Center,
            Foreground = (System.Windows.Media.Brush)FindResource("DarkGrayBrush")
        };

        stackPanel.Children.Add(titleText);
        stackPanel.Children.Add(messageText);
        placeholderControl.Content = stackPanel;

        return placeholderControl;
    }

    private UserControl CreatePatientsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<PatientsListViewModel>();
            var patientsView = new PatientsView(viewModel);

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.InitializeAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات المرضى. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المرضى: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return patientsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المرضى: {ex.Message}");
        }
    }

    private UserControl CreateDoctorsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<DoctorsListViewModel>();
            var doctorsView = new DoctorsView(viewModel);

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.RefreshAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات الأطباء. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات الأطباء: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return doctorsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الأطباء: {ex.Message}");
        }
    }

    private UserControl CreateEmployeesListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<EmployeesListViewModel>();
            var employeesView = new EmployeesView();
            employeesView.DataContext = viewModel;

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.RefreshAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات الموظفين. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return employeesView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الموظفين: {ex.Message}");
        }
    }

    private UserControl CreateAppointmentsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<AppointmentsListViewModel>();
            var appointmentsView = new AppointmentsView(viewModel);

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadAppointmentsAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات المواعيد. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المواعيد: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return appointmentsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المواعيد: {ex.Message}");
        }
    }

    private UserControl CreateInvoicesListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<InvoicesListViewModel>();
            var invoicesView = new InvoicesView(viewModel);

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadInvoicesAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفواتير: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return invoicesView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الفواتير: {ex.Message}");
        }
    }

    private UserControl CreateReceiptsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<ReceiptsListViewModel>();
            var receiptsView = new ReceiptsView(viewModel);

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadReceiptsAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإيصالات: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return receiptsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الإيصالات: {ex.Message}");
        }
    }

    private UserControl CreatePaymentVouchersListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<PaymentVouchersListViewModel>();
            var paymentVouchersView = new PaymentVouchersView(viewModel);

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadPaymentVouchersAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل سندات الصرف: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return paymentVouchersView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة سندات الصرف: {ex.Message}");
        }
    }

    private UserControl CreateAccountStatementsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<AccountStatementsListViewModel>();
            var accountStatementsView = new AccountStatementsView(viewModel);

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadAccountStatementsAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل كشف الحساب: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return accountStatementsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة كشف الحساب: {ex.Message}");
        }
    }

    private UserControl CreateLabOrdersListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<LabOrdersListViewModel>();
            var labOrdersView = new LabOrdersView(viewModel);

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadLabOrdersAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات طلبات المعمل. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات طلبات المعمل: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return labOrdersView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة طلبات المعمل: {ex.Message}");
        }
    }

    private UserControl CreateInventoryContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<InventoryListViewModel>();
            var inventoryView = new InventoryListView();
            inventoryView.DataContext = viewModel;

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadInventoryAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات المخزون. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المخزون: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return inventoryView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المخزون: {ex.Message}");
        }
    }

    private UserControl CreatePurchasesListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<PurchasesListViewModel>();
            var purchasesView = new PurchasesListView();
            purchasesView.DataContext = viewModel;

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadPurchasesAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات المشتريات. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المشتريات: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return purchasesView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المشتريات: {ex.Message}");
        }
    }

    private UserControl CreateLabsListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<LabsListViewModel>();
            var labsView = new LabsListView();
            labsView.DataContext = viewModel;

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadLabsAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات المعامل. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المعامل: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return labsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المعامل: {ex.Message}");
        }
    }

    private UserControl CreateSuppliersListContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<SuppliersListViewModel>();
            var suppliersView = new SuppliersListView();
            suppliersView.DataContext = viewModel;

            // تهيئة البيانات في الخلفية بشكل آمن مع timeout
            _ = Task.Run(async () =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // timeout 30 ثانية
                    await viewModel.LoadSuppliersAsync().WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show("انتهت مهلة تحميل بيانات الموردين. يرجى المحاولة مرة أخرى.", "تحذير",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }
                catch (Exception ex)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات الموردين: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            return suppliersView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الموردين: {ex.Message}");
        }
    }

    private UserControl CreateAddPatientContent()
    {
        // إظهار نافذة إضافة مريض جديد
        ShowAddPatientDialog();

        // إرجاع رسالة مؤقتة
        return CreatePlaceholderPatientContent("Add");
    }

    private UserControl CreatePatientsSearchContent()
    {
        // إرجاع نفس قائمة المرضى مع تفعيل البحث
        var patientsView = CreatePatientsListContent();
        return patientsView;
    }

    private UserControl CreatePatientsStatisticsContent()
    {
        // سيتم تطوير صفحة الإحصائيات لاحقاً
        return CreatePlaceholderPatientContent("Statistics");
    }

    private UserControl CreateMedicalRecordsContent()
    {
        return CreatePlaceholderPatientContent("MedicalRecords");
    }

    private UserControl CreatePrescriptionsContent()
    {
        return CreatePlaceholderPatientContent("Prescriptions");
    }

    private UserControl CreateTestResultsContent()
    {
        return CreatePlaceholderPatientContent("TestResults");
    }

    private UserControl CreateMedicalImagesContent()
    {
        return CreatePlaceholderPatientContent("MedicalImages");
    }

    private UserControl CreatePatientsReportsContent()
    {
        return CreatePlaceholderPatientContent("Reports");
    }

    private UserControl CreateExportContent()
    {
        return CreatePlaceholderPatientContent("Export");
    }

    private UserControl CreatePlaceholderPatientContent(string action)
    {
        var control = new UserControl();
        var grid = new Grid();

        var content = action switch
        {
            "Add" => "نموذج إضافة مريض جديد",
            "List" => "قائمة جميع المرضى",
            "Search" => "البحث المتقدم في المرضى",
            "Statistics" => "إحصائيات المرضى",
            "MedicalRecords" => "السجلات الطبية للمرضى",
            "Prescriptions" => "الوصفات الطبية",
            "TestResults" => "نتائج الفحوصات المخبرية",
            "MedicalImages" => "الصور الطبية والأشعة",
            "Reports" => "تقارير المرضى",
            "Archive" => "أرشيف المرضى",
            "Export" => "تصدير بيانات المرضى",
            "PatientFiles" => "ملفات المرضى",
            _ => $"محتوى {action}"
        };

        var textBlock = new TextBlock
        {
            Text = $"{content}\n\nسيتم تطوير هذا القسم قريباً",
            FontFamily = (System.Windows.Media.FontFamily)FindResource("ArabicFontFamily"),
            FontSize = 16,
            TextAlignment = TextAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center,
            Foreground = (System.Windows.Media.Brush)FindResource("DarkGrayBrush")
        };

        grid.Children.Add(textBlock);
        control.Content = grid;
        return control;
    }

    private UserControl CreateErrorContent(string errorMessage)
    {
        var control = new UserControl();
        var stackPanel = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var errorIcon = new TextBlock
        {
            Text = "⚠️",
            FontSize = 48,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var errorText = new TextBlock
        {
            Text = errorMessage,
            FontFamily = (System.Windows.Media.FontFamily)FindResource("ArabicFontFamily"),
            FontSize = 16,
            TextAlignment = TextAlignment.Center,
            Foreground = (System.Windows.Media.Brush)FindResource("DangerBrush"),
            TextWrapping = TextWrapping.Wrap,
            MaxWidth = 400
        };

        stackPanel.Children.Add(errorIcon);
        stackPanel.Children.Add(errorText);
        control.Content = stackPanel;
        return control;
    }

    private void ShowAddPatientDialog()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<AddEditPatientViewModel>();
            var dialog = new AddEditPatientView(viewModel);

            // تهيئة البيانات في الخلفية
            _ = Task.Run(() =>
            {
                try
                {
                    viewModel.PrepareForNewPatient();
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"خطأ في تهيئة بيانات المريض: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });

            if (dialog.ShowDialog() == true)
            {
                // تحديث قائمة المرضى إذا كانت مفتوحة
                RefreshPatientsListIfOpen();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة المريض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowEditPatientDialog(int patientId)
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<AddEditPatientViewModel>();
            var dialog = new AddEditPatientView(viewModel);

            // تحميل بيانات المريض
            _ = dialog.LoadPatientAsync(patientId);

            if (dialog.ShowDialog() == true)
            {
                // تحديث قائمة المرضى إذا كانت مفتوحة
                RefreshPatientsListIfOpen();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة تعديل المريض: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowPatientDetails(int patientId)
    {
        // سيتم تطوير صفحة تفاصيل المريض لاحقاً
        MessageBox.Show($"سيتم فتح تفاصيل المريض رقم: {patientId}\nهذه الوظيفة قيد التطوير",
            "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void RefreshPatientsListIfOpen()
    {
        // البحث عن PatientsView في المحتوى الحالي وتحديثه
        if (MainContentControl.Content is PatientsView patientsView &&
            patientsView.DataContext is PatientsListViewModel viewModel)
        {
            _ = viewModel.RefreshAsync();
        }
    }

    // دوال المحتوى الجديدة
    private UserControl CreateOrthodonticPlansContent()
    {
        return CreatePlaceholderControl("مخططات تقويم الأسنان");
    }

    private UserControl CreateShadesContent()
    {
        return CreatePlaceholderControl("الألوان والظلال");
    }

    private UserControl CreateDoctorScheduleContent()
    {
        return CreatePlaceholderControl("جدول الأطباء");
    }

    private UserControl CreateAppointmentCalendarContent()
    {
        return CreatePlaceholderControl("جدول المواعيد");
    }

    private UserControl CreateEmployeeAttendanceContent()
    {
        return CreatePlaceholderControl("الحضور والانصراف");
    }

    private UserControl CreateEmployeeLeavesContent()
    {
        return CreatePlaceholderControl("الإجازات");
    }

    private UserControl CreateEmployeeSalariesContent()
    {
        return CreatePlaceholderControl("الرواتب");
    }

    private UserControl CreateEmployeeReportsContent()
    {
        return CreatePlaceholderControl("تقارير الموظفين");
    }

    private UserControl CreateFinancialReportsContent()
    {
        return CreatePlaceholderControl("التقارير المالية");
    }

    private UserControl CreatePatientReportsContent()
    {
        return CreatePlaceholderControl("تقارير المرضى");
    }

    private UserControl CreateAppointmentReportsContent()
    {
        return CreatePlaceholderControl("تقارير المواعيد");
    }

    private UserControl CreateInventoryReportsContent()
    {
        return CreatePlaceholderControl("تقارير المخزون");
    }

    private UserControl CreateUsersContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<UsersListViewModel>();
            var usersView = new UsersView();
            usersView.DataContext = viewModel;

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadUsersAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المستخدمين: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return usersView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة المستخدمين: {ex.Message}");
        }
    }

    private UserControl CreateRolesContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<RolesListViewModel>();
            var rolesView = new RolesView();
            rolesView.DataContext = viewModel;
            return rolesView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء قائمة الصلاحيات: {ex.Message}");
        }
    }

    private UserControl CreateActivityLogContent()
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<ViewModels.Users.ActivityLogViewModel>();
            var view = new Views.Users.ActivityLogView(viewModel);

            return view;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في تحميل سجل النشاط: {ex.Message}");
        }
    }

    private UserControl CreateRestoreContent()
    {
        try
        {
            var view = new Views.Backup.BackupMainView();
            return view;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في تحميل وحدة النسخ الاحتياطي: {ex.Message}");
        }
    }

    private UserControl CreateBackupManagementContent()
    {
        try
        {
            var view = new Views.Backup.BackupMainView();
            return view;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في تحميل وحدة النسخ الاحتياطي: {ex.Message}");
        }
    }

    private UserControl CreatePrintSettingsContent()
    {
        return CreatePlaceholderControl("إعدادات الطباعة");
    }

    private UserControl CreateNotificationSettingsContent()
    {
        return CreatePlaceholderControl("إعدادات الإشعارات");
    }

    private UserControl CreateSettingsContent()
    {
        try
        {
            var settingsService = _serviceProvider.GetRequiredService<ISettingsService>();
            var logger = _serviceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<SettingsMainViewModel>>();
            var settingsWindow = new SettingsView(settingsService, logger);
            settingsWindow.Owner = this;
            settingsWindow.ShowDialog();
            // بعد إغلاق نافذة الإعدادات، يمكن إعادة تحميل الإعدادات أو تحديث الواجهة إذا لزم الأمر
            return CreatePlaceholderControl("تم فتح نافذة الإعدادات");
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء صفحة الإعدادات: {ex.Message}");
        }
    }

    private UserControl CreateReportsMainContent(Models.Reports.ReportType? reportType = null)
    {
        try
        {
            var viewModel = _serviceProvider.GetRequiredService<ReportsMainViewModel>();
            var reportsView = new ReportsMainView();
            reportsView.DataContext = viewModel;

            // تحميل البيانات بشكل آمن بدون blocking
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                    await viewModel.LoadReportsAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التقارير: {ex.Message}");
                    // لا نعرض MessageBox هنا لتجنب التعليق
                }
            });

            return reportsView;
        }
        catch (Exception ex)
        {
            return CreateErrorContent($"خطأ في إنشاء صفحة التقارير: {ex.Message}");
        }
    }

    private void ShowAddDoctorDialog()
    {
        // سيتم تنفيذ هذا لاحقاً
        MessageBox.Show("سيتم إضافة نافذة إضافة طبيب جديد قريباً", "قيد التطوير",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ShowAddAppointmentDialog()
    {
        // سيتم تنفيذ هذا لاحقاً
        MessageBox.Show("سيتم إضافة نافذة إضافة موعد جديد قريباً", "قيد التطوير",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// التحقق من كون الخطأ متعلق بالتسلسل
    /// </summary>
    /// <param name="ex">الخطأ المراد فحصه</param>
    /// <returns>true إذا كان خطأ تسلسل</returns>
    private bool IsSerializationError(Exception ex)
    {
        if (ex == null) return false;

        var errorMessage = ex.Message.ToLower();
        return errorMessage.Contains("serialization") ||
               errorMessage.Contains("binaryformatter") ||
               errorMessage.Contains("deserializer") ||
               errorMessage.Contains("system.windows.baml2006") ||
               errorMessage.Contains("deferredbinarydeserializerextension") ||
               ex.GetType().Name.Contains("Serialization");
    }
}

// فئة معلومات الوحدة
public class ModuleInfo
{
    public string Title { get; set; }
    public string Icon { get; set; }

    public ModuleInfo(string title, string icon)
    {
        Title = title;
        Icon = icon;
    }
}