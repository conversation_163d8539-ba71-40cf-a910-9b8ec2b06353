using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Receipts;

namespace AqlanCenterProApp.Views.Receipts
{
    /// <summary>
    /// Interaction logic for ReceiptsView.xaml
    /// </summary>
    public partial class ReceiptsView : UserControl
    {
        public ReceiptsView()
        {
            InitializeComponent();
        }

        public ReceiptsView(ReceiptsListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += ReceiptsView_Loaded;
        }

        private void ReceiptsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReceiptsListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadReceiptsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadReceiptsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات الإيصالات: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ReceiptsView_Loaded: {ex.Message}");
            }
        }
    }
}