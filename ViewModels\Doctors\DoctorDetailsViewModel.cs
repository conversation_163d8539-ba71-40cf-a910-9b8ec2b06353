using System;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.ViewModels.Doctors
{
    /// <summary>
    /// ViewModel لنافذة تفاصيل الطبيب
    /// </summary>
    public partial class DoctorDetailsViewModel : ObservableObject
    {
        #region Properties

        [ObservableProperty]
        private Doctor _doctor;

        #endregion

        #region Commands

        public ICommand EditCommand { get; }
        public ICommand ShowStatisticsCommand { get; }
        public ICommand ShowSessionsCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand CloseCommand { get; }

        #endregion

        #region Events

        public event EventHandler<bool>? CloseRequested;
        public event EventHandler<Doctor>? EditRequested;
        public event EventHandler<Doctor>? StatisticsRequested;
        public event EventHandler<Doctor>? SessionsRequested;

        #endregion

        #region Constructor

        public DoctorDetailsViewModel(Doctor doctor)
        {
            _doctor = doctor ?? throw new ArgumentNullException(nameof(doctor));

            // تهيئة الأوامر
            EditCommand = new RelayCommand(EditDoctor);
            ShowStatisticsCommand = new RelayCommand(ShowStatistics);
            ShowSessionsCommand = new RelayCommand(ShowSessions);
            PrintCommand = new RelayCommand(PrintDetails);
            CloseCommand = new RelayCommand(Close);
        }

        #endregion

        #region Methods

        /// <summary>
        /// تعديل الطبيب
        /// </summary>
        private void EditDoctor()
        {
            try
            {
                EditRequested?.Invoke(this, Doctor);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض الإحصائيات المفصلة
        /// </summary>
        private void ShowStatistics()
        {
            try
            {
                StatisticsRequested?.Invoke(this, Doctor);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة الإحصائيات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض جلسات الطبيب
        /// </summary>
        private void ShowSessions()
        {
            try
            {
                SessionsRequested?.Invoke(this, Doctor);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة الجلسات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة تفاصيل الطبيب
        /// </summary>
        private void PrintDetails()
        {
            try
            {
                // إنشاء محتوى الطباعة
                var printContent = GeneratePrintContent();

                // هنا يمكن إضافة منطق الطباعة الفعلي
                System.Windows.MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, false);
        }

        /// <summary>
        /// إنشاء محتوى الطباعة
        /// </summary>
        private string GeneratePrintContent()
        {
            return $@"
تفاصيل الطبيب
================

البيانات الأساسية:
- الاسم الكامل: {Doctor.FullName}
- التخصص: {Doctor.Specialization}
- رقم الهاتف: {Doctor.Phone}
- رقم الجوال: {Doctor.Mobile}
- البريد الإلكتروني: {Doctor.Email}
- العنوان: {Doctor.Address}
- الحالة: {Doctor.Status}

معلومات التعاقد:
- نوع التعاقد: {Doctor.ContractType}
- تاريخ الانضمام: {Doctor.JoinDate:yyyy-MM-dd}
- نسبة العمولة: {Doctor.CommissionPercentage}%
- الراتب الثابت: {Doctor.FixedSalary:N0} {Doctor.SalaryCurrency}
- عملة العمولة: {Doctor.CommissionCurrency}

الإحصائيات:
- إجمالي المرضى: {Doctor.TotalPatientsCount}
- الجلسات المكتملة: {Doctor.CompletedSessionsCount}
- إجمالي الأرباح: {Doctor.TotalEarnings:N0} ريال
- التقييم: {Doctor.Rating:F1}/5 ({Doctor.RatingCount} تقييم)

معلومات إضافية:
- الجنس: {Doctor.Gender}
- تاريخ الميلاد: {Doctor.DateOfBirth:yyyy-MM-dd}
- المؤهلات: {Doctor.Qualifications}
- ملاحظات: {Doctor.AdditionalNotes}

تاريخ الطباعة: {DateTime.Now:yyyy-MM-dd HH:mm}
";
        }

        #endregion
    }
}
