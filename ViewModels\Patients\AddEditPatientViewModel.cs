using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Patients;

/// <summary>
/// ViewModel لإضافة وتعديل المرضى مع التحقق من صحة البيانات
/// </summary>
public class AddEditPatientViewModel : BaseViewModel
{
    private readonly IPatientService _patientService;
    private Patient? _originalPatient; // للتعديل
    private bool _isEditMode;

    #region Properties

    private int _fileNumber;
    private string _firstName = string.Empty;
    private string _middleName = string.Empty;
    private string _lastName = string.Empty;
    private string _gender = "ذكر";
    private DateTime _dateOfBirth = DateTime.Now.AddYears(-25);
    private string _phone = string.Empty;
    private string _mobile = string.Empty;
    private string _address = string.Empty;
    private string _patientCategory = "جديد";
    private string _fileStatus = "نشط";
    private string? _patientImage;
    private string? _medicalHistory;
    private string? _allergies;
    private string? _emergencyContact;
    private string? _emergencyPhone;
    private string? _notes;
    private decimal _consultationFee = 50.00m; // مبلغ المعاينة الافتراضي

    /// <summary>
    /// رقم الملف
    /// </summary>
    public int FileNumber
    {
        get => _fileNumber;
        set => SetProperty(ref _fileNumber, value, ValidateFileNumber);
    }

    /// <summary>
    /// الاسم الأول
    /// </summary>
    [Required(ErrorMessage = "الاسم الأول مطلوب")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "الاسم الأول يجب أن يكون بين 2 و 50 حرف")]
    public string FirstName
    {
        get => _firstName;
        set => SetProperty(ref _firstName, value, ValidateFirstName);
    }

    /// <summary>
    /// الاسم الأوسط
    /// </summary>
    [StringLength(50, ErrorMessage = "الاسم الأوسط لا يجب أن يزيد عن 50 حرف")]
    public string MiddleName
    {
        get => _middleName;
        set => SetProperty(ref _middleName, value);
    }

    /// <summary>
    /// الاسم الأخير
    /// </summary>
    [Required(ErrorMessage = "الاسم الأخير مطلوب")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "الاسم الأخير يجب أن يكون بين 2 و 50 حرف")]
    public string LastName
    {
        get => _lastName;
        set => SetProperty(ref _lastName, value, ValidateLastName);
    }

    /// <summary>
    /// الاسم الكامل
    /// </summary>
    public string FullName => $"{FirstName} {MiddleName} {LastName}".Trim();

    /// <summary>
    /// الجنس
    /// </summary>
    public string Gender
    {
        get => _gender;
        set => SetProperty(ref _gender, value);
    }

    /// <summary>
    /// تاريخ الميلاد
    /// </summary>
    public DateTime DateOfBirth
    {
        get => _dateOfBirth;
        set => SetProperty(ref _dateOfBirth, value, ValidateDateOfBirth);
    }

    /// <summary>
    /// العمر المحسوب
    /// </summary>
    public int Age
    {
        get
        {
            var today = DateTime.Today;
            var age = today.Year - DateOfBirth.Year;
            if (DateOfBirth.Date > today.AddYears(-age)) age--;
            return age;
        }
    }

    /// <summary>
    /// رقم الهاتف الأساسي
    /// </summary>
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    public string Phone
    {
        get => _phone;
        set => SetProperty(ref _phone, value, ValidatePhone);
    }

    /// <summary>
    /// رقم الهاتف المحمول
    /// </summary>
    public string Mobile
    {
        get => _mobile;
        set => SetProperty(ref _mobile, value, ValidateMobile);
    }

    /// <summary>
    /// العنوان
    /// </summary>
    public string Address
    {
        get => _address;
        set => SetProperty(ref _address, value);
    }

    /// <summary>
    /// تصنيف المريض
    /// </summary>
    public string PatientCategory
    {
        get => _patientCategory;
        set => SetProperty(ref _patientCategory, value);
    }

    /// <summary>
    /// حالة الملف
    /// </summary>
    public string FileStatus
    {
        get => _fileStatus;
        set => SetProperty(ref _fileStatus, value);
    }

    /// <summary>
    /// صورة المريض
    /// </summary>
    public string? PatientImage
    {
        get => _patientImage;
        set => SetProperty(ref _patientImage, value);
    }

    /// <summary>
    /// التاريخ الطبي
    /// </summary>
    public string? MedicalHistory
    {
        get => _medicalHistory;
        set => SetProperty(ref _medicalHistory, value);
    }

    /// <summary>
    /// الحساسية
    /// </summary>
    public string? Allergies
    {
        get => _allergies;
        set => SetProperty(ref _allergies, value);
    }

    /// <summary>
    /// جهة الاتصال الطارئة
    /// </summary>
    public string? EmergencyContact
    {
        get => _emergencyContact;
        set => SetProperty(ref _emergencyContact, value);
    }

    /// <summary>
    /// هاتف الطوارئ
    /// </summary>
    public string? EmergencyPhone
    {
        get => _emergencyPhone;
        set => SetProperty(ref _emergencyPhone, value, ValidateEmergencyPhone);
    }

    /// <summary>
    /// ملاحظات
    /// </summary>
    public string? Notes
    {
        get => _notes;
        set => SetProperty(ref _notes, value);
    }

    /// <summary>
    /// مبلغ المعاينة
    /// </summary>
    [Required(ErrorMessage = "مبلغ المعاينة مطلوب")]
    [Range(0, 10000, ErrorMessage = "مبلغ المعاينة يجب أن يكون بين 0 و 10000")]
    public decimal ConsultationFee
    {
        get => _consultationFee;
        set => SetProperty(ref _consultationFee, value, ValidateConsultationFee);
    }

    /// <summary>
    /// وضع التعديل
    /// </summary>
    public bool IsEditMode
    {
        get => _isEditMode;
        private set => SetProperty(ref _isEditMode, value);
    }

    /// <summary>
    /// عنوان النافذة
    /// </summary>
    public string WindowTitle => IsEditMode ? $"تعديل بيانات المريض - ملف رقم: {FileNumber}" : "إضافة مريض جديد";

    #endregion

    #region Collections

    /// <summary>
    /// قائمة الأجناس
    /// </summary>
    public ObservableCollection<string> Genders { get; } = new() { "ذكر", "أنثى" };

    /// <summary>
    /// قائمة التصنيفات
    /// </summary>
    public ObservableCollection<string> Categories { get; } = new()
    {
        "جديد", "متابع", "طارئ", "VIP", "تقويم", "حشو", "تجميل",
        "حشو فضة", "نزع عصب", "تركيب زركون", "تركيب خزف"
    };

    /// <summary>
    /// قائمة حالات الملف
    /// </summary>
    public ObservableCollection<string> FileStatuses { get; } = new()
    {
        "نشط", "مؤرشف", "معلق", "VIP"
    };

    #endregion

    #region Validation Properties

    private Dictionary<string, string> _validationErrors = new();

    /// <summary>
    /// أخطاء التحقق
    /// </summary>
    public Dictionary<string, string> ValidationErrors
    {
        get => _validationErrors;
        set => SetProperty(ref _validationErrors, value);
    }

    /// <summary>
    /// صحة البيانات
    /// </summary>
    public bool IsValid => !ValidationErrors.Any();

    #endregion

    #region Commands

    /// <summary>
    /// أمر الحفظ
    /// </summary>
    public ICommand SaveCommand { get; }

    /// <summary>
    /// أمر الإلغاء
    /// </summary>
    public ICommand CancelCommand { get; }

    /// <summary>
    /// أمر اختيار صورة
    /// </summary>
    public ICommand SelectImageCommand { get; }

    /// <summary>
    /// أمر إزالة الصورة
    /// </summary>
    public ICommand RemoveImageCommand { get; }

    /// <summary>
    /// أمر إضافة تصنيف جديد
    /// </summary>
    public ICommand AddCategoryCommand { get; }

    /// <summary>
    /// أمر طباعة سند المعاينة
    /// </summary>
    public ICommand PrintConsultationReceiptCommand { get; }

    #endregion

    #region Events

    /// <summary>
    /// حدث حفظ المريض
    /// </summary>
    public event EventHandler<Patient>? PatientSaved;

    /// <summary>
    /// حدث الإلغاء
    /// </summary>
    public event EventHandler? Cancelled;

    #endregion

    #region Constructor

    public AddEditPatientViewModel(IPatientService patientService)
    {
        _patientService = patientService ?? throw new ArgumentNullException(nameof(patientService));

        // تهيئة الأوامر
        SaveCommand = new AsyncRelayCommand(SaveAsync, () => IsValid && !IsLoading);
        CancelCommand = new RelayCommand(Cancel);
        SelectImageCommand = new RelayCommand(SelectImage);
        RemoveImageCommand = new RelayCommand(RemoveImage, () => !string.IsNullOrEmpty(PatientImage));
        AddCategoryCommand = new RelayCommand(AddCategory);
        PrintConsultationReceiptCommand = new RelayCommand(PrintConsultationReceipt);

        // تعيين رقم ملف افتراضي مؤقت
        FileNumber = 8500; // سيتم تحديثه في InitializeAsync
    }

    /// <summary>
    /// كونستركتور للتعديل
    /// </summary>
    public AddEditPatientViewModel(IPatientService patientService, Patient patientToEdit) : this(patientService)
    {
        _originalPatient = patientToEdit ?? throw new ArgumentNullException(nameof(patientToEdit));
        _isEditMode = true;

        // تحميل بيانات المريض للتعديل
        LoadPatientData(patientToEdit);
    }

    #endregion

    #region Initialization

    /// <summary>
    /// تحضير النموذج لمريض جديد (بدون استدعاءات async)
    /// </summary>
    public void PrepareForNewPatient()
    {
        IsEditMode = false;
        _originalPatient = null;

        // إعادة تعيين القيم الافتراضية
        FirstName = string.Empty;
        MiddleName = string.Empty;
        LastName = string.Empty;
        Gender = "ذكر";
        DateOfBirth = DateTime.Now.AddYears(-25);
        Phone = string.Empty;
        Mobile = string.Empty;
        Address = string.Empty;
        PatientCategory = "جديد";
        FileStatus = "نشط";
        PatientImage = null;
        MedicalHistory = null;
        Allergies = null;
        EmergencyContact = null;
        EmergencyPhone = null;
        Notes = null;
        ConsultationFee = 50.00m;

        // تعيين رقم ملف مؤقت
        FileNumber = 8500;
    }

    /// <summary>
    /// تهيئة البيانات الأولية (يجب استدعاؤها بعد إنشاء الكائن)
    /// </summary>
    public async Task InitializeAsync()
    {
        if (!IsEditMode)
        {
            await LoadNextFileNumberAsync();
        }
    }

    /// <summary>
    /// تحميل بيانات المريض للتعديل
    /// </summary>
    private void LoadPatientData(Patient patient)
    {
        FileNumber = patient.FileNumber;

        // تقسيم الاسم الكامل إذا كان موجوداً
        if (!string.IsNullOrEmpty(patient.FullName))
        {
            var nameParts = patient.FullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            FirstName = nameParts.Length > 0 ? nameParts[0] : string.Empty;
            MiddleName = nameParts.Length > 2 ? nameParts[1] : string.Empty;
            LastName = nameParts.Length > 1 ? nameParts[^1] : string.Empty;
        }

        Gender = patient.Gender ?? "ذكر";
        DateOfBirth = patient.DateOfBirth ?? DateTime.Now.AddYears(-25);
        Phone = patient.Phone ?? string.Empty;
        Mobile = patient.Mobile ?? string.Empty;
        Address = patient.Address ?? string.Empty;
        PatientCategory = patient.PatientCategory ?? "جديد";
        FileStatus = patient.FileStatus ?? "نشط";
        PatientImage = patient.PatientImage;
        MedicalHistory = patient.MedicalHistory;
        Allergies = patient.Allergies;
        EmergencyContact = patient.EmergencyContact;
        EmergencyPhone = patient.EmergencyPhone;
        Notes = patient.Notes;
        ConsultationFee = patient.ConsultationFee ?? 50.00m;
    }

    /// <summary>
    /// تحميل رقم الملف التالي
    /// </summary>
    private async Task LoadNextFileNumberAsync()
    {
        if (!IsEditMode)
        {
            try
            {
                FileNumber = await _patientService.GetNextFileNumberAsync();
            }
            catch (Exception ex)
            {
                // في حالة الفشل، استخدم رقم افتراضي
                FileNumber = 8500;
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل رقم الملف التالي: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// تحميل بيانات مريض للتعديل
    /// </summary>
    public async Task LoadPatientAsync(int patientId)
    {
        await ExecuteAsync(async () =>
        {
            var patient = await _patientService.GetPatientByIdAsync(patientId);
            if (patient != null)
            {
                LoadPatientData(patient);
                _originalPatient = patient;
                IsEditMode = true;
            }
        }, "جاري تحميل بيانات المريض...");
    }



    #endregion

    #region Validation

    /// <summary>
    /// التحقق من رقم الملف
    /// </summary>
    private async void ValidateFileNumber()
    {
        ClearValidationError(nameof(FileNumber));

        if (!_patientService.IsValidFileNumber(FileNumber))
        {
            AddValidationError(nameof(FileNumber), "رقم الملف غير صحيح");
            return;
        }

        if (!IsEditMode || (_originalPatient != null && _originalPatient.Id != FileNumber))
        {
            try
            {
                var isAvailable = await _patientService.IsFileNumberAvailableAsync(FileNumber, _originalPatient?.Id);
                if (!isAvailable)
                {
                    AddValidationError(nameof(FileNumber), "رقم الملف مستخدم بالفعل");
                }
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في التحقق الفوري
            }
        }
    }

    /// <summary>
    /// التحقق من الاسم الأول
    /// </summary>
    private void ValidateFirstName()
    {
        ClearValidationError(nameof(FirstName));

        if (string.IsNullOrWhiteSpace(FirstName))
        {
            AddValidationError(nameof(FirstName), "الاسم الأول مطلوب");
        }
        else if (FirstName.Length < 2)
        {
            AddValidationError(nameof(FirstName), "الاسم الأول يجب أن يكون حرفين على الأقل");
        }
        else if (FirstName.Any(char.IsDigit))
        {
            AddValidationError(nameof(FirstName), "الاسم الأول لا يجب أن يحتوي على أرقام");
        }

        OnPropertyChanged(nameof(FullName));
    }

    /// <summary>
    /// التحقق من الاسم الأخير
    /// </summary>
    private void ValidateLastName()
    {
        ClearValidationError(nameof(LastName));

        if (string.IsNullOrWhiteSpace(LastName))
        {
            AddValidationError(nameof(LastName), "الاسم الأخير مطلوب");
        }
        else if (LastName.Length < 2)
        {
            AddValidationError(nameof(LastName), "الاسم الأخير يجب أن يكون حرفين على الأقل");
        }
        else if (LastName.Any(char.IsDigit))
        {
            AddValidationError(nameof(LastName), "الاسم الأخير لا يجب أن يحتوي على أرقام");
        }

        OnPropertyChanged(nameof(FullName));
    }

    /// <summary>
    /// التحقق من تاريخ الميلاد
    /// </summary>
    private void ValidateDateOfBirth()
    {
        ClearValidationError(nameof(DateOfBirth));

        if (!_patientService.IsValidDateOfBirth(DateOfBirth))
        {
            AddValidationError(nameof(DateOfBirth), "تاريخ الميلاد غير صحيح");
        }

        OnPropertyChanged(nameof(Age));
    }

    /// <summary>
    /// التحقق من رقم الهاتف الأساسي
    /// </summary>
    private async void ValidatePhone()
    {
        ClearValidationError(nameof(Phone));

        if (string.IsNullOrWhiteSpace(Phone))
        {
            AddValidationError(nameof(Phone), "رقم الهاتف مطلوب");
            return;
        }

        if (!_patientService.IsValidYemeniPhoneNumber(Phone))
        {
            AddValidationError(nameof(Phone), "رقم الهاتف غير صحيح (يجب أن يبدأ بـ 77، 73، 70، أو 71)");
            return;
        }

        try
        {
            var isAvailable = await _patientService.IsPhoneNumberAvailableAsync(Phone, _originalPatient?.Id);
            if (!isAvailable)
            {
                AddValidationError(nameof(Phone), "رقم الهاتف مستخدم بالفعل");
            }
        }
        catch (Exception)
        {
            // تجاهل الأخطاء في التحقق الفوري
        }
    }

    /// <summary>
    /// التحقق من رقم الهاتف المحمول
    /// </summary>
    private void ValidateMobile()
    {
        ClearValidationError(nameof(Mobile));

        if (!string.IsNullOrWhiteSpace(Mobile) && !_patientService.IsValidYemeniPhoneNumber(Mobile))
        {
            AddValidationError(nameof(Mobile), "رقم الهاتف المحمول غير صحيح");
        }
    }

    /// <summary>
    /// التحقق من هاتف الطوارئ
    /// </summary>
    private void ValidateEmergencyPhone()
    {
        ClearValidationError(nameof(EmergencyPhone));

        if (!string.IsNullOrWhiteSpace(EmergencyPhone) && !_patientService.IsValidYemeniPhoneNumber(EmergencyPhone))
        {
            AddValidationError(nameof(EmergencyPhone), "رقم هاتف الطوارئ غير صحيح");
        }
    }

    /// <summary>
    /// التحقق من مبلغ المعاينة
    /// </summary>
    private void ValidateConsultationFee()
    {
        ClearValidationError(nameof(ConsultationFee));

        if (ConsultationFee < 0 || ConsultationFee > 10000)
        {
            AddValidationError(nameof(ConsultationFee), "مبلغ المعاينة يجب أن يكون بين 0 و 10000");
        }
    }

    /// <summary>
    /// إضافة خطأ تحقق
    /// </summary>
    private void AddValidationError(string propertyName, string error)
    {
        ValidationErrors[propertyName] = error;
        OnPropertyChanged(nameof(ValidationErrors));
        OnPropertyChanged(nameof(IsValid));
        CommandManager.InvalidateRequerySuggested();
    }

    /// <summary>
    /// مسح خطأ تحقق
    /// </summary>
    private void ClearValidationError(string propertyName)
    {
        if (ValidationErrors.ContainsKey(propertyName))
        {
            ValidationErrors.Remove(propertyName);
            OnPropertyChanged(nameof(ValidationErrors));
            OnPropertyChanged(nameof(IsValid));
            CommandManager.InvalidateRequerySuggested();
        }
    }

    #endregion

    #region Commands Implementation

    /// <summary>
    /// حفظ بيانات المريض
    /// </summary>
    private async Task SaveAsync()
    {
        if (!IsValid) return;

        await ExecuteAsync(async () =>
        {
            var patient = CreatePatientFromForm();

            if (IsEditMode)
            {
                await _patientService.UpdatePatientAsync(patient, "CurrentUser"); // يجب تمرير المستخدم الحالي
            }
            else
            {
                patient = await _patientService.AddPatientAsync(patient, "CurrentUser"); // يجب تمرير المستخدم الحالي
            }

            PatientSaved?.Invoke(this, patient);
        }, IsEditMode ? "جاري تحديث بيانات المريض..." : "جاري حفظ بيانات المريض...");
    }

    /// <summary>
    /// إنشاء كائن المريض من النموذج
    /// </summary>
    public Patient CreatePatientFromForm()
    {
        return new Patient
        {
            Id = FileNumber,
            FullName = FullName,
            Gender = Gender,
            DateOfBirth = DateOfBirth,
            Phone = Phone,
            Mobile = string.IsNullOrWhiteSpace(Mobile) ? null : Mobile,
            Address = string.IsNullOrWhiteSpace(Address) ? null : Address,
            PatientCategory = PatientCategory,
            FileStatus = FileStatus,
            PatientImage = PatientImage,
            MedicalHistory = MedicalHistory,
            Allergies = Allergies,
            EmergencyContact = EmergencyContact,
            EmergencyPhone = EmergencyPhone,
            Notes = Notes,
            ConsultationFee = ConsultationFee,
            RegistrationDate = IsEditMode ? _originalPatient!.RegistrationDate : DateTime.Now
        };
    }

    /// <summary>
    /// إلغاء العملية
    /// </summary>
    private void Cancel()
    {
        Cancelled?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// اختيار صورة
    /// </summary>
    private void SelectImage()
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = "اختيار صورة المريض",
            Filter = "ملفات الصور|*.jpg;*.jpeg;*.png;*.bmp;*.gif|جميع الملفات|*.*",
            FilterIndex = 1
        };

        if (openFileDialog.ShowDialog() == true)
        {
            PatientImage = openFileDialog.FileName;
        }
    }

    /// <summary>
    /// إزالة الصورة
    /// </summary>
    private void RemoveImage()
    {
        PatientImage = null;
    }

    /// <summary>
    /// إضافة تصنيف جديد
    /// </summary>
    private void AddCategory()
    {
        // هنا يمكن إضافة نافذة لإدخال تصنيف جديد
        // مؤقتاً سنعرض رسالة
        System.Windows.MessageBox.Show("سيتم تطوير إضافة التصنيفات الجديدة قريباً", "معلومات");
    }

    /// <summary>
    /// طباعة سند المعاينة
    /// </summary>
    private void PrintConsultationReceipt()
    {
        // هنا يمكن إضافة عملية طباعة سند المعاينة
        // مؤقتاً سنعرض رسالة
        System.Windows.MessageBox.Show("سيتم تطوير عملية طباعة سند المعاينة قريباً", "معلومات");
    }

    #endregion
}
