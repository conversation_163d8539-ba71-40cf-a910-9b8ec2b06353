<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeDocumentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="مستندات الموظف" Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="مستندات الموظف" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        <DataGrid x:Name="DocumentsDataGrid"
                  ItemsSource="{Binding DocumentRecords}"
                  AutoGenerateColumns="False"
                  Margin="20"
                  IsReadOnly="True"
                  CanUserAddRows="False"
                  SelectionMode="Single">
            <DataGrid.Columns>
                <DataGridTextColumn Header="نوع المستند" Binding="{Binding DocumentType}" Width="*"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                <DataGridTextColumn Header="تاريخ الإصدار" Binding="{Binding IssueDate, StringFormat=yyyy/MM/dd}" Width="*"/>
                <DataGridTextColumn Header="تاريخ الانتهاء" Binding="{Binding ExpiryDate, StringFormat=yyyy/MM/dd}" Width="*"/>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>
        <Button Content="إغلاق" Width="100" Height="35" Margin="0,0,20,20" DockPanel.Dock="Bottom" HorizontalAlignment="Left" Click="Close_Click"/>
    </DockPanel>
</Window> 