using AqlanCenterProApp.ViewModels.Settings;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AqlanCenterProApp.Views.Settings
{
    public partial class SettingsView : Window
    {
        public SettingsView()
        {
            InitializeComponent();
            try
            {
                DataContext = new SettingsMainViewModel(null, null); // تهيئة افتراضية بدون DI
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء SettingsView: {ex.Message}");
            }
        }

        public SettingsView(ISettingsService settingsService, ILogger<SettingsMainViewModel> logger)
        {
            InitializeComponent();
            try
            {
                DataContext = new SettingsMainViewModel(settingsService, logger);
                Loaded += SettingsView_Loaded;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء SettingsView مع DI: {ex.Message}");
                DataContext = new SettingsMainViewModel(null, null); // fallback
            }
        }

        private void SettingsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is SettingsMainViewModel viewModel)
                {
                    // تحميل الإعدادات بشكل آمن
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadSettingsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadSettingsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SettingsView_Loaded: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}