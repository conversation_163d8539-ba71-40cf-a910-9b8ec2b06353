<Window x:Class="AqlanCenterProApp.Views.PaymentVouchers.AddEditPaymentVoucherView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="600" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <Window.Resources>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5,0,0,0"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="#0D47A1"/>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <TextBlock Text="{Binding WindowTitle}" FontSize="22" FontWeight="Bold" Foreground="#2196F3" Margin="0,20,0,20" HorizontalAlignment="Center"/>
        
        <!-- المحتوى الرئيسي مع ScrollViewer -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly">
            <StackPanel Margin="40,0,40,0">
                <TextBlock Text="رقم السند" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Voucher.VoucherNumber}" Style="{StaticResource InputStyle}" IsReadOnly="True"/>
                <TextBlock Text="تاريخ السند" Style="{StaticResource LabelStyle}"/>
                <DatePicker SelectedDate="{Binding Voucher.VoucherDate}" FontSize="15" Margin="0,0,0,15"/>
                <TextBlock Text="نوع المستفيد" Style="{StaticResource LabelStyle}"/>
                <ComboBox ItemsSource="{Binding BeneficiaryTypes}" SelectedItem="{Binding Voucher.BeneficiaryType}" Style="{StaticResource ComboBoxStyle}"/>
                <TextBlock Text="اسم المستفيد" Style="{StaticResource LabelStyle}"/>
                <ComboBox ItemsSource="{Binding Employees}" SelectedItem="{Binding Voucher.Employee}" DisplayMemberPath="FullName" Style="{StaticResource ComboBoxStyle}"
                          Visibility="{Binding Voucher.BeneficiaryType, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=موظف}"/>
                <TextBox Text="{Binding Voucher.BeneficiaryName}" Style="{StaticResource InputStyle}"
                         Visibility="{Binding Voucher.BeneficiaryType, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=أخرى}"/>
                <!-- يمكن إضافة ComboBox للموردين والمعامل لاحقًا -->
                <TextBlock Text="نوع المصروف" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Voucher.ExpenseType}" Style="{StaticResource InputStyle}"/>
                <TextBlock Text="الوصف" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Voucher.Description}" Style="{StaticResource InputStyle}"/>
                <TextBlock Text="المبلغ" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Voucher.Amount}" Style="{StaticResource InputStyle}"/>
                <TextBlock Text="طريقة الدفع" Style="{StaticResource LabelStyle}"/>
                <ComboBox ItemsSource="{Binding PaymentMethods}" SelectedItem="{Binding Voucher.PaymentMethod}" Style="{StaticResource ComboBoxStyle}"/>
                <TextBlock Text="الحالة" Style="{StaticResource LabelStyle}"/>
                <ComboBox SelectedItem="{Binding Voucher.Status}" Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="مكتمل"/>
                    <ComboBoxItem Content="معلق"/>
                </ComboBox>
                <TextBlock Text="المستلم" Style="{StaticResource LabelStyle}"/>
                <TextBox Text="{Binding Voucher.ReceivedBy}" Style="{StaticResource InputStyle}"/>
            </StackPanel>
        </ScrollViewer>
        
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,20">
            <Button Content="{Binding SaveButtonText}" Command="{Binding SaveCommand}" Style="{StaticResource ActionButtonStyle}" Background="#4CAF50" Width="120"/>
            <Button Content="إلغاء" Command="{Binding CancelCommand}" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Width="100"/>
        </StackPanel>
        <Grid Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}" Grid.RowSpan="3">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="10" Margin="0,0,0,10"/>
                <TextBlock Text="جاري الحفظ..." Foreground="White" HorizontalAlignment="Center" FontSize="16"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 