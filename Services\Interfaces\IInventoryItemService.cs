using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IInventoryItemService
    {
        Task<IEnumerable<InventoryItem>> GetAllInventoryItemsAsync();
        Task<IEnumerable<InventoryItem>> GetActiveInventoryItemsAsync();
        Task<InventoryItem?> GetInventoryItemByIdAsync(int id);
        Task<InventoryItem?> GetInventoryItemByCodeAsync(string code);
        Task<InventoryItem?> GetInventoryItemByBarcodeAsync(string barcode);
        Task<InventoryItem> CreateInventoryItemAsync(InventoryItem item);
        Task<InventoryItem> UpdateInventoryItemAsync(InventoryItem item);
        Task<bool> DeleteInventoryItemAsync(int id);
        Task<bool> DeactivateInventoryItemAsync(int id);
        Task<bool> ActivateInventoryItemAsync(int id);
        Task<IEnumerable<InventoryItem>> SearchInventoryItemsAsync(string searchTerm);
        Task<IEnumerable<InventoryItem>> GetInventoryItemsByCategoryAsync(string category);
        Task<IEnumerable<InventoryItem>> GetLowStockItemsAsync();
        Task<IEnumerable<InventoryItem>> GetExpiringItemsAsync(int daysThreshold = 30);
        Task<bool> UpdateItemQuantityAsync(int itemId, decimal quantity, string operation);
        Task<decimal> GetItemCurrentQuantityAsync(int itemId);
        Task<bool> CheckItemAvailabilityAsync(int itemId, decimal requiredQuantity);
        Task<IEnumerable<string>> GetItemCategoriesAsync();
        Task<decimal> GetItemAverageCostAsync(int itemId);
        Task UpdateItemAverageCostAsync(int itemId, decimal newPurchasePrice, decimal quantity);
    }
} 