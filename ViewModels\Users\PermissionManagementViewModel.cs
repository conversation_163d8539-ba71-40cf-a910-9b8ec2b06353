using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Users
{
    public class PermissionManagementViewModel : BaseViewModel
    {
        private readonly IPermissionService _permissionService;
        private readonly IUserService _userService;

        private User? _selectedUser;
        private Permission? _selectedPermission;
        private string _searchText = string.Empty;
        private string _selectedModule = "الكل";

        public PermissionManagementViewModel(IPermissionService permissionService, IUserService userService)
        {
            _permissionService = permissionService;
            _userService = userService;

            Users = new ObservableCollection<User>();
            AllPermissions = new ObservableCollection<Permission>();
            UserPermissions = new ObservableCollection<UserPermission>();
            AvailableModules = new ObservableCollection<string> { "الكل", "Patients", "Financial", "Employees", "Doctors", "Appointments", "Inventory", "Reports", "System" };

            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync());
            GrantPermissionCommand = new RelayCommand(async () => await GrantPermissionAsync(), () => CanGrantPermission());
            RevokePermissionCommand = new RelayCommand(async () => await RevokePermissionAsync(), () => CanRevokePermission());
            GrantAllPermissionsCommand = new RelayCommand(async () => await GrantAllPermissionsAsync(), () => CanGrantAllPermissions());
            RevokeAllPermissionsCommand = new RelayCommand(async () => await RevokeAllPermissionsAsync(), () => CanRevokeAllPermissions());
            GrantModulePermissionsCommand = new RelayCommand(async () => await GrantModulePermissionsAsync(), () => CanGrantModulePermissions());
            RefreshCommand = new RelayCommand(async () => await RefreshAsync());

            // Load data on initialization
            _ = LoadDataAsync();
        }

        public ObservableCollection<User> Users { get; set; }
        public ObservableCollection<Permission> AllPermissions { get; set; }
        public ObservableCollection<UserPermission> UserPermissions { get; set; }
        public ObservableCollection<string> AvailableModules { get; set; }

        public User? SelectedUser
        {
            get => _selectedUser;
            set
            {
                SetProperty(ref _selectedUser, value);
                _ = LoadUserPermissionsAsync();
                OnPropertyChanged(nameof(CanGrantPermission));
                OnPropertyChanged(nameof(CanRevokePermission));
                OnPropertyChanged(nameof(CanGrantAllPermissions));
                OnPropertyChanged(nameof(CanRevokeAllPermissions));
                OnPropertyChanged(nameof(CanGrantModulePermissions));
            }
        }

        public Permission? SelectedPermission
        {
            get => _selectedPermission;
            set
            {
                SetProperty(ref _selectedPermission, value);
                OnPropertyChanged(nameof(CanGrantPermission));
                OnPropertyChanged(nameof(CanRevokePermission));
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                FilterPermissions();
            }
        }

        public string SelectedModule
        {
            get => _selectedModule;
            set
            {
                SetProperty(ref _selectedModule, value);
                FilterPermissions();
            }
        }

        public ICommand LoadDataCommand { get; }
        public ICommand GrantPermissionCommand { get; }
        public ICommand RevokePermissionCommand { get; }
        public ICommand GrantAllPermissionsCommand { get; }
        public ICommand RevokeAllPermissionsCommand { get; }
        public ICommand GrantModulePermissionsCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;

                // Load users
                var users = await _userService.GetAllUsersAsync();
                Users.Clear();
                foreach (var user in users.OrderBy(u => u.FullName))
                {
                    Users.Add(user);
                }

                // Load permissions
                var permissions = await _permissionService.GetAllPermissionsAsync();
                AllPermissions.Clear();
                foreach (var permission in permissions)
                {
                    AllPermissions.Add(permission);
                }

                // Initialize permissions if empty
                if (!AllPermissions.Any())
                {
                    await _permissionService.InitializePermissionsAsync();
                    await LoadDataAsync(); // Reload
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadUserPermissionsAsync()
        {
            if (SelectedUser == null) return;

            try
            {
                IsBusy = true;
                var userPermissions = await _permissionService.GetUserPermissionsAsync(SelectedUser.Id);
                
                UserPermissions.Clear();
                foreach (var userPermission in userPermissions)
                {
                    UserPermissions.Add(userPermission);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل صلاحيات المستخدم: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void FilterPermissions()
        {
            // This will be implemented in the view with CollectionViewSource
        }

        private async Task GrantPermissionAsync()
        {
            if (SelectedUser == null || SelectedPermission == null) return;

            try
            {
                IsBusy = true;
                var success = await _permissionService.GrantPermissionToUserAsync(SelectedUser.Id, SelectedPermission.Id);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("تم منح الصلاحية بنجاح");
                    await LoadUserPermissionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("فشل في منح الصلاحية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في منح الصلاحية: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RevokePermissionAsync()
        {
            if (SelectedUser == null || SelectedPermission == null) return;

            try
            {
                IsBusy = true;
                var success = await _permissionService.RevokePermissionFromUserAsync(SelectedUser.Id, SelectedPermission.Id);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("تم إلغاء الصلاحية بنجاح");
                    await LoadUserPermissionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("فشل في إلغاء الصلاحية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إلغاء الصلاحية: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task GrantAllPermissionsAsync()
        {
            if (SelectedUser == null) return;

            try
            {
                IsBusy = true;
                var success = await _permissionService.GrantAllPermissionsToUserAsync(SelectedUser.Id);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("تم منح جميع الصلاحيات بنجاح");
                    await LoadUserPermissionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("فشل في منح جميع الصلاحيات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في منح جميع الصلاحيات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RevokeAllPermissionsAsync()
        {
            if (SelectedUser == null) return;

            try
            {
                IsBusy = true;
                var success = await _permissionService.RevokeAllPermissionsFromUserAsync(SelectedUser.Id);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("تم إلغاء جميع الصلاحيات بنجاح");
                    await LoadUserPermissionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("فشل في إلغاء جميع الصلاحيات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إلغاء جميع الصلاحيات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task GrantModulePermissionsAsync()
        {
            if (SelectedUser == null || SelectedModule == "الكل") return;

            try
            {
                IsBusy = true;
                var success = await _permissionService.GrantPermissionsByModuleToUserAsync(SelectedUser.Id, SelectedModule);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"تم منح صلاحيات وحدة {SelectedModule} بنجاح");
                    await LoadUserPermissionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("فشل في منح صلاحيات الوحدة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في منح صلاحيات الوحدة: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RefreshAsync()
        {
            await LoadDataAsync();
            if (SelectedUser != null)
            {
                await LoadUserPermissionsAsync();
            }
        }

        // Command CanExecute methods
        public bool CanGrantPermission() => SelectedUser != null && SelectedPermission != null;
        public bool CanRevokePermission() => SelectedUser != null && SelectedPermission != null;
        public bool CanGrantAllPermissions() => SelectedUser != null;
        public bool CanRevokeAllPermissions() => SelectedUser != null;
        public bool CanGrantModulePermissions() => SelectedUser != null && SelectedModule != "الكل";
    }
} 