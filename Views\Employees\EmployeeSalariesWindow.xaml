<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeSalariesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="سجلات رواتب الموظف" Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="سجلات رواتب الموظف" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        <DataGrid x:Name="SalariesDataGrid"
                  ItemsSource="{Binding SalaryRecords}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Margin="20"
                  RowHeight="32">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الشهر" Binding="{Binding SalaryMonth, StringFormat=MM/yyyy}" Width="*"/>
                <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary}" Width="*"/>
                <DataGridTextColumn Header="البدلات" Binding="{Binding Allowances}" Width="*"/>
                <DataGridTextColumn Header="الخصومات" Binding="{Binding Deductions}" Width="*"/>
                <DataGridTextColumn Header="الصافي" Binding="{Binding NetSalary}" Width="*"/>
                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="*"/>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>
        <Button Content="إغلاق" Width="100" Height="36" Margin="20" DockPanel.Dock="Bottom" HorizontalAlignment="Center" Click="Close_Click"/>
    </DockPanel>
</Window> 