using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Services;
using AqlanCenterProApp.Views.Appointments;
using System.Threading;

namespace AqlanCenterProApp.ViewModels.Appointments
{
    public class AppointmentsListViewModel : BaseViewModel
    {
        private readonly IAppointmentService _appointmentService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;
        private readonly IOrthodonticPlanService _orthodonticPlanService;

        #region Properties

        private ObservableCollection<Appointment> _appointments = new();
        private Appointment? _selectedAppointment;
        private string _searchTerm = string.Empty;
        private string _selectedStatus = "الكل";
        private DateTime _selectedDate = DateTime.Today;
        private int _totalAppointments = 0;
        private int _scheduledAppointments = 0;
        private int _completedAppointments = 0;
        private int _cancelledAppointments = 0;

        public ObservableCollection<Appointment> Appointments
        {
            get => _appointments;
            set => SetProperty(ref _appointments, value);
        }

        public Appointment? SelectedAppointment
        {
            get => _selectedAppointment;
            set
            {
                if (SetProperty(ref _selectedAppointment, value))
                {
                    // تحديث حالة الأوامر عند تغيير الموعد المحدد
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set => SetProperty(ref _searchTerm, value);
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    _ = Task.Run(async () => await LoadAppointmentsAsync());
                }
            }
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                if (SetProperty(ref _selectedDate, value))
                {
                    _ = Task.Run(async () => await LoadAppointmentsAsync());
                }
            }
        }

        public int TotalAppointments
        {
            get => _totalAppointments;
            set => SetProperty(ref _totalAppointments, value);
        }

        public int ScheduledAppointments
        {
            get => _scheduledAppointments;
            set => SetProperty(ref _scheduledAppointments, value);
        }

        public int CompletedAppointments
        {
            get => _completedAppointments;
            set => SetProperty(ref _completedAppointments, value);
        }

        public int CancelledAppointments
        {
            get => _cancelledAppointments;
            set => SetProperty(ref _cancelledAppointments, value);
        }

        public List<string> StatusOptions { get; } = new List<string>
        {
            "الكل",
            "مجدول",
            "مكتمل",
            "ملغي",
            "لم يحضر"
        };

        #endregion

        #region Commands

        public ICommand AddCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand CalendarCommand { get; }
        public ICommand DetailsCommand { get; }
        public ICommand CompleteCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand NoShowCommand { get; }
        public ICommand RescheduleCommand { get; }
        public ICommand AddOrthodonticPlanCommand { get; }

        #endregion

        public AppointmentsListViewModel(IAppointmentService appointmentService, IPatientService patientService, IDoctorService doctorService, IOrthodonticPlanService orthodonticPlanService)
        {
            _appointmentService = appointmentService;
            _patientService = patientService;
            _doctorService = doctorService;
            _orthodonticPlanService = orthodonticPlanService;

            // تهيئة الأوامر
            AddCommand = new RelayCommand(async _ => await AddAppointmentAsync());
            EditCommand = new RelayCommand(async _ => await EditAppointmentAsync(), _ => SelectedAppointment != null);
            DeleteCommand = new RelayCommand(async _ => await DeleteAppointmentAsync(), _ => SelectedAppointment != null);
            RefreshCommand = new RelayCommand(async _ => await LoadAppointmentsAsync());
            SearchCommand = new RelayCommand(async _ => await SearchAppointmentsAsync());
            CalendarCommand = new RelayCommand(_ => ShowCalendar());
            DetailsCommand = new RelayCommand(_ => ShowDetails(), _ => SelectedAppointment != null);
            CompleteCommand = new RelayCommand(async _ => await CompleteAppointmentAsync(), _ => SelectedAppointment != null);
            CancelCommand = new RelayCommand(async _ => await CancelAppointmentAsync(), _ => SelectedAppointment != null);
            NoShowCommand = new RelayCommand(async _ => await MarkAsNoShowAsync(), _ => SelectedAppointment != null);
            RescheduleCommand = new RelayCommand(async _ => await RescheduleAppointmentAsync(), _ => SelectedAppointment != null);
            AddOrthodonticPlanCommand = new RelayCommand(async _ => await AddOrthodonticPlanAsync(), _ => SelectedAppointment != null);

            // تحميل البيانات الأولية
            _ = LoadAppointmentsAsync();
        }

        public async Task LoadAppointmentsAsync()
        {
            try
            {
                IsLoading = true;
                
                // إضافة timeout للعملية
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(25));
                
                // تحميل البيانات في الخلفية مع timeout
                var appointments = await Task.Run(async () =>
                {
                    IEnumerable<Appointment> result;
                    if (SelectedStatus == "الكل")
                    {
                        result = await _appointmentService.GetAllAppointmentsAsync();
                    }
                    else
                    {
                        result = await _appointmentService.GetAllAppointmentsAsync();
                        result = result.Where(a => a.Status == SelectedStatus);
                    }
                    return result;
                }, cts.Token);

                // تحديث القائمة في الخيط الرئيسي
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Appointments.Clear();
                    foreach (var appointment in appointments)
                    {
                        Appointments.Add(appointment);
                    }
                });

                await LoadStatisticsAsync();
            }
            catch (OperationCanceledException)
            {
                MessageBox.Show("انتهت مهلة تحميل بيانات المواعيد. يرجى المحاولة مرة أخرى.", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المواعيد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        public async Task SearchAppointmentsAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadAppointmentsAsync();
                return;
            }

            await ExecuteAsync(async () =>
            {
                IsLoading = true;
                Appointments.Clear();

                var searchResults = await _appointmentService.SearchAppointmentsAsync(SearchTerm);
                foreach (var appointment in searchResults)
                {
                    Appointments.Add(appointment);
                }

                IsLoading = false;
            }, "جاري البحث...");
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var stats = await _appointmentService.GetAppointmentsStatisticsAsync();
                TotalAppointments = stats["إجمالي المواعيد"];
                ScheduledAppointments = stats["المواعيد المجدولة"];
                CompletedAppointments = stats["المواعيد المكتملة"];
                CancelledAppointments = stats["المواعيد الملغية"];
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private Task AddAppointmentAsync()
        {
            try
            {
                var viewModel = new AddEditAppointmentViewModel(_appointmentService, _patientService, _doctorService, _orthodonticPlanService);
                var window = new AddEditAppointmentView(viewModel);

                // ربط حدث إغلاق النافذة لتحديث القائمة
                viewModel.RequestClose += async () =>
                {
                    window.Close();
                    await LoadAppointmentsAsync();
                };

                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة موعد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            return Task.CompletedTask;
        }

        private Task EditAppointmentAsync()
        {
            if (SelectedAppointment == null) return Task.CompletedTask;

            try
            {
                var viewModel = new AddEditAppointmentViewModel(_appointmentService, _patientService, _doctorService, _orthodonticPlanService, SelectedAppointment);
                var window = new AddEditAppointmentView(viewModel);

                // ربط حدث إغلاق النافذة لتحديث القائمة
                viewModel.RequestClose += async () =>
                {
                    window.Close();
                    await LoadAppointmentsAsync();
                };

                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل موعد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            return Task.CompletedTask;
        }

        private async Task DeleteAppointmentAsync()
        {
            if (SelectedAppointment == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الموعد:\nالمريض: {SelectedAppointment.Patient?.FullName}\nالتاريخ: {SelectedAppointment.AppointmentDateTime:yyyy/MM/dd HH:mm}؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _appointmentService.DeleteAppointmentAsync(SelectedAppointment.Id);
                    if (success)
                    {
                        await LoadAppointmentsAsync();
                        SelectedAppointment = null;
                        MessageBox.Show("تم حذف الموعد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الموعد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري حذف الموعد...");
            }
        }

        private async Task CompleteAppointmentAsync()
        {
            if (SelectedAppointment == null) return;

            var result = MessageBox.Show(
                $"هل تريد تأكيد إكمال الموعد:\nالمريض: {SelectedAppointment.Patient?.FullName}؟",
                "تأكيد الإكمال",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _appointmentService.MarkAppointmentAsCompletedAsync(SelectedAppointment.Id);
                    if (success)
                    {
                        await LoadAppointmentsAsync();
                        MessageBox.Show("تم تأكيد إكمال الموعد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تأكيد إكمال الموعد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري تأكيد إكمال الموعد...");
            }
        }

        private async Task CancelAppointmentAsync()
        {
            if (SelectedAppointment == null) return;

            var reason = Microsoft.VisualBasic.Interaction.InputBox(
                "أدخل سبب الإلغاء:", "سبب إلغاء الموعد", "");

            if (!string.IsNullOrWhiteSpace(reason))
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _appointmentService.CancelAppointmentAsync(SelectedAppointment.Id, reason);
                    if (success)
                    {
                        await LoadAppointmentsAsync();
                        MessageBox.Show("تم إلغاء الموعد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إلغاء الموعد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري إلغاء الموعد...");
            }
        }

        private async Task MarkAsNoShowAsync()
        {
            if (SelectedAppointment == null) return;

            var result = MessageBox.Show(
                $"هل تريد تأكيد عدم حضور المريض:\n{SelectedAppointment.Patient?.FullName}؟",
                "تأكيد عدم الحضور",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _appointmentService.MarkAppointmentAsNoShowAsync(SelectedAppointment.Id);
                    if (success)
                    {
                        await LoadAppointmentsAsync();
                        MessageBox.Show("تم تأكيد عدم حضور المريض بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تأكيد عدم حضور المريض", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري تأكيد عدم الحضور...");
            }
        }

        private Task RescheduleAppointmentAsync()
        {
            if (SelectedAppointment == null) return Task.CompletedTask;

            // فتح نافذة إعادة جدولة الموعد
            MessageBox.Show("سيتم تطوير نافذة إعادة الجدولة في المرحلة القادمة", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
            return Task.CompletedTask;
        }

        private void ShowCalendar()
        {
            // فتح نافذة التقويم
            MessageBox.Show("سيتم تطوير نافذة التقويم في المرحلة القادمة", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowDetails()
        {
            if (SelectedAppointment == null) return;

            // فتح نافذة تفاصيل الموعد
            var details = $"تفاصيل الموعد:\n\n" +
                         $"المريض: {SelectedAppointment.Patient?.FullName}\n" +
                         $"الطبيب: {SelectedAppointment.Doctor?.FullName}\n" +
                         $"الخدمة: {SelectedAppointment.ServiceType}\n" +
                         $"النوع: {SelectedAppointment.AppointmentType}\n" +
                         $"التاريخ والوقت: {SelectedAppointment.AppointmentDateTime:yyyy/MM/dd HH:mm}\n" +
                         $"المدة: {SelectedAppointment.DurationMinutes} دقيقة\n" +
                         $"الحالة: {SelectedAppointment.Status}\n" +
                         $"ملاحظات: {SelectedAppointment.Notes ?? "لا توجد"}";

            MessageBox.Show(details, "تفاصيل الموعد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private Task AddOrthodonticPlanAsync()
        {
            if (SelectedAppointment == null) return Task.CompletedTask;

            // فتح نافذة إضافة خطة تقويم
            MessageBox.Show("سيتم تطوير نافذة إضافة خطة تقويم في المرحلة القادمة", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
            return Task.CompletedTask;
        }
    }
}