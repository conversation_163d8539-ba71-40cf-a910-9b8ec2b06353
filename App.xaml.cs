using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Data;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Windows;
using System;

namespace AqlanCenterProApp
{
    public partial class App : Application
    {
        public static IServiceProvider? Services { get; private set; }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            var services = new ServiceCollection();
            ConfigureServices(services);
            Services = services.BuildServiceProvider();

            // Show main window
            var mainWindow = new MainWindow(Services);
            mainWindow.Show();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
            });

            // Add DbContext
            services.AddDbContext<AqlanCenterDbContext>(options =>
                options.UseSqlite("Data Source=AqlanCenterDatabase.db"));

            // Add core services
            services.AddSingleton<ILoggingService, LoggingService>();
            services.AddSingleton<IDashboardDataService, DashboardDataService>();
            services.AddSingleton<IChartService, ChartService>();

            // Add business services
            services.AddSingleton<IDoctorService, DoctorService>();
            services.AddSingleton<IDashboardService, DashboardService>();
            services.AddSingleton<IPatientService, PatientService>();
            services.AddSingleton<IInvoiceService, InvoiceService>();
            services.AddSingleton<IReceiptService, ReceiptService>();
            services.AddSingleton<IPaymentVoucherService, PaymentVoucherService>();
            services.AddSingleton<IAccountStatementService, AccountStatementService>();
            services.AddSingleton<IBackupService, BackupService>();
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IPermissionService, PermissionService>();
            services.AddSingleton<ISettingsService, SettingsService>();
        }
    }
}
