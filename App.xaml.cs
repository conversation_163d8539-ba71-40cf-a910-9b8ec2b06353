using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Helpers;
using AqlanCenterProApp.ViewModels;
using AqlanCenterProApp.ViewModels.Patients;
using AqlanCenterProApp.ViewModels.Doctors;
using AqlanCenterProApp.ViewModels.Employees;
using AqlanCenterProApp.ViewModels.Appointments;
using AqlanCenterProApp.ViewModels.Invoices;
using AqlanCenterProApp.ViewModels.Receipts;
using AqlanCenterProApp.ViewModels.PaymentVouchers;
using AqlanCenterProApp.ViewModels.AccountStatements;
using AqlanCenterProApp.ViewModels.Labs;
using AqlanCenterProApp.ViewModels.LabOrders;
using AqlanCenterProApp.ViewModels.Inventory;
using AqlanCenterProApp.ViewModels.Purchases;
using AqlanCenterProApp.ViewModels.Suppliers;
using AqlanCenterProApp.ViewModels.Users;
using AqlanCenterProApp.ViewModels.Roles;
using AqlanCenterProApp.ViewModels.Settings;
using AqlanCenterProApp.ViewModels.Reports;
using AqlanCenterProApp.ViewModels.Dashboard;
using AqlanCenterProApp.Services;

namespace AqlanCenterProApp
{
    public partial class App : Application
    {
        private IHost? _host;
        public static IServiceProvider? Services { get; private set; }

        static App()
        {
            // تعطيل BinaryFormatter في .NET 8 لحل مشاكل التسلسل
            try
            {
                AppContext.SetSwitch("System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization", false);
                AppContext.SetSwitch("System.Runtime.Serialization.DisallowBinaryFormatterSerialization", true);
                AppContext.SetSwitch("System.Windows.Markup.DoNotUseSha256ForMarkupCompilerChecksumAlgorithm", true);
                AppContext.SetSwitch("Microsoft.Extensions.DependencyInjection.DisableDynamicEngine", false);

                Console.WriteLine("✅ تم تعطيل BinaryFormatter بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ تحذير: لا يمكن تعطيل BinaryFormatter: {ex.Message}");
            }
        }

        public App()
        {
            // معالجة الأخطاء غير المتوقعة
            AppDomain.CurrentDomain.UnhandledException += (s, e) =>
            {
                var ex = e.ExceptionObject as Exception;
                HandleUnhandledException(ex);
            };

            // معالجة أخطاء التسلسل المحددة
            AppDomain.CurrentDomain.UnhandledException += (s, e) =>
            {
                var ex = e.ExceptionObject as Exception;
                if (ex != null && IsSerializationError(ex))
                {
                    HandleSerializationError(ex);
                }
            };
        }

        private void HandleUnhandledException(Exception? ex)
        {
            string message = $"Unhandled Exception: {ex?.Message}\n{ex?.StackTrace}";
            try
            {
                File.AppendAllText("log.txt", $"[{DateTime.Now}] {message}\n");
            }
            catch { }

            // عرض رسالة مبسطة للمستخدم
            var userMessage = IsSerializationError(ex)
                ? "حدث خطأ في معالجة البيانات. سيتم إعادة تشغيل التطبيق."
                : "حدث خطأ غير متوقع في التطبيق.";

            MessageBox.Show(userMessage, "خطأ في التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private bool IsSerializationError(Exception? ex)
        {
            if (ex == null) return false;

            var errorMessage = ex.Message.ToLower();
            return errorMessage.Contains("serialization") ||
                   errorMessage.Contains("binaryformatter") ||
                   errorMessage.Contains("deserializer") ||
                   errorMessage.Contains("system.windows.baml2006");
        }

        private void HandleSerializationError(Exception ex)
        {
            Console.WriteLine($"🔧 معالجة خطأ التسلسل: {ex.Message}");

            // تسجيل الخطأ
            try
            {
                var errorLog = new
                {
                    Timestamp = DateTime.Now,
                    ErrorType = "SerializationError",
                    Message = ex.Message,
                    StackTrace = ex.StackTrace,
                    InnerException = ex.InnerException?.Message
                };

                var logJson = Helpers.SerializationHelper.SerializeToJson(errorLog);
                File.AppendAllText("serialization_errors.log", logJson + "\n");
            }
            catch { }
        }

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                Console.WriteLine("🚀 بدء تشغيل التطبيق...");

                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
                    .Build();

                Console.WriteLine("✅ تم تحميل الإعدادات");

                // Build host
                _host = Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        ConfigureServices(services, configuration);
                    })
                    .Build();

                // Start the host
                _host.Start();
                Services = _host.Services;

                Console.WriteLine("✅ تم تهيئة الخدمات");

                // Initialize database with error handling - WAIT for completion
                Console.WriteLine("🔄 بدء تهيئة قاعدة البيانات...");
                await InitializeDatabaseWithValidationAsync();
                Console.WriteLine("✅ تم الانتهاء من تهيئة قاعدة البيانات");

                // Show main window
                var mainWindow = new MainWindow(_host.Services);
                mainWindow.Show();

                Console.WriteLine("✅ تم عرض النافذة الرئيسية");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[App Startup Exception] {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"حدث خطأ أثناء بدء التطبيق:\n{ex.Message}", "خطأ في بدء التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _host?.StopAsync(TimeSpan.FromSeconds(5)).Wait();
                _host?.Dispose();
                Console.WriteLine("✅ تم إيقاف التطبيق بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إيقاف التطبيق: {ex.Message}");
            }

            base.OnExit(e);
        }

        private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // Add configuration
            services.AddSingleton(configuration);

            // Add logging
            services.AddLogging();

            // قاعدة بيانات واحدة موحدة للنظام بالكامل
            var connectionString = DatabasePathHelper.GetConnectionString();
            DatabasePathHelper.LogDatabaseInfo();

            // Add DbContext
            services.AddDbContext<AqlanCenterDbContext>(options =>
                options.UseSqlite(connectionString));

            // ربط جميع الخدمات مع الـ Implementations
            services.AddScoped<ILoggingService, LoggingService>();
            services.AddScoped<IPatientService, PatientService>();
            services.AddScoped<IAppointmentService, AppointmentService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IActivityLogService, ActivityLogService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IPurchaseService, PurchaseService>();
            services.AddScoped<IInventoryItemService, InventoryItemService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<ILabOrderService, LabOrderService>();
            services.AddScoped<IShadeService, ShadeService>();
            services.AddScoped<ILabService, LabService>();
            services.AddScoped<IServiceService, ServiceService>();
            services.AddScoped<IAccountStatementService, AccountStatementService>();
            services.AddScoped<IPaymentVoucherService, PaymentVoucherService>();
            services.AddScoped<IReceiptService, ReceiptService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IOrthodonticPlanService, OrthodonticPlanService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<IDoctorService, DoctorService>();
            services.AddScoped<IPermissionService, PermissionService>();

            // تسجيل ViewModels
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<PatientsListViewModel>();
            services.AddTransient<AddEditPatientViewModel>();  // إضافة ViewModel المفقود
            services.AddTransient<DoctorsListViewModel>();
            services.AddTransient<EmployeesListViewModel>();
            services.AddTransient<AppointmentsListViewModel>();
            services.AddTransient<InvoicesListViewModel>();
            services.AddTransient<ReceiptsListViewModel>();
            services.AddTransient<PaymentVouchersListViewModel>();
            services.AddTransient<AccountStatementsListViewModel>();
            services.AddTransient<LabsListViewModel>();
            services.AddTransient<LabOrdersListViewModel>();
            services.AddTransient<InventoryListViewModel>();
            services.AddTransient<PurchasesListViewModel>();
            services.AddTransient<SuppliersListViewModel>();
            services.AddTransient<UsersListViewModel>();
            services.AddTransient<RolesListViewModel>();
            services.AddTransient<SettingsMainViewModel>();
            services.AddTransient<ReportsMainViewModel>();
            services.AddTransient<ViewModels.Users.ActivityLogViewModel>(); // إضافة ActivityLogViewModel
        }

        private async Task InitializeDatabaseWithValidationAsync()
        {
            try
            {
                Console.WriteLine("🔍 فحص قاعدة البيانات الموحدة...");

                // طباعة معلومات قاعدة البيانات
                var dbPath = DatabasePathHelper.GetDatabasePath();
                var connectionString = DatabasePathHelper.GetConnectionString();
                Console.WriteLine($"📁 مسار قاعدة البيانات: {dbPath}");
                Console.WriteLine($"🔗 سلسلة الاتصال: {connectionString}");

                // التحقق من وجود قاعدة البيانات الموحدة
                if (!DatabaseManager.IsUnifiedDatabaseExists())
                {
                    Console.WriteLine("📁 قاعدة البيانات الموحدة غير موجودة، سيتم إنشاؤها...");
                    var created = await DatabaseManager.CreateUnifiedDatabaseAsync();
                    if (!created)
                    {
                        throw new Exception("فشل في إنشاء قاعدة البيانات الموحدة");
                    }
                    Console.WriteLine("✅ تم إنشاء قاعدة البيانات الموحدة بنجاح");
                }
                else
                {
                    // فحص صحة قاعدة البيانات الموجودة
                    var (isValid, message) = await DatabaseManager.ValidateUnifiedDatabaseAsync();
                    if (!isValid)
                    {
                        Console.WriteLine($"⚠️ قاعدة البيانات تحتاج إعادة إنشاء: {message}");
                        var reset = await DatabaseManager.ResetUnifiedDatabaseAsync();
                        if (!reset)
                        {
                            throw new Exception($"فشل في إعادة إنشاء قاعدة البيانات: {message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"✅ {message}");
                    }
                }

                Console.WriteLine("🎉 قاعدة البيانات الموحدة جاهزة للاستخدام!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"🔍 الخطأ الداخلي: {ex.InnerException.Message}");
                }

                // عرض رسالة خطأ للمستخدم
                DatabaseErrorHandler.ShowDatabaseError(ex, "تهيئة قاعدة البيانات");

                // إنهاء التطبيق في حالة فشل تهيئة قاعدة البيانات
                Environment.Exit(1);
            }
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                using var scope = _host!.Services.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AqlanCenterDbContext>();

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Initialize database with seed data
                await DatabaseInitializer.InitializeAsync(context);

                Console.WriteLine("✅ تم تهيئة قاعدة البيانات");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}", "خطأ في قاعدة البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
}
