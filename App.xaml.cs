using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace AqlanCenterProApp
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            var services = new ServiceCollection();
            ConfigureServices(services);
            
            // ... existing code ...
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Add the new DoctorService
            services.AddSingleton<IDoctorService, DoctorService>();
            
            // ... other service registrations ...
        }
    }
}
