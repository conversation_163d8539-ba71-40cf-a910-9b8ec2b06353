using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class BackupSettings : BaseEntity
    {
        [Required]
        [MaxLength(500)]
        public string BackupDirectory { get; set; } = string.Empty;

        public bool EnableAutoBackup { get; set; } = false;

        [MaxLength(50)]
        public string AutoBackupFrequency { get; set; } = "Daily"; // Daily, Weekly, Monthly

        public int AutoBackupHour { get; set; } = 2; // 2 AM

        public int AutoBackupMinute { get; set; } = 0;

        public int RetentionDays { get; set; } = 30;

        public bool EnableCompression { get; set; } = true;

        public bool EnableEncryption { get; set; } = false;

        [MaxLength(100)]
        public string? EncryptionPassword { get; set; }

        public bool EnableNotifications { get; set; } = true;

        public bool NotifyOnSuccess { get; set; } = true;

        public bool NotifyOnFailure { get; set; } = true;

        public bool NotifyOnRetentionExpiry { get; set; } = true;

        [MaxLength(500)]
        public string? CloudBackupPath { get; set; }

        public bool EnableCloudBackup { get; set; } = false;

        public int MaxBackupSizeMB { get; set; } = 1000; // 1GB

        public bool EnableBackupVerification { get; set; } = true;

        public bool EnableChecksumValidation { get; set; } = true;

        [MaxLength(100)]
        public string? LastBackupDate { get; set; }

        [MaxLength(100)]
        public string? LastBackupStatus { get; set; }

        public int FailedBackupAttempts { get; set; } = 0;

        public DateTime? LastFailedBackupDate { get; set; }

        [MaxLength(1000)]
        public string? LastError { get; set; }
    }
} 