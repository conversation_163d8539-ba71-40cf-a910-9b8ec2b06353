using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace AqlanCenterProApp.ViewModels.Settings
{
    public class SettingsMainViewModel : BaseViewModel
    {
        private readonly ISettingsService _settingsService;
        private readonly ILogger<SettingsMainViewModel> _logger;

        private ClinicSettings _clinicSettings;
        private SystemSettings _systemSettings;
        private NotificationSettings _notificationSettings;
        private ObservableCollection<SystemLookup> _lookups;
        private string _selectedLookupType;
        private bool _isPasswordRequired;
        private string _settingsPassword;
        private bool _isLoading;

        public SettingsMainViewModel(ISettingsService settingsService, ILogger<SettingsMainViewModel> logger)
        {
            _settingsService = settingsService;
            _logger = logger;

            InitializeCommands();
            LoadSettingsAsync();
        }

        #region Properties
        public ClinicSettings ClinicSettings
        {
            get => _clinicSettings;
            set => SetProperty(ref _clinicSettings, value);
        }

        public SystemSettings SystemSettings
        {
            get => _systemSettings;
            set => SetProperty(ref _systemSettings, value);
        }

        public NotificationSettings NotificationSettings
        {
            get => _notificationSettings;
            set => SetProperty(ref _notificationSettings, value);
        }

        public ObservableCollection<SystemLookup> Lookups
        {
            get => _lookups;
            set => SetProperty(ref _lookups, value);
        }

        public string SelectedLookupType
        {
            get => _selectedLookupType;
            set
            {
                SetProperty(ref _selectedLookupType, value);
                LoadLookupsAsync();
            }
        }

        public bool IsPasswordRequired
        {
            get => _isPasswordRequired;
            set => SetProperty(ref _isPasswordRequired, value);
        }

        public string SettingsPassword
        {
            get => _settingsPassword;
            set => SetProperty(ref _settingsPassword, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public List<string> LookupTypes { get; } = new List<string>
        {
            "TreatmentType",
            "ServiceType",
            "MedicalCategory",
            "PaymentMethod",
            "AppointmentStatus",
            "PatientStatus",
            "EmployeeStatus",
            "DocumentType",
            "LeaveType",
            "SalaryType",
            "InventoryCategory",
            "LabType",
            "ProsthesisType",
            "ShadeType"
        };

        public List<string> ColorSchemes { get; } = new List<string>
        {
            "Default",
            "Blue",
            "Green",
            "Purple",
            "Orange",
            "Dark"
        };

        public List<string> Fonts { get; } = new List<string>
        {
            "Segoe UI",
            "Arial",
            "Tahoma",
            "Times New Roman",
            "Calibri"
        };

        public List<string> Languages { get; } = new List<string>
        {
            "ar",
            "en"
        };
        #endregion

        #region Commands
        public ICommand SaveClinicSettingsCommand { get; private set; }
        public ICommand SaveSystemSettingsCommand { get; private set; }
        public ICommand SaveNotificationSettingsCommand { get; private set; }
        public ICommand AddLookupCommand { get; private set; }
        public ICommand EditLookupCommand { get; private set; }
        public ICommand DeleteLookupCommand { get; private set; }
        public ICommand ExportSettingsCommand { get; private set; }
        public ICommand ImportSettingsCommand { get; private set; }
        public ICommand ResetToDefaultsCommand { get; private set; }
        public ICommand CheckForUpdatesCommand { get; private set; }
        public ICommand ValidatePasswordCommand { get; private set; }
        public ICommand TestBackupPathCommand { get; private set; }

        private void InitializeCommands()
        {
            SaveClinicSettingsCommand = new RelayCommand(async () => await SaveClinicSettingsAsync());
            SaveSystemSettingsCommand = new RelayCommand(async () => await SaveSystemSettingsAsync());
            SaveNotificationSettingsCommand = new RelayCommand(async () => await SaveNotificationSettingsAsync());
            AddLookupCommand = new RelayCommand(async () => await AddLookupAsync());
            EditLookupCommand = new RelayCommand<SystemLookup>(async (lookup) => await EditLookupAsync(lookup));
            DeleteLookupCommand = new RelayCommand<SystemLookup>(async (lookup) => await DeleteLookupAsync(lookup));
            ExportSettingsCommand = new RelayCommand(async () => await ExportSettingsAsync());
            ImportSettingsCommand = new RelayCommand(async () => await ImportSettingsAsync());
            ResetToDefaultsCommand = new RelayCommand(async () => await ResetToDefaultsAsync());
            CheckForUpdatesCommand = new RelayCommand(async () => await CheckForUpdatesAsync());
            ValidatePasswordCommand = new RelayCommand(async () => await ValidatePasswordAsync());
            TestBackupPathCommand = new RelayCommand(async () => await TestBackupPathAsync());
        }
        #endregion

        #region Methods
        public async Task LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;

                // تحميل إعدادات افتراضية مباشرة لتجنب مشاكل قاعدة البيانات
                await LoadDefaultSettingsAsync();

                // محاولة تحميل الإعدادات الحقيقية في الخلفية
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var clinicSettings = await _settingsService.GetClinicSettingsAsync().ConfigureAwait(false);
                        var systemSettings = await _settingsService.GetSystemSettingsAsync().ConfigureAwait(false);
                        var notificationSettings = await _settingsService.GetNotificationSettingsAsync().ConfigureAwait(false);

                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            ClinicSettings = clinicSettings;
                            SystemSettings = systemSettings;
                            NotificationSettings = notificationSettings;

                            // تحقق من الحاجة لكلمة مرور
                            IsPasswordRequired = SystemSettings.RequirePasswordForSettings;

                            // تحميل القوائم المساعدة
                            if (LookupTypes.Any())
                            {
                                SelectedLookupType = LookupTypes.First();
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات الحقيقية: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الإعدادات");
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");

                // تحميل إعدادات افتراضية في حالة الخطأ
                await LoadDefaultSettingsAsync();
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadDefaultSettingsAsync()
        {
            try
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // إعدادات افتراضية للعيادة
                    ClinicSettings = new ClinicSettings
                    {
                        ClinicName = "عيادة الأسنان",
                        Address = "العنوان",
                        PhoneNumber = "123456789",
                        Email = "<EMAIL>"
                    };

                    // إعدادات افتراضية للنظام
                    SystemSettings = new SystemSettings
                    {
                        RequirePasswordForSettings = false,
                        EnableAutoBackup = true,
                        AutoBackupIntervalHours = 24
                    };

                    // إعدادات افتراضية للتنبيهات
                    NotificationSettings = new NotificationSettings
                    {
                        EnableEmailNotifications = true,
                        EnableSmsNotifications = false
                    };
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات الافتراضية: {ex.Message}");
            }
        }

        private async Task LoadLookupsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedLookupType))
                    return;

                var lookups = await _settingsService.GetLookupsByTypeAsync(SelectedLookupType);
                Lookups = new ObservableCollection<SystemLookup>(lookups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل القوائم المساعدة");
                MessageHelper.ShowError("خطأ في تحميل القوائم المساعدة");
            }
        }

        private async Task SaveClinicSettingsAsync()
        {
            try
            {
                if (IsPasswordRequired && !await ValidatePasswordAsync())
                    return;

                var success = await _settingsService.UpdateClinicSettingsAsync(ClinicSettings);
                if (success)
                {
                    MessageHelper.ShowSuccess("تم حفظ إعدادات العيادة بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حفظ إعدادات العيادة");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعدادات العيادة");
                MessageHelper.ShowError("خطأ في حفظ إعدادات العيادة");
            }
        }

        private async Task SaveSystemSettingsAsync()
        {
            try
            {
                if (IsPasswordRequired && !await ValidatePasswordAsync())
                    return;

                var success = await _settingsService.UpdateSystemSettingsAsync(SystemSettings);
                if (success)
                {
                    MessageHelper.ShowSuccess("تم حفظ الإعدادات العامة بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حفظ الإعدادات العامة");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الإعدادات العامة");
                MessageHelper.ShowError("خطأ في حفظ الإعدادات العامة");
            }
        }

        private async Task SaveNotificationSettingsAsync()
        {
            try
            {
                if (IsPasswordRequired && !await ValidatePasswordAsync())
                    return;

                var success = await _settingsService.UpdateNotificationSettingsAsync(NotificationSettings);
                if (success)
                {
                    MessageHelper.ShowSuccess("تم حفظ إعدادات الإشعارات بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حفظ إعدادات الإشعارات");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعدادات الإشعارات");
                MessageHelper.ShowError("خطأ في حفظ إعدادات الإشعارات");
            }
        }

        private async Task AddLookupAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedLookupType))
                {
                    MessageHelper.ShowError("يرجى اختيار نوع القائمة أولاً");
                    return;
                }

                var lookup = new SystemLookup
                {
                    LookupType = SelectedLookupType,
                    Name = "عنصر جديد",
                    SortOrder = Lookups.Count,
                    IsActive = true
                };

                var success = await _settingsService.AddLookupAsync(lookup);
                if (success)
                {
                    await LoadLookupsAsync();
                    MessageHelper.ShowSuccess("تم إضافة العنصر بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في إضافة العنصر");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة عنصر القائمة");
                MessageHelper.ShowError("خطأ في إضافة عنصر القائمة");
            }
        }

        private async Task EditLookupAsync(SystemLookup lookup)
        {
            try
            {
                if (lookup == null)
                    return;

                var success = await _settingsService.UpdateLookupAsync(lookup);
                if (success)
                {
                    MessageHelper.ShowSuccess("تم تحديث العنصر بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في تحديث العنصر");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عنصر القائمة");
                MessageHelper.ShowError("خطأ في تحديث عنصر القائمة");
            }
        }

        private async Task DeleteLookupAsync(SystemLookup lookup)
        {
            try
            {
                if (lookup == null)
                    return;

                var success = await _settingsService.DeleteLookupAsync(lookup.Id);
                if (success)
                {
                    await LoadLookupsAsync();
                    MessageHelper.ShowSuccess("تم حذف العنصر بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حذف العنصر");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف عنصر القائمة");
                MessageHelper.ShowError("خطأ في حذف عنصر القائمة");
            }
        }

        private async Task ExportSettingsAsync()
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "ملف JSON (*.json)|*.json",
                    FileName = $"settings_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (dialog.ShowDialog() == true)
                {
                    var success = await _settingsService.ExportSettingsAsync(dialog.FileName);
                    if (success)
                    {
                        MessageHelper.ShowSuccess("تم تصدير الإعدادات بنجاح");
                    }
                    else
                    {
                        MessageHelper.ShowError("فشل في تصدير الإعدادات");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الإعدادات");
                MessageHelper.ShowError("خطأ في تصدير الإعدادات");
            }
        }

        private async Task ImportSettingsAsync()
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Filter = "ملف JSON (*.json)|*.json"
                };

                if (dialog.ShowDialog() == true)
                {
                    var success = await _settingsService.ImportSettingsAsync(dialog.FileName);
                    if (success)
                    {
                        await LoadSettingsAsync();
                        MessageHelper.ShowSuccess("تم استيراد الإعدادات بنجاح");
                    }
                    else
                    {
                        MessageHelper.ShowError("فشل في استيراد الإعدادات");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استيراد الإعدادات");
                MessageHelper.ShowError("خطأ في استيراد الإعدادات");
            }
        }

        private async Task ResetToDefaultsAsync()
        {
            try
            {
                if (IsPasswordRequired && !await ValidatePasswordAsync())
                    return;

                var success = await _settingsService.ResetToDefaultsAsync();
                if (success)
                {
                    await LoadSettingsAsync();
                    MessageHelper.ShowSuccess("تم إعادة تعيين الإعدادات بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في إعادة تعيين الإعدادات");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                MessageHelper.ShowError("خطأ في إعادة تعيين الإعدادات");
            }
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                var hasUpdates = await _settingsService.CheckForUpdatesAsync();
                if (hasUpdates)
                {
                    MessageHelper.ShowInfo("يوجد تحديث جديد متاح");
                }
                else
                {
                    MessageHelper.ShowInfo("النظام محدث إلى أحدث إصدار");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من التحديثات");
                MessageHelper.ShowError("خطأ في التحقق من التحديثات");
            }
        }

        private async Task<bool> ValidatePasswordAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SettingsPassword))
                {
                    MessageHelper.ShowError("يرجى إدخال كلمة المرور");
                    return false;
                }

                var isValid = await _settingsService.ValidateSettingsPasswordAsync(SettingsPassword);
                if (!isValid)
                {
                    MessageHelper.ShowError("كلمة المرور غير صحيحة");
                    SettingsPassword = string.Empty;
                }
                else
                {
                    SettingsPassword = string.Empty;
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من كلمة المرور");
                MessageHelper.ShowError("خطأ في التحقق من كلمة المرور");
                return false;
            }
        }

        private async Task TestBackupPathAsync()
        {
            try
            {
                var success = await _settingsService.TestBackupPathAsync(SystemSettings.BackupPath);
                if (success)
                {
                    MessageHelper.ShowSuccess("مسار النسخ الاحتياطي صحيح");
                }
                else
                {
                    MessageHelper.ShowError("مسار النسخ الاحتياطي غير صحيح");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اختبار مسار النسخ الاحتياطي");
                MessageHelper.ShowError("خطأ في اختبار مسار النسخ الاحتياطي");
            }
        }
        #endregion
    }
}