using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class PurchaseItem : BaseEntity
    {
        [Required]
        public int PurchaseId { get; set; }
        
        [Required]
        public int InventoryItemId { get; set; }
        
        public decimal Quantity { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; } = 0;
        
        public decimal DiscountPercentage { get; set; } = 0;
        
        public decimal DiscountAmount { get; set; } = 0;
        
        public decimal NetPrice { get; set; } = 0;
        
        public DateTime? ExpiryDate { get; set; }
        
        public decimal ReceivedQuantity { get; set; } = 0;
        
        public decimal RemainingQuantity { get; set; } = 0;
        
        [StringLength(200)]
        public new string? Notes { get; set; }
        
        // Navigation Properties
        [ForeignKey("PurchaseId")]
        public virtual Purchase Purchase { get; set; } = null!;
        
        [ForeignKey("InventoryItemId")]
        public virtual InventoryItem InventoryItem { get; set; } = null!;
    }
}
