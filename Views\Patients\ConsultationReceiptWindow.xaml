<Window x:Class="AqlanCenterProApp.Views.Patients.ConsultationReceiptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="سند معاينة - مركز عقلان للأسنان"
        Height="650"
        Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Background="#F8F9FA">

    <Window.Resources>
        <!-- خطوط وأنماط محسنة حسب المعايير العالمية -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#4A90E2"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,4"/>
        </Style>
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,8"/>
        </Style>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,4"/>
        </Style>
        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="Margin" Value="0,4"/>
        </Style>
        <Style x:Key="AmountStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="22"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Padding="0,25,0,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#3498DB" Offset="0"/>
                    <GradientStop Color="#2980B9" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel HorizontalAlignment="Center">
                <!-- Logo in Circle -->
                <Border Width="80" Height="80" Background="White" CornerRadius="40" HorizontalAlignment="Center" Margin="0,0,0,8">
                    <Border.Effect>
                        <DropShadowEffect BlurRadius="8" ShadowDepth="2" Opacity="0.25"/>
                    </Border.Effect>
                    <Image Source="pack://application:,,,/Resources/logo.png" Width="50" Height="50" Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                </Border>
                <!-- Clinic Name -->
                <TextBlock Text="مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان" Style="{StaticResource TitleStyle}" FontSize="16" MaxWidth="350" TextWrapping="Wrap" TextAlignment="Center"/>
                <!-- Slogan -->
                <TextBlock Text="معنا تصبح ابتسامتك كما تحب أن تكون" FontSize="12" Foreground="#E67E22" FontWeight="SemiBold" HorizontalAlignment="Center" TextAlignment="Center" MaxWidth="300" TextWrapping="Wrap" Margin="0,4,0,0"/>
                <!-- Receipt Title -->
                <Border Background="#33FFFFFF" CornerRadius="20" Padding="12,8" HorizontalAlignment="Center" Margin="0,12,0,0">
                    <TextBlock Text="🧾 سند معاينة" FontSize="16" FontWeight="Bold" Foreground="White"/>
                </Border>
            </StackPanel>
        </Border>

        <!-- Content Section -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E9ECEF" BorderThickness="1,0,1,0" Padding="15">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Receipt Info -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,8">
                        <TextBlock x:Name="ReceiptNumberText" Style="{StaticResource ValueStyle}" Margin="8,0"/>
                        <TextBlock Text="|" Foreground="#B0B0B0" Margin="4,0"/>
                        <TextBlock x:Name="ReceiptDateText" Style="{StaticResource ValueStyle}" Margin="8,0"/>
                        <TextBlock Text="|" Foreground="#B0B0B0" Margin="4,0"/>
                        <TextBlock x:Name="ReceiptTimeText" Style="{StaticResource ValueStyle}" Margin="8,0"/>
                    </StackPanel>

                    <!-- Patient Information -->
                    <Border Background="#F8F9FA" Padding="12" CornerRadius="6" BorderBrush="#4A90E2" BorderThickness="0,0,0,2" Margin="0,0,0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <!-- Patient Name -->
                            <StackPanel Grid.Row="0" Grid.Column="0">
                                <TextBlock Text="اسم المريض" Style="{StaticResource LabelStyle}" FontSize="11" Foreground="#666"/>
                                <TextBlock x:Name="PatientNameText" Style="{StaticResource ValueStyle}" FontWeight="Bold" Foreground="#333"/>
                            </StackPanel>
                            <!-- File Number -->
                            <StackPanel Grid.Row="0" Grid.Column="1">
                                <TextBlock Text="رقم الملف" Style="{StaticResource LabelStyle}" FontSize="11" Foreground="#666"/>
                                <TextBlock x:Name="FileNumberText" Style="{StaticResource ValueStyle}" FontWeight="Bold" Foreground="#333"/>
                            </StackPanel>
                            <!-- Phone -->
                            <StackPanel Grid.Row="1" Grid.Column="0">
                                <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}" FontSize="11" Foreground="#666"/>
                                <TextBlock x:Name="PhoneText" Style="{StaticResource ValueStyle}" FontWeight="Bold" Foreground="#333"/>
                            </StackPanel>
                            <!-- Service Type -->
                            <StackPanel Grid.Row="1" Grid.Column="1">
                                <TextBlock Text="نوع الخدمة" Style="{StaticResource LabelStyle}" FontSize="11" Foreground="#666"/>
                                <TextBlock Text="معاينة أولية" Style="{StaticResource ValueStyle}" FontWeight="Bold" Foreground="#333"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Amount Section -->
                    <Border CornerRadius="8" Padding="20" Margin="0,15">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#28A745" Offset="0"/>
                                <GradientStop Color="#20C997" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel>
                            <TextBlock Text="المبلغ المدفوع" FontSize="14" Foreground="White" HorizontalAlignment="Center" Opacity="0.9"/>
                            <TextBlock x:Name="AmountText" Style="{StaticResource AmountStyle}"/>
                            <TextBlock x:Name="AmountWordsText" FontSize="12" Foreground="White" HorizontalAlignment="Center" Opacity="0.8" FontStyle="Italic" Margin="0,6,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Signature Section -->
                    <Border Background="#F8F9FA" Padding="15" CornerRadius="6" Margin="0,15,0,0">
                        <StackPanel>
                            <TextBlock Text="✍️ توقيع المستلم" Style="{StaticResource HeaderStyle}" HorizontalAlignment="Right"/>
                            <Border BorderBrush="#333" BorderThickness="0,0,0,2" Width="180" Height="35" Margin="0,15"/>
                            <TextBlock Text="توقيع وختم المسؤول" Style="{StaticResource ValueStyle}" HorizontalAlignment="Center" FontSize="11"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Footer Section -->
        <Border Grid.Row="2" Background="#3498DB" CornerRadius="0,0,8,8" Padding="15,8">
            <StackPanel>
                <Grid Margin="0,0,0,4">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="📍 تعز – شارع التحرير الأعلى – جوار جامع الأزهر" Foreground="White" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" TextAlignment="Center"/>
                    <TextBlock Grid.Column="1" Text="📞 04253028 – 770245745 – 711752823" Foreground="White" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" TextAlignment="Center"/>
                    <TextBlock Grid.Column="2" Text="🌟 شكراً لثقتكم بنا - نتمنى لكم دوام الصحة والعافية 🌟" Foreground="White" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" TextAlignment="Center"/>
                </Grid>
                <TextBlock x:Name="IssueDateText" Foreground="White" HorizontalAlignment="Center" FontSize="9" Opacity="0.8" Margin="0,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Print Button -->
        <Border Grid.Row="2" Background="#2980B9" CornerRadius="6" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,20">
            <Button Content="🖨️ طباعة السند" Background="Transparent" Foreground="White" BorderThickness="0" Padding="12,8" FontSize="14" FontWeight="Bold" Click="PrintButton_Click"/>
        </Border>
    </Grid>
</Window>
