# Test script to run the application and capture output
Write-Host "Starting AqlanCenterProApp test..." -ForegroundColor Green

# Build the application
Write-Host "Building application..." -ForegroundColor Yellow
dotnet build AqlanCenterProApp.csproj

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful. Running application..." -ForegroundColor Green
    
    # Run the application and capture output
    $process = Start-Process -FilePath ".\bin\Debug\net8.0-windows\AqlanCenterProApp.exe" -PassThru -NoNewWindow -RedirectStandardOutput "app_output.txt" -RedirectStandardError "app_error.txt"
    
    # Wait for a few seconds
    Start-Sleep -Seconds 10
    
    # Check if process is still running
    if (!$process.HasExited) {
        Write-Host "Application is running. Stopping it..." -ForegroundColor Yellow
        $process.Kill()
    }
    
    # Display output
    Write-Host "=== STANDARD OUTPUT ===" -ForegroundColor Cyan
    if (Test-Path "app_output.txt") {
        Get-Content "app_output.txt"
    } else {
        Write-Host "No standard output file found"
    }
    
    Write-Host "=== ERROR OUTPUT ===" -ForegroundColor Red
    if (Test-Path "app_error.txt") {
        Get-Content "app_error.txt"
    } else {
        Write-Host "No error output file found"
    }
    
    # Check database files
    Write-Host "=== DATABASE FILES ===" -ForegroundColor Magenta
    Get-ChildItem -Path . -Filter "*.db*" -Recurse | Select-Object FullName, Length, LastWriteTime
    
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}
