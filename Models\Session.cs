using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class Session : BaseEntity
    {
        /// <summary>
        /// رقم الجلسة (تسلسلي تلقائي)
        /// </summary>
        public int SessionId { get; set; }

        [Required]
        public int PatientId { get; set; }

        [Required]
        public int DoctorId { get; set; }

        /// <summary>
        /// اسم المريض (للعرض السريع)
        /// </summary>
        [StringLength(100)]
        public string PatientName { get; set; } = string.Empty;

        [Required]
        public DateTime SessionDate { get; set; } = DateTime.Now;

        /// <summary>
        /// وقت بداية الجلسة
        /// </summary>
        public TimeSpan StartTime { get; set; } = TimeSpan.FromHours(9);

        /// <summary>
        /// مدة الجلسة بالدقائق
        /// </summary>
        public int Duration { get; set; } = 30;

        [Required]
        [StringLength(100)]
        public string TreatmentType { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue)]
        public decimal Amount { get; set; }

        /// <summary>
        /// تكلفة الجلسة (نفس Amount للتوافق)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Cost
        {
            get => Amount;
            set => Amount = value;
        }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [StringLength(500)]
        public new string? Notes { get; set; }

        /// <summary>
        /// حالة الجلسة
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "مكتملة"; // مكتملة، ملغية، مؤجلة، قيد التنفيذ

        [StringLength(20)]
        public string PaymentStatus { get; set; } = "غير مدفوعة"; // مدفوعة، غير مدفوعة، مدفوعة جزئياً

        [StringLength(100)]
        public string? Doctor { get; set; } // اسم الطبيب أو معرّفه

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ServiceCost { get; set; } // تكلفة الخدمة الفعلية

        [Required]
        [StringLength(100)]
        public string TreatmentDescription { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات الطبيب
        /// </summary>
        [StringLength(1000)]
        public string? DoctorNotes { get; set; }

        /// <summary>
        /// عمولة الطبيب من هذه الجلسة
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DoctorCommission { get; set; } = 0;

        // Navigation Properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("DoctorId")]
        public virtual Doctor? DoctorEntity { get; set; }

        // Computed Properties
        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount => Cost - PaidAmount;

        /// <summary>
        /// هل الجلسة مكتملة
        /// </summary>
        public bool IsCompleted => Status == "مكتملة";

        /// <summary>
        /// هل الجلسة ملغية
        /// </summary>
        public bool IsCancelled => Status == "ملغية";
    }
}
