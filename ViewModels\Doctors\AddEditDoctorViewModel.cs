using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.ViewModels.Doctors
{
    public partial class AddEditDoctorViewModel : ObservableValidator
    {
        private readonly IDoctorService _doctorService;
        private Doctor? _originalDoctor;
        private bool _isEditMode;

        public AddEditDoctorViewModel(IDoctorService doctorService)
        {
            _doctorService = doctorService;

            // تهيئة الأوامر
            SaveCommand = new AsyncRelayCommand(SaveAsync);
            CancelCommand = new RelayCommand(Cancel);

            // تعيين القيم الافتراضية
            JoinDate = DateTime.Now;
            Status = "نشط";
            Gender = "ذكر";
            ContractType = "دائم";
            IsAvailableForAppointments = true;
        }

        #region Properties

        [ObservableProperty]
        private string windowTitle = "إضافة طبيب جديد";

        [ObservableProperty]
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        private string fullName = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "التخصص مطلوب")]
        private string specialization = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        private string mobile = string.Empty;

        [ObservableProperty]
        private string email = string.Empty;

        [ObservableProperty]
        private string gender = "ذكر";

        [ObservableProperty]
        private string nationality = string.Empty;

        [ObservableProperty]
        private DateTime joinDate = DateTime.Now;

        [ObservableProperty]
        private string status = "نشط";

        [ObservableProperty]
        private string contractType = "دائم";

        [ObservableProperty]
        private decimal? commissionPercentage;

        [ObservableProperty]
        private string commissionCurrency = "ر.ي";

        [ObservableProperty]
        private decimal? fixedSalary;

        [ObservableProperty]
        private string salaryCurrency = "ر.ي";

        [ObservableProperty]
        private string licenseNumber = string.Empty;

        [ObservableProperty]
        private DateTime? licenseExpiryDate;

        [ObservableProperty]
        private bool isAvailableForAppointments = true;

        [ObservableProperty]
        private string qualifications = string.Empty;

        [ObservableProperty]
        private string notes = string.Empty;

        #endregion

        #region Commands

        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        #endregion

        #region Events

        public event EventHandler<bool>? CloseRequested;

        #endregion

        #region Methods

        /// <summary>
        /// تهيئة النموذج للإضافة
        /// </summary>
        public void Initialize()
        {
            _isEditMode = false;
            WindowTitle = "إضافة طبيب جديد";
        }

        /// <summary>
        /// تهيئة النموذج للتعديل
        /// </summary>
        public void Initialize(Doctor doctor)
        {
            _isEditMode = true;
            _originalDoctor = doctor;
            WindowTitle = $"تعديل الطبيب: {doctor.FullName}";

            // تحميل البيانات
            FullName = doctor.FullName;
            Specialization = doctor.Specialization;
            Mobile = doctor.Mobile ?? string.Empty;
            Email = doctor.Email ?? string.Empty;
            Gender = doctor.Gender ?? "ذكر";
            Nationality = doctor.Nationality ?? string.Empty;
            JoinDate = doctor.JoinDate;
            Status = doctor.Status ?? "نشط";
            ContractType = doctor.ContractType ?? "دائم";
            CommissionPercentage = doctor.CommissionPercentage;
            CommissionCurrency = doctor.CommissionCurrency ?? "ر.ي";
            FixedSalary = doctor.FixedSalary;
            SalaryCurrency = doctor.SalaryCurrency ?? "ر.ي";
            LicenseNumber = doctor.LicenseNumber ?? string.Empty;
            LicenseExpiryDate = doctor.LicenseExpiryDate;
            IsAvailableForAppointments = doctor.IsAvailableForAppointments;
            Qualifications = doctor.Qualifications ?? string.Empty;
            Notes = doctor.Notes ?? string.Empty;
        }

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        private async Task SaveAsync()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateData())
                    return;

                Doctor doctor;
                bool success;

                if (_isEditMode && _originalDoctor != null)
                {
                    // تحديث الطبيب الموجود
                    doctor = _originalDoctor;
                    UpdateDoctorData(doctor);
                    success = await _doctorService.UpdateDoctorAsync(doctor, "System");
                }
                else
                {
                    // إضافة طبيب جديد
                    doctor = CreateNewDoctor();
                    success = await _doctorService.AddDoctorAsync(doctor, "System");
                }

                if (success)
                {
                    System.Windows.MessageBox.Show(
                        _isEditMode ? "تم تحديث بيانات الطبيب بنجاح" : "تم إضافة الطبيب بنجاح",
                        "نجح",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);

                    CloseRequested?.Invoke(this, true);
                }
                else
                {
                    System.Windows.MessageBox.Show(
                        _isEditMode ? "فشل في تحديث بيانات الطبيب" : "فشل في إضافة الطبيب",
                        "خطأ",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void Cancel()
        {
            CloseRequested?.Invoke(this, false);
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrWhiteSpace(FullName))
            {
                System.Windows.MessageBox.Show("الاسم الكامل مطلوب", "تنبيه",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(Specialization))
            {
                System.Windows.MessageBox.Show("التخصص مطلوب", "تنبيه",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(Mobile))
            {
                System.Windows.MessageBox.Show("رقم الهاتف مطلوب", "تنبيه",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// إنشاء طبيب جديد
        /// </summary>
        private Doctor CreateNewDoctor()
        {
            return new Doctor
            {
                FullName = FullName.Trim(),
                Specialization = Specialization.Trim(),
                Mobile = Mobile.Trim(),
                Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim(),
                Gender = Gender,
                Nationality = string.IsNullOrWhiteSpace(Nationality) ? null : Nationality.Trim(),
                JoinDate = JoinDate,
                Status = Status,
                ContractType = ContractType,
                CommissionPercentage = CommissionPercentage ?? 0,
                CommissionCurrency = CommissionCurrency,
                FixedSalary = FixedSalary,
                SalaryCurrency = SalaryCurrency,
                LicenseNumber = string.IsNullOrWhiteSpace(LicenseNumber) ? null : LicenseNumber.Trim(),
                LicenseExpiryDate = LicenseExpiryDate,
                IsAvailableForAppointments = IsAvailableForAppointments,
                Qualifications = string.IsNullOrWhiteSpace(Qualifications) ? null : Qualifications.Trim(),
                Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim(),
                IsActive = Status == "نشط",
                CreatedAt = DateTime.Now,
                CreatedBy = "System" // يمكن تحديثه لاحقاً ليكون المستخدم الحالي
            };
        }

        /// <summary>
        /// تحديث بيانات الطبيب الموجود
        /// </summary>
        private void UpdateDoctorData(Doctor doctor)
        {
            doctor.FullName = FullName.Trim();
            doctor.Specialization = Specialization.Trim();
            doctor.Mobile = Mobile.Trim();
            doctor.Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim();
            doctor.Gender = Gender;
            doctor.Nationality = string.IsNullOrWhiteSpace(Nationality) ? null : Nationality.Trim();
            doctor.JoinDate = JoinDate;
            doctor.Status = Status;
            doctor.ContractType = ContractType;
            doctor.CommissionPercentage = CommissionPercentage ?? 0;
            doctor.CommissionCurrency = CommissionCurrency;
            doctor.FixedSalary = FixedSalary;
            doctor.SalaryCurrency = SalaryCurrency;
            doctor.LicenseNumber = string.IsNullOrWhiteSpace(LicenseNumber) ? null : LicenseNumber.Trim();
            doctor.LicenseExpiryDate = LicenseExpiryDate;
            doctor.IsAvailableForAppointments = IsAvailableForAppointments;
            doctor.Qualifications = string.IsNullOrWhiteSpace(Qualifications) ? null : Qualifications.Trim();
            doctor.Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim();
            doctor.IsActive = Status == "نشط";
            doctor.UpdatedAt = DateTime.Now;
            doctor.UpdatedBy = "System"; // يمكن تحديثه لاحقاً ليكون المستخدم الحالي
        }

        #endregion
    }
}
