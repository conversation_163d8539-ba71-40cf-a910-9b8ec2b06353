using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Reports
{
    /// <summary>
    /// تقرير المخزون
    /// </summary>
    public class InventoryReport : ReportBase
    {
        public InventoryReport()
        {
            Type = ReportType.InventoryReport;
        }

        // إحصائيات عامة
        public int TotalItems { get; set; }
        public int ActiveItems { get; set; }
        public int InactiveItems { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int ExpiringItems { get; set; }

        // القيم المالية
        public decimal TotalInventoryValue { get; set; }
        public decimal LowStockValue { get; set; }
        public decimal ExpiringValue { get; set; }

        // الأصناف منخفضة المخزون
        public List<LowStockItem> LowStockItemsList { get; set; } = new();

        // الأصناف قريبة انتهاء الصلاحية
        public List<ExpiringItem> ExpiringItemsList { get; set; } = new();

        // الأصناف الأكثر طلباً
        public List<MostRequestedItem> MostRequestedItems { get; set; } = new();

        // الأصناف الأقل حركة
        public List<SlowMovingItem> SlowMovingItems { get; set; } = new();

        // توزيع المخزون حسب الفئة
        public List<CategoryInventory> CategoryInventory { get; set; } = new();

        // حركة المخزون
        public List<InventoryMovement> RecentMovements { get; set; } = new();
    }

    /// <summary>
    /// صنف منخفض المخزون
    /// </summary>
    public class LowStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderQuantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public int DaysUntilStockout { get; set; }
    }

    /// <summary>
    /// صنف قريب انتهاء الصلاحية
    /// </summary>
    public class ExpiringItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public DateTime ExpiryDate { get; set; }
        public int DaysUntilExpiry { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
    }

    /// <summary>
    /// الصنف الأكثر طلباً
    /// </summary>
    public class MostRequestedItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime LastRequestDate { get; set; }
    }

    /// <summary>
    /// الصنف بطيء الحركة
    /// </summary>
    public class SlowMovingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int LastMovementDays { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime LastMovementDate { get; set; }
    }

    /// <summary>
    /// مخزون الفئة
    /// </summary>
    public class CategoryInventory
    {
        public string CategoryName { get; set; } = string.Empty;
        public int ItemsCount { get; set; }
        public int TotalStock { get; set; }
        public decimal TotalValue { get; set; }
        public double PercentageOfTotal { get; set; }
    }

    /// <summary>
    /// حركة المخزون
    /// </summary>
    public class InventoryMovement
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string MovementType { get; set; } = string.Empty; // In/Out
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime MovementDate { get; set; }
        public string? Notes { get; set; }
    }
} 