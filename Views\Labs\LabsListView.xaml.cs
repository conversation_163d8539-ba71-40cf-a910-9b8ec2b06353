using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Labs;

namespace AqlanCenterProApp.Views.Labs
{
    /// <summary>
    /// Interaction logic for LabsListView.xaml
    /// </summary>
    public partial class LabsListView : UserControl
    {
        public LabsListView()
        {
            InitializeComponent();
        }

        public LabsListView(LabsListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += LabsListView_Loaded;
        }

        private void LabsListView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is LabsListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadLabsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadLabsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات المعامل: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في LabsListView_Loaded: {ex.Message}");
            }
        }
    }
}
