using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Data;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace AqlanCenterProApp.Services.Implementations
{
    public class DoctorService : IDoctorService
    {
        private readonly AqlanCenterDbContext _context;

        public DoctorService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Doctor>> GetAllDoctorsAsync()
        {
            return await _context.Doctors.ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetActiveDoctorsAsync()
        {
            return await _context.Doctors.Where(d => d.IsActive).ToListAsync();
        }

        public async Task<Doctor?> GetDoctorByIdAsync(int id)
        {
            return await _context.Doctors.FindAsync(id);
        }

        public async Task<IEnumerable<Doctor>> SearchDoctorsAsync(string searchTerm)
        {
            return await _context.Doctors
                .Where(d => d.FullName.Contains(searchTerm) ||
                           d.Specialization.Contains(searchTerm) ||
                           (d.Phone != null && d.Phone.Contains(searchTerm)))
                .ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetDoctorsBySpecializationAsync(string specialization)
        {
            return await _context.Doctors
                .Where(d => d.Specialization == specialization)
                .ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetDoctorsByStatusAsync(string status)
        {
            return await _context.Doctors
                .Where(d => d.Status == status)
                .ToListAsync();
        }

        public async Task<Doctor> AddDoctorAsync(Doctor doctor, string currentUser)
        {
            doctor.CreatedAt = DateTime.Now;
            doctor.CreatedBy = currentUser;
            _context.Doctors.Add(doctor);
            await _context.SaveChangesAsync();
            return doctor;
        }

        public async Task<Doctor> UpdateDoctorAsync(Doctor doctor, string currentUser)
        {
            doctor.UpdatedAt = DateTime.Now;
            doctor.UpdatedBy = currentUser;
            _context.Doctors.Update(doctor);
            await _context.SaveChangesAsync();
            return doctor;
        }

        public async Task<bool> DeleteDoctorAsync(int id, string currentUser)
        {
            var doctor = await _context.Doctors.FindAsync(id);
            if (doctor != null)
            {
                _context.Doctors.Remove(doctor);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> ToggleDoctorStatusAsync(int id, string currentUser)
        {
            var doctor = await _context.Doctors.FindAsync(id);
            if (doctor != null)
            {
                doctor.IsActive = !doctor.IsActive;
                doctor.Status = doctor.IsActive ? "نشط" : "غير نشط";
                doctor.UpdatedAt = DateTime.Now;
                doctor.UpdatedBy = currentUser;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<object> GetDoctorStatisticsAsync(int doctorId)
        {
            var doctor = await _context.Doctors.FindAsync(doctorId);
            if (doctor == null) return new { };

            var appointmentsCount = await _context.Appointments.CountAsync(a => a.DoctorId == doctorId);
            var sessionsCount = await _context.Sessions.CountAsync(s => s.DoctorId == doctorId);

            return new
            {
                DoctorId = doctorId,
                DoctorName = doctor.FullName,
                AppointmentsCount = appointmentsCount,
                SessionsCount = sessionsCount,
                Rating = doctor.Rating ?? 0,
                Status = doctor.Status
            };
        }

        public async Task<object> GetGeneralStatisticsAsync()
        {
            var totalDoctors = await _context.Doctors.CountAsync();
            var activeDoctors = await _context.Doctors.CountAsync(d => d.IsActive);
            var averageRating = await _context.Doctors.Where(d => d.Rating.HasValue).AverageAsync(d => d.Rating) ?? 0;

            return new
            {
                TotalDoctors = totalDoctors,
                ActiveDoctors = activeDoctors,
                InactiveDoctors = totalDoctors - activeDoctors,
                AverageRating = averageRating
            };
        }

        public async Task<bool> UpdateDoctorRatingAsync(int doctorId, decimal rating)
        {
            var doctor = await _context.Doctors.FindAsync(doctorId);
            if (doctor != null)
            {
                doctor.Rating = rating;
                doctor.RatingCount++;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<IEnumerable<Doctor>> GetAvailableDoctorsAsync()
        {
            return await _context.Doctors
                .Where(d => d.IsActive && d.IsAvailableForAppointments)
                .ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetDoctorsWithExpiringLicensesAsync(int daysBeforeExpiry)
        {
            var expiryDate = DateTime.Now.AddDays(daysBeforeExpiry);
            return await _context.Doctors
                .Where(d => d.LicenseExpiryDate.HasValue && d.LicenseExpiryDate <= expiryDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetDoctorsWithExpiringContractsAsync(int daysBeforeExpiry)
        {
            var expiryDate = DateTime.Now.AddDays(daysBeforeExpiry);
            return await _context.Doctors
                .Where(d => d.ContractEndDate.HasValue && d.ContractEndDate <= expiryDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Doctor>> GetTopRatedDoctorsAsync(int count)
        {
            return await _context.Doctors
                .Where(d => d.Rating.HasValue)
                .OrderByDescending(d => d.Rating)
                .Take(count)
                .ToListAsync();
        }

        public async Task<bool> UpdateDoctorStatisticsAsync(int doctorId)
        {
            var doctor = await _context.Doctors.FindAsync(doctorId);
            if (doctor != null)
            {
                // Update statistics logic here
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<int> GetDoctorCountAsync()
        {
            return await _context.Doctors.CountAsync();
        }
    }
}