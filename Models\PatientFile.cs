using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class PatientFile : BaseEntity
    {
        [Required]
        public int PatientId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string FileType { get; set; } = string.Empty; // صورة، أشعة، تقرير، وصفة
        
        [StringLength(20)]
        public string FileExtension { get; set; } = string.Empty;
        
        public long FileSize { get; set; } = 0;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public DateTime UploadDate { get; set; } = DateTime.Now;
        
        [StringLength(100)]
        public string? UploadedBy { get; set; }
        
        // Navigation Properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;
    }
}
