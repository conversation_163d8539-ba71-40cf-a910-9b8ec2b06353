<Window x:Class="AqlanCenterProApp.Views.Patients.PrescriptionsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الوصفات الطبية"
        Width="1100"
        Height="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F6FA">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="الوصفات الطبية للمرضى"
                   FontSize="28"
                   FontWeight="Bold"
                   Foreground="#3498DB"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- شريط البحث والفلاتر -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <TextBox Grid.Column="0"
                     x:Name="SearchTextBox"
                     Height="35"
                     FontSize="14"
                     VerticalContentAlignment="Center"
                     Margin="0,0,10,0"/>

            <!-- فلتر المريض -->
            <ComboBox Grid.Column="1"
                      x:Name="PatientFilterComboBox"
                      Width="200"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع المرضى"/>
            </ComboBox>

            <!-- فلتر الطبيب -->
            <ComboBox Grid.Column="2"
                      x:Name="DoctorFilterComboBox"
                      Width="150"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع الأطباء"/>
            </ComboBox>

            <!-- زر البحث -->
            <Button Grid.Column="3"
                    Content="🔍 بحث"
                    Width="100"
                    Height="35"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- قائمة الوصفات الطبية -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <DataGrid x:Name="PrescriptionsDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      HeadersVisibility="Column"
                      CanUserAddRows="False"
                      RowHeight="40"
                      EnableRowVirtualization="True">

                <DataGrid.Columns>
                    <!-- رقم الوصفة -->
                    <DataGridTextColumn Header="رقم الوصفة" 
                                        Binding="{Binding Id}" 
                                        Width="80"/>

                    <!-- اسم المريض -->
                    <DataGridTextColumn Header="اسم المريض" 
                                        Binding="{Binding PatientName}" 
                                        Width="180"/>

                    <!-- تاريخ الوصفة -->
                    <DataGridTextColumn Header="تاريخ الوصفة" 
                                        Binding="{Binding PrescriptionDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>

                    <!-- الطبيب -->
                    <DataGridTextColumn Header="الطبيب المعالج" 
                                        Binding="{Binding DoctorName}" 
                                        Width="150"/>

                    <!-- الأدوية -->
                    <DataGridTextColumn Header="الأدوية المقررة" 
                                        Binding="{Binding Medications}" 
                                        Width="300"/>

                    <!-- الجرعة -->
                    <DataGridTextColumn Header="الجرعة والتعليمات" 
                                        Binding="{Binding Dosage}" 
                                        Width="200"/>

                    <!-- الحالة -->
                    <DataGridTextColumn Header="الحالة" 
                                        Binding="{Binding Status}" 
                                        Width="100"/>

                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="👁️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="عرض التفاصيل"
                                            Click="ViewDetailsButton_Click"/>
                                    <Button Content="🖨️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="طباعة"
                                            Click="PrintButton_Click"/>
                                    <Button Content="✏️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="تعديل"
                                            Click="EditButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <!-- قائمة السياق -->
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="👁️ عرض التفاصيل" Click="ViewDetailsMenuItem_Click"/>
                        <MenuItem Header="🖨️ طباعة الوصفة" Click="PrintMenuItem_Click"/>
                        <MenuItem Header="✏️ تعديل الوصفة" Click="EditMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="📋 نسخ المعلومات" Click="CopyMenuItem_Click"/>
                        <MenuItem Header="📤 تصدير الوصفة" Click="ExportMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="🗑️ حذف الوصفة" Click="DeleteMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>
        </ScrollViewer>

        <!-- الأزرار السفلية -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="AddPrescriptionButton"
                    Content="➕ إضافة وصفة جديدة"
                    Width="150"
                    Height="40"
                    Background="#27AE60"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="AddPrescriptionButton_Click"/>
            
            <Button x:Name="RefreshButton"
                    Content="🔄 تحديث"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="RefreshButton_Click"/>
            
            <Button x:Name="ExportAllButton"
                    Content="📤 تصدير الكل"
                    Width="120"
                    Height="40"
                    Background="#F39C12"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="ExportAllButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="❌ إغلاق"
                    Width="120"
                    Height="40"
                    Background="#E74C3C"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
