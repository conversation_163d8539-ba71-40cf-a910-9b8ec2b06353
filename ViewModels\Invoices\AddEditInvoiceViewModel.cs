using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.Invoices
{
    /// <summary>
    /// ViewModel إضافة/تعديل الفاتورة
    /// </summary>
    public class AddEditInvoiceViewModel : BaseViewModel
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPatientService _patientService;
        private readonly IServiceService _serviceService;

        private Invoice _invoice;
        private ObservableCollection<InvoiceItem> _invoiceItems;
        private InvoiceItem? _selectedItem;
        private Patient? _selectedPatient;
        private List<Patient> _patients;
        private bool _isEditMode;
        private bool _isBusy;
        private InvoiceItem? _lastRemovedItem;
        private int _lastRemovedIndex;

        public ObservableCollection<Service> AvailableServices { get; set; } = new();
        public Service? SelectedService { get; set; }
        public ICommand AddServiceToInvoiceCommand { get; }
        public ICommand UndoRemoveItemCommand { get; }
        public bool CanUndoRemove => _lastRemovedItem != null;
        public ICommand PrintCommand { get; }
        public ICommand PreviewCommand { get; }

        public AddEditInvoiceViewModel(IInvoiceService invoiceService, IPatientService patientService, IServiceService serviceService)
        {
            _invoiceService = invoiceService;
            _patientService = patientService;
            _serviceService = serviceService;
            _invoice = new Invoice { InvoiceDate = DateTime.Now, InvoiceStatus = "مفتوحة" };
            _patients = new List<Patient>();
            _invoiceItems = new ObservableCollection<InvoiceItem>();
            _invoiceItems.CollectionChanged += InvoiceItems_CollectionChanged;
            
            // تهيئة جميع الأوامر
            LoadPatientsCommand = new RelayCommand(async () => await LoadPatientsAsync());
            AddItemCommand = new RelayCommand(AddItem);
            RemoveItemCommand = new RelayCommand(RemoveItem, () => SelectedItem != null);
            CalculateTotalsCommand = new RelayCommand(CalculateTotals);
            SaveCommand = new RelayCommand(async () => await SaveAsync());
            CancelCommand = new RelayCommand(Cancel);
            AddNewPatientCommand = new RelayCommand(OpenAddNewPatientWindow);
            AddServiceToInvoiceCommand = new RelayCommand(AddServiceToInvoice, () => SelectedService != null);
            UndoRemoveItemCommand = new RelayCommand(UndoRemoveItem, () => CanUndoRemove);
            PrintCommand = new RelayCommand(PrintInvoice);
            PreviewCommand = new RelayCommand(PreviewInvoice);
            
            LoadAvailableServices();
        }

        public void OnLoaded()
        {
            // تحميل البيانات في الخلفية
            _ = Task.Run(async () =>
            {
                await LoadPatientsAsync();
                if (_isEditMode)
                    await LoadInvoiceItemsAsync();
            });
        }

        #region Properties

        public Invoice Invoice
        {
            get => _invoice;
            set => SetProperty(ref _invoice, value);
        }

        public ObservableCollection<InvoiceItem> InvoiceItems
        {
            get => _invoiceItems;
            set => SetProperty(ref _invoiceItems, value);
        }

        public InvoiceItem? SelectedItem
        {
            get => _selectedItem;
            set
            {
                SetProperty(ref _selectedItem, value);
                System.Windows.Input.CommandManager.InvalidateRequerySuggested();
            }
        }

        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set
            {
                SetProperty(ref _selectedPatient, value);
                if (value != null)
                {
                    Invoice.PatientId = value.Id;
                }
            }
        }

        public List<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public string WindowTitle => IsEditMode ? "تعديل الفاتورة" : "إضافة فاتورة جديدة";

        public string SaveButtonText => IsEditMode ? "تحديث" : "حفظ";

        public List<string> PaymentMethods { get; } = new List<string>
        {
            "نقداً",
            "بطاقة ائتمان",
            "تحويل بنكي",
            "شيك",
            "آجل"
        };

        public bool IsBusy
        {
            get => _isBusy;
            set { _isBusy = value; OnPropertyChanged(); }
        }

        public decimal DiscountAmount
        {
            get => Invoice.DiscountAmount;
            set
            {
                Invoice.DiscountAmount = value;
                CalculateTotals();
                OnPropertyChanged();
            }
        }

        public decimal TaxAmount
        {
            get => Invoice.TaxAmount;
            set
            {
                Invoice.TaxAmount = value;
                CalculateTotals();
                OnPropertyChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand LoadPatientsCommand { get; }
        public ICommand AddItemCommand { get; }
        public ICommand RemoveItemCommand { get; }
        public ICommand CalculateTotalsCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand AddNewPatientCommand { get; }

        #endregion

        #region Methods

        private async Task LoadPatientsAsync()
        {
            try
            {
                IsBusy = true;
                var patients = await _patientService.GetAllPatientsAsync();
                Patients = patients.ToList();

                // تحديد المريض المحدد إذا كان في وضع التعديل
                if (IsEditMode && Invoice.PatientId > 0)
                {
                    SelectedPatient = Patients.FirstOrDefault(p => p.Id == Invoice.PatientId);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المرضى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadInvoiceItemsAsync()
        {
            try
            {
                IsBusy = true;
                var items = await _invoiceService.GetInvoiceItemsAsync(Invoice.Id);
                
                InvoiceItems.Clear();
                foreach (var item in items)
                {
                    InvoiceItems.Add(item);
                }

                CalculateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل عناصر الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddItem()
        {
            try
            {
                var newItem = new InvoiceItem
                {
                    InvoiceId = Invoice.Id,
                    ItemDescription = "خدمة جديدة",
                    Quantity = 1,
                    UnitPrice = 0,
                    TotalPrice = 0
                };

                InvoiceItems.Add(newItem);
                SelectedItem = newItem;
                CalculateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العنصر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveItem()
        {
            if (SelectedItem != null)
            {
                _lastRemovedIndex = InvoiceItems.IndexOf(SelectedItem);
                _lastRemovedItem = SelectedItem;
                InvoiceItems.Remove(SelectedItem);
                OnPropertyChanged(nameof(CanUndoRemove));
            }
        }

        private void UndoRemoveItem()
        {
            if (_lastRemovedItem != null)
            {
                InvoiceItems.Insert(_lastRemovedIndex, _lastRemovedItem);
                _lastRemovedItem = null;
                OnPropertyChanged(nameof(CanUndoRemove));
            }
        }

        private void CalculateTotals()
        {
            decimal subTotal = InvoiceItems.Sum(i => i.Quantity * i.UnitPrice);
            Invoice.SubTotal = subTotal;
            decimal discount = Invoice.DiscountAmount;
            decimal tax = Invoice.TaxAmount;
            Invoice.TotalAmount = subTotal - discount + tax;
            OnPropertyChanged(nameof(Invoice));
        }

        private void InvoiceItems_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            CalculateTotals();
        }

        private async Task SaveAsync()
        {
            // تحقق من صحة البيانات قبل الحفظ
            if (SelectedPatient == null)
            {
                System.Windows.MessageBox.Show("يرجى اختيار المريض قبل الحفظ.", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }
            if (InvoiceItems.Count == 0)
            {
                System.Windows.MessageBox.Show("يجب إضافة عنصر واحد على الأقل للفاتورة.", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }
            if (InvoiceItems.Any(i => i.Quantity <= 0 || i.UnitPrice <= 0))
            {
                System.Windows.MessageBox.Show("تأكد أن الكمية وسعر الوحدة لكل عنصر أكبر من الصفر.", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }
            if (Invoice.TotalAmount < 0)
            {
                System.Windows.MessageBox.Show("الإجمالي لا يمكن أن يكون أقل من الصفر.", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }
            try
            {
                // التحقق من صحة البيانات
                if (!await ValidateDataAsync())
                    return;

                IsBusy = true;

                // تعيين رقم الفاتورة إذا كان جديداً
                if (!IsEditMode && string.IsNullOrEmpty(Invoice.InvoiceNumber))
                {
                    Invoice.InvoiceNumber = await _invoiceService.GetNextInvoiceNumberAsync();
                }

                // حفظ الفاتورة
                if (IsEditMode)
                {
                    Invoice = await _invoiceService.UpdateInvoiceAsync(Invoice);
                }
                else
                {
                    Invoice = await _invoiceService.CreateInvoiceAsync(Invoice);
                }

                // حفظ عناصر الفاتورة
                foreach (var item in InvoiceItems)
                {
                    item.InvoiceId = Invoice.Id;
                    
                    if (item.Id == 0) // عنصر جديد
                    {
                        await _invoiceService.AddInvoiceItemAsync(item);
                    }
                    else // تحديث عنصر موجود
                    {
                        await _invoiceService.UpdateInvoiceItemAsync(item);
                    }
                }

                // تحديث مبالغ الفاتورة
                await _invoiceService.UpdateInvoiceAmountsAsync(Invoice.Id);

                MessageBox.Show(
                    IsEditMode ? "تم تحديث الفاتورة بنجاح" : "تم إنشاء الفاتورة بنجاح",
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // إغلاق النافذة
                CloseWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void Cancel()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع البيانات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                CloseWindow();
            }
        }

        private Task<bool> ValidateDataAsync()
        {
            if (SelectedPatient == null)
            {
                MessageBox.Show("يرجى اختيار المريض", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Invoice.InvoiceDate == default)
            {
                MessageBox.Show("يرجى تحديد تاريخ الفاتورة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (!InvoiceItems.Any())
            {
                MessageBox.Show("يرجى إضافة عنصر واحد على الأقل للفاتورة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            foreach (var item in InvoiceItems)
            {
                if (string.IsNullOrWhiteSpace(item.ItemDescription))
                {
                    MessageBox.Show("يرجى تحديد وصف لجميع العناصر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return Task.FromResult(false);
                }
                if (item.Quantity <= 0)
                {
                    MessageBox.Show("يجب أن تكون الكمية أكبر من صفر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return Task.FromResult(false);
                }
                if (item.UnitPrice < 0)
                {
                    MessageBox.Show("يجب أن يكون سعر الوحدة موجباً", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return Task.FromResult(false);
                }
                item.TotalPrice = item.Quantity * item.UnitPrice;
            }
            if (Invoice.DiscountAmount < 0)
            {
                MessageBox.Show("يجب أن يكون مبلغ الخصم موجباً", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            if (Invoice.TaxAmount < 0)
            {
                MessageBox.Show("يجب أن يكون مبلغ الضريبة موجباً", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return Task.FromResult(false);
            }
            return Task.FromResult(true);
        }

        private void CloseWindow()
        {
            // إغلاق النافذة الحالية
            if (System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().Any(w => w.IsActive))
            {
                System.Windows.Application.Current.Windows.OfType<System.Windows.Window>().First(w => w.IsActive).Close();
            }
        }

        private void OpenAddNewPatientWindow()
        {
            try
            {
                var addPatientWindow = new Views.Patients.ModernAddPatientWindow();
                var mainWindow = System.Windows.Application.Current.MainWindow;
                if (mainWindow != null && mainWindow != addPatientWindow)
                {
                    addPatientWindow.Owner = mainWindow;
                    addPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;
                }
                else
                {
                    addPatientWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
                }
                var result = addPatientWindow.ShowDialog();
                if (result == true)
                {
                    // تحديث قائمة المرضى بعد الإضافة
                    _ = LoadPatientsAsync();
                    System.Windows.MessageBox.Show("تم إضافة المريض بنجاح", "نجح", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة المريض: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void LoadAvailableServices()
        {
            AvailableServices.Clear();
            var services = _serviceService.GetAllServices();
            foreach (var service in services)
                AvailableServices.Add(service);
        }

        private void AddServiceToInvoice()
        {
            if (SelectedService == null) return;
            var item = new InvoiceItem
            {
                ItemDescription = SelectedService.Name,
                Quantity = 1,
                UnitPrice = SelectedService.Price,
                TotalPrice = SelectedService.Price
            };
            InvoiceItems.Add(item);
            CalculateTotals();
            SelectedService = null;
        }

        private void PrintInvoice()
        {
            System.Windows.MessageBox.Show("تم إرسال الفاتورة للطباعة (تجريبي).","طباعة",System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        private void PreviewInvoice()
        {
            System.Windows.MessageBox.Show("معاينة الفاتورة قبل الطباعة (تجريبي).","معاينة",System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        #endregion
    }
} 