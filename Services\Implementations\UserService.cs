using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace AqlanCenterProApp.Services.Implementations
{
    public class UserService : IUserService
    {
        private readonly AqlanCenterDbContext _context;

        public UserService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Include(u => u.Role)
                .Where(u => !u.IsDeleted)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<User?> GetUserByIdAsync(int id)
        {
            return await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted);
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            user.PasswordHash = HashPassword(password);
            user.CreatedAt = DateTime.Now;
            user.LastPasswordChangeDate = DateTime.Now;
            user.FailedLoginAttempts = 0;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();
            return user;
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null)
                throw new ArgumentException("User not found");

            existingUser.FullName = user.FullName;
            existingUser.Email = user.Email;
            existingUser.Phone = user.Phone;
            existingUser.RoleId = user.RoleId;
            existingUser.IsActive = user.IsActive;
            existingUser.Language = user.Language;
            existingUser.Theme = user.Theme;
            existingUser.UserImage = user.UserImage;
            existingUser.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return existingUser;
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null) return false;

            user.IsDeleted = true;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ActivateUserAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null) return false;

            user.IsActive = true;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeactivateUserAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null) return false;

            user.IsActive = false;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            if (!VerifyPassword(currentPassword, user.PasswordHash))
                return false;

            user.PasswordHash = HashPassword(newPassword);
            user.LastPasswordChangeDate = DateTime.Now;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            user.PasswordHash = HashPassword(newPassword);
            user.LastPasswordChangeDate = DateTime.Now;
            user.UpdatedAt = DateTime.Now;
            user.FailedLoginAttempts = 0;
            user.LockoutEndDate = null;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> LockUserAsync(int userId, DateTime lockoutEndDate)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            user.LockoutEndDate = lockoutEndDate;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UnlockUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null) return false;

            user.LockoutEndDate = null;
            user.FailedLoginAttempts = 0;
            user.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ValidateCredentialsAsync(string username, string password)
        {
            var user = await GetUserByUsernameAsync(username);
            if (user == null || !user.IsActive) return false;

            if (user.LockoutEndDate.HasValue && user.LockoutEndDate.Value > DateTime.Now)
                return false;

            if (VerifyPassword(password, user.PasswordHash))
            {
                await ResetFailedLoginAttemptsAsync(user.Id);
                return true;
            }

            await IncrementFailedLoginAttemptsAsync(user.Id);
            return false;
        }

        public async Task<bool> ValidatePasswordAsync(int userId, string password)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            return VerifyPassword(password, user.PasswordHash);
        }

        public async Task UpdateLastLoginAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.LastLoginDate = DateTime.Now;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            return await _context.Users
                .Include(u => u.Role)
                .Where(u => !u.IsDeleted && 
                           (u.FullName.Contains(searchTerm) || 
                            u.Username.Contains(searchTerm) || 
                            u.Email.Contains(searchTerm)))
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetUsersByRoleAsync(int roleId)
        {
            return await _context.Users
                .Include(u => u.Role)
                .Where(u => !u.IsDeleted && u.RoleId == roleId)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<bool> IsUsernameUniqueAsync(string username, int? excludeUserId = null)
        {
            return !await _context.Users
                .AnyAsync(u => u.Username == username && 
                              !u.IsDeleted && 
                              (!excludeUserId.HasValue || u.Id != excludeUserId.Value));
        }

        public async Task<bool> IsEmailUniqueAsync(string email, int? excludeUserId = null)
        {
            return !await _context.Users
                .AnyAsync(u => u.Email == email && 
                              !u.IsDeleted && 
                              (!excludeUserId.HasValue || u.Id != excludeUserId.Value));
        }

        public async Task<int> GetFailedLoginAttemptsAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            return user?.FailedLoginAttempts ?? 0;
        }

        public async Task IncrementFailedLoginAttemptsAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.FailedLoginAttempts++;
                await _context.SaveChangesAsync();
            }
        }

        public async Task ResetFailedLoginAttemptsAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.FailedLoginAttempts = 0;
                await _context.SaveChangesAsync();
            }
        }

        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }
    }
} 