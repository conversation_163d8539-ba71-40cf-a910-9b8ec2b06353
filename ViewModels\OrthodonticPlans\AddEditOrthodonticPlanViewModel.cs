using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Services;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.OrthodonticPlans
{
    /// <summary>
    /// ViewModel لإضافة وتعديل خطط علاج التقويم
    /// </summary>
    public class AddEditOrthodonticPlanViewModel : BaseViewModel
    {
        private readonly IOrthodonticPlanService _orthodonticPlanService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;
        private readonly bool _isEditMode;

        public AddEditOrthodonticPlanViewModel(
            IOrthodonticPlanService orthodonticPlanService,
            IPatientService patientService,
            IDoctorService doctorService,
            OrthodonticPlan? plan = null)
        {
            _orthodonticPlanService = orthodonticPlanService;
            _patientService = patientService;
            _doctorService = doctorService;
            _isEditMode = plan != null;

            // تحميل القوائم
            _ = LoadPatientsAsync();
            _ = LoadDoctorsAsync();

            if (_isEditMode && plan != null)
            {
                LoadPlanData(plan);
            }
            else
            {
                InitializeDefaultValues();
            }

            // تهيئة الأوامر
            SaveCommand = new RelayCommand(async _ => await SaveAsync());
            CancelCommand = new RelayCommand(_ => Cancel());
            ValidateCommand = new RelayCommand(async _ => await ValidateAsync());
        }

        #region Properties

        public int PlanId { get; set; }

        private ObservableCollection<Patient> _patients = new();
        public ObservableCollection<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }

        private ObservableCollection<Doctor> _doctors = new();
        public ObservableCollection<Doctor> Doctors
        {
            get => _doctors;
            set => SetProperty(ref _doctors, value);
        }

        private Patient? _selectedPatient;
        [Required(ErrorMessage = "يجب اختيار المريض")]
        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set => SetProperty(ref _selectedPatient, value);
        }

        private Doctor? _selectedDoctor;
        [Required(ErrorMessage = "يجب اختيار الطبيب")]
        public Doctor? SelectedDoctor
        {
            get => _selectedDoctor;
            set => SetProperty(ref _selectedDoctor, value);
        }

        private DateTime _startDate = DateTime.Today;
        [Required(ErrorMessage = "تاريخ البداية مطلوب")]
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value, OnDateChanged);
        }

        private DateTime _expectedEndDate = DateTime.Today.AddYears(1);
        [Required(ErrorMessage = "تاريخ النهاية المتوقع مطلوب")]
        public DateTime ExpectedEndDate
        {
            get => _expectedEndDate;
            set => SetProperty(ref _expectedEndDate, value, OnDateChanged);
        }

        private int _totalSessions = 20;
        [Required(ErrorMessage = "عدد الجلسات مطلوب")]
        [Range(1, 100, ErrorMessage = "عدد الجلسات يجب أن يكون بين 1 و 100")]
        public int TotalSessions
        {
            get => _totalSessions;
            set => SetProperty(ref _totalSessions, value, OnSessionsChanged);
        }

        private int _sessionIntervalDays = 21;
        [Required(ErrorMessage = "الفترة بين الجلسات مطلوبة")]
        [Range(1, 365, ErrorMessage = "الفترة بين الجلسات يجب أن تكون بين 1 و 365 يوم")]
        public int SessionIntervalDays
        {
            get => _sessionIntervalDays;
            set => SetProperty(ref _sessionIntervalDays, value, OnIntervalChanged);
        }

        private int _sessionDurationMinutes = 30;
        [Required(ErrorMessage = "مدة الجلسة مطلوبة")]
        [Range(15, 180, ErrorMessage = "مدة الجلسة يجب أن تكون بين 15 و 180 دقيقة")]
        public int SessionDurationMinutes
        {
            get => _sessionDurationMinutes;
            set => SetProperty(ref _sessionDurationMinutes, value);
        }

        private string _treatmentType = string.Empty;
        [Required(ErrorMessage = "نوع العلاج مطلوب")]
        [MaxLength(100, ErrorMessage = "نوع العلاج لا يمكن أن يتجاوز 100 حرف")]
        public string TreatmentType
        {
            get => _treatmentType;
            set => SetProperty(ref _treatmentType, value);
        }

        private string? _treatmentDescription;
        [MaxLength(1000, ErrorMessage = "وصف العلاج لا يمكن أن يتجاوز 1000 حرف")]
        public string? TreatmentDescription
        {
            get => _treatmentDescription;
            set => SetProperty(ref _treatmentDescription, value);
        }

        private string? _notes;
        [MaxLength(2000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 2000 حرف")]
        public string? Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        private string _status = "نشطة";
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        // خصائص حسابية
        public string EstimatedDuration => CalculateEstimatedDuration();
        public string SessionFrequency => CalculateSessionFrequency();
        public bool IsValidPlan => ValidatePlanData();

        // قوائم الخيارات
        public ObservableCollection<string> TreatmentTypes { get; } = new()
        {
            "تقويم أسنان علوي",
            "تقويم أسنان سفلي",
            "تقويم أسنان كامل",
            "تقويم أسنان تجميلي",
            "تقويم أسنان وظيفي",
            "تقويم أسنان جراحي",
            "تقويم أسنان للكبار",
            "تقويم أسنان للأطفال"
        };

        public ObservableCollection<string> StatusOptions { get; } = new()
        {
            "نشطة",
            "مكتملة",
            "متأخرة",
            "ملغية"
        };

        public ObservableCollection<int> SessionIntervalOptions { get; } = new()
        {
            7,   // أسبوع
            14,  // أسبوعين
            21,  // 3 أسابيع
            28,  // 4 أسابيع
            35,  // 5 أسابيع
            42   // 6 أسابيع
        };

        public ObservableCollection<int> SessionDurationOptions { get; } = new()
        {
            15,  // 15 دقيقة
            30,  // 30 دقيقة
            45,  // 45 دقيقة
            60,  // ساعة
            90,  // ساعة ونصف
            120  // ساعتين
        };

        #endregion

        #region Commands

        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ValidateCommand { get; }

        #endregion

        #region Events

        public event Action? RequestClose;
        public event Action<string>? ShowMessage;

        #endregion

        #region Public Methods

        /// <summary>
        /// حفظ الخطة
        /// </summary>
        public async Task SaveAsync()
        {
            if (!ValidatePlanData())
            {
                MessageBox.Show("يرجى تصحيح الأخطاء في البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            await ExecuteAsync(async () =>
            {
                var plan = new OrthodonticPlan
                {
                    Id = PlanId,
                    PatientId = SelectedPatient!.Id,
                    DoctorId = SelectedDoctor!.Id,
                    StartDate = StartDate,
                    ExpectedEndDate = ExpectedEndDate,
                    TotalSessions = TotalSessions,
                    SessionIntervalDays = SessionIntervalDays,
                    SessionDurationMinutes = SessionDurationMinutes,
                    TreatmentType = TreatmentType,
                    TreatmentDescription = TreatmentDescription,
                    Status = Status,
                    Notes = Notes
                };

                try
                {
                    if (_isEditMode)
                    {
                        await _orthodonticPlanService.UpdatePlanAsync(plan);
                        MessageBox.Show("تم تحديث خطة التقويم بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        await _orthodonticPlanService.CreatePlanAsync(plan);
                        MessageBox.Show("تم إنشاء خطة التقويم بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }

                    RequestClose?.Invoke();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الخطة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }, "جاري حفظ الخطة...");
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        public void Cancel()
        {
            RequestClose?.Invoke();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public async Task ValidateAsync()
        {
            var plan = new OrthodonticPlan
            {
                PatientId = SelectedPatient?.Id ?? 0,
                DoctorId = SelectedDoctor?.Id ?? 0,
                StartDate = StartDate,
                ExpectedEndDate = ExpectedEndDate,
                TotalSessions = TotalSessions,
                SessionIntervalDays = SessionIntervalDays,
                SessionDurationMinutes = SessionDurationMinutes,
                TreatmentType = TreatmentType
            };

            await ExecuteAsync(async () =>
            {
                var (isValid, errorMessage) = await _orthodonticPlanService.ValidatePlanAsync(plan);
                if (isValid)
                {
                    MessageBox.Show("البيانات صحيحة", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"خطأ في البيانات: {errorMessage}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }, "جاري التحقق من البيانات...");
        }

        #endregion

        #region Private Methods

        private void LoadPlanData(OrthodonticPlan plan)
        {
            PlanId = plan.Id;
            SelectedPatient = plan.Patient;
            SelectedDoctor = plan.Doctor;
            StartDate = plan.StartDate;
            ExpectedEndDate = plan.ExpectedEndDate;
            TotalSessions = plan.TotalSessions;
            SessionIntervalDays = plan.SessionIntervalDays;
            SessionDurationMinutes = plan.SessionDurationMinutes;
            TreatmentType = plan.TreatmentType;
            TreatmentDescription = plan.TreatmentDescription;
            Status = plan.Status;
            Notes = plan.Notes;
        }

        private void InitializeDefaultValues()
        {
            StartDate = DateTime.Today;
            ExpectedEndDate = DateTime.Today.AddYears(1);
            TotalSessions = 20;
            SessionIntervalDays = 21;
            SessionDurationMinutes = 30;
            TreatmentType = "تقويم أسنان كامل";
            Status = "نشطة";
        }

        private async Task LoadPatientsAsync()
        {
            try
            {
                var (patients, _, _) = await _patientService.GetPatientsAsync(pageSize: 1000);
                Patients.Clear();
                foreach (var patient in patients)
                {
                    Patients.Add(patient);
                }
            }
            catch (Exception ex)
            {
                ShowMessage?.Invoke($"خطأ في تحميل المرضى: {ex.Message}");
            }
        }

        private async Task LoadDoctorsAsync()
        {
            try
            {
                var doctors = await _doctorService.GetAllDoctorsAsync();
                Doctors.Clear();
                foreach (var doctor in doctors)
                {
                    Doctors.Add(doctor);
                }
            }
            catch (Exception ex)
            {
                ShowMessage?.Invoke($"خطأ في تحميل الأطباء: {ex.Message}");
            }
        }

        private void OnDateChanged()
        {
            OnPropertyChanged(nameof(EstimatedDuration));
            OnPropertyChanged(nameof(IsValidPlan));
        }

        private void OnSessionsChanged()
        {
            OnPropertyChanged(nameof(EstimatedDuration));
            OnPropertyChanged(nameof(IsValidPlan));
        }

        private void OnIntervalChanged()
        {
            OnPropertyChanged(nameof(SessionFrequency));
            OnPropertyChanged(nameof(EstimatedDuration));
            OnPropertyChanged(nameof(IsValidPlan));
        }

        private string CalculateEstimatedDuration()
        {
            if (TotalSessions <= 0 || SessionIntervalDays <= 0)
                return "غير محدد";

            var totalDays = (TotalSessions - 1) * SessionIntervalDays;
            var duration = StartDate.AddDays(totalDays);

            if (duration <= StartDate)
                return "غير محدد";

            var months = ((duration.Year - StartDate.Year) * 12) + duration.Month - StartDate.Month;
            var years = months / 12;
            months = months % 12;

            if (years > 0)
                return $"{years} سنة و {months} شهر";
            else
                return $"{months} شهر";
        }

        private string CalculateSessionFrequency()
        {
            return SessionIntervalDays switch
            {
                7 => "أسبوعياً",
                14 => "كل أسبوعين",
                21 => "كل 3 أسابيع",
                28 => "شهرياً",
                35 => "كل 5 أسابيع",
                42 => "كل 6 أسابيع",
                _ => $"كل {SessionIntervalDays} يوم"
            };
        }

        private bool ValidatePlanData()
        {
            if (SelectedPatient == null || SelectedDoctor == null)
                return false;

            if (StartDate < DateTime.Today)
                return false;

            if (ExpectedEndDate <= StartDate)
                return false;

            if (TotalSessions <= 0 || TotalSessions > 100)
                return false;

            if (SessionIntervalDays <= 0 || SessionIntervalDays > 365)
                return false;

            if (SessionDurationMinutes < 15 || SessionDurationMinutes > 180)
                return false;

            if (string.IsNullOrWhiteSpace(TreatmentType))
                return false;

            return true;
        }

        #endregion
    }
} 