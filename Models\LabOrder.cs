using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class LabOrder : BaseEntity
    {
        [Key]
        public int LabOrderId { get; set; }

        [Required(ErrorMessage = "رقم الطلب مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الطلب لا يمكن أن يتجاوز 20 حرف")]
        public string OrderNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "المريض مطلوب")]
        public int PatientId { get; set; }

        [Required(ErrorMessage = "الطبيب مطلوب")]
        public int DoctorId { get; set; }

        public int? AppointmentId { get; set; }

        [Required(ErrorMessage = "المعمل مطلوب")]
        public int LabId { get; set; }

        public int? ProsthesisTypeId { get; set; }

        [Required(ErrorMessage = "نوع العمل مطلوب")]
        [StringLength(50, ErrorMessage = "نوع العمل لا يمكن أن يتجاوز 50 حرف")]
        public string WorkType { get; set; } = string.Empty;

        [Required(ErrorMessage = "عدد القطع مطلوب")]
        [Range(1, 100, ErrorMessage = "عدد القطع يجب أن يكون بين 1 و 100")]
        public int PiecesCount { get; set; }

        public int? ShadeId { get; set; }

        [Required(ErrorMessage = "تاريخ الإرسال مطلوب")]
        public DateTime SendDate { get; set; } = DateTime.Now;

        public DateTime? ExpectedReturnDate { get; set; }

        public DateTime? ActualReturnDate { get; set; }

        public DateTime? TrialDate { get; set; }

        public DateTime? FinalInstallDate { get; set; }

        [Required(ErrorMessage = "حالة الطلب مطلوبة")]
        [StringLength(20, ErrorMessage = "حالة الطلب لا يمكن أن تتجاوز 20 حرف")]
        public string Status { get; set; } = "منشأ";

        [StringLength(1000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 1000 حرف")]
        public new string? Notes { get; set; }

        [StringLength(500, ErrorMessage = "مسار المرفق لا يمكن أن يتجاوز 500 حرف")]
        public string? AttachmentPath { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }

        [StringLength(20, ErrorMessage = "رقم الفاتورة لا يمكن أن يتجاوز 20 حرف")]
        public string? InvoiceNumber { get; set; }

        public bool IsPaid { get; set; } = false;

        public DateTime? PaymentDate { get; set; }

        [Range(1, 5, ErrorMessage = "التقييم يجب أن يكون بين 1 و 5")]
        public int? Rating { get; set; }

        [StringLength(500, ErrorMessage = "ملاحظات التقييم لا يمكن أن تتجاوز 500 حرف")]
        public string? RatingNotes { get; set; }

        // Navigation Properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        [ForeignKey("AppointmentId")]
        public virtual Appointment? Appointment { get; set; }

        [ForeignKey("LabId")]
        public virtual Lab Lab { get; set; } = null!;

        [ForeignKey("ProsthesisTypeId")]
        public virtual ProsthesisType? ProsthesisType { get; set; }

        [ForeignKey("ShadeId")]
        public virtual Shade? Shade { get; set; }

        // Computed Properties
        [NotMapped]
        public bool IsOverdue => Status == "قيد التنفيذ" && ExpectedReturnDate.HasValue && ExpectedReturnDate.Value < DateTime.Now;

        [NotMapped]
        public bool IsReady => Status == "جاهز";

        [NotMapped]
        public bool IsCompleted => Status == "تم التسليم";

        [NotMapped]
        public int DaysOverdue => IsOverdue ? (int)(DateTime.Now - ExpectedReturnDate!.Value).TotalDays : 0;
    }
}
