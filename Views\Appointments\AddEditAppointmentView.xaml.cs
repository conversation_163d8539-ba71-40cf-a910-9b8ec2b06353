using System.Windows;
using AqlanCenterProApp.ViewModels.Appointments;

namespace AqlanCenterProApp.Views.Appointments
{
    /// <summary>
    /// Interaction logic for AddEditAppointmentView.xaml
    /// </summary>
    public partial class AddEditAppointmentView : Window
    {
        public AddEditAppointmentView()
        {
            InitializeComponent();
        }

        public AddEditAppointmentView(AddEditAppointmentViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;

            // ربط الأحداث
            viewModel.RequestClose += () => Close();
            viewModel.ShowMessage += (message) => MessageBox.Show(message, "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
} 