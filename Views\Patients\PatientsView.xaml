<UserControl x:Class="AqlanCenterProApp.Views.Patients.PatientsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:AqlanCenterProApp.Converters"
             mc:Ignorable="d"
             d:DesignWidth="1000"
             d:DesignHeight="700"
             Background="#F5F6FA"
             FlowDirection="RightToLeft">

        <UserControl.Resources>
                <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        </UserControl.Resources>

        <!-- ScrollViewer رئيسي للصفحة كاملة -->
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly"
                      ScrollViewer.CanContentScroll="False"
                      ScrollViewer.IsDeferredScrollingEnabled="False"
                      Focusable="False">
                <!-- الهيدر والأزرار -->
                <StackPanel Margin="20"
                            Orientation="Vertical"
                            FlowDirection="RightToLeft">
                        <!-- عنوان الوحدة -->
                        <TextBlock Text="إدارة المرضى"
                                   FontSize="36"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,18"/>

                        <!-- شريط الأزرار الرئيسي (من اليمين لليسار) -->
                        <WrapPanel HorizontalAlignment="Stretch"
                                   Margin="0,0,0,14"
                                   ItemWidth="135"
                                   ItemHeight="40"
                                   FlowDirection="RightToLeft">
                                <Button Content="➕ إضافة مريض"
                                        Command="{Binding AddPatientCommand}"
                                        Background="#47BE3E"
                                        Foreground="White"
                                        Margin="5"/>
                                <Button Content="✏️ تعديل"
                                        Command="{Binding EditPatientCommand}"
                                        Background="#FFC107"
                                        Foreground="Black"
                                        Margin="5"/>
                                <Button Content="🗑️ حذف"
                                        Command="{Binding DeletePatientCommand}"
                                        Background="#F44336"
                                        Foreground="White"
                                        Margin="5"/>
                                <Button Content="🔄 تحديث"
                                        Command="{Binding RefreshCommand}"
                                        Margin="5"/>
                                <Button Content="📤 تصدير"
                                        Command="{Binding ExportCommand}"
                                        Margin="5"/>
                                <Button Content="📄 نسخ"
                                        Command="{Binding CopyCommand}"
                                        Margin="5"/>
                                <Button Content="🖨️ طباعة"
                                        Command="{Binding PrintCommand}"
                                        Margin="5"/>
                                <Button Content="🧾 سند معاينة"
                                        Command="{Binding PrintConsultationReceiptCommand}"
                                        Background="#17A2B8"
                                        Foreground="White"
                                        Margin="5"/>
                                <Button Content="📑 كشف حساب"
                                        Command="{Binding ShowAccountStatementCommand}"
                                        Background="#007BFF"
                                        Foreground="White"
                                        Margin="5"/>
                                <Button Content="📊 إحصائيات"
                                        Command="{Binding ShowStatisticsCommand}"
                                        Margin="5"/>
                                <Button Content="🔍 بحث متقدم"
                                        Command="{Binding AdvancedSearchCommand}"
                                        Background="#9B59B6"
                                        Foreground="White"
                                        Margin="5"/>
                        </WrapPanel>

                        <!-- كروت الإحصائيات مرتبة من اليمين -->
                        <UniformGrid Columns="4"
                                     Margin="0,0,0,22"
                                     HorizontalAlignment="Center"
                                     FlowDirection="RightToLeft">
                                <Border Background="#E3F0FF"
                                        CornerRadius="10"
                                        Margin="8"
                                        Padding="15">
                                        <StackPanel>
                                                <TextBlock Text="إجمالي المرضى"
                                                           FontWeight="Bold"
                                                           FontSize="14"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding Statistics.TotalPatients}"
                                                           FontSize="30"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                        </StackPanel>
                                </Border>
                                <Border Background="#E3FFD3"
                                        CornerRadius="10"
                                        Margin="8"
                                        Padding="15">
                                        <StackPanel>
                                                <TextBlock Text="المرضى النشطين"
                                                           FontWeight="Bold"
                                                           FontSize="14"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding Statistics.ActivePatients}"
                                                           FontSize="30"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                        </StackPanel>
                                </Border>
                                <Border Background="#FFF9D3"
                                        CornerRadius="10"
                                        Margin="8"
                                        Padding="15">
                                        <StackPanel>
                                                <TextBlock Text="الجدد هذا الشهر"
                                                           FontWeight="Bold"
                                                           FontSize="14"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding Statistics.NewPatientsThisMonth}"
                                                           FontSize="30"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                        </StackPanel>
                                </Border>
                                <Border Background="#FFD7D7"
                                        CornerRadius="10"
                                        Margin="8"
                                        Padding="15">
                                        <StackPanel>
                                                <TextBlock Text="المدينين"
                                                           FontWeight="Bold"
                                                           FontSize="14"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding Statistics.DebtorPatients}"
                                                           FontSize="30"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                        </StackPanel>
                                </Border>
                        </UniformGrid>

                        <!-- مربع البحث -->
                        <DockPanel Margin="0,0,0,10"
                                   LastChildFill="True"
                                   FlowDirection="RightToLeft">
                                <TextBox x:Name="SearchBox"
                                         Width="320"
                                         Height="32"
                                         VerticalAlignment="Center"
                                         Margin="5"
                                         FontSize="16"
                                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                                <Button Content="🔍"
                                        Width="40"
                                        Height="34"
                                        Margin="4,0,0,0"
                                        Command="{Binding SearchCommand}"/>
                        </DockPanel>

                        <!-- جدول المرضى -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Auto"
                                      Height="300"
                                      CanContentScroll="True"
                                      PanningMode="Both"
                                      ScrollViewer.IsDeferredScrollingEnabled="False">
                                <DataGrid x:Name="PatientsDataGrid"
                                          ItemsSource="{Binding Patients}"
                                          SelectedItem="{Binding SelectedPatient}"
                                          AutoGenerateColumns="False"
                                          IsReadOnly="True"
                                          HeadersVisibility="Column"
                                          Margin="0,0,0,8"
                                          SelectionMode="Single"
                                          CanUserAddRows="False"
                                          RowHeight="34"
                                          EnableRowVirtualization="True"
                                          EnableColumnVirtualization="True"
                                          VirtualizingPanel.IsVirtualizing="True"
                                          VirtualizingPanel.VirtualizationMode="Recycling">

                                        <!-- قائمة السياق للمريض -->
                                        <DataGrid.ContextMenu>
                                                <ContextMenu>
                                                        <MenuItem Header="📝 تعديل المريض"
                                                                  Command="{Binding EditPatientCommand}"
                                                                  Icon="✏️"/>
                                                        <MenuItem Header="➕ إضافة جلسة"
                                                                  Command="{Binding AddSessionCommand}"
                                                                  Icon="🩺"/>
                                                        <Separator/>
                                                        <MenuItem Header="💰 عرض المدفوعات"
                                                                  Command="{Binding ViewPaymentsCommand}"
                                                                  CommandParameter="{Binding SelectedPatient}"
                                                                  Icon="💳"/>
                                                        <MenuItem Header="📅 عرض المواعيد"
                                                                  Command="{Binding ViewAppointmentsCommand}"
                                                                  CommandParameter="{Binding SelectedPatient}"
                                                                  Icon="📆"/>
                                                        <MenuItem Header="📋 السجلات الطبية"
                                                                  Command="{Binding ViewMedicalRecordsCommand}"
                                                                  Icon="🏥"/>
                                                        <MenuItem Header="💊 الوصفات الطبية"
                                                                  Command="{Binding ViewPrescriptionsCommand}"
                                                                  Icon="💊"/>
                                                        <MenuItem Header="🔬 نتائج الفحوصات"
                                                                  Command="{Binding ViewTestResultsCommand}"
                                                                  Icon="🔬"/>
                                                        <MenuItem Header="📸 الصور الطبية"
                                                                  Command="{Binding ViewMedicalImagesCommand}"
                                                                  Icon="📸"/>
                                                        <Separator/>
                                                        <MenuItem Header="📱 إرسال واتساب"
                                                                  Command="{Binding SendWhatsAppCommand}"
                                                                  CommandParameter="{Binding SelectedPatient}"
                                                                  Icon="💬"/>
                                                        <Separator/>
                                                        <MenuItem Header="🖨️ طباعة سند المعاينة"
                                                                  Command="{Binding PrintConsultationReceiptCommand}"
                                                                  CommandParameter="{Binding SelectedPatient}"
                                                                  Icon="🧾"/>
                                                        <MenuItem Header="📄 طباعة بيانات المريض"
                                                                  Command="{Binding PrintPatientCommand}"
                                                                  CommandParameter="{Binding SelectedPatient}"
                                                                  Icon="🖨️"/>
                                                        <Separator/>
                                                        <MenuItem Header="🗑️ حذف المريض"
                                                                  Command="{Binding DeletePatientCommand}"
                                                                  Icon="❌"/>
                                                </ContextMenu>
                                        </DataGrid.ContextMenu>
                                        <DataGrid.Columns>
                                                <DataGridTextColumn Header="رقم الملف"
                                                                    Width="80"
                                                                    Binding="{Binding FileNumber}"/>
                                                <DataGridTextColumn Header="اسم المريض"
                                                                    Width="170"
                                                                    Binding="{Binding FullName}"/>
                                                <DataGridTextColumn Header="الجنس"
                                                                    Width="70"
                                                                    Binding="{Binding Gender}"/>
                                                <DataGridTextColumn Header="الهاتف"
                                                                    Width="120"
                                                                    Binding="{Binding Phone}"/>
                                                <DataGridTextColumn Header="التصنيف"
                                                                    Width="90"
                                                                    Binding="{Binding PatientCategory}"/>
                                                <DataGridTextColumn Header="تاريخ التسجيل"
                                                                    Width="120"
                                                                    Binding="{Binding RegistrationDate, StringFormat=yyyy/MM/dd}"/>
                                                <DataGridTextColumn Header="الرصيد الحالي"
                                                                    Width="100"
                                                                    Binding="{Binding CurrentBalance, StringFormat=N2}"/>
                                                <DataGridTextColumn Header="الحالة"
                                                                    Width="90"
                                                                    Binding="{Binding FileStatus}"/>
                                        </DataGrid.Columns>
                                </DataGrid>
                        </ScrollViewer>

                        <!-- الجلسات للمريض المحدد -->
                        <StackPanel x:Name="SessionsPanel"
                                    Orientation="Vertical"
                                    Margin="0,10,0,0"
                                    Visibility="{Binding SelectedPatient, Converter={StaticResource NullToVisibilityConverter}}">
                                <TextBlock Text="الجلسات العلاجية للمريض المحدد"
                                           FontWeight="Bold"
                                           FontSize="18"
                                           Margin="0,0,0,8"/>
                                <Button Content="➕ إضافة جلسة جديدة"
                                        Width="180"
                                        Height="36"
                                        Margin="0,0,0,10"
                                        Command="{Binding AddSessionCommand}"
                                        HorizontalAlignment="Right"/>
                                <ScrollViewer VerticalScrollBarVisibility="Auto"
                                              HorizontalScrollBarVisibility="Auto"
                                              Height="180"
                                              CanContentScroll="True">
                                        <DataGrid x:Name="SessionsDataGrid"
                                                  AutoGenerateColumns="False"
                                                  IsReadOnly="True"
                                                  HeadersVisibility="Column"
                                                  Margin="0,0,0,8"
                                                  CanUserAddRows="False"
                                                  RowHeight="32"
                                                  EnableRowVirtualization="True"
                                                  VirtualizingPanel.IsVirtualizing="True">
                                                <DataGrid.Columns>
                                                        <DataGridTextColumn Header="التاريخ"
                                                                            Width="120"
                                                                            Binding="{Binding SessionDate, StringFormat=yyyy/MM/dd}"/>
                                                        <DataGridTextColumn Header="نوع العلاج"
                                                                            Width="150"
                                                                            Binding="{Binding TreatmentType}"/>
                                                        <DataGridTextColumn Header="المبلغ"
                                                                            Width="100"
                                                                            Binding="{Binding Amount}"/>
                                                        <DataGridTextColumn Header="ملاحظات"
                                                                            Width="200"
                                                                            Binding="{Binding Notes}"/>
                                                        <DataGridTextColumn Header="حالة الجلسة"
                                                                            Width="100"
                                                                            Binding="{Binding SessionStatus}"/>
                                                        <DataGridTextColumn Header="حالة الدفع"
                                                                            Width="100"
                                                                            Binding="{Binding PaymentStatus}"/>
                                                </DataGrid.Columns>
                                        </DataGrid>
                                </ScrollViewer>
                        </StackPanel>
                </StackPanel>
        </ScrollViewer>
</UserControl>
