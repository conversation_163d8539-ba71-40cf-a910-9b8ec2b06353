using Microsoft.Extensions.DependencyInjection;
using AqlanCenterProApp.ViewModels.Invoices;
using AqlanCenterProApp.Views.Invoices;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Appointments;
using AqlanCenterProApp.Views.Appointments;
using System;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using AqlanCenterProApp.Models.Dashboard;
using AqlanCenterProApp.Services.Interfaces;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System.Collections.Concurrent;
using System.Windows;
using System.Linq;

namespace AqlanCenterProApp.ViewModels.Dashboard
{
    /// <summary>
    /// نموذج عرض الداشبورد المحسن بشكل شامل
    /// </summary>
    public partial class DashboardViewModel : ObservableObject, IDisposable
    {
        private readonly IDashboardDataService _dashboardDataService;
        private readonly IChartService _chartService;
        private readonly ILogger<DashboardViewModel> _logger;
        private readonly DispatcherTimer _refreshTimer;
        private readonly SemaphoreSlim _refreshSemaphore = new SemaphoreSlim(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private readonly ConcurrentDictionary<string, object> _localCache = new ConcurrentDictionary<string, object>();
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(3);
        private bool _disposed = false;
        private bool _isInitialized = false;

        // البيانات الأساسية
        [ObservableProperty]
        private ObservableCollection<StatisticCardViewModel> _statisticCards = new();

        [ObservableProperty]
        private ObservableCollection<UpcomingAppointment> _upcomingAppointments = new();

        [ObservableProperty]
        private ObservableCollection<SmartAlert> _smartAlerts = new();

        [ObservableProperty]
        private ObservableCollection<TreatmentTypeDistribution> _treatmentDistribution = new();

        [ObservableProperty]
        private ObservableCollection<Models.Dashboard.DoctorStatistics> _doctorStatistics = new();

        [ObservableProperty]
        private PaymentStatistics _paymentStatistics = new();

        [ObservableProperty]
        private InventoryStatistics _inventoryStatistics = new();

        // الإحصائيات المحسنة الجديدة
        [ObservableProperty]
        private EnhancedPatientStatistics _enhancedPatientStatistics = new();

        [ObservableProperty]
        private EnhancedAppointmentStatistics _enhancedAppointmentStatistics = new();

        [ObservableProperty]
        private EnhancedEmployeeStatistics _enhancedEmployeeStatistics = new();

        [ObservableProperty]
        private List<EnhancedDoctorStatistics> _enhancedDoctorStatistics = new();

        [ObservableProperty]
        private EnhancedRevenueStatistics _enhancedRevenueStatistics = new();

        [ObservableProperty]
        private List<EnhancedSmartAlert> _enhancedSmartAlerts = new();

        [ObservableProperty]
        private List<CurrencySettings> _availableCurrencies = new();

        [ObservableProperty]
        private ComprehensiveDashboardSettings _dashboardSettings = new();

        [ObservableProperty]
        private DashboardState _dashboardState = new();

        // إعدادات المستخدم
        [ObservableProperty]
        private string _selectedCurrency = "YER";

        [ObservableProperty]
        private bool _isAutoRefreshEnabled = true;

        [ObservableProperty]
        private DateTime _lastUpdated = DateTime.Now;

        [ObservableProperty]
        private bool _isRefreshing = false;

        // حالة التحميل والأخطاء
        [ObservableProperty]
        private bool _isLoading = false;

        [ObservableProperty]
        private string _errorMessage = string.Empty;

        [ObservableProperty]
        private bool _hasError = false;

        [ObservableProperty]
        private string _loadingMessage = "جاري تحميل البيانات...";

        [ObservableProperty]
        private bool _isEssentialDataLoaded = false;

        [ObservableProperty]
        private bool _isSecondaryDataLoaded = false;

        // المخططات البيانية
        [ObservableProperty]
        private ISeries[] _revenueSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _patientSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _treatmentPieSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _appointmentSeries = Array.Empty<ISeries>();

        // محاور المخططات
        [ObservableProperty]
        private Axis[] _xAxes = Array.Empty<Axis>();

        [ObservableProperty]
        private Axis[] _yAxes = Array.Empty<Axis>();

        // إحصائيات الأداء
        [ObservableProperty]
        private string _performanceStatus = "جيد";

        [ObservableProperty]
        private double _loadTime = 0;

        [ObservableProperty]
        private int _cacheHitCount = 0;

        [ObservableProperty]
        private int _errorCount = 0;

        public DashboardViewModel(IDashboardDataService dashboardDataService, IChartService chartService, IDoctorService doctorService, ILogger<DashboardViewModel> logger)
        {
            _dashboardDataService = dashboardDataService ?? throw new ArgumentNullException(nameof(dashboardDataService));
            _chartService = chartService ?? throw new ArgumentNullException(nameof(chartService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // إعداد مؤقت التحديث التلقائي
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5)
            };
            _refreshTimer.Tick += OnRefreshTimerTick;

            _logger.LogInformation("تم إنشاء DashboardViewModel بنجاح");
        }

        /// <summary>
        /// تهيئة الداشبورد بشكل آمن ومحسن
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_disposed || _isInitialized) return;

            try
            {
                _logger.LogInformation("بدء تهيئة الداشبورد المحسن");

                await SetLoadingStateAsync(true, "جاري تحميل البيانات الأساسية...");

                // تحميل البيانات الأساسية أولاً (الضرورية للعرض الفوري)
                await LoadEssentialDataAsync();

                // تحميل البيانات الثانوية في الخلفية (المخططات والإحصائيات التفصيلية)
                _ = Task.Run(async () => await LoadSecondaryDataAsync(), _cancellationTokenSource.Token);

                // بدء التحديث التلقائي
                if (IsAutoRefreshEnabled)
                {
                    StartAutoRefresh();
                }

                _isInitialized = true;
                _logger.LogInformation("تم تهيئة الداشبورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة الداشبورد");
                await HandleErrorAsync("حدث خطأ في تهيئة الداشبورد", ex);
            }
            finally
            {
                await SetLoadingStateAsync(false);
            }
        }

        /// <summary>
        /// تحميل البيانات الأساسية (الضرورية للعرض الفوري)
        /// </summary>
        private async Task LoadEssentialDataAsync()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("بدء تحميل البيانات الأساسية");

                // استخدام المهام المتوازية لتحسين الأداء
                var statisticsTask = LoadStatisticsWithCacheAsync();
                var appointmentsTask = LoadAppointmentsWithCacheAsync();
                var alertsTask = LoadAlertsWithCacheAsync();

                // انتظار اكتمال جميع المهام الأساسية
                await Task.WhenAll(statisticsTask, appointmentsTask, alertsTask);

                // تحديث البيانات في الواجهة بشكل آمن
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        var statistics = statisticsTask.Result;
                        UpdateStatisticCards(statistics);

                        UpcomingAppointments.Clear();
                        foreach (var appointment in appointmentsTask.Result)
                        {
                            UpcomingAppointments.Add(appointment);
                        }

                        SmartAlerts.Clear();
                        foreach (var alert in alertsTask.Result)
                        {
                            SmartAlerts.Add(alert);
                        }

                        IsEssentialDataLoaded = true;
                        LastUpdated = DateTime.Now;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في تحديث البيانات الأساسية في الواجهة");
                        throw;
                    }
                });

                stopwatch.Stop();
                LoadTime = stopwatch.ElapsedMilliseconds;
                UpdatePerformanceStatus();

                _logger.LogInformation($"تم تحميل البيانات الأساسية بنجاح في {LoadTime} مللي ثانية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الأساسية");
                await HandleErrorAsync("فشل في تحميل البيانات الأساسية", ex);
                throw;
            }
        }

        /// <summary>
        /// تحميل البيانات الثانوية (المخططات والإحصائيات التفصيلية)
        /// </summary>
        private async Task LoadSecondaryDataAsync()
        {
            try
            {
                _logger.LogInformation("بدء تحميل البيانات الثانوية");

                // تحميل المخططات والإحصائيات التفصيلية
                var chartsTask = UpdateChartsAsync();
                var detailedStatsTask = LoadDetailedStatisticsAsync();

                await Task.WhenAll(chartsTask, detailedStatsTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsSecondaryDataLoaded = true;
                });

                _logger.LogInformation("تم تحميل البيانات الثانوية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الثانوية");
                ErrorCount++;
                // لا نرمي الاستثناء هنا لأن البيانات الثانوية ليست ضرورية
            }
        }

        /// <summary>
        /// تحميل الإحصائيات مع الكاش
        /// </summary>
        private async Task<DashboardStatistics> LoadStatisticsWithCacheAsync()
        {
            var cacheKey = "dashboard_statistics";

            if (TryGetFromCache<DashboardStatistics>(cacheKey, out var cachedStats))
            {
                CacheHitCount++;
                return cachedStats;
            }

            // This is a placeholder. In a real scenario, you would get individual values.
            var totalPatients = await _dashboardDataService.GetTotalPatientsAsync();
            var totalAppointments = await _dashboardDataService.GetTotalAppointmentsAsync();
            var totalRevenue = await _dashboardDataService.GetTotalRevenueAsync();
            var pendingOrders = await _dashboardDataService.GetPendingLabOrdersAsync();
            var newPatientsThisMonth = await _dashboardDataService.GetNewPatientsThisMonthAsync();
            var todayRevenue = await _dashboardDataService.GetTodayRevenueAsync();
            var lowStockItems = await _dashboardDataService.GetLowStockItemsAsync();
            var totalDoctors = await _dashboardDataService.GetTotalDoctorsAsync();

            var stats = new DashboardStatistics
            {
                TotalPatients = totalPatients,
                TodayAppointments = totalAppointments,
                ThisMonthRevenue = totalRevenue,
                TotalOutstandingPayments = pendingOrders,
                NewPatientsThisMonth = newPatientsThisMonth,
                TodayRevenue = todayRevenue,
                LowStockItems = lowStockItems,
                TotalDoctors = totalDoctors
            };
            AddToCache(cacheKey, stats);
            return stats;
        }

        /// <summary>
        /// تحميل المواعيد مع الكاش
        /// </summary>
        private async Task<List<UpcomingAppointment>> LoadAppointmentsWithCacheAsync()
        {
            var cacheKey = "upcoming_appointments";

            if (TryGetFromCache<List<UpcomingAppointment>>(cacheKey, out var cachedAppointments))
            {
                CacheHitCount++;
                return cachedAppointments;
            }

            var appointments = new List<UpcomingAppointment>(); // Placeholder
            AddToCache(cacheKey, appointments);
            return appointments;
        }

        /// <summary>
        /// تحميل التنبيهات مع الكاش
        /// </summary>
        private async Task<List<SmartAlert>> LoadAlertsWithCacheAsync()
        {
            var cacheKey = "smart_alerts";

            if (TryGetFromCache<List<SmartAlert>>(cacheKey, out var cachedAlerts))
            {
                CacheHitCount++;
                return cachedAlerts;
            }

            var alerts = new List<SmartAlert>(); // Placeholder
            AddToCache(cacheKey, alerts);
            return alerts;
        }

        /// <summary>
        /// تحميل الإحصائيات التفصيلية
        /// </summary>
        private async Task LoadDetailedStatisticsAsync()
        {
            try
            {
                var paymentStatsTask = Task.FromResult(new PaymentStatistics()); // Placeholder
                var inventoryStatsTask = Task.FromResult(new InventoryStatistics()); // Placeholder
                var doctorStatsTask = Task.FromResult(new List<Models.Dashboard.DoctorStatistics>()); // Placeholder
                var treatmentDistTask = Task.FromResult(new List<TreatmentTypeDistribution>()); // Placeholder

                await Task.WhenAll(paymentStatsTask, inventoryStatsTask, doctorStatsTask, treatmentDistTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    PaymentStatistics = paymentStatsTask.Result;
                    InventoryStatistics = inventoryStatsTask.Result;

                    DoctorStatistics.Clear();
                    foreach (var doctor in doctorStatsTask.Result)
                    {
                        DoctorStatistics.Add(doctor);
                    }

                    TreatmentDistribution.Clear();
                    foreach (var treatment in treatmentDistTask.Result)
                    {
                        TreatmentDistribution.Add(treatment);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الإحصائيات التفصيلية");
            }
        }

        /// <summary>
        /// تحديث المخططات البيانية
        /// </summary>
        private async Task UpdateChartsAsync()
        {
            try
            {
                var revenueDataTask = _chartService.GetRevenueChartDataAsync();
                var appointmentDataTask = _chartService.GetAppointmentsChartDataAsync();
                var patientDataTask = Task.FromResult(new List<ChartData>()); // Placeholder
                var treatmentDataTask = Task.FromResult(new List<TreatmentTypeDistribution>()); // Placeholder

                await Task.WhenAll(revenueDataTask, patientDataTask, appointmentDataTask, treatmentDataTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // تحديث مخطط الإيرادات
                    var revenueData = revenueDataTask.Result;
                    RevenueSeries = new ISeries[]
                    {
                        new LineSeries<decimal>
                        {
                            Values = revenueData.Select(d => (decimal)d.Value),
                            Fill = new SolidColorPaint(SKColors.LightBlue.WithAlpha(50)),
                            Stroke = new SolidColorPaint(SKColors.Blue, 3),
                            GeometrySize = 8,
                            GeometryFill = new SolidColorPaint(SKColors.Blue)
                        }
                    };

                    XAxes = new Axis[]
                    {
                        new Axis
                        {
                            Labels = revenueData.Select(d => d.Label).ToArray(),
                            LabelsRotation = 45
                        }
                    };

                    YAxes = new Axis[]
                    {
                        new Axis
                        {
                            Labeler = value => $"{value:N0} ريال"
                        }
                    };

                    // تحديث مخطط المرضى
                    var patientData = patientDataTask.Result;
                    PatientSeries = new ISeries[]
                    {
                        new ColumnSeries<int>
                        {
                            Values = patientData.Select(d => (int)d.Value).ToArray(),
                            Fill = new SolidColorPaint(SKColors.Green),
                            Stroke = new SolidColorPaint(SKColors.DarkGreen, 2)
                        }
                    };

                    // تحديث مخطط المواعيد
                    var appointmentData = appointmentDataTask.Result;
                    AppointmentSeries = new ISeries[]
                    {
                        new LineSeries<int>
                        {
                            Values = appointmentData.Select(d => (int)d.Value).ToArray(),
                            Fill = new SolidColorPaint(SKColors.Orange.WithAlpha(50)),
                            Stroke = new SolidColorPaint(SKColors.Orange, 3),
                            GeometrySize = 6,
                            GeometryFill = new SolidColorPaint(SKColors.Orange)
                        }
                    };

                    // تحديث مخطط توزيع العلاج
                    var treatmentData = treatmentDataTask.Result;
                    TreatmentPieSeries = treatmentData.Select(t => new PieSeries<decimal>
                    {
                        Values = new[] { (decimal)t.Count },
                        Name = t.TreatmentType,
                        Fill = new SolidColorPaint(GetRandomSKColor())
                    }).ToArray();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المخططات البيانية");
            }
        }

        /// <summary>
        /// الحصول على لون عشوائي للمخططات
        /// </summary>
        private SKColor GetRandomSKColor()
        {
            var colors = new[]
            {
                SKColors.Blue, SKColors.Green, SKColors.Orange, SKColors.Red,
                SKColors.Purple, SKColors.Teal, SKColors.Pink, SKColors.Brown
            };
            return colors[new Random().Next(colors.Length)];
        }

       private void UpdateStatisticCards(DashboardStatistics stats)
       {
            StatisticCards.Clear();
            StatisticCards.Add(new StatisticCardViewModel { Title = "إجمالي المرضى", Value = stats.TotalPatients.ToString(), Icon = "M12,14c-2.67,0-8,1.34-8,4v2h16v-2C20,15.34,14.67,14,12,14z M12,12c2.21,0,4-1.79,4-4s-1.79-4-4-4S8,5.79,8,8S9.79,12,12,12z", BackgroundColor = "#3498DB" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "مواعيد اليوم", Value = stats.TodayAppointments.ToString(), Icon = "M19,4h-1V2h-2v2H8V2H6v2H5C3.89,4,3.01,4.89,3.01,6L3,20c0,1.11,0.89,2,2,2h14c1.11,0,2-0.89,2-2V6C21,4.89,20.11,4,19,4z M19,20H5V9h14V20z M19,7H5V6h14V7z", BackgroundColor = "#E67E22" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "إيرادات الشهر", Value = stats.ThisMonthRevenue.ToString("C"), Icon = "M11.8,10.9c-2.27-0.59-3-1.2-3-2.15c0-1.09,1.01-1.85,2.7-1.85c1.78,0,2.44,0.85,2.5,2.1h2.21c-0.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94,0.42-3.5,1.68-3.5,3.61c0,2.31,1.91,3.46,4.7,4.13c2.5,0.6,3,1.48,3,2.41c0,0.69-0.49,1.79-2.7,1.79c-2.06,0-2.87-0.92-2.98-2.1h-2.2c0.12,2.19,1.76,3.42,3.68,3.83V21h3v-2.15c2.13-0.47,3.5-1.73,3.5-3.96c0-2.14-1.51-3.32-4.2-3.99z", BackgroundColor = "#2ECC71" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "طلبات معمل", Value = stats.TotalOutstandingPayments.ToString(), Icon = "M20,8h-2.81c-0.45-1.77-2.02-3-3.94-3c-1.92,0-3.49,1.23-3.94,3H6.25C6.11,8,6,8.11,6,8.25v0.5C6,8.89,6.11,9,6.25,9H10v1H8v1h2v1H8v1h2v1H8v1h2v1H8v1h2v1h4v-1h2v-1h-2v-1h2v-1h-2v-1h2v-1h-2v-1h2v-1h1.75c0.14,0,0.25-0.11,0.25-0.25v-0.5C20.25,8.11,20.14,8,20,8z M12.25,6.5c0.41,0,0.75,0.34,0.75,0.75s-0.34,0.75-0.75,0.75s-0.75-0.34-0.75-0.75S11.84,6.5,12.25,6.5z", BackgroundColor = "#9B59B6" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "مرضى جدد (الشهر)", Value = stats.NewPatientsThisMonth.ToString(), Icon = "M15,12c2.21,0,4-1.79,4-4s-1.79-4-4-4s-4,1.79-4,4S12.79,12,15,12z M6,10V7h3V4H6V1H4v3H1v3h3v3H4z", BackgroundColor = "#1ABC9C" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "إيرادات اليوم", Value = stats.TodayRevenue.ToString("C"), Icon = "M7,15H5.5v-4.5H4V9h3v6Zm3.5-6H9V7.5h1.5V9Zm3.5,0h-1.5V7.5H14V9Zm2,3h1.5v-1.5H16V12Zm0,3h1.5v-1.5H16V15Z", BackgroundColor = "#F1C40F" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "نواقص المخزون", Value = stats.LowStockItems.ToString(), Icon = "M18,6h-2c0-2.21-1.79-4-4-4S8,3.79,8,6H6C4.9,6,4,6.9,4,8v12c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V8C20,6.9,19.1,6,18,6z M12,4c1.1,0,2,0.9,2,2h-4C10,4.9,10.9,4,12,4z", BackgroundColor = "#E74C3C" });
            StatisticCards.Add(new StatisticCardViewModel { Title = "إجمالي الأطباء", Value = stats.TotalDoctors.ToString(), Icon = "M12,12c2.21,0,4-1.79,4-4s-1.79-4-4-4S8,5.79,8,8S9.79,12,12,12z M12,14c-2.67,0-8,1.34-8,4v2h16v-2C20,15.34,14.67,14,12,14z", BackgroundColor = "#34495E" });
       }
        /// <summary>
        /// إدارة الكاش المحلي
        /// </summary>
        private bool TryGetFromCache<T>(string key, out T result)
        {
            if (_localCache.TryGetValue(key, out var cachedItem) &&
                cachedItem is CachedItem<T> item &&
                DateTime.Now - item.CachedAt < _cacheExpiration)
            {
                result = item.Data;
                return true;
            }

            result = default(T);
            return false;
        }

        private void AddToCache<T>(string key, T data)
        {
            _localCache.AddOrUpdate(key, new CachedItem<T>(data, DateTime.Now), (k, v) => new CachedItem<T>(data, DateTime.Now));
        }

        /// <summary>
        /// تحديث حالة الأداء
        /// </summary>
        private void UpdatePerformanceStatus()
        {
            if (LoadTime < 1000)
            {
                PerformanceStatus = "ممتاز";
            }
            else if (LoadTime < 3000)
            {
                PerformanceStatus = "جيد";
            }
            else if (LoadTime < 5000)
            {
                PerformanceStatus = "متوسط";
            }
            else
            {
                PerformanceStatus = "بطيء";
            }
        }

        /// <summary>
        /// معالجة الأخطاء بشكل شامل
        /// </summary>
        private async Task HandleErrorAsync(string userMessage, Exception ex)
        {
            ErrorCount++;
            _logger.LogError(ex, userMessage);

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ErrorMessage = $"{userMessage}: {ex.Message}";
                HasError = true;
                IsLoading = false;
            });

            // إعادة المحاولة تلقائياً بعد 30 ثانية
            _ = Task.Delay(30000).ContinueWith(async _ =>
            {
                if (!_disposed && HasError)
                {
                    await RefreshDataAsync();
                }
            });
        }

        /// <summary>
        /// تحديث حالة التحميل
        /// </summary>
        private async Task SetLoadingStateAsync(bool isLoading, string message = "")
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                IsLoading = isLoading;
                if (!string.IsNullOrEmpty(message))
                {
                    LoadingMessage = message;
                }
            });
        }

        /// <summary>
        /// معالج حدث مؤقت التحديث
        /// </summary>
        private async void OnRefreshTimerTick(object sender, EventArgs e)
        {
            if (!_disposed && !IsRefreshing && IsAutoRefreshEnabled)
            {
                _logger.LogInformation("تحديث تلقائي للداشبورد");
                await RefreshDataAsync();
            }
        }

        /// <summary>
        /// أمر تحديث البيانات
        /// </summary>
        [RelayCommand]
        public async Task RefreshDataAsync()
        {
            if (_disposed || IsRefreshing) return;

            try
            {
                await _refreshSemaphore.WaitAsync();
                IsRefreshing = true;

                _logger.LogInformation("بدء تحديث بيانات الداشبورد");

                // مسح الكاش المحلي
                _localCache.Clear();

                // إعادة تحميل البيانات الأساسية
                await LoadEssentialDataAsync();

                // إعادة تحميل البيانات الثانوية في الخلفية
                _ = Task.Run(async () => await LoadSecondaryDataAsync(), _cancellationTokenSource.Token);

                // مسح الأخطاء السابقة
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    HasError = false;
                    ErrorMessage = string.Empty;
                });

                _logger.LogInformation("تم تحديث بيانات الداشبورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات الداشبورد");
                await HandleErrorAsync("فشل في تحديث البيانات", ex);
            }
            finally
            {
                IsRefreshing = false;
                _refreshSemaphore.Release();
            }
        }

        /// <summary>
        /// تبديل التحديث التلقائي
        /// </summary>
        [RelayCommand]
        public void ToggleAutoRefresh()
        {
            IsAutoRefreshEnabled = !IsAutoRefreshEnabled;

            if (IsAutoRefreshEnabled)
            {
                StartAutoRefresh();
                _logger.LogInformation("تم تفعيل التحديث التلقائي");
            }
            else
            {
                StopAutoRefresh();
                _logger.LogInformation("تم إيقاف التحديث التلقائي");
            }
        }

        /// <summary>
        /// بدء التحديث التلقائي
        /// </summary>
        private void StartAutoRefresh()
        {
            if (!_refreshTimer.IsEnabled)
            {
                _refreshTimer.Start();
            }
        }

        /// <summary>
        /// إيقاف التحديث التلقائي
        /// </summary>
        private void StopAutoRefresh()
        {
            if (_refreshTimer.IsEnabled)
            {
                _refreshTimer.Stop();
            }
        }

        /// <summary>
        /// تغيير العملة
        /// </summary>
        [RelayCommand]
        public async Task ChangeCurrencyAsync(string currency)
        {
            try
            {
                SelectedCurrency = currency;
                // await _dashboardService.UpdateSelectedCurrencyAsync(currency); // This service is removed

                // إعادة تحميل البيانات المالية
                await LoadEssentialDataAsync();

                _logger.LogInformation($"تم تغيير العملة إلى {currency}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير العملة");
                await HandleErrorAsync("فشل في تغيير العملة", ex);
            }
        }

        /// <summary>
        /// تحديد التنبيه كمقروء
        /// </summary>
        [RelayCommand]
        public async Task MarkAlertAsReadAsync(SmartAlert alert)
        {
            try
            {
                // await _dashboardService.MarkAlertAsReadAsync(alert.Id); // This service is removed
                SmartAlerts.Remove(alert);
                _logger.LogInformation($"تم تحديد التنبيه {alert.Id} كمقروء");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديد التنبيه كمقروء");
            }
        }

        /// <summary>
        /// حذف التنبيه
        /// </summary>
        [RelayCommand]
        public async Task DeleteAlertAsync(SmartAlert alert)
        {
            try
            {
                // await _dashboardService.DeleteAlertAsync(alert.Id); // This service is removed
                SmartAlerts.Remove(alert);
                _logger.LogInformation($"تم حذف التنبيه {alert.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف التنبيه");
            }
        }

        /// <summary>
        /// الاتصال بالمريض
        /// </summary>
        [RelayCommand]
        public void CallPatient(string phoneNumber)
        {
            try
            {
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = $"tel:{phoneNumber}",
                        UseShellExecute = true
                    });
                    _logger.LogInformation($"تم بدء الاتصال بالرقم {phoneNumber}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في بدء الاتصال");
            }
        }

        /// <summary>
        /// إرسال رسالة واتساب
        /// </summary>
        [RelayCommand]
        public void SendWhatsApp(string phoneNumber)
        {
            try
            {
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    var whatsappUrl = $"https://wa.me/{phoneNumber.Replace("+", "").Replace(" ", "")}";
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = whatsappUrl,
                        UseShellExecute = true
                    });
                    _logger.LogInformation($"تم فتح واتساب للرقم {phoneNumber}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح واتساب");
            }
        }

        /// <summary>
        /// إضافة مريض جديد
        /// </summary>
        [RelayCommand]
        public void AddPatient()
        {
            try
            {
                var addPatientWindow = new AqlanCenterProApp.Views.Patients.ModernAddPatientWindow();
                addPatientWindow.Show();
                _logger.LogInformation("تم فتح نافذة إضافة مريض جديد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة مريض");
            }
        }

        /// <summary>
        /// إضافة موعد جديد
        /// </summary>
        [RelayCommand]
        public void AddAppointment()
        {
            try
            {
                var appointmentService = App.Services.GetRequiredService<IAppointmentService>();
                var patientService = App.Services.GetRequiredService<IPatientService>();
                var doctorService = _doctorService;
                var orthodonticPlanService = App.Services.GetRequiredService<IOrthodonticPlanService>();

                var viewModel = new AddEditAppointmentViewModel(appointmentService, patientService, doctorService, orthodonticPlanService);
                var window = new AddEditAppointmentView(viewModel);
                window.Show();
                _logger.LogInformation("تم فتح نافذة إضافة موعد جديد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة موعد");
            }
        }

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        [RelayCommand]
        public void AddInvoice()
        {
            try
            {
                var invoiceService = App.Services.GetRequiredService<IInvoiceService>();
                var patientService = App.Services.GetRequiredService<IPatientService>();
                var serviceService = App.Services.GetRequiredService<IServiceService>();

                var viewModel = new AddEditInvoiceViewModel(invoiceService, patientService, serviceService);
                var window = new AddEditInvoiceView(viewModel);
                window.Show();
                _logger.LogInformation("تم فتح نافذة إضافة فاتورة جديدة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة فاتورة");
            }
        }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        [RelayCommand]
        public void ViewReports()
        {
            try
            {
                // هنا يتم فتح نافذة التقارير
                _logger.LogInformation("تم طلب فتح نافذة التقارير");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة التقارير");
            }
        }

        /// <summary>
        /// تصدير لوحة التحكم
        /// </summary>
        [RelayCommand]
        public async Task ExportDashboardAsync(string format = "PDF")
        {
            try
            {
                // var data = await _dashboardService.ExportDashboardDataAsync(format); // This service is removed
                var data = new byte[0]; // Placeholder
                _logger.LogInformation($"تم تصدير لوحة التحكم بصيغة {format}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير لوحة التحكم");
            }
        }

        /// <summary>
        /// تحديث إعدادات لوحة التحكم
        /// </summary>
        [RelayCommand]
        public async Task UpdateDashboardSettingsAsync()
        {
            try
            {
                // await _dashboardService.SaveComprehensiveDashboardSettingsAsync(DashboardSettings); // This service is removed
                _logger.LogInformation("تم حفظ إعدادات لوحة التحكم");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعدادات لوحة التحكم");
            }
        }

        /// <summary>
        /// تحسين الأداء
        /// </summary>
        [RelayCommand]
        public async Task OptimizePerformanceAsync()
        {
            try
            {
                // await _dashboardService.OptimizePerformanceAsync(); // This service is removed
                _logger.LogInformation("تم تحسين أداء لوحة التحكم");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الأداء");
            }
        }

        /// <summary>
        /// إنشاء تنبيه مخصص
        /// </summary>
        public async Task CreateCustomAlertAsync(string title, string message, AlertType type)
        {
            try
            {
                // var alert = await _dashboardService.CreateSmartAlertAsync(title, message, type); // This service is removed
                var alert = new EnhancedSmartAlert(); // Placeholder
                EnhancedSmartAlerts.Insert(0, alert);
                _logger.LogInformation("تم إنشاء تنبيه مخصص");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التنبيه المخصص");
            }
        }

        /// <summary>
        /// تحديث البيانات المحسنة
        /// </summary>
        [RelayCommand]
        public async Task RefreshEnhancedDataAsync()
        {
            try
            {
                await LoadEnhancedDataAsync();
                _logger.LogInformation("تم تحديث البيانات المحسنة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات المحسنة");
            }
        }

        /// <summary>
        /// تبديل وضع العرض
        /// </summary>
        [RelayCommand]
        public void ToggleViewMode()
        {
            try
            {
                // يمكن إضافة منطق تبديل وضع العرض (مضغوط/مفصل)
                _logger.LogInformation("تم تبديل وضع العرض");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تبديل وضع العرض");
            }
        }



        /// <summary>
        /// تنسيق العملة
        /// </summary>
        // public string FormatCurrency(decimal amount)
        // {
        //     return _dashboardService.FormatCurrency(amount, SelectedCurrency);
        // }

        /// <summary>
        /// تحميل البيانات المحسنة
        /// </summary>
        private async Task LoadEnhancedDataAsync()
        {
            if (_disposed) return;

            try
            {
                LoadingMessage = "جاري تحميل البيانات المحسنة...";

                // تحميل البيانات المحسنة في مهام متوازية
                var enhancedPatientTask = Task.FromResult(new EnhancedPatientStatistics()); // Placeholder
                var enhancedAppointmentTask = Task.FromResult(new EnhancedAppointmentStatistics()); // Placeholder
                var enhancedEmployeeTask = Task.FromResult(new EnhancedEmployeeStatistics()); // Placeholder
                var enhancedDoctorTask = Task.FromResult(new List<EnhancedDoctorStatistics>()); // Placeholder
                var enhancedRevenueTask = Task.FromResult(new EnhancedRevenueStatistics()); // Placeholder
                var enhancedAlertsTask = Task.FromResult(new List<EnhancedSmartAlert>()); // Placeholder
                var currenciesTask = Task.FromResult(new List<CurrencySettings>()); // Placeholder
                var settingsTask = Task.FromResult(new ComprehensiveDashboardSettings()); // Placeholder
                var stateTask = Task.FromResult(new DashboardState()); // Placeholder

                await Task.WhenAll(enhancedPatientTask, enhancedAppointmentTask, enhancedEmployeeTask,
                                 enhancedDoctorTask, enhancedRevenueTask, enhancedAlertsTask,
                                 currenciesTask, settingsTask, stateTask)
                    .ConfigureAwait(false);

                // تحديث البيانات في الواجهة بشكل آمن
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        EnhancedPatientStatistics = enhancedPatientTask.Result;
                        EnhancedAppointmentStatistics = enhancedAppointmentTask.Result;
                        EnhancedEmployeeStatistics = enhancedEmployeeTask.Result;

                        EnhancedDoctorStatistics.Clear();
                        foreach (var item in enhancedDoctorTask.Result)
                        {
                            EnhancedDoctorStatistics.Add(item);
                        }

                        EnhancedRevenueStatistics = enhancedRevenueTask.Result;

                        EnhancedSmartAlerts.Clear();
                        foreach (var item in enhancedAlertsTask.Result)
                        {
                            EnhancedSmartAlerts.Add(item);
                        }

                        AvailableCurrencies.Clear();
                        foreach (var item in currenciesTask.Result)
                        {
                            AvailableCurrencies.Add(item);
                        }

                        DashboardSettings = settingsTask.Result;
                        DashboardState = stateTask.Result;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في تحديث البيانات المحسنة في الواجهة");
                        throw;
                    }
                });

                _logger.LogInformation("تم تحميل البيانات المحسنة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات المحسنة");
                _logger.LogWarning("سيتم المتابعة بدون البيانات المحسنة");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _refreshTimer?.Stop();
                _refreshSemaphore?.Dispose();
                _localCache?.Clear();
                _disposed = true;
                _logger.LogInformation("تم تنظيف موارد DashboardViewModel");
            }
        }
    }

    /// <summary>
    /// عنصر الكاش المحلي
    /// </summary>
    public class CachedItem<T>
    {
        public T Data { get; }
        public DateTime CachedAt { get; }

        public CachedItem(T data, DateTime cachedAt)
        {
            Data = data;
            CachedAt = cachedAt;
        }
    }
}
