using System;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using AqlanCenterProApp.Models.Dashboard;
using AqlanCenterProApp.Services.Interfaces;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System.Collections.Concurrent;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.Dashboard
{
    /// <summary>
    /// نموذج عرض الداشبورد المحسن بشكل شامل
    /// </summary>
    public partial class DashboardViewModel : ObservableObject, IDisposable
    {
        private readonly IDashboardService _dashboardService;
        private readonly ILogger<DashboardViewModel> _logger;
        private readonly DispatcherTimer _refreshTimer;
        private readonly SemaphoreSlim _refreshSemaphore = new SemaphoreSlim(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private readonly ConcurrentDictionary<string, object> _localCache = new ConcurrentDictionary<string, object>();
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(3);
        private bool _disposed = false;
        private bool _isInitialized = false;

        // البيانات الأساسية
        [ObservableProperty]
        private DashboardStatistics _statistics = new();

        [ObservableProperty]
        private ObservableCollection<UpcomingAppointment> _upcomingAppointments = new();

        [ObservableProperty]
        private ObservableCollection<SmartAlert> _smartAlerts = new();

        [ObservableProperty]
        private ObservableCollection<TreatmentTypeDistribution> _treatmentDistribution = new();

        [ObservableProperty]
        private ObservableCollection<Models.Dashboard.DoctorStatistics> _doctorStatistics = new();

        [ObservableProperty]
        private PaymentStatistics _paymentStatistics = new();

        [ObservableProperty]
        private InventoryStatistics _inventoryStatistics = new();

        // الإحصائيات المحسنة الجديدة
        [ObservableProperty]
        private EnhancedPatientStatistics _enhancedPatientStatistics = new();

        [ObservableProperty]
        private EnhancedAppointmentStatistics _enhancedAppointmentStatistics = new();

        [ObservableProperty]
        private EnhancedEmployeeStatistics _enhancedEmployeeStatistics = new();

        [ObservableProperty]
        private List<EnhancedDoctorStatistics> _enhancedDoctorStatistics = new();

        [ObservableProperty]
        private EnhancedRevenueStatistics _enhancedRevenueStatistics = new();

        [ObservableProperty]
        private List<EnhancedSmartAlert> _enhancedSmartAlerts = new();

        [ObservableProperty]
        private List<CurrencySettings> _availableCurrencies = new();

        [ObservableProperty]
        private ComprehensiveDashboardSettings _dashboardSettings = new();

        [ObservableProperty]
        private DashboardState _dashboardState = new();

        // إعدادات المستخدم
        [ObservableProperty]
        private string _selectedCurrency = "YER";

        [ObservableProperty]
        private bool _isAutoRefreshEnabled = true;

        [ObservableProperty]
        private DateTime _lastUpdated = DateTime.Now;

        [ObservableProperty]
        private bool _isRefreshing = false;

        // حالة التحميل والأخطاء
        [ObservableProperty]
        private bool _isLoading = false;

        [ObservableProperty]
        private string _errorMessage = string.Empty;

        [ObservableProperty]
        private bool _hasError = false;

        [ObservableProperty]
        private string _loadingMessage = "جاري تحميل البيانات...";

        [ObservableProperty]
        private bool _isEssentialDataLoaded = false;

        [ObservableProperty]
        private bool _isSecondaryDataLoaded = false;

        // المخططات البيانية
        [ObservableProperty]
        private ISeries[] _revenueSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _patientSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _treatmentPieSeries = Array.Empty<ISeries>();

        [ObservableProperty]
        private ISeries[] _appointmentSeries = Array.Empty<ISeries>();

        // محاور المخططات
        [ObservableProperty]
        private Axis[] _xAxes = Array.Empty<Axis>();

        [ObservableProperty]
        private Axis[] _yAxes = Array.Empty<Axis>();

        // إحصائيات الأداء
        [ObservableProperty]
        private string _performanceStatus = "جيد";

        [ObservableProperty]
        private double _loadTime = 0;

        [ObservableProperty]
        private int _cacheHitCount = 0;

        [ObservableProperty]
        private int _errorCount = 0;

        public DashboardViewModel(IDashboardService dashboardService, ILogger<DashboardViewModel> logger)
        {
            _dashboardService = dashboardService ?? throw new ArgumentNullException(nameof(dashboardService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // إعداد مؤقت التحديث التلقائي
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5)
            };
            _refreshTimer.Tick += OnRefreshTimerTick;

            _logger.LogInformation("تم إنشاء DashboardViewModel بنجاح");
        }

        /// <summary>
        /// تهيئة الداشبورد بشكل آمن ومحسن
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_disposed || _isInitialized) return;

            try
            {
                _logger.LogInformation("بدء تهيئة الداشبورد المحسن");

                await SetLoadingStateAsync(true, "جاري تحميل البيانات الأساسية...");

                // تحميل البيانات الأساسية أولاً (الضرورية للعرض الفوري)
                await LoadEssentialDataAsync();

                // تحميل البيانات الثانوية في الخلفية (المخططات والإحصائيات التفصيلية)
                _ = Task.Run(async () => await LoadSecondaryDataAsync(), _cancellationTokenSource.Token);

                // بدء التحديث التلقائي
                if (IsAutoRefreshEnabled)
                {
                    StartAutoRefresh();
                }

                _isInitialized = true;
                _logger.LogInformation("تم تهيئة الداشبورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة الداشبورد");
                await HandleErrorAsync("حدث خطأ في تهيئة الداشبورد", ex);
            }
            finally
            {
                await SetLoadingStateAsync(false);
            }
        }

        /// <summary>
        /// تحميل البيانات الأساسية (الضرورية للعرض الفوري)
        /// </summary>
        private async Task LoadEssentialDataAsync()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("بدء تحميل البيانات الأساسية");

                // استخدام المهام المتوازية لتحسين الأداء
                var statisticsTask = LoadStatisticsWithCacheAsync();
                var appointmentsTask = LoadAppointmentsWithCacheAsync();
                var alertsTask = LoadAlertsWithCacheAsync();

                // انتظار اكتمال جميع المهام الأساسية
                await Task.WhenAll(statisticsTask, appointmentsTask, alertsTask);

                // تحديث البيانات في الواجهة بشكل آمن
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        Statistics = statisticsTask.Result;

                        UpcomingAppointments.Clear();
                        foreach (var appointment in appointmentsTask.Result)
                        {
                            UpcomingAppointments.Add(appointment);
                        }

                        SmartAlerts.Clear();
                        foreach (var alert in alertsTask.Result)
                        {
                            SmartAlerts.Add(alert);
                        }

                        IsEssentialDataLoaded = true;
                        LastUpdated = DateTime.Now;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في تحديث البيانات الأساسية في الواجهة");
                        throw;
                    }
                });

                stopwatch.Stop();
                LoadTime = stopwatch.ElapsedMilliseconds;
                UpdatePerformanceStatus();

                _logger.LogInformation($"تم تحميل البيانات الأساسية بنجاح في {LoadTime} مللي ثانية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الأساسية");
                await HandleErrorAsync("فشل في تحميل البيانات الأساسية", ex);
                throw;
            }
        }

        /// <summary>
        /// تحميل البيانات الثانوية (المخططات والإحصائيات التفصيلية)
        /// </summary>
        private async Task LoadSecondaryDataAsync()
        {
            try
            {
                _logger.LogInformation("بدء تحميل البيانات الثانوية");

                // تحميل المخططات والإحصائيات التفصيلية
                var chartsTask = UpdateChartsAsync();
                var detailedStatsTask = LoadDetailedStatisticsAsync();

                await Task.WhenAll(chartsTask, detailedStatsTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsSecondaryDataLoaded = true;
                });

                _logger.LogInformation("تم تحميل البيانات الثانوية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الثانوية");
                ErrorCount++;
                // لا نرمي الاستثناء هنا لأن البيانات الثانوية ليست ضرورية
            }
        }

        /// <summary>
        /// تحميل الإحصائيات مع الكاش
        /// </summary>
        private async Task<DashboardStatistics> LoadStatisticsWithCacheAsync()
        {
            var cacheKey = "dashboard_statistics";

            if (TryGetFromCache<DashboardStatistics>(cacheKey, out var cachedStats))
            {
                CacheHitCount++;
                return cachedStats;
            }

            var stats = await _dashboardService.GetDashboardStatisticsAsync();
            AddToCache(cacheKey, stats);
            return stats;
        }

        /// <summary>
        /// تحميل المواعيد مع الكاش
        /// </summary>
        private async Task<List<UpcomingAppointment>> LoadAppointmentsWithCacheAsync()
        {
            var cacheKey = "upcoming_appointments";

            if (TryGetFromCache<List<UpcomingAppointment>>(cacheKey, out var cachedAppointments))
            {
                CacheHitCount++;
                return cachedAppointments;
            }

            var appointments = await _dashboardService.GetUpcomingAppointmentsAsync(7);
            AddToCache(cacheKey, appointments);
            return appointments;
        }

        /// <summary>
        /// تحميل التنبيهات مع الكاش
        /// </summary>
        private async Task<List<SmartAlert>> LoadAlertsWithCacheAsync()
        {
            var cacheKey = "smart_alerts";

            if (TryGetFromCache<List<SmartAlert>>(cacheKey, out var cachedAlerts))
            {
                CacheHitCount++;
                return cachedAlerts;
            }

            var alerts = await _dashboardService.GetSmartAlertsAsync();
            AddToCache(cacheKey, alerts);
            return alerts;
        }

        /// <summary>
        /// تحميل الإحصائيات التفصيلية
        /// </summary>
        private async Task LoadDetailedStatisticsAsync()
        {
            try
            {
                var paymentStatsTask = _dashboardService.GetPaymentStatisticsAsync();
                var inventoryStatsTask = _dashboardService.GetInventoryStatisticsAsync();
                var doctorStatsTask = _dashboardService.GetDoctorStatisticsAsync();
                var treatmentDistTask = _dashboardService.GetTreatmentTypeDistributionAsync();

                await Task.WhenAll(paymentStatsTask, inventoryStatsTask, doctorStatsTask, treatmentDistTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    PaymentStatistics = paymentStatsTask.Result;
                    InventoryStatistics = inventoryStatsTask.Result;

                    DoctorStatistics.Clear();
                    foreach (var doctor in doctorStatsTask.Result)
                    {
                        DoctorStatistics.Add(doctor);
                    }

                    TreatmentDistribution.Clear();
                    foreach (var treatment in treatmentDistTask.Result)
                    {
                        TreatmentDistribution.Add(treatment);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الإحصائيات التفصيلية");
            }
        }

        /// <summary>
        /// تحديث المخططات البيانية
        /// </summary>
        private async Task UpdateChartsAsync()
        {
            try
            {
                var revenueDataTask = _dashboardService.GetRevenueChartDataAsync("monthly");
                var patientDataTask = _dashboardService.GetPatientChartDataAsync("monthly");
                var appointmentDataTask = _dashboardService.GetAppointmentChartDataAsync("weekly");
                var treatmentDataTask = _dashboardService.GetTreatmentTypeDistributionAsync();

                await Task.WhenAll(revenueDataTask, patientDataTask, appointmentDataTask, treatmentDataTask);

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // تحديث مخطط الإيرادات
                    var revenueData = revenueDataTask.Result;
                    RevenueSeries = new ISeries[]
                    {
                        new LineSeries<decimal>
                        {
                            Values = revenueData.Select(d => (decimal)d.Value),
                            Fill = new SolidColorPaint(SKColors.LightBlue.WithAlpha(50)),
                            Stroke = new SolidColorPaint(SKColors.Blue, 3),
                            GeometrySize = 8,
                            GeometryFill = new SolidColorPaint(SKColors.Blue)
                        }
                    };

                    XAxes = new Axis[]
                    {
                        new Axis
                        {
                            Labels = revenueData.Select(d => d.Label).ToArray(),
                            LabelsRotation = 45
                        }
                    };

                    YAxes = new Axis[]
                    {
                        new Axis
                        {
                            Labeler = value => $"{value:N0} ريال"
                        }
                    };

                    // تحديث مخطط المرضى
                    var patientData = patientDataTask.Result;
                    PatientSeries = new ISeries[]
                    {
                        new ColumnSeries<int>
                        {
                            Values = patientData.Select(d => (int)d.Value).ToArray(),
                            Fill = new SolidColorPaint(SKColors.Green),
                            Stroke = new SolidColorPaint(SKColors.DarkGreen, 2)
                        }
                    };

                    // تحديث مخطط المواعيد
                    var appointmentData = appointmentDataTask.Result;
                    AppointmentSeries = new ISeries[]
                    {
                        new LineSeries<int>
                        {
                            Values = appointmentData.Select(d => (int)d.Value).ToArray(),
                            Fill = new SolidColorPaint(SKColors.Orange.WithAlpha(50)),
                            Stroke = new SolidColorPaint(SKColors.Orange, 3),
                            GeometrySize = 6,
                            GeometryFill = new SolidColorPaint(SKColors.Orange)
                        }
                    };

                    // تحديث مخطط توزيع العلاج
                    var treatmentData = treatmentDataTask.Result;
                    TreatmentPieSeries = treatmentData.Select(t => new PieSeries<decimal>
                    {
                        Values = new[] { (decimal)t.Count },
                        Name = t.TreatmentType,
                        Fill = new SolidColorPaint(GetRandomSKColor())
                    }).ToArray();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المخططات البيانية");
            }
        }

        /// <summary>
        /// الحصول على لون عشوائي للمخططات
        /// </summary>
        private SKColor GetRandomSKColor()
        {
            var colors = new[]
            {
                SKColors.Blue, SKColors.Green, SKColors.Orange, SKColors.Red,
                SKColors.Purple, SKColors.Teal, SKColors.Pink, SKColors.Brown
            };
            return colors[new Random().Next(colors.Length)];
        }

        /// <summary>
        /// إدارة الكاش المحلي
        /// </summary>
        private bool TryGetFromCache<T>(string key, out T result)
        {
            if (_localCache.TryGetValue(key, out var cachedItem) &&
                cachedItem is CachedItem<T> item &&
                DateTime.Now - item.CachedAt < _cacheExpiration)
            {
                result = item.Data;
                return true;
            }

            result = default(T);
            return false;
        }

        private void AddToCache<T>(string key, T data)
        {
            _localCache.AddOrUpdate(key, new CachedItem<T>(data, DateTime.Now), (k, v) => new CachedItem<T>(data, DateTime.Now));
        }

        /// <summary>
        /// تحديث حالة الأداء
        /// </summary>
        private void UpdatePerformanceStatus()
        {
            if (LoadTime < 1000)
            {
                PerformanceStatus = "ممتاز";
            }
            else if (LoadTime < 3000)
            {
                PerformanceStatus = "جيد";
            }
            else if (LoadTime < 5000)
            {
                PerformanceStatus = "متوسط";
            }
            else
            {
                PerformanceStatus = "بطيء";
            }
        }

        /// <summary>
        /// معالجة الأخطاء بشكل شامل
        /// </summary>
        private async Task HandleErrorAsync(string userMessage, Exception ex)
        {
            ErrorCount++;
            _logger.LogError(ex, userMessage);

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ErrorMessage = $"{userMessage}: {ex.Message}";
                HasError = true;
                IsLoading = false;
            });

            // إعادة المحاولة تلقائياً بعد 30 ثانية
            _ = Task.Delay(30000).ContinueWith(async _ =>
            {
                if (!_disposed && HasError)
                {
                    await RefreshDataAsync();
                }
            });
        }

        /// <summary>
        /// تحديث حالة التحميل
        /// </summary>
        private async Task SetLoadingStateAsync(bool isLoading, string message = "")
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                IsLoading = isLoading;
                if (!string.IsNullOrEmpty(message))
                {
                    LoadingMessage = message;
                }
            });
        }

        /// <summary>
        /// معالج حدث مؤقت التحديث
        /// </summary>
        private async void OnRefreshTimerTick(object sender, EventArgs e)
        {
            if (!_disposed && !IsRefreshing && IsAutoRefreshEnabled)
            {
                _logger.LogInformation("تحديث تلقائي للداشبورد");
                await RefreshDataAsync();
            }
        }

        /// <summary>
        /// أمر تحديث البيانات
        /// </summary>
        [RelayCommand]
        public async Task RefreshDataAsync()
        {
            if (_disposed || IsRefreshing) return;

            try
            {
                await _refreshSemaphore.WaitAsync();
                IsRefreshing = true;

                _logger.LogInformation("بدء تحديث بيانات الداشبورد");

                // مسح الكاش المحلي
                _localCache.Clear();

                // إعادة تحميل البيانات الأساسية
                await LoadEssentialDataAsync();

                // إعادة تحميل البيانات الثانوية في الخلفية
                _ = Task.Run(async () => await LoadSecondaryDataAsync(), _cancellationTokenSource.Token);

                // مسح الأخطاء السابقة
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    HasError = false;
                    ErrorMessage = string.Empty;
                });

                _logger.LogInformation("تم تحديث بيانات الداشبورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات الداشبورد");
                await HandleErrorAsync("فشل في تحديث البيانات", ex);
            }
            finally
            {
                IsRefreshing = false;
                _refreshSemaphore.Release();
            }
        }

        /// <summary>
        /// تبديل التحديث التلقائي
        /// </summary>
        [RelayCommand]
        public void ToggleAutoRefresh()
        {
            IsAutoRefreshEnabled = !IsAutoRefreshEnabled;

            if (IsAutoRefreshEnabled)
            {
                StartAutoRefresh();
                _logger.LogInformation("تم تفعيل التحديث التلقائي");
            }
            else
            {
                StopAutoRefresh();
                _logger.LogInformation("تم إيقاف التحديث التلقائي");
            }
        }

        /// <summary>
        /// بدء التحديث التلقائي
        /// </summary>
        private void StartAutoRefresh()
        {
            if (!_refreshTimer.IsEnabled)
            {
                _refreshTimer.Start();
            }
        }

        /// <summary>
        /// إيقاف التحديث التلقائي
        /// </summary>
        private void StopAutoRefresh()
        {
            if (_refreshTimer.IsEnabled)
            {
                _refreshTimer.Stop();
            }
        }

        /// <summary>
        /// تغيير العملة
        /// </summary>
        [RelayCommand]
        public async Task ChangeCurrencyAsync(string currency)
        {
            try
            {
                SelectedCurrency = currency;
                await _dashboardService.UpdateSelectedCurrencyAsync(currency);

                // إعادة تحميل البيانات المالية
                await LoadEssentialDataAsync();

                _logger.LogInformation($"تم تغيير العملة إلى {currency}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير العملة");
                await HandleErrorAsync("فشل في تغيير العملة", ex);
            }
        }

        /// <summary>
        /// تحديد التنبيه كمقروء
        /// </summary>
        [RelayCommand]
        public async Task MarkAlertAsReadAsync(SmartAlert alert)
        {
            try
            {
                await _dashboardService.MarkAlertAsReadAsync(alert.Id);
                SmartAlerts.Remove(alert);
                _logger.LogInformation($"تم تحديد التنبيه {alert.Id} كمقروء");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديد التنبيه كمقروء");
            }
        }

        /// <summary>
        /// حذف التنبيه
        /// </summary>
        [RelayCommand]
        public async Task DeleteAlertAsync(SmartAlert alert)
        {
            try
            {
                await _dashboardService.DeleteAlertAsync(alert.Id);
                SmartAlerts.Remove(alert);
                _logger.LogInformation($"تم حذف التنبيه {alert.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف التنبيه");
            }
        }

        /// <summary>
        /// الاتصال بالمريض
        /// </summary>
        [RelayCommand]
        public void CallPatient(string phoneNumber)
        {
            try
            {
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = $"tel:{phoneNumber}",
                        UseShellExecute = true
                    });
                    _logger.LogInformation($"تم بدء الاتصال بالرقم {phoneNumber}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في بدء الاتصال");
            }
        }

        /// <summary>
        /// إرسال رسالة واتساب
        /// </summary>
        [RelayCommand]
        public void SendWhatsApp(string phoneNumber)
        {
            try
            {
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    var whatsappUrl = $"https://wa.me/{phoneNumber.Replace("+", "").Replace(" ", "")}";
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = whatsappUrl,
                        UseShellExecute = true
                    });
                    _logger.LogInformation($"تم فتح واتساب للرقم {phoneNumber}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح واتساب");
            }
        }

        /// <summary>
        /// إضافة مريض جديد
        /// </summary>
        [RelayCommand]
        public void AddPatient()
        {
            try
            {
                // هنا يتم فتح نافذة إضافة مريض جديد
                _logger.LogInformation("تم طلب فتح نافذة إضافة مريض جديد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة مريض");
            }
        }

        /// <summary>
        /// إضافة موعد جديد
        /// </summary>
        [RelayCommand]
        public void AddAppointment()
        {
            try
            {
                // هنا يتم فتح نافذة إضافة موعد جديد
                _logger.LogInformation("تم طلب فتح نافذة إضافة موعد جديد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة موعد");
            }
        }

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        [RelayCommand]
        public void AddInvoice()
        {
            try
            {
                // هنا يتم فتح نافذة إضافة فاتورة جديدة
                _logger.LogInformation("تم طلب فتح نافذة إضافة فاتورة جديدة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة إضافة فاتورة");
            }
        }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        [RelayCommand]
        public void ViewReports()
        {
            try
            {
                // هنا يتم فتح نافذة التقارير
                _logger.LogInformation("تم طلب فتح نافذة التقارير");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فتح نافذة التقارير");
            }
        }

        /// <summary>
        /// تصدير لوحة التحكم
        /// </summary>
        [RelayCommand]
        public async Task ExportDashboardAsync(string format = "PDF")
        {
            try
            {
                var data = await _dashboardService.ExportDashboardDataAsync(format);
                _logger.LogInformation($"تم تصدير لوحة التحكم بصيغة {format}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير لوحة التحكم");
            }
        }

        /// <summary>
        /// تحديث إعدادات لوحة التحكم
        /// </summary>
        [RelayCommand]
        public async Task UpdateDashboardSettingsAsync()
        {
            try
            {
                await _dashboardService.SaveComprehensiveDashboardSettingsAsync(DashboardSettings);
                _logger.LogInformation("تم حفظ إعدادات لوحة التحكم");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعدادات لوحة التحكم");
            }
        }

        /// <summary>
        /// تحسين الأداء
        /// </summary>
        [RelayCommand]
        public async Task OptimizePerformanceAsync()
        {
            try
            {
                await _dashboardService.OptimizePerformanceAsync();
                _logger.LogInformation("تم تحسين أداء لوحة التحكم");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحسين الأداء");
            }
        }

        /// <summary>
        /// إنشاء تنبيه مخصص
        /// </summary>
        public async Task CreateCustomAlertAsync(string title, string message, AlertType type)
        {
            try
            {
                var alert = await _dashboardService.CreateSmartAlertAsync(title, message, type);
                EnhancedSmartAlerts.Insert(0, alert);
                _logger.LogInformation("تم إنشاء تنبيه مخصص");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء التنبيه المخصص");
            }
        }

        /// <summary>
        /// تحديث البيانات المحسنة
        /// </summary>
        [RelayCommand]
        public async Task RefreshEnhancedDataAsync()
        {
            try
            {
                await LoadEnhancedDataAsync();
                _logger.LogInformation("تم تحديث البيانات المحسنة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات المحسنة");
            }
        }

        /// <summary>
        /// تبديل وضع العرض
        /// </summary>
        [RelayCommand]
        public void ToggleViewMode()
        {
            try
            {
                // يمكن إضافة منطق تبديل وضع العرض (مضغوط/مفصل)
                _logger.LogInformation("تم تبديل وضع العرض");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تبديل وضع العرض");
            }
        }



        /// <summary>
        /// تنسيق العملة
        /// </summary>
        public string FormatCurrency(decimal amount)
        {
            return _dashboardService.FormatCurrency(amount, SelectedCurrency);
        }

        /// <summary>
        /// تحميل البيانات المحسنة
        /// </summary>
        private async Task LoadEnhancedDataAsync()
        {
            if (_disposed) return;

            try
            {
                LoadingMessage = "جاري تحميل البيانات المحسنة...";

                // تحميل البيانات المحسنة في مهام متوازية
                var enhancedPatientTask = _dashboardService.GetEnhancedPatientStatisticsAsync();
                var enhancedAppointmentTask = _dashboardService.GetEnhancedAppointmentStatisticsAsync();
                var enhancedEmployeeTask = _dashboardService.GetEnhancedEmployeeStatisticsAsync();
                var enhancedDoctorTask = _dashboardService.GetEnhancedDoctorStatisticsAsync();
                var enhancedRevenueTask = _dashboardService.GetEnhancedRevenueStatisticsAsync();
                var enhancedAlertsTask = _dashboardService.GetEnhancedSmartAlertsAsync();
                var currenciesTask = _dashboardService.GetAvailableCurrenciesAsync();
                var settingsTask = _dashboardService.GetComprehensiveDashboardSettingsAsync();
                var stateTask = _dashboardService.GetDashboardStateAsync();

                await Task.WhenAll(enhancedPatientTask, enhancedAppointmentTask, enhancedEmployeeTask,
                                 enhancedDoctorTask, enhancedRevenueTask, enhancedAlertsTask,
                                 currenciesTask, settingsTask, stateTask)
                    .ConfigureAwait(false);

                // تحديث البيانات في الواجهة بشكل آمن
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        EnhancedPatientStatistics = enhancedPatientTask.Result;
                        EnhancedAppointmentStatistics = enhancedAppointmentTask.Result;
                        EnhancedEmployeeStatistics = enhancedEmployeeTask.Result;

                        EnhancedDoctorStatistics.Clear();
                        foreach (var item in enhancedDoctorTask.Result)
                        {
                            EnhancedDoctorStatistics.Add(item);
                        }

                        EnhancedRevenueStatistics = enhancedRevenueTask.Result;

                        EnhancedSmartAlerts.Clear();
                        foreach (var item in enhancedAlertsTask.Result)
                        {
                            EnhancedSmartAlerts.Add(item);
                        }

                        AvailableCurrencies.Clear();
                        foreach (var item in currenciesTask.Result)
                        {
                            AvailableCurrencies.Add(item);
                        }

                        DashboardSettings = settingsTask.Result;
                        DashboardState = stateTask.Result;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في تحديث البيانات المحسنة في الواجهة");
                        throw;
                    }
                });

                _logger.LogInformation("تم تحميل البيانات المحسنة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات المحسنة");
                _logger.LogWarning("سيتم المتابعة بدون البيانات المحسنة");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _refreshTimer?.Stop();
                _refreshSemaphore?.Dispose();
                _localCache?.Clear();
                _disposed = true;
                _logger.LogInformation("تم تنظيف موارد DashboardViewModel");
            }
        }
    }

    /// <summary>
    /// عنصر الكاش المحلي
    /// </summary>
    public class CachedItem<T>
    {
        public T Data { get; }
        public DateTime CachedAt { get; }

        public CachedItem(T data, DateTime cachedAt)
        {
            Data = data;
            CachedAt = cachedAt;
        }
    }
}
