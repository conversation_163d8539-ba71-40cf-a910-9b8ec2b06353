using System.ComponentModel.DataAnnotations;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;


namespace AqlanCenterProApp.ViewModels.Labs
{
    /// <summary>
    /// ViewModel لإضافة وتعديل المعامل
    /// </summary>
    public class AddEditLabViewModel : BaseViewModel
    {
        private readonly ILabService _labService;
        private readonly Lab? _originalLab;

        /// <summary>
        /// هل هو وضع التعديل
        /// </summary>
        public bool IsEditMode => _originalLab != null;

        /// <summary>
        /// عنوان النافذة
        /// </summary>
        public string WindowTitle => IsEditMode ? "تعديل معمل" : "إضافة معمل جديد";

        private string _name = string.Empty;
        /// <summary>
        /// اسم المعمل
        /// </summary>
        [Required(ErrorMessage = "اسم المعمل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المعمل لا يمكن أن يتجاوز 100 حرف")]
        public string Name
        {
            get => _name;
            set
            {
                SetProperty(ref _name, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private string? _address;
        /// <summary>
        /// العنوان
        /// </summary>
        [StringLength(200, ErrorMessage = "العنوان لا يمكن أن يتجاوز 200 حرف")]
        public string? Address
        {
            get => _address;
            set
            {
                SetProperty(ref _address, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private string? _phone;
        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرف")]
        public string? Phone
        {
            get => _phone;
            set
            {
                SetProperty(ref _phone, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private string? _whatsApp;
        /// <summary>
        /// رقم الواتساب
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الواتساب لا يمكن أن يتجاوز 20 حرف")]
        public string? WhatsApp
        {
            get => _whatsApp;
            set
            {
                SetProperty(ref _whatsApp, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private string? _email;
        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        public string? Email
        {
            get => _email;
            set
            {
                SetProperty(ref _email, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private string? _notes;
        /// <summary>
        /// الملاحظات
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات لا يمكن أن تتجاوز 1000 حرف")]
        public string? Notes
        {
            get => _notes;
            set
            {
                SetProperty(ref _notes, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        private bool _isActive = true;
        /// <summary>
        /// هل المعمل نشط
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        /// <summary>
        /// هل يمكن الحفظ
        /// </summary>
        public bool CanSave => !string.IsNullOrWhiteSpace(Name);

        // Commands
        /// <summary>
        /// أمر الحفظ
        /// </summary>
        public ICommand SaveCommand { get; private set; } = null!;

        /// <summary>
        /// أمر الإلغاء
        /// </summary>
        public ICommand CancelCommand { get; private set; } = null!;

        /// <summary>
        /// أمر إعادة التعيين
        /// </summary>
        public ICommand ResetCommand { get; private set; } = null!;

        // Events
        /// <summary>
        /// حدث طلب الإغلاق
        /// </summary>
        public event EventHandler<bool>? CloseRequested;

        public AddEditLabViewModel(ILabService labService, Lab? lab = null)
        {
            _labService = labService;
            _originalLab = lab;

            // تهيئة الأوامر
            InitializeCommands();

            // تحميل البيانات إذا كان في وضع التعديل
            if (IsEditMode && _originalLab != null)
            {
                LoadLabData(_originalLab);
            }
        }

        /// <summary>
        /// تهيئة الأوامر
        /// </summary>
        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(async () => await SaveAsync(), () => CanSave);
            CancelCommand = new RelayCommand(() => CloseRequested?.Invoke(this, false));
            ResetCommand = new RelayCommand(ResetForm);
        }

        /// <summary>
        /// تحميل بيانات المعمل للتعديل
        /// </summary>
        private void LoadLabData(Lab lab)
        {
            Name = lab.Name;
            Address = lab.Address;
            Phone = lab.Phone;
            WhatsApp = lab.WhatsApp;
            Email = lab.Email;
            Notes = lab.Notes;
            IsActive = lab.IsActive;
        }

        /// <summary>
        /// حفظ المعمل
        /// </summary>
        private async Task SaveAsync()
        {
            if (!CanSave) return;

            await ExecuteAsync(async () =>
            {
                var lab = new Lab
                {
                    Name = Name.Trim(),
                    Address = Address?.Trim(),
                    Phone = Phone?.Trim(),
                    WhatsApp = WhatsApp?.Trim(),
                    Email = Email?.Trim(),
                    Notes = Notes?.Trim(),
                    IsActive = IsActive
                };

                if (IsEditMode && _originalLab != null)
                {
                    lab.Id = _originalLab.Id;
                    lab.CreatedAt = _originalLab.CreatedAt;
                    await _labService.UpdateLabAsync(lab);
                }
                else
                {
                    await _labService.AddLabAsync(lab);
                }

                CloseRequested?.Invoke(this, true);
            }, "جاري حفظ المعمل...");
        }

        /// <summary>
        /// إعادة تعيين النموذج
        /// </summary>
        private void ResetForm()
        {
            if (IsEditMode && _originalLab != null)
            {
                LoadLabData(_originalLab);
            }
            else
            {
                Name = string.Empty;
                Address = null;
                Phone = null;
                WhatsApp = null;
                Email = null;
                Notes = null;
                IsActive = true;
            }

            ClearError();
        }

        /// <summary>
        /// التحقق من صحة جميع الخصائص
        /// </summary>
        private void ValidateAllProperties()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(Name))
            {
                ErrorMessage = "اسم المعمل مطلوب";
                return;
            }

            // التحقق من البريد الإلكتروني إذا كان موجوداً
            if (!string.IsNullOrWhiteSpace(Email))
            {
                var emailAttribute = new EmailAddressAttribute();
                if (!emailAttribute.IsValid(Email))
                {
                    ErrorMessage = "البريد الإلكتروني غير صحيح";
                    return;
                }
            }

            ClearError();
        }

        protected override void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == nameof(Name) || 
                propertyName == nameof(Email))
            {
                ValidateAllProperties();
            }
        }
    }
}
