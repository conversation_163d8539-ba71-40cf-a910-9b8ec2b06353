namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج إحصائيات كشوف الحساب
    /// </summary>
    public class AccountStatementStatistics
    {
        public int TotalStatements { get; set; }
        public int PrintedStatements { get; set; }
        public int UnprintedStatements { get; set; }
        public decimal TotalDebitBalance { get; set; }
        public decimal TotalCreditBalance { get; set; }
        public int DebtorEntities { get; set; }
        public int CreditorEntities { get; set; }
        public Dictionary<string, int> StatementsByEntityType { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> StatementsByMonth { get; set; } = new Dictionary<string, int>();
    }
} 