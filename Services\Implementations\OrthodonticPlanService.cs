using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تطبيق خدمة إدارة خطط علاج التقويم
    /// </summary>
    public class OrthodonticPlanService : IOrthodonticPlanService
    {
        private readonly AqlanCenterDbContext _context;
        private readonly IAppointmentService _appointmentService;

        public OrthodonticPlanService(AqlanCenterDbContext context, IAppointmentService appointmentService)
        {
            _context = context;
            _appointmentService = appointmentService;
        }

        #region العمليات الأساسية (CRUD)

        public async Task<IEnumerable<OrthodonticPlan>> GetAllPlansAsync()
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<OrthodonticPlan?> GetPlanByIdAsync(int id)
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments.OrderBy(a => a.AppointmentDateTime))
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetPlansByPatientAsync(int patientId)
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => p.PatientId == patientId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetPlansByDoctorAsync(int doctorId)
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Appointments)
                .Where(p => p.DoctorId == doctorId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<OrthodonticPlan> CreatePlanAsync(OrthodonticPlan plan)
        {
            // التحقق من صحة البيانات
            var (isValid, errorMessage) = await ValidatePlanAsync(plan);
            if (!isValid)
            {
                throw new InvalidOperationException(errorMessage);
            }

            plan.CreatedAt = DateTime.Now;
            plan.Status = "نشطة";
            plan.CompletedSessions = 0;

            _context.OrthodonticPlans.Add(plan);
            await _context.SaveChangesAsync();

            return plan;
        }

        public async Task<OrthodonticPlan> UpdatePlanAsync(OrthodonticPlan plan)
        {
            var existingPlan = await _context.OrthodonticPlans.FindAsync(plan.Id);
            if (existingPlan == null)
            {
                throw new InvalidOperationException("خطة التقويم غير موجودة");
            }

            // التحقق من صحة البيانات
            var (isValid, errorMessage) = await ValidatePlanAsync(plan);
            if (!isValid)
            {
                throw new InvalidOperationException(errorMessage);
            }

            existingPlan.TreatmentType = plan.TreatmentType;
            existingPlan.TreatmentDescription = plan.TreatmentDescription;
            existingPlan.StartDate = plan.StartDate;
            existingPlan.ExpectedEndDate = plan.ExpectedEndDate;
            existingPlan.TotalSessions = plan.TotalSessions;
            existingPlan.SessionIntervalDays = plan.SessionIntervalDays;
            existingPlan.SessionDurationMinutes = plan.SessionDurationMinutes;
            existingPlan.Status = plan.Status;
            existingPlan.Notes = plan.Notes;
            existingPlan.LastUpdated = DateTime.Now;

            await _context.SaveChangesAsync();

            return existingPlan;
        }

        public async Task<bool> DeletePlanAsync(int id)
        {
            var plan = await _context.OrthodonticPlans
                .Include(p => p.Appointments)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (plan == null) return false;

            // حذف جميع المواعيد المرتبطة
            _context.Appointments.RemoveRange(plan.Appointments);
            _context.OrthodonticPlans.Remove(plan);
            await _context.SaveChangesAsync();

            return true;
        }

        #endregion

        #region إدارة المواعيد التلقائية

        public async Task<bool> GenerateAppointmentsAsync(int planId)
        {
            var plan = await GetPlanByIdAsync(planId);
            if (plan == null) return false;

            // التحقق من عدم إنشاء المواعيد مسبقاً
            if (plan.AppointmentsGenerated)
            {
                throw new InvalidOperationException("تم إنشاء المواعيد لهذه الخطة مسبقاً");
            }

            var currentDate = plan.StartDate;
            var appointmentsCreated = 0;

            for (int i = 0; i < plan.TotalSessions; i++)
            {
                // التحقق من توفر الطبيب
                var isAvailable = await IsDoctorAvailableAsync(plan.DoctorId, currentDate, plan.SessionDurationMinutes)
                    .ConfigureAwait(false);
                if (!isAvailable)
                {
                    // البحث عن وقت متاح في نفس اليوم أو اليوم التالي
                    currentDate = await FindNextAvailableTimeAsync(plan.DoctorId, currentDate, plan.SessionDurationMinutes)
                        .ConfigureAwait(false);
                }

                var appointment = new Appointment
                {
                    PatientId = plan.PatientId,
                    DoctorId = plan.DoctorId,
                    ServiceType = "تقويم",
                    AppointmentType = "جلسة تقويم",
                    Status = "مجدول",
                    AppointmentDateTime = currentDate,
                    DurationMinutes = plan.SessionDurationMinutes,
                    Notes = $"جلسة {i + 1} من {plan.TotalSessions} - {plan.TreatmentType}",
                    IsOrthodonticPlan = true,
                    OrthodonticPlanId = plan.Id,
                    CreatedAt = DateTime.Now
                };

                _context.Appointments.Add(appointment);
                appointmentsCreated++;

                // الانتقال للجلسة التالية
                currentDate = currentDate.AddDays(plan.SessionIntervalDays);
            }

            // تحديث حالة الخطة
            plan.AppointmentsGenerated = true;
            plan.AppointmentsGeneratedDate = DateTime.Now;
            plan.LastUpdated = DateTime.Now;

            await _context.SaveChangesAsync();

            return appointmentsCreated == plan.TotalSessions;
        }

        public async Task<Appointment?> CreateNextAppointmentAsync(int planId)
        {
            var plan = await GetPlanByIdAsync(planId);
            if (plan == null || plan.CompletedSessions >= plan.TotalSessions) return null;

            var nextSessionDate = plan.NextSessionDate;
            if (nextSessionDate == null) return null;

            // التحقق من توفر الطبيب
            var isAvailable = await IsDoctorAvailableAsync(plan.DoctorId, nextSessionDate.Value, plan.SessionDurationMinutes)
                .ConfigureAwait(false);
            if (!isAvailable)
            {
                nextSessionDate = await FindNextAvailableTimeAsync(plan.DoctorId, nextSessionDate.Value, plan.SessionDurationMinutes)
                    .ConfigureAwait(false);
            }

            var appointment = new Appointment
            {
                PatientId = plan.PatientId,
                DoctorId = plan.DoctorId,
                ServiceType = "تقويم",
                AppointmentType = "جلسة تقويم",
                Status = "مجدول",
                AppointmentDateTime = nextSessionDate.Value,
                DurationMinutes = plan.SessionDurationMinutes,
                Notes = $"جلسة {plan.CompletedSessions + 1} من {plan.TotalSessions} - {plan.TreatmentType}",
                IsOrthodonticPlan = true,
                OrthodonticPlanId = plan.Id,
                CreatedAt = DateTime.Now
            };

            _context.Appointments.Add(appointment);
            await _context.SaveChangesAsync();

            return appointment;
        }

        public async Task UpdatePlanProgressAsync(int planId)
        {
            var plan = await GetPlanByIdAsync(planId);
            if (plan == null) return;

            var completedSessions = plan.Appointments.Count(a => a.Status == "مكتمل");
            plan.CompletedSessions = completedSessions;

            // تحديث حالة الخطة
            if (completedSessions >= plan.TotalSessions)
            {
                plan.Status = "مكتملة";
            }
            else if (plan.ExpectedEndDate < DateTime.Now)
            {
                plan.Status = "متأخرة";
            }

            plan.LastUpdated = DateTime.Now;
            await _context.SaveChangesAsync();
        }

        public async Task<bool> RescheduleRemainingAppointmentsAsync(int planId, DateTime newStartDate)
        {
            var plan = await GetPlanByIdAsync(planId);
            if (plan == null) return false;

            var remainingAppointments = plan.Appointments
                .Where(a => a.Status == "مجدول" && a.AppointmentDateTime > DateTime.Now)
                .OrderBy(a => a.AppointmentDateTime)
                .ToList();

            var currentDate = newStartDate;

            foreach (var appointment in remainingAppointments)
            {
                // التحقق من توفر الطبيب
                var isAvailable = await IsDoctorAvailableAsync(plan.DoctorId, currentDate, plan.SessionDurationMinutes)
                    .ConfigureAwait(false);
                if (!isAvailable)
                {
                    currentDate = await FindNextAvailableTimeAsync(plan.DoctorId, currentDate, plan.SessionDurationMinutes)
                        .ConfigureAwait(false);
                }

                appointment.AppointmentDateTime = currentDate;
                appointment.UpdatedAt = DateTime.Now;

                currentDate = currentDate.AddDays(plan.SessionIntervalDays);
            }

            plan.StartDate = newStartDate;
            plan.LastUpdated = DateTime.Now;

            await _context.SaveChangesAsync()
                .ConfigureAwait(false);
            return true;
        }

        #endregion

        #region البحث والفلترة

        public async Task<IEnumerable<OrthodonticPlan>> SearchPlansAsync(string searchTerm)
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => (p.Patient != null && p.Patient.FullName.Contains(searchTerm)) ||
                           (p.Doctor != null && p.Doctor.FullName.Contains(searchTerm)) ||
                           p.TreatmentType.Contains(searchTerm) ||
                           (p.TreatmentDescription != null && p.TreatmentDescription.Contains(searchTerm)))
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetActivePlansAsync()
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => p.Status == "نشطة")
                .OrderBy(p => p.StartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetCompletedPlansAsync()
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => p.Status == "مكتملة")
                .OrderByDescending(p => p.LastUpdated)
                .ToListAsync();
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetOverduePlansAsync()
        {
            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => p.Status == "نشطة" && p.ExpectedEndDate < DateTime.Now)
                .OrderBy(p => p.ExpectedEndDate)
                .ToListAsync();
        }

        #endregion

        #region الإحصائيات والتقارير

        public async Task<OrthodonticStatistics> GetStatisticsAsync()
        {
            var plans = await GetAllPlansAsync();
            var appointments = await _context.Appointments
                .Where(a => a.IsOrthodonticPlan)
                .ToListAsync();

            var stats = new OrthodonticStatistics
            {
                TotalPlans = plans.Count(),
                ActivePlans = plans.Count(p => p.Status == "نشطة"),
                CompletedPlans = plans.Count(p => p.Status == "مكتملة"),
                OverduePlans = plans.Count(p => p.Status == "متأخرة"),
                TotalSessions = appointments.Count,
                CompletedSessions = appointments.Count(a => a.Status == "مكتمل"),
                MissedSessions = appointments.Count(a => a.Status == "لم يحضر"),
                PatientsWithActivePlans = plans.Where(p => p.Status == "نشطة").Select(p => p.PatientId).Distinct().Count()
            };

            // حساب متوسط نسبة التقدم
            var activePlans = plans.Where(p => p.Status == "نشطة").ToList();
            if (activePlans.Any())
            {
                stats.AverageProgressPercentage = activePlans.Average(p => p.ProgressPercentage);
            }

            // إحصائيات حسب الحالة
            stats.PlansByStatus = plans.GroupBy(p => p.Status)
                .ToDictionary(g => g.Key, g => g.Count());

            // إحصائيات الجلسات حسب الشهر
            var currentYear = DateTime.Now.Year;
            for (int month = 1; month <= 12; month++)
            {
                var monthName = new DateTime(currentYear, month, 1).ToString("MMMM");
                var sessionsInMonth = appointments.Count(a => a.AppointmentDateTime.Month == month && a.AppointmentDateTime.Year == currentYear);
                stats.SessionsByMonth[monthName] = sessionsInMonth;
            }

            return stats;
        }

        public async Task<IEnumerable<OrthodonticPlan>> GetPlansNeedingFollowUpAsync()
        {
            var today = DateTime.Today;
            var nextWeek = today.AddDays(7);

            return await _context.OrthodonticPlans
                .Include(p => p.Patient)
                .Include(p => p.Doctor)
                .Include(p => p.Appointments)
                .Where(p => p.Status == "نشطة" &&
                           p.Appointments.Any(a => a.Status == "مجدول" &&
                                                   a.AppointmentDateTime >= today &&
                                                   a.AppointmentDateTime <= nextWeek))
                .OrderBy(p => p.Appointments.First(a => a.Status == "مجدول" && a.AppointmentDateTime >= today).AppointmentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Patient>> GetPatientsWithMissedOrthodonticSessionsAsync()
        {
            var missedAppointments = await _context.Appointments
                .Include(a => a.Patient)
                .Where(a => a.IsOrthodonticPlan &&
                           a.Status == "لم يحضر" &&
                           a.AppointmentDateTime < DateTime.Now)
                .ToListAsync();

            return missedAppointments.Select(a => a.Patient).Distinct().ToList();
        }

        #endregion

        #region التحقق من صحة البيانات

        public async Task<bool> IsDoctorAvailableAsync(int doctorId, DateTime dateTime, int durationMinutes)
        {
            return await _appointmentService.IsTimeSlotAvailableAsync(doctorId, dateTime, durationMinutes);
        }

        public async Task<(bool IsValid, string ErrorMessage)> ValidatePlanAsync(OrthodonticPlan plan)
        {
            // التحقق من المريض
            var patient = await _context.Patients.FindAsync(plan.PatientId);
            if (patient == null)
            {
                return (false, "المريض غير موجود");
            }

            // التحقق من الطبيب
            var doctor = await _context.Doctors.FindAsync(plan.DoctorId);
            if (doctor == null)
            {
                return (false, "الطبيب غير موجود");
            }

            // التحقق من التواريخ
            if (plan.StartDate < DateTime.Today)
            {
                return (false, "تاريخ البداية لا يمكن أن يكون في الماضي");
            }

            if (plan.ExpectedEndDate <= plan.StartDate)
            {
                return (false, "تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
            }

            // التحقق من عدد الجلسات
            if (plan.TotalSessions <= 0 || plan.TotalSessions > 100)
            {
                return (false, "عدد الجلسات يجب أن يكون بين 1 و 100");
            }

            // التحقق من الفترة بين الجلسات
            if (plan.SessionIntervalDays <= 0 || plan.SessionIntervalDays > 365)
            {
                return (false, "الفترة بين الجلسات يجب أن تكون بين 1 و 365 يوم");
            }

            // التحقق من مدة الجلسة
            if (plan.SessionDurationMinutes < 15 || plan.SessionDurationMinutes > 180)
            {
                return (false, "مدة الجلسة يجب أن تكون بين 15 و 180 دقيقة");
            }

            return (true, string.Empty);
        }

        #endregion

        #region الطرق المساعدة

        private async Task<DateTime> FindNextAvailableTimeAsync(int doctorId, DateTime startDate, int durationMinutes)
        {
            var currentDate = startDate;
            var maxAttempts = 30; // البحث لمدة شهر كحد أقصى
            var attempts = 0;

            while (attempts < maxAttempts)
            {
                var isAvailable = await IsDoctorAvailableAsync(doctorId, currentDate, durationMinutes)
                    .ConfigureAwait(false);
                if (isAvailable)
                {
                    return currentDate;
                }

                currentDate = currentDate.AddDays(1);
                attempts++;
            }

            // إذا لم نجد وقت متاح، نعيد التاريخ الأصلي
            return startDate;
        }

        #endregion
    }
}