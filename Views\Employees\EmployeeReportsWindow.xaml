<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقارير الموظفين" Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="تقارير الموظفين" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        
        <ScrollViewer>
            <StackPanel Margin="20">
                <!-- Report Options -->
                <Border Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="خيارات التقرير" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Report Type -->
                            <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                <TextBlock Text="نوع التقرير:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="ReportTypeComboBox" 
                                         ItemsSource="{Binding ReportTypes}"
                                         SelectedItem="{Binding SelectedReportType}"
                                         Width="200" HorizontalAlignment="Right"/>
                            </StackPanel>

                            <!-- Date Range -->
                            <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                <TextBlock Text="الفترة الزمنية:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="DateRangeComboBox"
                                         ItemsSource="{Binding DateRanges}"
                                         SelectedItem="{Binding SelectedDateRange}"
                                         Width="200" HorizontalAlignment="Right"/>
                            </StackPanel>

                            <!-- Department Filter -->
                            <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,10">
                                <TextBlock Text="القسم:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="DepartmentComboBox"
                                         ItemsSource="{Binding Departments}"
                                         SelectedItem="{Binding SelectedDepartment}"
                                         Width="200" HorizontalAlignment="Right"/>
                            </StackPanel>

                            <!-- Status Filter -->
                            <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,10">
                                <TextBlock Text="الحالة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="StatusComboBox"
                                         ItemsSource="{Binding Statuses}"
                                         SelectedItem="{Binding SelectedStatus}"
                                         Width="200" HorizontalAlignment="Right"/>
                            </StackPanel>

                            <!-- Generate Report Button -->
                            <Button Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2"
                                    Content="إنشاء التقرير" 
                                    Command="{Binding GenerateReportCommand}"
                                    Background="#2196F3" Foreground="White"
                                    Width="150" Height="35" Margin="0,10,0,0"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Report Preview -->
                <Border Background="White" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="معاينة التقرير" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        <TextBlock Text="{Binding ReportPreview}" 
                                   TextWrapping="Wrap" 
                                   FontSize="14" 
                                   Foreground="#666"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <Button Content="إغلاق" Width="100" Height="35" Margin="0,0,20,20" DockPanel.Dock="Bottom" HorizontalAlignment="Left" Click="Close_Click"/>
    </DockPanel>
</Window> 