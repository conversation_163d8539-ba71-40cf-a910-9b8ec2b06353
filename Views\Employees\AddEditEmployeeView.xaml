<Window x:Class="AqlanCenterProApp.Views.Employees.AddEditEmployeeView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل موظف" Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Button Styles -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#2E7D32"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#9E9E9E"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#757575"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- TextBox Style -->
        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <TextBox Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Text, UpdateSourceTrigger=PropertyChanged}"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         Padding="{TemplateBinding Padding}"
                                         FontSize="{TemplateBinding FontSize}"
                                         VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <!-- DatePicker Style -->
        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="FormLabelStyle" TargetType="Label">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- TabControl Style -->
        <Style x:Key="ModernTabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
        </Style>

        <!-- TabItem Style -->
        <Style x:Key="ModernTabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1,1,1,0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6,6,0,0">
                            <ContentPresenter ContentSource="Header" 
                                            HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="White"/>
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="Foreground" Value="#2196F3"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="0,0,0,1" Padding="20,15">
            <TextBlock Text="{Binding WindowTitle}" 
                       FontSize="20" 
                       FontWeight="Bold" 
                       Foreground="#333333"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Tab Control -->
        <TabControl Grid.Row="1" Style="{StaticResource ModernTabControlStyle}">
            
            <!-- Basic Information Tab -->
            <TabItem Header="المعلومات الأساسية" Style="{StaticResource ModernTabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Personal Information Section -->
                        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" 
                                   Text="المعلومات الشخصية" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2196F3"
                                   Margin="0,0,0,15"/>

                        <!-- First Name -->
                        <Label Grid.Row="1" Grid.Column="0" 
                               Content="الاسم الأول:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="0" 
                                 Text="{Binding Employee.FirstName, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Last Name -->
                        <Label Grid.Row="1" Grid.Column="1" 
                               Content="اسم العائلة:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" 
                                 Text="{Binding Employee.LastName, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Employee Number -->
                        <Label Grid.Row="3" Grid.Column="0" 
                               Content="الرقم الوظيفي:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="4" Grid.Column="0" 
                                 Text="{Binding Employee.EmployeeNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- National ID -->
                        <Label Grid.Row="3" Grid.Column="1" 
                               Content="رقم الهوية الوطنية:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="4" Grid.Column="1" 
                                 Text="{Binding Employee.NationalId, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Contact Information Section -->
                        <TextBlock Grid.Row="5" Grid.ColumnSpan="2" 
                                   Text="معلومات الاتصال" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2196F3"
                                   Margin="0,20,0,15"/>

                        <!-- Phone Number -->
                        <Label Grid.Row="6" Grid.Column="0" 
                               Content="رقم الهاتف:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="7" Grid.Column="0" 
                                 Text="{Binding Employee.PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Email -->
                        <Label Grid.Row="6" Grid.Column="1" 
                               Content="البريد الإلكتروني:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="7" Grid.Column="1" 
                                 Text="{Binding Employee.Email, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Address -->
                        <Label Grid.Row="8" Grid.ColumnSpan="2" 
                               Content="العنوان:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="9" Grid.ColumnSpan="2" 
                                 Text="{Binding Employee.Address, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Employment Information Section -->
                        <TextBlock Grid.Row="10" Grid.ColumnSpan="2" 
                                   Text="معلومات التوظيف" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2196F3"
                                   Margin="0,20,0,15"/>

                        <!-- Department -->
                        <Label Grid.Row="11" Grid.Column="0" 
                               Content="القسم:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Grid.Row="12" Grid.Column="0" 
                                  ItemsSource="{Binding Departments}"
                                  SelectedItem="{Binding Employee.Department}"
                                  DisplayMemberPath="Name"
                                  Style="{StaticResource FormComboBoxStyle}"/>

                        <!-- Position -->
                        <Label Grid.Row="11" Grid.Column="1" 
                               Content="المنصب:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Grid.Row="12" Grid.Column="1" 
                                  ItemsSource="{Binding Positions}"
                                  SelectedItem="{Binding Employee.Position}"
                                  DisplayMemberPath="Name"
                                  Style="{StaticResource FormComboBoxStyle}"/>

                        <!-- Hire Date -->
                        <Label Grid.Row="13" Grid.Column="0" 
                               Content="تاريخ التعيين:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Grid.Row="14" Grid.Column="0" 
                                    SelectedDate="{Binding Employee.HireDate}"
                                    Style="{StaticResource FormDatePickerStyle}"/>

                        <!-- Status -->
                        <Label Grid.Row="13" Grid.Column="1" 
                               Content="الحالة:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Grid.Row="14" Grid.Column="1" 
                                  ItemsSource="{Binding Statuses}"
                                  SelectedItem="{Binding Employee.Status}"
                                  Style="{StaticResource FormComboBoxStyle}"/>

                        <!-- Is Active -->
                        <Label Grid.Row="15" Grid.Column="0" 
                               Content="نشط:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <CheckBox Grid.Row="16" Grid.Column="0" 
                                  IsChecked="{Binding Employee.IsActive}"
                                  VerticalAlignment="Center"
                                  Margin="0,0,0,10"/>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- Salary Information Tab -->
            <TabItem Header="معلومات الراتب" Style="{StaticResource ModernTabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Basic Salary Section -->
                        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" 
                                   Text="الراتب الأساسي" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#4CAF50"
                                   Margin="0,0,0,15"/>

                        <!-- Basic Salary -->
                        <Label Grid.Row="1" Grid.Column="0" 
                               Content="الراتب الأساسي (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="0" 
                                 Text="{Binding Employee.BasicSalary, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Currency Display -->
                        <Label Grid.Row="1" Grid.Column="1" 
                               Content="العملة:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" 
                                 Text="ريال يمني" 
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Allowances Section -->
                        <TextBlock Grid.Row="3" Grid.ColumnSpan="2" 
                                   Text="البدلات" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#FF9800"
                                   Margin="0,20,0,15"/>

                        <!-- Housing Allowance -->
                        <Label Grid.Row="4" Grid.Column="0" 
                               Content="بدل السكن (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="5" Grid.Column="0" 
                                 Text="{Binding Employee.HousingAllowance, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Transportation Allowance -->
                        <Label Grid.Row="4" Grid.Column="1" 
                               Content="بدل النقل (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="5" Grid.Column="1" 
                                 Text="{Binding Employee.TransportationAllowance, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Food Allowance -->
                        <Label Grid.Row="6" Grid.Column="0" 
                               Content="بدل الطعام (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="7" Grid.Column="0" 
                                 Text="{Binding Employee.FoodAllowance, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Other Allowances -->
                        <Label Grid.Row="6" Grid.Column="1" 
                               Content="بدلات أخرى (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="7" Grid.Column="1" 
                                 Text="{Binding Employee.OtherAllowances, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Deductions Section -->
                        <TextBlock Grid.Row="8" Grid.ColumnSpan="2" 
                                   Text="الخصومات" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#F44336"
                                   Margin="0,20,0,15"/>

                        <!-- Insurance Deduction -->
                        <Label Grid.Row="9" Grid.Column="0" 
                               Content="خصم التأمين (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="10" Grid.Column="0" 
                                 Text="{Binding Employee.InsuranceDeduction, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Tax Deduction -->
                        <Label Grid.Row="9" Grid.Column="1" 
                               Content="خصم الضريبة (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="10" Grid.Column="1" 
                                 Text="{Binding Employee.TaxDeduction, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Other Deductions -->
                        <Label Grid.Row="11" Grid.Column="0" 
                               Content="خصومات أخرى (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="12" Grid.Column="0" 
                                 Text="{Binding Employee.OtherDeductions, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Total Section -->
                        <TextBlock Grid.Row="13" Grid.ColumnSpan="2" 
                                   Text="الإجمالي" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2196F3"
                                   Margin="0,20,0,15"/>

                        <!-- Total Salary -->
                        <Label Grid.Row="14" Grid.Column="0" 
                               Content="إجمالي الراتب (ريال يمني):" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="15" Grid.Column="0" 
                                 Text="{Binding TotalSalary, Mode=OneWay}"
                                 IsReadOnly="True"
                                 Background="#E8F5E8"
                                 FontWeight="Bold"
                                 Style="{StaticResource FormTextBoxStyle}"/>

                        <!-- Salary Notes -->
                        <Label Grid.Row="14" Grid.Column="1" 
                               Content="ملاحظات الراتب:" 
                               Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Grid.Row="15" Grid.Column="1" 
                                 Text="{Binding Employee.SalaryNotes, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource FormTextBoxStyle}"
                                 Height="60"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#DDDDDD" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="حفظ" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Command="{Binding SaveCommand}"
                        Width="100"/>
                <Button Content="إلغاء" 
                        Style="{StaticResource CancelButtonStyle}"
                        Command="{Binding CancelCommand}"
                        Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 