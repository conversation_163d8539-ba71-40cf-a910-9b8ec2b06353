using System.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace AqlanCenterProApp.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IServiceProvider? _serviceProvider;

        public LoginWindow()
        {
            InitializeComponent();
        }

        public LoginWindow(IServiceProvider serviceProvider) : this()
        {
            _serviceProvider = serviceProvider;
        }

        private void OnLoginClick(object sender, RoutedEventArgs e)
        {
            var username = UsernameTextBox.Text.Trim();
            var password = PasswordBox.Password;

            // تحقق بسيط من بيانات تسجيل الدخول
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                ShowError("يرجى إدخال اسم المستخدم وكلمة المرور");
                return;
            }

            // تحقق من البيانات الافتراضية
            if (username == "admin" && password == "admin123")
            {
                // فتح النافذة الرئيسية
                if (_serviceProvider != null)
                {
                    var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowError("خطأ في النظام - يرجى إعادة تشغيل التطبيق");
                }
            }
            else
            {
                ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }
    }
}
