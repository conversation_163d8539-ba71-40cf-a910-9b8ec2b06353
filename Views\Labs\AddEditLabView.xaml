<Window x:Class="AqlanCenterProApp.Views.Labs.AddEditLabView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding WindowTitle}"
        Height="600" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#2E7D32"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#424242"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="{Binding WindowTitle}" 
                   FontSize="20" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#333333"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم المعمل -->
                <TextBlock Text="اسم المعمل *" 
                           FontWeight="SemiBold" 
                           Margin="0,10,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"/>

                <!-- العنوان -->
                <TextBlock Text="العنوان" 
                           FontWeight="SemiBold" 
                           Margin="0,15,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"/>

                <!-- رقم الهاتف -->
                <TextBlock Text="رقم الهاتف" 
                           FontWeight="SemiBold" 
                           Margin="0,15,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"/>

                <!-- رقم الواتساب -->
                <TextBlock Text="رقم الواتساب" 
                           FontWeight="SemiBold" 
                           Margin="0,15,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding WhatsApp, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"/>

                <!-- البريد الإلكتروني -->
                <TextBlock Text="البريد الإلكتروني" 
                           FontWeight="SemiBold" 
                           Margin="0,15,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"/>

                <!-- الملاحظات -->
                <TextBlock Text="الملاحظات" 
                           FontWeight="SemiBold" 
                           Margin="0,15,0,5"
                           Foreground="#333333"/>
                <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ModernTextBoxStyle}"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>

                <!-- الحالة -->
                <CheckBox Content="معمل نشط" 
                          IsChecked="{Binding IsActive}" 
                          Margin="0,15,0,0"
                          FontWeight="SemiBold"
                          Foreground="#333333"/>
            </StackPanel>
        </ScrollViewer>

        <!-- رسالة الخطأ -->
        <Border Grid.Row="2" 
                Background="#FFEBEE" 
                BorderBrush="#F44336" 
                BorderThickness="1" 
                CornerRadius="4" 
                Padding="10" 
                Margin="0,10,0,0"
                Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}">
            <TextBlock Text="{Binding ErrorMessage}" 
                       Foreground="#D32F2F" 
                       FontWeight="SemiBold"/>
        </Border>

        <!-- الأزرار -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button Content="حفظ" 
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource SuccessButtonStyle}"/>
            <Button Content="إلغاء" 
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource CancelButtonStyle}"/>
            <Button Content="إعادة تعيين" 
                    Command="{Binding ResetCommand}"
                    Style="{StaticResource ModernButtonStyle}"/>
        </StackPanel>
    </Grid>
</Window> 