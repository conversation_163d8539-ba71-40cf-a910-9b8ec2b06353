using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.ViewModels.Patients;

namespace AqlanCenterProApp.Views.Patients;

/// <summary>
/// نافذة إضافة وتعديل المرضى
/// </summary>
public partial class AddEditPatientView : Window
{
    private readonly AddEditPatientViewModel _viewModel;

    public AddEditPatientView(AddEditPatientViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        DataContext = _viewModel;

        // ربط الأحداث
        _viewModel.PatientSaved += OnPatientSaved;
        _viewModel.Cancelled += OnCancelled;

        // تركيز على أول حقل عند تحميل النافذة
        Loaded += (s, e) => MoveFocus(new TraversalRequest(FocusNavigationDirection.First));
    }

    /// <summary>
    /// معالج حفظ المريض بنجاح
    /// </summary>
    private void OnPatientSaved(object? sender, Models.Patient patient)
    {
        DialogResult = true;
        Close();
    }

    /// <summary>
    /// معالج إلغاء العملية
    /// </summary>
    private void OnCancelled(object? sender, EventArgs e)
    {
        DialogResult = false;
        Close();
    }

    /// <summary>
    /// تنظيف الموارد
    /// </summary>
    protected override void OnClosed(EventArgs e)
    {
        _viewModel.PatientSaved -= OnPatientSaved;
        _viewModel.Cancelled -= OnCancelled;
        base.OnClosed(e);
    }

    /// <summary>
    /// الحصول على المريض المحفوظ
    /// </summary>
    public Models.Patient? SavedPatient { get; private set; }

    /// <summary>
    /// تحميل بيانات مريض للتعديل
    /// </summary>
    public async Task LoadPatientAsync(int patientId)
    {
        await _viewModel.LoadPatientAsync(patientId);
    }
}
