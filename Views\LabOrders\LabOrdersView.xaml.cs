using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.LabOrders;

namespace AqlanCenterProApp.Views.LabOrders
{
    /// <summary>
    /// Interaction logic for LabOrdersView.xaml
    /// </summary>
    public partial class LabOrdersView : UserControl
    {
        public LabOrdersView()
        {
            InitializeComponent();
        }

        public LabOrdersView(LabOrdersListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += LabOrdersView_Loaded;
        }

        private void LabOrdersView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is LabOrdersListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadLabOrdersAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadLabOrdersAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات طلبات المعمل: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في LabOrdersView_Loaded: {ex.Message}");
            }
        }
    }
}