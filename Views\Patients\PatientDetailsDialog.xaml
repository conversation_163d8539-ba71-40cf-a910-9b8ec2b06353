<Window x:Class="AqlanCenterProApp.Views.Patients.PatientDetailsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل المريض" Height="440" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="ToolWindow"
        Background="{StaticResource WhiteBrush}">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <TextBlock Text="تفاصيل المريض" FontSize="22" FontWeight="Bold" Foreground="{StaticResource PrimaryBlueBrush}" HorizontalAlignment="Center" Margin="0,0,0,18"/>

        <!-- تفاصيل المريض -->
        <StackPanel Grid.Row="1" Margin="0,0,0,10">
            <TextBlock Text="{Binding FullName}" FontSize="18" FontWeight="SemiBold" Foreground="{StaticResource PrimaryBlueBrush}" Margin="0,0,0,8"/>
            <TextBlock Text="{Binding FileNumber, StringFormat=رقم الملف: {0}}" FontSize="15" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding Phone, StringFormat=الهاتف: {0}}" FontSize="15" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding PatientCategory, StringFormat=التصنيف: {0}}" FontSize="15" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding FileStatus, StringFormat=حالة الملف: {0}}" FontSize="15" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding CurrentBalance, Converter={StaticResource CurrencyConverter}, StringFormat=الرصيد الحالي: {0}}" FontSize="15" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding RegistrationDate, Converter={StaticResource ArabicDateConverter}, StringFormat=تاريخ التسجيل: {0}}" FontSize="15" Margin="0,0,0,4"/>
        </StackPanel>

        <!-- أزرار سريعة -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
            <Button Content="📱 واتساب" Width="110" Height="36" Margin="8,0" Click="WhatsApp_Click"/>
            <Button Content="🖨️ طباعة" Width="110" Height="36" Margin="8,0" Click="Print_Click"/>
            <Button Content="📋 نسخ الهاتف" Width="120" Height="36" Margin="8,0" Click="CopyPhone_Click"/>
            <Button Content="إغلاق" Width="100" Height="36" Margin="8,0" Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window> 