using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج خطة علاج التقويم
    /// </summary>
    public class OrthodonticPlan : BaseEntity
    {
        [Key]
        public new int Id { get; set; }

        /// <summary>
        /// معرف المريض
        /// </summary>
        [Required]
        public int PatientId { get; set; }
        public Patient Patient { get; set; } = null!;

        /// <summary>
        /// معرف الطبيب
        /// </summary>
        [Required]
        public int DoctorId { get; set; }
        public Doctor Doctor { get; set; } = null!;

        /// <summary>
        /// تاريخ بداية العلاج
        /// </summary>
        [Required]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية العلاج المتوقع
        /// </summary>
        [Required]
        public DateTime ExpectedEndDate { get; set; }

        /// <summary>
        /// عدد الجلسات المخططة
        /// </summary>
        [Required]
        [Range(1, 100)]
        public int TotalSessions { get; set; }

        /// <summary>
        /// عدد الجلسات المكتملة
        /// </summary>
        public int CompletedSessions { get; set; } = 0;

        /// <summary>
        /// الفترة بين الجلسات بالأيام
        /// </summary>
        [Required]
        [Range(1, 365)]
        public int SessionIntervalDays { get; set; }

        /// <summary>
        /// مدة كل جلسة بالدقائق
        /// </summary>
        [Required]
        [Range(15, 180)]
        public int SessionDurationMinutes { get; set; } = 30;

        /// <summary>
        /// نوع العلاج
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string TreatmentType { get; set; } = string.Empty;

        /// <summary>
        /// وصف العلاج
        /// </summary>
        [MaxLength(1000)]
        public string? TreatmentDescription { get; set; }

        /// <summary>
        /// حالة الخطة (نشطة/مكتملة/ملغية)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "نشطة";

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(2000)]
        public new string? Notes { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// هل تم إنشاء المواعيد تلقائياً
        /// </summary>
        public bool AppointmentsGenerated { get; set; } = false;

        /// <summary>
        /// تاريخ إنشاء المواعيد
        /// </summary>
        public DateTime? AppointmentsGeneratedDate { get; set; }

        /// <summary>
        /// الجلسات المرتبطة بهذه الخطة
        /// </summary>
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();

        /// <summary>
        /// حساب النسبة المئوية للتقدم
        /// </summary>
        public double ProgressPercentage => TotalSessions > 0 ? (double)CompletedSessions / TotalSessions * 100 : 0;

        /// <summary>
        /// حساب عدد الجلسات المتبقية
        /// </summary>
        public int RemainingSessions => TotalSessions - CompletedSessions;

        /// <summary>
        /// حساب تاريخ الجلسة التالية
        /// </summary>
        public DateTime? NextSessionDate
        {
            get
            {
                if (CompletedSessions >= TotalSessions) return null;

                var lastAppointment = Appointments
                    .Where(a => a.Status == "مكتمل")
                    .OrderByDescending(a => a.AppointmentDateTime)
                    .FirstOrDefault();

                if (lastAppointment != null)
                {
                    return lastAppointment.AppointmentDateTime.AddDays(SessionIntervalDays);
                }

                return StartDate;
            }
        }
    }
}