using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Suppliers;

namespace AqlanCenterProApp.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SuppliersListView.xaml
    /// </summary>
    public partial class SuppliersListView : UserControl
    {
        public SuppliersListView()
        {
            InitializeComponent();
        }

        public SuppliersListView(SuppliersListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += SuppliersListView_Loaded;
        }

        private void SuppliersListView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is SuppliersListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadSuppliersAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadSuppliersAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات الموردين: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SuppliersListView_Loaded: {ex.Message}");
            }
        }
    }
}