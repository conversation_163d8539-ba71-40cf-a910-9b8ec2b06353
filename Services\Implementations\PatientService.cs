using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Helpers;
using System.Text.RegularExpressions;

namespace AqlanCenterProApp.Services.Implementations;

/// <summary>
/// تطبيق خدمة إدارة المرضى
/// يحتوي على جميع العمليات المطلوبة لإدارة بيانات المرضى مع نظام الترقيم المحدد
/// </summary>
public class PatientService : IPatientService
{
    private readonly AqlanCenterDbContext _context;

    // ثوابت نظام ترقيم المرضى
    private const int NEW_PATIENT_START_NUMBER = 8500;
    private const int OLD_PATIENT_MAX_NUMBER = 8499;

    // نمط رقم الهاتف اليمني
    private static readonly Regex YemeniPhoneRegex = new(@"^(77|73|70|71)\d{7}$", RegexOptions.Compiled);

    public PatientService(AqlanCenterDbContext context)
    {
        _context = context;
    }

    #region العمليات الأساسية (CRUD)

    public async Task<(List<Patient> Patients, int TotalCount, int TotalPages)> GetPatientsAsync(
        int pageNumber = 1,
        int pageSize = 50,
        string? searchTerm = null,
        string? category = null,
        string? fileStatus = null,
        bool includeDeleted = false)
    {
        try
        {
            var query = _context.Patients.AsQueryable();

            // تطبيق فلتر المحذوفين
            if (!includeDeleted)
            {
                query = query.Where(p => !p.IsDeleted);
            }

            // تطبيق البحث
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var search = searchTerm.Trim().ToLower();
                query = query.Where(p =>
                    p.FullName.ToLower().Contains(search) ||
                    (p.Phone != null && p.Phone.Contains(search)) ||
                    (p.Mobile != null && p.Mobile.Contains(search)) ||
                    p.Id.ToString().Contains(search));
            }

            // تطبيق فلتر التصنيف
            if (!string.IsNullOrWhiteSpace(category))
            {
                query = query.Where(p => p.PatientCategory == category);
            }

            // تطبيق فلتر حالة الملف
            if (!string.IsNullOrWhiteSpace(fileStatus))
            {
                query = query.Where(p => p.FileStatus == fileStatus);
            }

            // حساب العدد الكلي
            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            // تطبيق الترقيم والترتيب
            var patients = await query
                .OrderByDescending(p => p.RegistrationDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (patients, totalCount, totalPages);
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ
            await LogActivityAsync("GetPatientsAsync", $"خطأ في جلب قائمة المرضى: {ex.Message}", "System");
            throw new Exception($"خطأ في جلب قائمة المرضى: {ex.Message}", ex);
        }
    }

    public async Task<Patient?> GetPatientByIdAsync(int patientId, bool includeRelated = false)
    {
        try
        {
            var query = _context.Patients.AsQueryable();

            if (includeRelated)
            {
                query = query
                    .Include(p => p.Sessions)
                    .Include(p => p.Payments)
                    .Include(p => p.Appointments)
                    .Include(p => p.PatientFiles)
                    .Include(p => p.LabOrders);
            }

            return await query.FirstOrDefaultAsync(p => p.Id == patientId && !p.IsDeleted)
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetPatientByIdAsync", $"خطأ في جلب بيانات المريض {patientId}: {ex.Message}", "System")
                .ConfigureAwait(false);
            throw new Exception($"خطأ في جلب بيانات المريض: {ex.Message}", ex);
        }
    }

    public async Task<Patient?> GetPatientByFileNumberAsync(int fileNumber, bool includeRelated = false)
    {
        try
        {
            var query = _context.Patients.AsQueryable();

            if (includeRelated)
            {
                query = query
                    .Include(p => p.Sessions)
                    .Include(p => p.Payments)
                    .Include(p => p.Appointments)
                    .Include(p => p.PatientFiles)
                    .Include(p => p.LabOrders);
            }

            return await query.FirstOrDefaultAsync(p => p.Id == fileNumber && !p.IsDeleted)
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetPatientByFileNumberAsync", $"خطأ في جلب بيانات المريض برقم الملف {fileNumber}: {ex.Message}", "System")
                .ConfigureAwait(false);
            throw new Exception($"خطأ في جلب بيانات المريض: {ex.Message}", ex);
        }
    }

    public async Task<Patient> AddPatientAsync(Patient patient, string createdBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // التحقق من صحة البيانات
            await ValidatePatientDataAsync(patient);

            // تعيين رقم الملف التلقائي إذا لم يكن محدداً
            if (patient.FileNumber == 0)
            {
                patient.FileNumber = await GetNextFileNumberAsync();
            }
            else
            {
                // التحقق من توفر رقم الملف المحدد
                if (!await IsFileNumberAvailableAsync(patient.FileNumber))
                {
                    throw new InvalidOperationException($"رقم الملف {patient.FileNumber} مستخدم بالفعل");
                }

                if (!IsValidFileNumber(patient.FileNumber))
                {
                    throw new InvalidOperationException($"رقم الملف {patient.FileNumber} غير صحيح");
                }
            }

            // تعيين البيانات الافتراضية
            patient.CreatedAt = DateTime.Now;
            patient.CreatedBy = createdBy;
            patient.IsDeleted = false;
            patient.RegistrationDate = DateTime.Now;

            // إضافة المريض
            _context.Patients.Add(patient);
            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await LogActivityAsync("AddPatient", $"تم إضافة مريض جديد: {patient.FullName} - ملف رقم: {patient.Id}", createdBy);

            await transaction.CommitAsync();
            return patient;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            await LogActivityAsync("AddPatientAsync", $"خطأ في إضافة مريض جديد: {ex.Message}", createdBy);

            var errorMessage = DatabaseErrorHandler.HandleDatabaseError(ex);
            throw new Exception($"خطأ في إضافة المريض: {errorMessage}", ex);
        }
    }

    public async Task<bool> UpdatePatientAsync(Patient patient, string updatedBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var existingPatient = await _context.Patients.FindAsync(patient.Id);
            if (existingPatient == null || existingPatient.IsDeleted)
            {
                throw new InvalidOperationException("المريض غير موجود");
            }

            // التحقق من صحة البيانات
            await ValidatePatientDataAsync(patient, patient.Id);

            // حفظ القيم القديمة للمقارنة
            var oldValues = new
            {
                existingPatient.FullName,
                existingPatient.Phone,
                existingPatient.Mobile,
                existingPatient.PatientCategory,
                existingPatient.FileStatus
            };

            // تحديث البيانات
            existingPatient.FullName = patient.FullName;
            existingPatient.Gender = patient.Gender;
            existingPatient.DateOfBirth = patient.DateOfBirth;
            existingPatient.Phone = patient.Phone;
            existingPatient.Mobile = patient.Mobile;
            existingPatient.Address = patient.Address;
            existingPatient.PatientCategory = patient.PatientCategory;
            existingPatient.FileStatus = patient.FileStatus;
            existingPatient.PatientImage = patient.PatientImage;
            existingPatient.MedicalHistory = patient.MedicalHistory;
            existingPatient.Allergies = patient.Allergies;
            existingPatient.EmergencyContact = patient.EmergencyContact;
            existingPatient.EmergencyPhone = patient.EmergencyPhone;
            existingPatient.Notes = patient.Notes;
            existingPatient.UpdatedAt = DateTime.Now;
            existingPatient.UpdatedBy = updatedBy;

            await _context.SaveChangesAsync();

            // تسجيل التغييرات
            var changes = new List<string>();
            if (oldValues.FullName != patient.FullName) changes.Add($"الاسم: {oldValues.FullName} → {patient.FullName}");
            if (oldValues.Phone != patient.Phone) changes.Add($"الهاتف: {oldValues.Phone} → {patient.Phone}");
            if (oldValues.PatientCategory != patient.PatientCategory) changes.Add($"التصنيف: {oldValues.PatientCategory} → {patient.PatientCategory}");

            if (changes.Any())
            {
                await LogActivityAsync("UpdatePatient", $"تم تحديث بيانات المريض {patient.FullName} - ملف رقم: {patient.Id}\nالتغييرات: {string.Join(", ", changes)}", updatedBy);
            }

            await transaction.CommitAsync();
            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            await LogActivityAsync("UpdatePatientAsync", $"خطأ في تحديث بيانات المريض {patient.Id}: {ex.Message}", updatedBy);

            var errorMessage = DatabaseErrorHandler.HandleDatabaseError(ex);
            throw new Exception($"خطأ في تحديث بيانات المريض: {errorMessage}", ex);
        }
    }

    public async Task<bool> DeletePatientAsync(int patientId, string deletedBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var patient = await _context.Patients.FindAsync(patientId);
            if (patient == null || patient.IsDeleted)
            {
                throw new InvalidOperationException("المريض غير موجود");
            }

            // التحقق من وجود بيانات مرتبطة
            var hasRelatedData = await _context.Sessions.AnyAsync(s => s.PatientId == patientId) ||
                                await _context.Payments.AnyAsync(p => p.PatientId == patientId) ||
                                await _context.Appointments.AnyAsync(a => a.PatientId == patientId);

            if (hasRelatedData)
            {
                // Soft Delete
                patient.IsDeleted = true;
                patient.UpdatedAt = DateTime.Now;
                patient.UpdatedBy = deletedBy;
                patient.FileStatus = "مؤرشف";
            }
            else
            {
                // Hard Delete إذا لم توجد بيانات مرتبطة
                _context.Patients.Remove(patient);
            }

            await _context.SaveChangesAsync();
            await LogActivityAsync("DeletePatient", $"تم حذف المريض: {patient.FullName} - ملف رقم: {patient.Id}", deletedBy);

            await transaction.CommitAsync();
            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            await LogActivityAsync("DeletePatientAsync", $"خطأ في حذف المريض {patientId}: {ex.Message}", deletedBy);

            var errorMessage = DatabaseErrorHandler.HandleDatabaseError(ex);
            throw new Exception($"خطأ في حذف المريض: {errorMessage}", ex);
        }
    }

    public async Task<bool> RestorePatientAsync(int patientId, string restoredBy)
    {
        try
        {
            var patient = await _context.Patients.FindAsync(patientId);
            if (patient == null || !patient.IsDeleted)
            {
                throw new InvalidOperationException("المريض غير موجود أو غير محذوف");
            }

            patient.IsDeleted = false;
            patient.UpdatedAt = DateTime.Now;
            patient.UpdatedBy = restoredBy;
            patient.FileStatus = "نشط";

            await _context.SaveChangesAsync();
            await LogActivityAsync("RestorePatient", $"تم استعادة المريض: {patient.FullName} - ملف رقم: {patient.Id}", restoredBy);

            return true;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("RestorePatientAsync", $"خطأ في استعادة المريض {patientId}: {ex.Message}", restoredBy);
            throw new Exception($"خطأ في استعادة المريض: {ex.Message}", ex);
        }
    }

    #endregion

    #region نظام ترقيم المرضى

    public async Task<int> GetNextFileNumberAsync()
    {
        try
        {
            // البحث عن أعلى رقم ملف للمرضى الجدد (>= 8500)
            var lastFileNumber = await _context.Patients
                .Where(p => p.FileNumber >= NEW_PATIENT_START_NUMBER)
                .MaxAsync(p => (int?)p.FileNumber) ?? (NEW_PATIENT_START_NUMBER - 1);

            return lastFileNumber + 1;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetNextFileNumberAsync", $"خطأ في الحصول على رقم الملف التالي: {ex.Message}", "System");
            // بدلاً من رمي استثناء، إرجاع رقم افتراضي
            return NEW_PATIENT_START_NUMBER;
        }
    }

    public async Task<bool> IsFileNumberAvailableAsync(int fileNumber, int? excludePatientId = null)
    {
        try
        {
            var query = _context.Patients.Where(p => p.FileNumber == fileNumber);

            if (excludePatientId.HasValue)
            {
                query = query.Where(p => p.Id != excludePatientId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("IsFileNumberAvailableAsync", $"خطأ في التحقق من توفر رقم الملف {fileNumber}: {ex.Message}", "System");
            return false;
        }
    }

    public bool IsValidFileNumber(int fileNumber)
    {
        // رقم الملف يجب أن يكون موجباً
        if (fileNumber <= 0) return false;

        // المرضى الجدد: >= 8500
        // المرضى القدامى: 1-8499
        return fileNumber <= OLD_PATIENT_MAX_NUMBER || fileNumber >= NEW_PATIENT_START_NUMBER;
    }

    #endregion

    #region البحث والإحصائيات

    public async Task<List<Patient>> SearchPatientsAsync(PatientSearchCriteria searchCriteria)
    {
        try
        {
            var query = _context.Patients.Where(p => !p.IsDeleted);

            // تطبيق معايير البحث
            if (!string.IsNullOrWhiteSpace(searchCriteria.Name))
            {
                var name = searchCriteria.Name.Trim().ToLower();
                query = query.Where(p => p.FullName.ToLower().Contains(name));
            }

            if (!string.IsNullOrWhiteSpace(searchCriteria.PhoneNumber))
            {
                var phone = searchCriteria.PhoneNumber.Trim();
                query = query.Where(p => p.Phone != null && p.Phone.Contains(phone) ||
                                        p.Mobile != null && p.Mobile.Contains(phone));
            }

            if (searchCriteria.FileNumber.HasValue)
            {
                query = query.Where(p => p.Id == searchCriteria.FileNumber.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchCriteria.Category))
            {
                query = query.Where(p => p.PatientCategory == searchCriteria.Category);
            }

            if (!string.IsNullOrWhiteSpace(searchCriteria.Gender))
            {
                query = query.Where(p => p.Gender == searchCriteria.Gender);
            }

            if (searchCriteria.RegistrationDateFrom.HasValue)
            {
                query = query.Where(p => p.RegistrationDate >= searchCriteria.RegistrationDateFrom.Value);
            }

            if (searchCriteria.RegistrationDateTo.HasValue)
            {
                query = query.Where(p => p.RegistrationDate <= searchCriteria.RegistrationDateTo.Value);
            }

            return await query.OrderByDescending(p => p.RegistrationDate).ToListAsync();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("SearchPatientsAsync", $"خطأ في البحث المتقدم: {ex.Message}", "System");
            throw new Exception($"خطأ في البحث: {ex.Message}", ex);
        }
    }

    public async Task<PatientStatistics> GetPatientStatisticsAsync()
    {
        try
        {
            var stats = new PatientStatistics();

            // الإحصائيات الأساسية
            stats.TotalPatients = await _context.Patients.CountAsync(p => !p.IsDeleted);
            stats.ActivePatients = await _context.Patients.CountAsync(p => !p.IsDeleted && p.FileStatus == "نشط");
            stats.ArchivedPatients = await _context.Patients.CountAsync(p => p.IsDeleted || p.FileStatus == "مؤرشف");

            // المرضى الجدد هذا الشهر
            var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            stats.NewPatientsThisMonth = await _context.Patients
                .CountAsync(p => !p.IsDeleted && p.RegistrationDate >= startOfMonth);

            // إحصائيات الأرصدة (سيتم تطويرها مع جدول المدفوعات)
            stats.DebtorPatients = 0; // مؤقت
            stats.CreditorPatients = 0; // مؤقت
            stats.TotalDebt = 0; // مؤقت
            stats.TotalCredit = 0; // مؤقت

            // التوزيع حسب التصنيف
            stats.PatientsByCategory = await _context.Patients
                .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.PatientCategory))
                .GroupBy(p => p.PatientCategory)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            // التوزيع حسب الجنس
            stats.PatientsByGender = await _context.Patients
                .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.Gender))
                .GroupBy(p => p.Gender)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return stats;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetPatientStatisticsAsync", $"خطأ في جلب الإحصائيات: {ex.Message}", "System");
            throw new Exception($"خطأ في جلب الإحصائيات: {ex.Message}", ex);
        }
    }

    public async Task<List<Patient>> GetPatientsByCategoryAsync(string category)
    {
        try
        {
            return await _context.Patients
                .Where(p => !p.IsDeleted && p.PatientCategory == category)
                .OrderByDescending(p => p.RegistrationDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetPatientsByCategoryAsync", $"خطأ في جلب المرضى حسب التصنيف {category}: {ex.Message}", "System");
            throw new Exception($"خطأ في جلب المرضى: {ex.Message}", ex);
        }
    }

    public async Task<List<Patient>> GetDebtorPatientsAsync(decimal minimumDebt = 0)
    {
        try
        {
            // سيتم تطوير هذه الوظيفة مع جدول المدفوعات
            // مؤقتاً نرجع قائمة فارغة
            return new List<Patient>();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetDebtorPatientsAsync", $"خطأ في جلب المرضى المدينين: {ex.Message}", "System");
            throw new Exception($"خطأ في جلب المرضى المدينين: {ex.Message}", ex);
        }
    }

    #endregion

    #region التصنيفات

    public async Task<List<string>> GetPatientCategoriesAsync()
    {
        try
        {
            return await _context.Patients
                .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.PatientCategory))
                .Select(p => p.PatientCategory)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetPatientCategoriesAsync", $"خطأ في جلب التصنيفات: {ex.Message}", "System");
            throw new Exception($"خطأ في جلب التصنيفات: {ex.Message}", ex);
        }
    }

    public async Task<bool> AddPatientCategoryAsync(string category)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(category))
                return false;

            // التحقق من عدم وجود التصنيف
            var exists = await _context.Patients
                .AnyAsync(p => p.PatientCategory == category.Trim());

            if (exists)
                return false;

            // إضافة التصنيف عن طريق إضافة مريض وهمي مؤقت (يمكن تحسين هذا لاحقاً)
            // أو إنشاء جدول منفصل للتصنيفات

            await LogActivityAsync("AddPatientCategoryAsync", $"تم إضافة تصنيف جديد: {category}", "System");
            return true;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("AddPatientCategoryAsync", $"خطأ في إضافة التصنيف {category}: {ex.Message}", "System");
            return false;
        }
    }

    #endregion

    #region التحقق من صحة البيانات

    public bool IsValidYemeniPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // إزالة المسافات والرموز
        var cleanNumber = phoneNumber.Trim().Replace(" ", "").Replace("-", "");

        // إزالة رمز الدولة إذا وجد
        if (cleanNumber.StartsWith("+967"))
            cleanNumber = cleanNumber.Substring(4);
        else if (cleanNumber.StartsWith("967"))
            cleanNumber = cleanNumber.Substring(3);
        else if (cleanNumber.StartsWith("00967"))
            cleanNumber = cleanNumber.Substring(5);

        return YemeniPhoneRegex.IsMatch(cleanNumber);
    }

    public async Task<bool> IsPhoneNumberAvailableAsync(string phoneNumber, int? excludePatientId = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return true;

            var query = _context.Patients.Where(p => !p.IsDeleted &&
                (p.Phone == phoneNumber || p.Mobile == phoneNumber));

            if (excludePatientId.HasValue)
            {
                query = query.Where(p => p.Id != excludePatientId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            await LogActivityAsync("IsPhoneNumberAvailableAsync", $"خطأ في التحقق من توفر رقم الهاتف: {ex.Message}", "System");
            return false;
        }
    }

    public bool IsValidDateOfBirth(DateTime dateOfBirth)
    {
        var today = DateTime.Today;
        var age = today.Year - dateOfBirth.Year;

        // تعديل العمر إذا لم يحن موعد عيد الميلاد هذا العام
        if (dateOfBirth.Date > today.AddYears(-age))
            age--;

        // العمر يجب أن يكون بين 1 و 120 سنة
        return age >= 1 && age <= 120;
    }

    #endregion

    #region العمليات المساعدة

    public async Task<decimal> UpdatePatientBalanceAsync(int patientId)
    {
        try
        {
            // سيتم تطوير هذه الوظيفة مع جدول المدفوعات والجلسات
            // مؤقتاً نرجع 0
            return 0;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("UpdatePatientBalanceAsync", $"خطأ في تحديث رصيد المريض {patientId}: {ex.Message}", "System");
            return 0;
        }
    }

    public async Task<DateTime?> GetLastActivityDateAsync(int patientId)
    {
        try
        {
            // البحث عن آخر نشاط في الجلسات أو المواعيد
            var lastSession = await _context.Sessions
                .Where(s => s.PatientId == patientId)
                .MaxAsync(s => (DateTime?)s.SessionDate);

            var lastAppointment = await _context.Appointments
                .Where(a => a.PatientId == patientId)
                .MaxAsync(a => (DateTime?)a.AppointmentDateTime);

            var lastUpdate = await _context.Patients
                .Where(p => p.Id == patientId)
                .Select(p => p.UpdatedAt)
                .FirstOrDefaultAsync();

            // إرجاع أحدث تاريخ
            var dates = new[] { lastSession, lastAppointment, lastUpdate }.Where(d => d.HasValue);
            return dates.Any() ? dates.Max() : null;
        }
        catch (Exception ex)
        {
            await LogActivityAsync("GetLastActivityDateAsync", $"خطأ في جلب آخر نشاط للمريض {patientId}: {ex.Message}", "System");
            return null;
        }
    }

    #endregion

    #region وظائف مساعدة

    private async Task ValidatePatientDataAsync(Patient patient, int? excludePatientId = null)
    {
        var errors = new List<string>();

        // التحقق من الاسم
        if (string.IsNullOrWhiteSpace(patient.FullName) || patient.FullName.Length < 2)
        {
            errors.Add("اسم المريض مطلوب ولا يقل عن حرفين");
        }

        // التحقق من رقم الهاتف
        if (!string.IsNullOrWhiteSpace(patient.Phone))
        {
            if (!IsValidYemeniPhoneNumber(patient.Phone))
            {
                errors.Add("رقم الهاتف غير صحيح (يجب أن يبدأ بـ 77، 73، 70، أو 71 ويتكون من 9 أرقام)");
            }
            else if (!await IsPhoneNumberAvailableAsync(patient.Phone, excludePatientId))
            {
                errors.Add("رقم الهاتف مستخدم بالفعل");
            }
        }

        // التحقق من تاريخ الميلاد
        if (patient.DateOfBirth.HasValue && !IsValidDateOfBirth(patient.DateOfBirth.Value))
        {
            errors.Add("تاريخ الميلاد غير صحيح");
        }

        if (errors.Any())
        {
            throw new ArgumentException(string.Join("\n", errors));
        }
    }

    private async Task LogActivityAsync(string action, string description, string userId)
    {
        try
        {
            var log = new ActivityLog
            {
                Action = action,
                Description = description,
                UserId = 1, // مؤقتاً - يجب تمرير معرف المستخدم الصحيح
                EntityType = "Patient",
                IpAddress = "Local", // يمكن تحسينه لاحقاً
                ActionDate = DateTime.Now,
                CreatedAt = DateTime.Now
            };

            _context.ActivityLogs.Add(log);
            await _context.SaveChangesAsync();
        }
        catch
        {
            // تجاهل أخطاء التسجيل لتجنب التأثير على العملية الأساسية
        }
    }

    #endregion

    #region Additional Interface Methods (للتوافق مع الواجهة القديمة)

    /// <summary>
    /// الحصول على جميع المرضى (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<List<Patient>> GetAllPatientsAsync()
    {
        return await _context.Patients.ToListAsync();
    }

    /// <summary>
    /// الحصول على مريض بالمعرف (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<Patient?> GetPatientByIdAsync(int id)
    {
        return await GetPatientByIdAsync(id, false);
    }

    /// <summary>
    /// إضافة مريض (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<Patient> AddPatientAsync(Patient patient)
    {
        return await AddPatientAsync(patient, "System");
    }

    /// <summary>
    /// تحديث مريض (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<Patient> UpdatePatientAsync(Patient patient)
    {
        await UpdatePatientAsync(patient, "System");
        return patient;
    }

    /// <summary>
    /// حذف مريض (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<bool> DeletePatientAsync(int id)
    {
        return await DeletePatientAsync(id, "System");
    }

    /// <summary>
    /// البحث في المرضى (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<IEnumerable<Patient>> SearchPatientsAsync(string searchTerm)
    {
        var criteria = new PatientSearchCriteria { Name = searchTerm };
        return await SearchPatientsAsync(criteria);
    }

    /// <summary>
    /// الحصول على رصيد المريض (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task<decimal> GetPatientBalanceAsync(int patientId)
    {
        // حساب الرصيد من الجلسات والمدفوعات
        var sessions = await _context.Sessions
            .Where(s => s.PatientId == patientId)
            .SumAsync(s => s.ServiceCost ?? 0);

        var payments = await _context.Payments
            .Where(p => p.PatientId == patientId)
            .SumAsync(p => p.Amount);

        return sessions - payments;
    }

    /// <summary>
    /// تحديث رصيد المريض (للتوافق مع الواجهة القديمة)
    /// </summary>
    public async Task UpdatePatientBalanceAsync(int patientId, decimal amount)
    {
        await UpdatePatientBalanceAsync(patientId);
    }

    #endregion
}
