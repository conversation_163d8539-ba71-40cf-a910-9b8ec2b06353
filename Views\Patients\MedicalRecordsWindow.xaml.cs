using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class MedicalRecordsWindow : Window
    {
        public ObservableCollection<MedicalRecord> MedicalRecords { get; set; }

        public MedicalRecordsWindow()
        {
            InitializeComponent();
            MedicalRecords = new ObservableCollection<MedicalRecord>();
            LoadSampleData();
            MedicalRecordsDataGrid.ItemsSource = MedicalRecords;
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للعرض
            MedicalRecords.Add(new MedicalRecord
            {
                Id = 1,
                PatientName = "أحمد محمد علي",
                FileNumber = 8501,
                RecordDate = DateTime.Now.AddDays(-5),
                RecordType = "فحص دوري",
                Diagnosis = "تسوس في الضرس العلوي الأيمن",
                Treatment = "حشو أبيض + تنظيف",
                DoctorName = "د. سارة أحمد",
                Status = "مكتمل"
            });

            MedicalRecords.Add(new MedicalRecord
            {
                Id = 2,
                PatientName = "فاطمة حسن محمد",
                FileNumber = 8502,
                RecordDate = DateTime.Now.AddDays(-3),
                RecordType = "استشارة تقويم",
                Diagnosis = "عدم انتظام الأسنان الأمامية",
                Treatment = "تقويم معدني لمدة 18 شهر",
                DoctorName = "د. محمد عبدالله",
                Status = "قيد العلاج"
            });

            MedicalRecords.Add(new MedicalRecord
            {
                Id = 3,
                PatientName = "خالد عبدالرحمن",
                FileNumber = 8503,
                RecordDate = DateTime.Now.AddDays(-1),
                RecordType = "زراعة أسنان",
                Diagnosis = "فقدان الضرس السفلي الأيسر",
                Treatment = "زراعة سن واحد مع تاج",
                DoctorName = "د. أمل سالم",
                Status = "مجدول"
            });
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            // تطبيق البحث والفلاتر
            MessageBox.Show("سيتم تطبيق البحث والفلاتر", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalRecord record)
            {
                ShowRecordDetails(record);
            }
        }

        private void EditRecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalRecord record)
            {
                EditRecord(record);
            }
        }

        private void PrintRecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalRecord record)
            {
                PrintRecord(record);
            }
        }

        private void ViewDetailsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (MedicalRecordsDataGrid.SelectedItem is MedicalRecord record)
            {
                ShowRecordDetails(record);
            }
        }

        private void EditRecordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (MedicalRecordsDataGrid.SelectedItem is MedicalRecord record)
            {
                EditRecord(record);
            }
        }

        private void PrintRecordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (MedicalRecordsDataGrid.SelectedItem is MedicalRecord record)
            {
                PrintRecord(record);
            }
        }

        private void CopyRecordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (MedicalRecordsDataGrid.SelectedItem is MedicalRecord record)
            {
                var recordInfo = $"السجل الطبي رقم: {record.Id}\n" +
                               $"المريض: {record.PatientName}\n" +
                               $"التاريخ: {record.RecordDate:dd/MM/yyyy}\n" +
                               $"التشخيص: {record.Diagnosis}\n" +
                               $"العلاج: {record.Treatment}";
                
                Clipboard.SetText(recordInfo);
                MessageBox.Show("تم نسخ معلومات السجل", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportRecordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير وظيفة التصدير قريباً", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteRecordMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (MedicalRecordsDataGrid.SelectedItem is MedicalRecord record)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف السجل الطبي للمريض {record.PatientName}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    MedicalRecords.Remove(record);
                    MessageBox.Show("تم حذف السجل بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void AddRecordButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة سجل طبي جديد", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportAllButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير جميع السجلات الطبية", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintAllButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة جميع السجلات الطبية", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ShowRecordDetails(MedicalRecord record)
        {
            var details = $"تفاصيل السجل الطبي\n\n" +
                         $"رقم السجل: {record.Id}\n" +
                         $"اسم المريض: {record.PatientName}\n" +
                         $"رقم الملف: {record.FileNumber}\n" +
                         $"تاريخ السجل: {record.RecordDate:dd/MM/yyyy}\n" +
                         $"نوع السجل: {record.RecordType}\n" +
                         $"التشخيص: {record.Diagnosis}\n" +
                         $"العلاج المقترح: {record.Treatment}\n" +
                         $"الطبيب المعالج: {record.DoctorName}\n" +
                         $"الحالة: {record.Status}";

            MessageBox.Show(details, "تفاصيل السجل الطبي", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditRecord(MedicalRecord record)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل السجل الطبي للمريض: {record.PatientName}", 
                "تعديل السجل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintRecord(MedicalRecord record)
        {
            MessageBox.Show($"سيتم طباعة السجل الطبي للمريض: {record.PatientName}", 
                "طباعة السجل", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // نموذج السجل الطبي
    public class MedicalRecord
    {
        public int Id { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public int FileNumber { get; set; }
        public DateTime RecordDate { get; set; }
        public string RecordType { get; set; } = string.Empty;
        public string Diagnosis { get; set; } = string.Empty;
        public string Treatment { get; set; } = string.Empty;
        public string DoctorName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}
