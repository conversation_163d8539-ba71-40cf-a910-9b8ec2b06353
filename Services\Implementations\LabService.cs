using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Services.Implementations
{
    public class LabService : ILabService
    {
        private readonly AqlanCenterDbContext _context;

        public LabService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Lab>> GetAllLabsAsync()
        {
            return await _context.Labs
                .Include(l => l.LabOrders)
                .OrderBy(l => l.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Lab>> GetActiveLabsAsync()
        {
            return await _context.Labs
                .Where(l => l.IsActive)
                .OrderBy(l => l.Name)
                .ToListAsync();
        }

        public async Task<Lab?> GetLabByIdAsync(int id)
        {
            return await _context.Labs
                .Include(l => l.LabOrders)
                .FirstOrDefaultAsync(l => l.LabId == id);
        }

        public async Task<Lab> AddLabAsync(Lab lab)
        {
            lab.CreatedAt = DateTime.Now;
            lab.UpdatedAt = DateTime.Now;
            _context.Labs.Add(lab);
            await _context.SaveChangesAsync();
            return lab;
        }

        public async Task<Lab> UpdateLabAsync(Lab lab)
        {
            lab.UpdatedAt = DateTime.Now;
            _context.Labs.Update(lab);
            await _context.SaveChangesAsync();
            return lab;
        }

        public async Task<bool> DeleteLabAsync(int id)
        {
            var lab = await _context.Labs.FindAsync(id);
            if (lab == null) return false;

            // Check if lab has orders
            var hasOrders = await _context.LabOrders.AnyAsync(lo => lo.LabId == id);
            if (hasOrders)
            {
                // Soft delete - mark as inactive
                lab.IsActive = false;
                lab.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
                return true;
            }

            _context.Labs.Remove(lab);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> LabExistsAsync(int id)
        {
            return await _context.Labs.AnyAsync(l => l.LabId == id);
        }

        public async Task<IEnumerable<Lab>> SearchLabsAsync(string searchTerm)
        {
            return await _context.Labs
                .Where(l => l.IsActive && (l.Name.Contains(searchTerm) || 
                                         l.Phone!.Contains(searchTerm) || 
                                         l.Address!.Contains(searchTerm)))
                .OrderBy(l => l.Name)
                .ToListAsync();
        }

        public async Task<Lab> GetLabByNameAsync(string name)
        {
            return await _context.Labs
                .FirstOrDefaultAsync(l => l.Name == name && l.IsActive);
        }

        public async Task<IEnumerable<Lab>> GetLabsByPerformanceAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Labs
                .Where(l => l.IsActive)
                .Include(l => l.LabOrders.Where(lo => lo.SendDate >= startDate && lo.SendDate <= endDate))
                .OrderByDescending(l => l.LabOrders.Count)
                .ToListAsync();
        }

        public async Task<decimal> GetLabAverageRatingAsync(int labId)
        {
            var averageRating = await _context.LabOrders
                .Where(lo => lo.LabId == labId && lo.Rating.HasValue)
                .AverageAsync(lo => (double)lo.Rating.Value);
            return Math.Round((decimal)averageRating, 2);
        }

        public async Task<int> GetLabOrdersCountAsync(int labId, DateTime startDate, DateTime endDate)
        {
            return await _context.LabOrders
                .CountAsync(lo => lo.LabId == labId && 
                                 lo.SendDate >= startDate && 
                                 lo.SendDate <= endDate);
        }

        public async Task<int> GetLabOverdueOrdersCountAsync(int labId)
        {
            return await _context.LabOrders
                .CountAsync(lo => lo.LabId == labId && 
                                 lo.Status == "قيد التنفيذ" && 
                                 lo.ExpectedReturnDate.HasValue && 
                                 lo.ExpectedReturnDate.Value < DateTime.Now);
        }
    }
} 