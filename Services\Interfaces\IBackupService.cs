using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IBackupService
    {
        // عمليات النسخ الاحتياطي
        Task<BackupInfo> CreateBackupAsync(string description = "", string createdBy = "");
        Task<bool> RestoreBackupAsync(string backupFilePath, string restoredBy = "");
        Task<bool> VerifyBackupAsync(string backupFilePath);
        
        // إدارة النسخ الاحتياطية
        Task<IEnumerable<BackupInfo>> GetAllBackupsAsync();
        Task<BackupInfo?> GetBackupByIdAsync(int id);
        Task<bool> DeleteBackupAsync(int id);
        Task<bool> DeleteExpiredBackupsAsync();
        
        // إعدادات النسخ الاحتياطي
        Task<BackupSettings> GetBackupSettingsAsync();
        Task<bool> UpdateBackupSettingsAsync(BackupSettings settings);
        Task<bool> InitializeBackupSettingsAsync();
        
        // النسخ الاحتياطي التلقائي
        Task<bool> ScheduleBackupAsync();
        Task<bool> CancelScheduledBackupAsync();
        Task<bool> IsBackupScheduledAsync();
        
        // التحقق والحماية
        Task<bool> ValidateBackupFileAsync(string filePath);
        Task<string> CalculateChecksumAsync(string filePath);
        Task<bool> CompressBackupAsync(string sourcePath, string destinationPath);
        Task<bool> DecompressBackupAsync(string sourcePath, string destinationPath);
        Task<bool> EncryptBackupAsync(string sourcePath, string destinationPath, string password);
        Task<bool> DecryptBackupAsync(string sourcePath, string destinationPath, string password);
        
        // الإشعارات والتقارير
        Task<bool> SendBackupNotificationAsync(string message, bool isSuccess = true);
        Task<string> GenerateBackupReportAsync();
        Task<Dictionary<string, object>> GetBackupStatisticsAsync();
        
        // عمليات مساعدة
        Task<string> GetBackupDirectoryAsync();
        Task<bool> CreateBackupDirectoryAsync();
        Task<long> GetBackupDirectorySizeAsync();
        Task<bool> CleanupBackupDirectoryAsync();
        
        // التحقق من الصلاحيات
        Task<bool> HasBackupPermissionAsync(string username);
        Task<bool> HasRestorePermissionAsync(string username);
    }
} 