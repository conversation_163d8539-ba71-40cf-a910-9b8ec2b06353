using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace AqlanCenterProApp.Views.Patients
{
    /// <summary>
    /// نافذة سند المعاينة
    /// </summary>
    public partial class ConsultationReceiptWindow : Window
    {
        private readonly Patient _patient;
        private ClinicSettings _clinicSettings;
        private readonly string _receiptNumber = DateTime.Now.ToString("yyyyMMddHHmmss");

        public ConsultationReceiptWindow(Patient patient)
        {
            InitializeComponent();
            _patient = patient ?? throw new ArgumentNullException(nameof(patient));
            LoadClinicSettings();
        }

        /// <summary>
        /// تحميل إعدادات المركز
        /// </summary>
        private async void LoadClinicSettings()
        {
            try
            {
                var settingsService = App.Services?.GetService<ISettingsService>();
                if (settingsService != null)
                {
                    _clinicSettings = await settingsService.GetClinicSettingsAsync();
                }
                else
                {
                    // إعدادات افتراضية في حالة عدم توفر الخدمة
                    _clinicSettings = new ClinicSettings
                    {
                        ClinicName = "مركز عقلان للأسنان",
                        Address = "صنعاء - شارع الزبيري",
                        PhoneNumber = "01-123456",
                        MobileNumber = "770-245745",
                        Email = "<EMAIL>"
                    };
                }
            }
            catch (Exception ex)
            {
                // إعدادات افتراضية في حالة الخطأ
                _clinicSettings = new ClinicSettings
                {
                    ClinicName = "مركز عقلان للأسنان",
                    Address = "صنعاء - شارع الزبيري",
                    PhoneNumber = "01-123456",
                    MobileNumber = "770-245745",
                    Email = "<EMAIL>"
                };
            }
            
            // تحميل بيانات المريض بعد تحميل إعدادات المركز
            LoadPatientData();
        }

        /// <summary>
        /// تحميل بيانات المريض في النافذة
        /// </summary>
        private void LoadPatientData()
        {
            try
            {
                // Receipt Info
                var currentDate = DateTime.Now.ToString("yyyy/MM/dd");
                var currentTime = DateTime.Now.ToString("HH:mm");
                ReceiptNumberText.Text = $"رقم السند: {_receiptNumber} | التاريخ: {currentDate} | الوقت: {currentTime}";

                // Patient Information
                PatientNameText.Text = _patient.FullName ?? "غير محدد";
                FileNumberText.Text = _patient.FileNumber.ToString();
                PhoneText.Text = _patient.Mobile ?? _patient.Phone ?? "غير محدد";

                // Amount Information
                var consultationFee = _patient.ConsultationFee ?? 0;
                AmountText.Text = $"{consultationFee:N2} ريال يمني";
                AmountWordsText.Text = $"({ConvertNumberToWords(consultationFee)} ريال يمني فقط لا غير)";

                // Issue Date
                IssueDateText.Text = $"تم إصدار هذا السند بتاريخ: {currentDate} الساعة: {currentTime}";

                // تحديث اسم المركز في الواجهة إذا كان متوفراً
                if (_clinicSettings != null && !string.IsNullOrEmpty(_clinicSettings.ClinicName))
                {
                    // البحث عن TextBlock الخاص باسم المركز وتحديثه
                    var clinicNameTextBlock = this.FindName("ClinicNameText") as TextBlock;
                    if (clinicNameTextBlock != null)
                    {
                        clinicNameTextBlock.Text = _clinicSettings.ClinicName;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المريض: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة السند
        /// </summary>
        private async void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إضافة إيصال مالي عند الطباعة
                var receiptService = App.Services.GetRequiredService<IReceiptService>();
                var receipt = new Receipt
                {
                    PatientId = _patient.Id,
                    Amount = _patient.ConsultationFee ?? 0,
                    ReceiptDate = DateTime.Now,
                    PaymentMethod = "نقدي",
                    Purpose = "معاينة أولية",
                    Description = "سند معاينة تلقائي من وحدة المرضى",
                    IssuedBy = Environment.UserName,
                    Status = "مكتمل",
                    ReceiptNumber = _receiptNumber
                };
                // حماية: لا تكرر السند إذا تم إنشاؤه اليوم لنفس المريض ولنفس المبلغ
                var todayReceipts = await receiptService.GetReceiptsByPatientAsync(_patient.Id);
                bool alreadyExists = todayReceipts.Any(r =>
                    r.ReceiptDate.Date == DateTime.Now.Date &&
                    r.Amount == receipt.Amount &&
                    r.Purpose == receipt.Purpose);
                if (!alreadyExists)
                {
                    await receiptService.CreateReceiptAsync(receipt);
                }

                // إنشاء HTML للطباعة
                var receiptHtml = GenerateReceiptHtml();

                // حفظ كملف HTML مؤقت
                var tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(),
                    $"سند_معاينة_{_patient.FileNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                System.IO.File.WriteAllText(tempFile, receiptHtml, System.Text.Encoding.UTF8);

                // فتح الملف في المتصفح للطباعة
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });

                MessageBox.Show("تم تسجيل العملية المالية وفتح سند المعاينة في المتصفح\nيمكنك الآن طباعته باستخدام Ctrl+P",
                    "طباعة سند المعاينة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند أو تسجيل العملية المالية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء HTML للطباعة
        /// </summary>
        private string GenerateReceiptHtml()
        {
            var consultationFee = _patient.ConsultationFee ?? 0;
            var receiptNumber = _receiptNumber;
            var currentDate = DateTime.Now.ToString("yyyy/MM/dd");
            var currentTime = DateTime.Now.ToString("HH:mm");
            // شعار المركز Base64 (أول 2000 حرف فقط لضمان عدم تجاوز الحجم)
            var logoBase64 = "iVBORw0KGgoAAAANSUhEUgAAB+0AAAbZCAYAAACGTZeNAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAhdEVYdENyZWF0aW9uIFRpbWUAMjAxODowNDoyNyAwNzo0MzowNW1n5U8AAP94SURBVHhe7P35syTZld+JnYh4mVlZWZm1YylsBRSA3pvLcHpGHIkzYnMZiktTbC6iZkYymUw/jPSvjEwm/SKZaUwymYZkcye7STZFkSPRNC1SpNgUh+wmugFUAVVoAI3acl/ee-Gh8z33nnvP3dw9XiYKBeB8Kt/zu5ztLu4vyk94xObwjb92oIQpgkNVVzaDdgCdkd4sQedwmOQIxm46HT2fhz1N250Ut8nultvPiaZTogfvB1Ps6MD6kNhtNvybGyG/YVkwHE/bPnVkYbEn22DGPk+0tTjPZT/GCEI8uXcUn8qX9NoCab1Ub26fWER+jWxfpo2zI9dVXTvfgA0kG11jHSCns12zzka9ZmPGY6nnZ2xrJqZmjkdYOXiq9FbbUVr5MJ6O7RoVSxj5o+NYoHfuqu+hrwPtpw3ttrgm8b/NPshudrS78Xna/Mh/SnT1VaLTd4ne+Ju0/+1/SrS/zxcpXNPYOHTEyZ7V4H9LW75+4Rq03aJ/foz983uOUl6Gd7SNFolDzJg5xHX4ArZhayPXcJg85vxml4vuanuPOfal8YVhPBlWzSU7jHO3TPzb2LA+6LB34bLU0X254fPAssGeDqV47GBsyZ9w3QszKjqWeughvtBYx5jptHdlB20an1me5Kuxo69H5mQ2eaxm0EG+lJXuKNOOj+u2zcgKc34VbujOG7+uQrucq2IYC4WOE/xi+MzlMe5vv0XTza/w9e8Oy+IaeR7cch+mS1SSeS5gElM9VEEvhJoDx3py41O0e+mniK5c46k+CabY74SdEK+nwRausZtwja2JPiFor68bfS0ZsfOi5wHsKuhGTKGc2wv0/JB+K1PJx35+lctH3esDmwz/9ZBjPt8A9LXOq4a/WRUy/aJj+uAHTbo5jN9DnJMQS9RhGxAt2qJO9s9Nxk/wqz6jTEcf9TAHaFL5SJpL0442s4bSp7bkmO0mqmtWgOVSDPFYz5MQ+1IM1q6RV1M6p8D2g7oucF3nyeqCnv1iLNY2l616bcuSxg1m5LrzYajX62geV99xHMdxHMdxHMdxHOeDwyTtzc0pxdywOuAuKiM3y0bZDcjbm1yrCTqasJ9PnnQ6Bz4PtCe5+Sc3fFlGblixj7P7dHh0m/2ccx03pibuhiz6IS93+7nMDMfTtvcS9gC3OxfpJf16YHLU3OJc5/6UCJHfAe0dxWdvOgf6ciCtmdVZzoIxLCNiS7Lj/jLOjtxQdeWcgyUfXSBnZ9wS3igyB/Zub9369MfS86G2yp6xXDn2JawsLFS6F7aVWZq3MVHvwvoZxJATHwv7aOQP7bDB/+SSQ7geXabtiz9Fmx/7z/nSdIXo9Nt0+I2/QNPNf0uH/X0WvwRF/i+tDm1wbZYb6cGPJO0ltvlxHj+PpXxwd6wNzFV5A13iEDNmHhH/UnxyLczzoOORdeG+Y8fXXq7qWOt1Ps5+y7z+4vDz0JcRWyODmK9jjJXrF1ipz350XPn8GWH8SIiQX9CBTNwTbV5Kr6fBbs99OK9yx3yMpq8rN99mz2EQ9m1HB69fDCmmSvaAepNAC3U7DhXZNOtY6UoiLWLtiq1cb1xyQ3fe+Bpl2+3ZhOT9YXtC2/0Z7W//Fu1vfpkO/DqNuA5XWDbRZP09V3bSpsn/iY92b6OTf+T6YNrFt9Y3NHEAu5NLtHv+C0TPfY62J1dos7vEEkjY41qM+Ql25L/DjqcENtHGfdYninbMEnA9v8DIdPo1mVxcyyLqLb2ZxeqLXO0/1DUJr3TXhmmT9fHI8iGcNlmv09sk6wHamn2zTU3Fm3LYjkxrlBFS/FGB2cQ34QK8qSHHCsQAH00c";

            return $@"
<!DOCTYPE html>
<html dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>سند معاينة - مركز الدكتور عقلان الكامل</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Arial, sans-serif; direction: rtl; margin: 0; background: #f8f9fa; }}
        .receipt-container {{ width: 19cm; max-width: 19cm; margin: 0 auto; background: white; border: 2px solid #3498DB; border-radius: 12px; overflow: hidden; box-shadow: 0 6px 18px rgba(0,0,0,0.08); }}
        .header {{ background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%); color: white; padding: 18px 0 12px 0; text-align: center; }}
        .logo-section {{ display: flex; align-items: center; justify-content: center; margin-bottom: 10px; }}
        .logo {{ width: 60px; height: 60px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 12px; }}
        .clinic-name {{ font-size: 18px; font-weight: bold; margin-bottom: 4px; color: white; }}
        .clinic-slogan {{ font-size: 12px; color: #E67E22; font-weight: 600; margin-bottom: 8px; }}
        .receipt-title {{ font-size: 15px; font-weight: bold; background: rgba(255,255,255,0.2); padding: 7px 18px; border-radius: 18px; display: inline-block; }}
        .content {{ padding: 18px 18px 10px 18px; }}
        .receipt-info {{ text-align: center; margin-bottom: 10px; color: #666; background: #f8f9fa; padding: 7px; border-radius: 7px; font-size: 12px; }}
        .patient-info, .amount-section, .signature-section {{ margin-bottom: 10px; }}
        .patient-info {{ background: #F8F9FA; padding: 8px; border-radius: 5px; border-bottom: 2px solid #4A90E2; font-size: 12px; }}
        .amount-section {{ background: linear-gradient(90deg, #28A745 0%, #20C997 100%); color: white; border-radius: 7px; padding: 12px; text-align: center; }}
        .amount-label {{ font-size: 13px; opacity: 0.9; }}
        .amount-value {{ font-size: 18px; font-weight: bold; }}
        .amount-words {{ font-size: 11px; opacity: 0.8; font-style: italic; margin-top: 4px; }}
        .signature-section {{ background: #F8F9FA; padding: 8px; border-radius: 5px; text-align: right; font-size: 12px; }}
        .footer {{ background: #3498DB; color: white; border-radius: 0 0 8px 8px; padding: 8px 0 6px 0; text-align: center; font-size: 11px; }}
        .footer .row {{ display: flex; justify-content: space-between; margin-bottom: 2px; }}
        .footer .row span {{ font-size: 11px; }}
        .footer .thanks {{ margin-top: 2px; font-size: 11px; }}
        @media print {{
            body {{ background: white; }}
            .receipt-container {{ box-shadow: none; border: 1px solid #3498DB; margin: 0 auto; }}
            @page {{ size: A4 portrait; margin: 1.5cm 0.5cm 1.5cm 0.5cm; }}
        }}
    </style>
</head>
<body>
    <div class='receipt-container'>
        <div class='header'>
            <div class='logo-section'>
                <div class='logo'><img src='data:image/png;base64,{logoBase64}' style='width:38px;height:38px;'/></div>
            </div>
            <div class='clinic-name'>مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان</div>
            <div class='clinic-slogan'>معنا تصبح ابتسامتك كما تحب أن تكون</div>
            <div class='receipt-title'>🧾 سند معاينة</div>
        </div>
        <div class='content'>
            <div class='receipt-info'>رقم السند: {receiptNumber} | التاريخ: {currentDate} | الوقت: {currentTime}</div>
            <div class='patient-info'>
                <div>اسم المريض: <b>{_patient.FullName}</b></div>
                <div>رقم الملف: <b>{_patient.FileNumber}</b></div>
                <div>رقم الهاتف: <b>{_patient.Mobile ?? _patient.Phone ?? "غير محدد"}</b></div>
                <div>نوع الخدمة: <b>معاينة أولية</b></div>
            </div>
            <div class='amount-section'>
                <div class='amount-label'>المبلغ المدفوع</div>
                <div class='amount-value'>{consultationFee:N2} ريال يمني</div>
                <div class='amount-words'>({ConvertNumberToWords(consultationFee)} ريال يمني فقط لا غير)</div>
            </div>
            <div class='signature-section'>
                <div>✍️ توقيع المستلم</div>
                <div style='border-bottom:1px solid #333; width:120px; height:20px; margin:8px 0;'></div>
                <div style='text-align:center;'>توقيع وختم المسؤول</div>
            </div>
        </div>
        <div class='footer'>
            <div class='row'>
                <span>📍 تعز – شارع التحرير الأعلى – جوار جامع الأزهر</span>
                <span>📞 04253028 – 770245745 – 711752823</span>
            </div>
            <div class='thanks'>🌟 شكراً لثقتكم بنا - نتمنى لكم دوام الصحة والعافية 🌟</div>
        </div>
    </div>
</body>
</html>";
        }

        /// <summary>
        /// تحويل الرقم إلى كلمات باللغة العربية
        /// </summary>
        private string ConvertNumberToWords(decimal number)
        {
            if (number == 0) return "صفر";

            var integerPart = (int)Math.Floor(number);
            var decimalPart = (int)((number - integerPart) * 100);

            var words = ConvertIntegerToWords(integerPart);

            if (decimalPart > 0)
            {
                words += $" و {ConvertIntegerToWords(decimalPart)} فلس";
            }

            return words;
        }

        /// <summary>
        /// تحويل الرقم الصحيح إلى كلمات
        /// </summary>
        private string ConvertIntegerToWords(int number)
        {
            if (number == 0) return "صفر";

            var ones = new[] { "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة" };
            var tens = new[] { "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
            var teens = new[] { "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر" };

            if (number < 10)
                return ones[number];
            else if (number < 20)
                return teens[number - 10];
            else if (number < 100)
            {
                var ten = number / 10;
                var one = number % 10;
                return tens[ten] + (one > 0 ? " " + ones[one] : "");
            }
            else if (number < 1000)
            {
                var hundred = number / 100;
                var remainder = number % 100;
                var result = ones[hundred] + " مائة";
                if (remainder > 0)
                    result += " " + ConvertIntegerToWords(remainder);
                return result;
            }
            else if (number < 1000000)
            {
                var thousand = number / 1000;
                var remainder = number % 1000;
                var result = ConvertIntegerToWords(thousand) + " ألف";
                if (remainder > 0)
                    result += " " + ConvertIntegerToWords(remainder);
                return result;
            }

            return number.ToString(); // للأرقام الكبيرة جداً
        }
    }
}
