using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AqlanCenterProApp.Models.Dashboard;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.Views.Controls
{
    /// <summary>
    /// Interaction logic for SmartAlertsPanel.xaml
    /// </summary>
    public partial class SmartAlertsPanel : UserControl, INotifyPropertyChanged
    {
        public SmartAlertsPanel()
        {
            InitializeComponent();
            DataContext = this;
            InitializeCommands();
            InitializeFilters();
        }

        #region Properties

        private ObservableCollection<EnhancedSmartAlert> _alerts = new();
        public ObservableCollection<EnhancedSmartAlert> Alerts
        {
            get => _alerts;
            set
            {
                _alerts = value;
                OnPropertyChanged();
                UpdateFilteredAlerts();
                UpdateAlertCounts();
            }
        }

        private ObservableCollection<EnhancedSmartAlert> _filteredAlerts = new();
        public ObservableCollection<EnhancedSmartAlert> FilteredAlerts
        {
            get => _filteredAlerts;
            set
            {
                _filteredAlerts = value;
                OnPropertyChanged();
            }
        }

        private ObservableCollection<string> _alertTypeFilters = new();
        public ObservableCollection<string> AlertTypeFilters
        {
            get => _alertTypeFilters;
            set
            {
                _alertTypeFilters = value;
                OnPropertyChanged();
            }
        }

        private string _selectedAlertTypeFilter = "الكل";
        public string SelectedAlertTypeFilter
        {
            get => _selectedAlertTypeFilter;
            set
            {
                _selectedAlertTypeFilter = value;
                OnPropertyChanged();
                UpdateFilteredAlerts();
            }
        }

        private bool _isLoadingAlerts = false;
        public bool IsLoadingAlerts
        {
            get => _isLoadingAlerts;
            set
            {
                _isLoadingAlerts = value;
                OnPropertyChanged();
            }
        }

        private DateTime _lastAlertsUpdate = DateTime.Now;
        public DateTime LastAlertsUpdate
        {
            get => _lastAlertsUpdate;
            set
            {
                _lastAlertsUpdate = value;
                OnPropertyChanged();
            }
        }

        // Alert Counts
        private int _totalAlertsCount;
        public int TotalAlertsCount
        {
            get => _totalAlertsCount;
            set
            {
                _totalAlertsCount = value;
                OnPropertyChanged();
            }
        }

        private int _unreadAlertsCount;
        public int UnreadAlertsCount
        {
            get => _unreadAlertsCount;
            set
            {
                _unreadAlertsCount = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasUnreadAlerts));
            }
        }

        private int _criticalAlertsCount;
        public int CriticalAlertsCount
        {
            get => _criticalAlertsCount;
            set
            {
                _criticalAlertsCount = value;
                OnPropertyChanged();
            }
        }

        private int _highPriorityAlertsCount;
        public int HighPriorityAlertsCount
        {
            get => _highPriorityAlertsCount;
            set
            {
                _highPriorityAlertsCount = value;
                OnPropertyChanged();
            }
        }

        private int _normalAlertsCount;
        public int NormalAlertsCount
        {
            get => _normalAlertsCount;
            set
            {
                _normalAlertsCount = value;
                OnPropertyChanged();
            }
        }

        private int _lowPriorityAlertsCount;
        public int LowPriorityAlertsCount
        {
            get => _lowPriorityAlertsCount;
            set
            {
                _lowPriorityAlertsCount = value;
                OnPropertyChanged();
            }
        }

        public bool HasUnreadAlerts => UnreadAlertsCount > 0;
        public bool HasNoAlerts => !IsLoadingAlerts && FilteredAlerts.Count == 0;

        #endregion

        #region Commands

        public ICommand RefreshAlertsCommand { get; private set; }
        public ICommand AlertSettingsCommand { get; private set; }
        public ICommand ExecuteAlertActionCommand { get; private set; }
        public ICommand DismissAlertCommand { get; private set; }
        public ICommand MarkAllAsReadCommand { get; private set; }
        public ICommand ClearAllAlertsCommand { get; private set; }

        private void InitializeCommands()
        {
            RefreshAlertsCommand = new RelayCommand(RefreshAlerts);
            AlertSettingsCommand = new RelayCommand(OpenAlertSettings);
            ExecuteAlertActionCommand = new RelayCommand<EnhancedSmartAlert>(ExecuteAlertAction);
            DismissAlertCommand = new RelayCommand<EnhancedSmartAlert>(DismissAlert);
            MarkAllAsReadCommand = new RelayCommand(MarkAllAsRead);
            ClearAllAlertsCommand = new RelayCommand(ClearAllAlerts);
        }

        #endregion

        #region Methods

        private void InitializeFilters()
        {
            AlertTypeFilters = new ObservableCollection<string>
            {
                "الكل",
                "حرجة",
                "مهمة", 
                "عادية",
                "منخفضة",
                "مدفوعات",
                "مخزون",
                "مواعيد",
                "موظفين",
                "نظام"
            };
        }

        private void UpdateFilteredAlerts()
        {
            if (Alerts == null) return;

            var filtered = Alerts.AsEnumerable();

            if (SelectedAlertTypeFilter != "الكل")
            {
                filtered = SelectedAlertTypeFilter switch
                {
                    "حرجة" => filtered.Where(a => a.Priority == AlertPriority.Critical),
                    "مهمة" => filtered.Where(a => a.Priority == AlertPriority.High),
                    "عادية" => filtered.Where(a => a.Priority == AlertPriority.Normal),
                    "منخفضة" => filtered.Where(a => a.Priority == AlertPriority.Low),
                    "مدفوعات" => filtered.Where(a => a.Type == AlertType.Payment),
                    "مخزون" => filtered.Where(a => a.Type == AlertType.Inventory),
                    "مواعيد" => filtered.Where(a => a.Type == AlertType.Appointment),
                    "موظفين" => filtered.Where(a => a.Type == AlertType.System),
                    "نظام" => filtered.Where(a => a.Type == AlertType.System),
                    _ => filtered
                };
            }

            FilteredAlerts = new ObservableCollection<EnhancedSmartAlert>(
                filtered.OrderByDescending(a => a.Priority)
                       .ThenByDescending(a => a.CreatedAt)
                       .Take(20));

            OnPropertyChanged(nameof(HasNoAlerts));
        }

        private void UpdateAlertCounts()
        {
            if (Alerts == null) return;

            TotalAlertsCount = Alerts.Count;
            UnreadAlertsCount = Alerts.Count(a => !a.IsRead);
            CriticalAlertsCount = Alerts.Count(a => a.Priority == AlertPriority.Critical);
            HighPriorityAlertsCount = Alerts.Count(a => a.Priority == AlertPriority.High);
            NormalAlertsCount = Alerts.Count(a => a.Priority == AlertPriority.Normal);
            LowPriorityAlertsCount = Alerts.Count(a => a.Priority == AlertPriority.Low);
        }

        private void RefreshAlerts()
        {
            // سيتم ربطها مع الخدمة الرئيسية
            AlertsRefreshRequested?.Invoke(this, EventArgs.Empty);
        }

        private void OpenAlertSettings()
        {
            // فتح نافذة إعدادات التنبيهات
            AlertSettingsRequested?.Invoke(this, EventArgs.Empty);
        }

        private void ExecuteAlertAction(EnhancedSmartAlert alert)
        {
            if (alert?.IsActionRequired == true)
            {
                AlertActionRequested?.Invoke(this, alert);
            }
        }

        private void DismissAlert(EnhancedSmartAlert alert)
        {
            if (alert != null)
            {
                AlertDismissRequested?.Invoke(this, alert);
            }
        }

        private void MarkAllAsRead()
        {
            foreach (var alert in Alerts.Where(a => !a.IsRead))
            {
                alert.IsRead = true;
            }
            UpdateAlertCounts();
            MarkAllAlertsAsReadRequested?.Invoke(this, EventArgs.Empty);
        }

        private void ClearAllAlerts()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من حذف جميع التنبيهات؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ClearAllAlertsRequested?.Invoke(this, EventArgs.Empty);
            }
        }

        #endregion

        #region Events

        public event EventHandler AlertsRefreshRequested;
        public event EventHandler AlertSettingsRequested;
        public event EventHandler<EnhancedSmartAlert> AlertActionRequested;
        public event EventHandler<EnhancedSmartAlert> AlertDismissRequested;
        public event EventHandler MarkAllAlertsAsReadRequested;
        public event EventHandler ClearAllAlertsRequested;

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
