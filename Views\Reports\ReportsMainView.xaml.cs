using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AqlanCenterProApp.ViewModels.Reports;

namespace AqlanCenterProApp.Views.Reports
{
    /// <summary>
    /// Interaction logic for ReportsMainView.xaml
    /// </summary>
    public partial class ReportsMainView : UserControl
    {
        public ReportsMainView()
        {
            InitializeComponent();
            Loaded += ReportsMainView_Loaded;
        }

        private void ReportsMainView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReportsMainViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadReportsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadReportsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات التقارير: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في ReportsMainView_Loaded: {ex.Message}");
            }
        }

        private void QuickReport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is QuickReport quickReport)
            {
                if (DataContext is ReportsMainViewModel viewModel)
                {
                    // تنفيذ الأمر من خلال ViewModel
                    if (viewModel.OpenQuickReportCommand.CanExecute(quickReport))
                    {
                        viewModel.OpenQuickReportCommand.Execute(quickReport);
                    }
                }
            }
        }

        private void Report_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is Models.Reports.ReportBase report)
            {
                if (DataContext is ReportsMainViewModel viewModel)
                {
                    viewModel.SelectedReport = report;
                }
            }
        }
    }
}