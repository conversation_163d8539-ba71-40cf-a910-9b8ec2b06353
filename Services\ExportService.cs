using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AqlanCenterProApp.Models;
using Microsoft.Win32;

namespace AqlanCenterProApp.Services
{
    /// <summary>
    /// خدمة التصدير والطباعة
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// تصدير قائمة الأطباء إلى CSV
        /// </summary>
        public async Task<bool> ExportDoctorsToCsvAsync(List<Doctor> doctors, string? filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                        DefaultExt = "csv",
                        FileName = $"Doctors_Export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                    };

                    if (saveFileDialog.ShowDialog() != true)
                        return false;

                    filePath = saveFileDialog.FileName;
                }

                var csv = new StringBuilder();
                
                // إضافة العناوين
                csv.AppendLine("الرقم,الاسم الكامل,التخصص,رقم الهاتف,رقم الجوال,البريد الإلكتروني,العنوان,نوع التعاقد,نسبة العمولة,الراتب الثابت,الحالة,تاريخ الانضمام,إجمالي المرضى,الجلسات المكتملة,إجمالي الأرباح,التقييم");

                // إضافة البيانات
                foreach (var doctor in doctors)
                {
                    csv.AppendLine($"{doctor.DoctorId}," +
                                 $"\"{doctor.FullName}\"," +
                                 $"\"{doctor.Specialization}\"," +
                                 $"\"{doctor.Phone}\"," +
                                 $"\"{doctor.Mobile}\"," +
                                 $"\"{doctor.Email}\"," +
                                 $"\"{doctor.Address}\"," +
                                 $"\"{doctor.ContractType}\"," +
                                 $"{doctor.CommissionPercentage}," +
                                 $"{doctor.FixedSalary}," +
                                 $"\"{doctor.Status}\"," +
                                 $"{doctor.JoinDate:yyyy-MM-dd}," +
                                 $"{doctor.TotalPatientsCount}," +
                                 $"{doctor.CompletedSessionsCount}," +
                                 $"{doctor.TotalEarnings}," +
                                 $"{doctor.Rating}");
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تصدير CSV: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تصدير قائمة الأطباء إلى HTML (للطباعة)
        /// </summary>
        public async Task<bool> ExportDoctorsToHtmlAsync(List<Doctor> doctors, string? filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "HTML files (*.html)|*.html|All files (*.*)|*.*",
                        DefaultExt = "html",
                        FileName = $"Doctors_Report_{DateTime.Now:yyyyMMdd_HHmmss}.html"
                    };

                    if (saveFileDialog.ShowDialog() != true)
                        return false;

                    filePath = saveFileDialog.FileName;
                }

                var html = GenerateHtmlReport(doctors);
                await File.WriteAllTextAsync(filePath, html, Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تصدير HTML: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// طباعة قائمة الأطباء
        /// </summary>
        public async Task<bool> PrintDoctorsListAsync(List<Doctor> doctors)
        {
            try
            {
                // إنشاء ملف HTML مؤقت
                var tempPath = Path.Combine(Path.GetTempPath(), $"Doctors_Print_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                var html = GenerateHtmlReport(doctors);
                await File.WriteAllTextAsync(tempPath, html, Encoding.UTF8);

                // فتح الملف في المتصفح للطباعة
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempPath,
                    UseShellExecute = true
                });

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الطباعة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء تقرير HTML
        /// </summary>
        private string GenerateHtmlReport(List<Doctor> doctors)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>تقرير الأطباء</title>");
            html.AppendLine("<style>");
            html.AppendLine(@"
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    margin: 20px; 
                    direction: rtl; 
                    text-align: right;
                }
                .header { 
                    text-align: center; 
                    margin-bottom: 30px; 
                    border-bottom: 2px solid #2196F3; 
                    padding-bottom: 20px;
                }
                .header h1 { 
                    color: #2196F3; 
                    margin: 0;
                }
                .header p { 
                    color: #666; 
                    margin: 5px 0;
                }
                table { 
                    width: 100%; 
                    border-collapse: collapse; 
                    margin-top: 20px;
                }
                th, td { 
                    border: 1px solid #ddd; 
                    padding: 12px; 
                    text-align: right;
                }
                th { 
                    background-color: #2196F3; 
                    color: white; 
                    font-weight: bold;
                }
                tr:nth-child(even) { 
                    background-color: #f9f9f9;
                }
                tr:hover { 
                    background-color: #f5f5f5;
                }
                .summary { 
                    background-color: #e3f2fd; 
                    padding: 15px; 
                    border-radius: 5px; 
                    margin-bottom: 20px;
                }
                .summary h3 { 
                    color: #1976d2; 
                    margin-top: 0;
                }
                .footer { 
                    margin-top: 30px; 
                    text-align: center; 
                    color: #666; 
                    border-top: 1px solid #ddd; 
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            ");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // العنوان
            html.AppendLine("<div class='header'>");
            html.AppendLine("<h1>🏥 مركز أقلان الطبي</h1>");
            html.AppendLine("<h2>📋 تقرير الأطباء</h2>");
            html.AppendLine($"<p>تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}</p>");
            html.AppendLine("</div>");

            // الملخص
            var totalDoctors = doctors.Count;
            var activeDoctors = doctors.Count(d => d.IsActive);
            var totalEarnings = doctors.Sum(d => d.TotalEarnings);
            var totalPatients = doctors.Sum(d => d.TotalPatientsCount);

            html.AppendLine("<div class='summary'>");
            html.AppendLine("<h3>📊 ملخص الإحصائيات</h3>");
            html.AppendLine($"<p><strong>إجمالي الأطباء:</strong> {totalDoctors}</p>");
            html.AppendLine($"<p><strong>الأطباء النشطون:</strong> {activeDoctors}</p>");
            html.AppendLine($"<p><strong>إجمالي الأرباح:</strong> {totalEarnings:N0} ريال</p>");
            html.AppendLine($"<p><strong>إجمالي المرضى:</strong> {totalPatients}</p>");
            html.AppendLine("</div>");

            // الجدول
            html.AppendLine("<table>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>الرقم</th>");
            html.AppendLine("<th>الاسم الكامل</th>");
            html.AppendLine("<th>التخصص</th>");
            html.AppendLine("<th>رقم الجوال</th>");
            html.AppendLine("<th>البريد الإلكتروني</th>");
            html.AppendLine("<th>نوع التعاقد</th>");
            html.AppendLine("<th>الحالة</th>");
            html.AppendLine("<th>تاريخ الانضمام</th>");
            html.AppendLine("<th>عدد المرضى</th>");
            html.AppendLine("<th>الجلسات</th>");
            html.AppendLine("<th>الأرباح</th>");
            html.AppendLine("<th>التقييم</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var doctor in doctors.OrderBy(d => d.FullName))
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td>{doctor.DoctorId}</td>");
                html.AppendLine($"<td><strong>{doctor.FullName}</strong></td>");
                html.AppendLine($"<td>{doctor.Specialization}</td>");
                html.AppendLine($"<td>{doctor.Mobile}</td>");
                html.AppendLine($"<td>{doctor.Email}</td>");
                html.AppendLine($"<td>{doctor.ContractType}</td>");
                html.AppendLine($"<td><span style='color: {(doctor.IsActive ? "green" : "red")}'>{doctor.Status}</span></td>");
                html.AppendLine($"<td>{doctor.JoinDate:yyyy-MM-dd}</td>");
                html.AppendLine($"<td>{doctor.TotalPatientsCount}</td>");
                html.AppendLine($"<td>{doctor.CompletedSessionsCount}</td>");
                html.AppendLine($"<td>{doctor.TotalEarnings:N0}</td>");
                html.AppendLine($"<td>{doctor.Rating:F1}/5</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");

            // التذييل
            html.AppendLine("<div class='footer'>");
            html.AppendLine("<p>© 2024 مركز أقلان الطبي - جميع الحقوق محفوظة</p>");
            html.AppendLine($"<p>تم إنشاء هذا التقرير في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// تصدير إحصائيات طبيب واحد
        /// </summary>
        public async Task<bool> ExportDoctorStatisticsAsync(Doctor doctor, string? filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "HTML files (*.html)|*.html|All files (*.*)|*.*",
                        DefaultExt = "html",
                        FileName = $"Doctor_{doctor.FullName}_Statistics_{DateTime.Now:yyyyMMdd_HHmmss}.html"
                    };

                    if (saveFileDialog.ShowDialog() != true)
                        return false;

                    filePath = saveFileDialog.FileName;
                }

                var html = GenerateDoctorStatisticsHtml(doctor);
                await File.WriteAllTextAsync(filePath, html, Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تصدير إحصائيات الطبيب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء HTML لإحصائيات طبيب واحد
        /// </summary>
        private string GenerateDoctorStatisticsHtml(Doctor doctor)
        {
            // سيتم تنفيذ هذه الوظيفة لاحقاً
            return $"<html><body><h1>إحصائيات الطبيب: {doctor.FullName}</h1></body></html>";
        }
    }
}
