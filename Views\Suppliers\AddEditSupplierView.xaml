<Window x:Class="AqlanCenterProApp.Views.Suppliers.AddEditSupplierView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:AqlanCenterProApp.Views.Suppliers"
        mc:Ignorable="d"
        Title="إضافة/تعديل مورد" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="إضافة/تعديل مورد" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="حفظ" 
                            Command="{Binding SaveCommand}"/>
                    <Button Content="إلغاء" 
                            Command="{Binding CancelCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Border Background="White" Padding="30" Margin="0,0,0,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Column -->
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="الاسم *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.Name, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"/>

                        <TextBlock Text="رقم الهاتف *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.Phone, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"/>

                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.Email, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"/>

                        <TextBlock Text="رقم الهوية الضريبية" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.TaxNumber, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"/>
                    </StackPanel>

                    <!-- Right Column -->
                    <StackPanel Grid.Column="1" Margin="15,0,0,0">
                        <TextBlock Text="العنوان" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.Address, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="80"
                                 VerticalScrollBarVisibility="Auto"/>

                        <TextBlock Text="ملاحظات" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Text="{Binding Supplier.Notes, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="80"
                                 VerticalScrollBarVisibility="Auto"/>

                        <CheckBox Content="نشط" 
                                  IsChecked="{Binding Supplier.IsActive}"
                                  Style="{StaticResource ModernCheckBoxStyle}"/>

                        <TextBlock Text="تاريخ الإنشاء" Style="{StaticResource FormLabelStyle}"/>
                        <TextBlock Text="{Binding Supplier.CreatedAt, StringFormat='dd/MM/yyyy HH:mm'}" 
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Background="#F5F5F5"
                                   IsEnabled="False"/>
                    </StackPanel>
                </Grid>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="حفظ" 
                        Command="{Binding SaveCommand}"/>
                <Button Content="إلغاء" 
                        Command="{Binding CancelCommand}"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" 
                            Width="100" 
                            Height="4" 
                            Margin="0,0,0,10"/>
                <TextBlock Text="جاري الحفظ..." 
                          Foreground="White" 
                          FontSize="16" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 