using System.Collections.ObjectModel;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Services;
using System.Windows;

namespace AqlanCenterProApp.ViewModels.OrthodonticPlans
{
    /// <summary>
    /// ViewModel لإدارة قائمة خطط علاج التقويم
    /// </summary>
    public class OrthodonticPlansListViewModel : BaseViewModel
    {
        private readonly IOrthodonticPlanService _orthodonticPlanService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;

        #region Properties

        private ObservableCollection<OrthodonticPlan> _plans = new();
        private OrthodonticPlan? _selectedPlan;
        private string _searchText = string.Empty;
        private string _selectedStatus = "الكل";
        private OrthodonticStatistics? _statistics;

        /// <summary>
        /// قائمة خطط التقويم
        /// </summary>
        public ObservableCollection<OrthodonticPlan> Plans
        {
            get => _plans;
            set => SetProperty(ref _plans, value);
        }

        /// <summary>
        /// الخطة المحددة
        /// </summary>
        public OrthodonticPlan? SelectedPlan
        {
            get => _selectedPlan;
            set => SetProperty(ref _selectedPlan, value, OnSelectedPlanChanged);
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value, OnSearchTextChanged);
        }

        /// <summary>
        /// الحالة المحددة للفلترة
        /// </summary>
        public string SelectedStatus
        {
            get => _selectedStatus;
            set => SetProperty(ref _selectedStatus, value, OnFilterChanged);
        }

        /// <summary>
        /// إحصائيات خطط التقويم
        /// </summary>
        public OrthodonticStatistics? Statistics
        {
            get => _statistics;
            set => SetProperty(ref _statistics, value);
        }

        /// <summary>
        /// قائمة الحالات المتاحة
        /// </summary>
        public ObservableCollection<string> StatusOptions { get; } = new()
        {
            "الكل",
            "نشطة",
            "مكتملة",
            "متأخرة"
        };

        /// <summary>
        /// هل يوجد خطة محددة
        /// </summary>
        public bool HasSelectedPlan => SelectedPlan != null;

        /// <summary>
        /// هل الخطة المحددة نشطة
        /// </summary>
        public bool IsSelectedPlanActive => SelectedPlan?.Status == "نشطة";

        /// <summary>
        /// هل يمكن إنشاء مواعيد للخطة المحددة
        /// </summary>
        public bool CanGenerateAppointments => SelectedPlan != null &&
                                              SelectedPlan.Status == "نشطة" &&
                                              !SelectedPlan.AppointmentsGenerated;

        #endregion

        #region Commands

        /// <summary>
        /// أمر إضافة خطة جديدة
        /// </summary>
        public ICommand AddPlanCommand { get; }

        /// <summary>
        /// أمر تعديل الخطة المحددة
        /// </summary>
        public ICommand EditPlanCommand { get; }

        /// <summary>
        /// أمر حذف الخطة المحددة
        /// </summary>
        public ICommand DeletePlanCommand { get; }

        /// <summary>
        /// أمر إنشاء مواعيد تلقائية
        /// </summary>
        public ICommand GenerateAppointmentsCommand { get; }

        /// <summary>
        /// أمر إنشاء موعد واحد
        /// </summary>
        public ICommand CreateNextAppointmentCommand { get; }

        /// <summary>
        /// أمر إعادة جدولة المواعيد
        /// </summary>
        public ICommand RescheduleAppointmentsCommand { get; }

        /// <summary>
        /// أمر تحديث التقدم
        /// </summary>
        public ICommand UpdateProgressCommand { get; }

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// أمر مسح البحث
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// أمر تحديث
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// أمر عرض الإحصائيات
        /// </summary>
        public ICommand ShowStatisticsCommand { get; }

        /// <summary>
        /// أمر عرض المرضى المتخلفين
        /// </summary>
        public ICommand ShowOverduePatientsCommand { get; }

        /// <summary>
        /// أمر تصدير البيانات
        /// </summary>
        public ICommand ExportCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// حدث طلب إضافة خطة جديدة
        /// </summary>
        public event Action? AddPlanRequested;

        /// <summary>
        /// حدث طلب تعديل خطة
        /// </summary>
        public event Action<OrthodonticPlan>? EditPlanRequested;

        /// <summary>
        /// حدث طلب عرض تفاصيل خطة
        /// </summary>
        public event Action<OrthodonticPlan>? ViewPlanDetailsRequested;

        /// <summary>
        /// إثارة حدث عرض تفاصيل الخطة
        /// </summary>
        protected virtual void OnViewPlanDetailsRequested(OrthodonticPlan plan)
        {
            ViewPlanDetailsRequested?.Invoke(plan);
        }

        #endregion

        public OrthodonticPlansListViewModel(
            IOrthodonticPlanService orthodonticPlanService,
            IPatientService patientService,
            IDoctorService doctorService)
        {
            _orthodonticPlanService = orthodonticPlanService;
            _patientService = patientService;
            _doctorService = doctorService;

            // تهيئة الأوامر
            AddPlanCommand = new RelayCommand(_ => AddPlan());
            EditPlanCommand = new RelayCommand(_ => EditPlan(), _ => HasSelectedPlan);
            DeletePlanCommand = new RelayCommand(async _ => await DeletePlanAsync(), _ => HasSelectedPlan);
            GenerateAppointmentsCommand = new RelayCommand(async _ => await GenerateAppointmentsAsync(), _ => CanGenerateAppointments);
            CreateNextAppointmentCommand = new RelayCommand(async _ => await CreateNextAppointmentAsync(), _ => IsSelectedPlanActive);
            RescheduleAppointmentsCommand = new RelayCommand(async _ => await RescheduleAppointmentsAsync(), _ => IsSelectedPlanActive);
            UpdateProgressCommand = new RelayCommand(async _ => await UpdateProgressAsync(), _ => HasSelectedPlan);
            SearchCommand = new RelayCommand(async _ => await SearchAsync());
            ClearSearchCommand = new RelayCommand(_ => ClearSearch());
            RefreshCommand = new RelayCommand(async _ => await LoadPlansAsync());
            ShowStatisticsCommand = new RelayCommand(async _ => await LoadStatisticsAsync());
            ShowOverduePatientsCommand = new RelayCommand(async _ => await ShowOverduePatientsAsync());
            ExportCommand = new RelayCommand(async _ => await ExportAsync());

            // تحميل البيانات الأولية
            _ = LoadPlansAsync();
            _ = LoadStatisticsAsync();
        }

        #region Public Methods

        /// <summary>
        /// تحميل خطط التقويم
        /// </summary>
        public async Task LoadPlansAsync()
        {
            await ExecuteAsync(async () =>
            {
                IsLoading = true;
                Plans.Clear();

                IEnumerable<OrthodonticPlan> plans;

                switch (SelectedStatus)
                {
                    case "نشطة":
                        plans = await _orthodonticPlanService.GetActivePlansAsync();
                        break;
                    case "مكتملة":
                        plans = await _orthodonticPlanService.GetCompletedPlansAsync();
                        break;
                    case "متأخرة":
                        plans = await _orthodonticPlanService.GetOverduePlansAsync();
                        break;
                    default:
                        plans = await _orthodonticPlanService.GetAllPlansAsync();
                        break;
                }

                foreach (var plan in plans)
                {
                    Plans.Add(plan);
                }

                IsLoading = false;
            }, "جاري تحميل خطط التقويم...");
        }

        /// <summary>
        /// تحميل الإحصائيات
        /// </summary>
        public async Task LoadStatisticsAsync()
        {
            await ExecuteAsync(async () =>
            {
                Statistics = await _orthodonticPlanService.GetStatisticsAsync();
            }, "جاري تحميل الإحصائيات...");
        }

        /// <summary>
        /// إضافة خطة جديدة
        /// </summary>
        public void AddPlan()
        {
            AddPlanRequested?.Invoke();
        }

        /// <summary>
        /// تعديل الخطة المحددة
        /// </summary>
        public void EditPlan()
        {
            if (SelectedPlan != null)
            {
                EditPlanRequested?.Invoke(SelectedPlan);
            }
        }

        /// <summary>
        /// حذف الخطة المحددة
        /// </summary>
        public async Task DeletePlanAsync()
        {
            if (SelectedPlan == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف خطة التقويم للمريض {SelectedPlan.Patient.FullName}؟\nسيتم حذف جميع المواعيد المرتبطة أيضاً.", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;

            if (result)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _orthodonticPlanService.DeletePlanAsync(SelectedPlan.Id);
                    if (success)
                    {
                        Plans.Remove(SelectedPlan);
                        SelectedPlan = null;
                        await LoadStatisticsAsync();
                        MessageBox.Show("تم حذف خطة التقويم بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف خطة التقويم", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري حذف خطة التقويم...");
            }
        }

        /// <summary>
        /// إنشاء مواعيد تلقائية
        /// </summary>
        public async Task GenerateAppointmentsAsync()
        {
            if (SelectedPlan == null) return;

            var result = MessageBox.Show($"هل تريد إنشاء {SelectedPlan.TotalSessions} موعد تلقائياً لخطة التقويم؟\nسيتم إنشاء المواعيد من تاريخ {SelectedPlan.StartDate:yyyy/MM/dd}", "تأكيد إنشاء المواعيد", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;

            if (result)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _orthodonticPlanService.GenerateAppointmentsAsync(SelectedPlan.Id);
                    if (success)
                    {
                        await LoadPlansAsync();
                        MessageBox.Show($"تم إنشاء {SelectedPlan.TotalSessions} موعد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء المواعيد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري إنشاء المواعيد...");
            }
        }

        /// <summary>
        /// إنشاء موعد واحد
        /// </summary>
        public async Task CreateNextAppointmentAsync()
        {
            if (SelectedPlan == null) return;

            await ExecuteAsync(async () =>
            {
                var appointment = await _orthodonticPlanService.CreateNextAppointmentAsync(SelectedPlan.Id);
                if (appointment != null)
                {
                    await LoadPlansAsync();
                    MessageBox.Show("تم إنشاء الموعد التالي بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا يمكن إنشاء موعد جديد - الخطة مكتملة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }, "جاري إنشاء الموعد...");
        }

        /// <summary>
        /// إعادة جدولة المواعيد
        /// </summary>
        public async Task RescheduleAppointmentsAsync()
        {
            if (SelectedPlan == null) return;

            // هنا يمكن إضافة نافذة لاختيار التاريخ الجديد
            var newStartDate = DateTime.Today.AddDays(7); // مثال: بعد أسبوع

            var result = MessageBox.Show($"هل تريد إعادة جدولة المواعيد المتبقية ليبدأ من {newStartDate:yyyy/MM/dd}؟", "تأكيد إعادة الجدولة", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;

            if (result)
            {
                await ExecuteAsync(async () =>
                {
                    var success = await _orthodonticPlanService.RescheduleRemainingAppointmentsAsync(SelectedPlan.Id, newStartDate);
                    if (success)
                    {
                        await LoadPlansAsync();
                        MessageBox.Show("تم إعادة جدولة المواعيد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إعادة جدولة المواعيد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }, "جاري إعادة جدولة المواعيد...");
            }
        }

        /// <summary>
        /// تحديث التقدم
        /// </summary>
        public async Task UpdateProgressAsync()
        {
            if (SelectedPlan == null) return;

            await ExecuteAsync(async () =>
            {
                await _orthodonticPlanService.UpdatePlanProgressAsync(SelectedPlan.Id);
                await LoadPlansAsync();
                MessageBox.Show("تم تحديث التقدم بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }, "جاري تحديث التقدم...");
        }

        /// <summary>
        /// البحث
        /// </summary>
        public async Task SearchAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadPlansAsync();
                return;
            }

            await ExecuteAsync(async () =>
            {
                IsLoading = true;
                Plans.Clear();

                var searchResults = await _orthodonticPlanService.SearchPlansAsync(SearchText);
                foreach (var plan in searchResults)
                {
                    Plans.Add(plan);
                }

                IsLoading = false;
            }, "جاري البحث...");
        }

        /// <summary>
        /// مسح البحث
        /// </summary>
        public void ClearSearch()
        {
            SearchText = string.Empty;
            // Fire-and-forget, intentionally not awaited
            _ = LoadPlansAsync();
        }

        /// <summary>
        /// عرض المرضى المتخلفين
        /// </summary>
        public async Task ShowOverduePatientsAsync()
        {
            await ExecuteAsync(async () =>
            {
                var overduePatients = await _orthodonticPlanService.GetPatientsWithMissedOrthodonticSessionsAsync();
                var message = overduePatients.Any()
                    ? $"عدد المرضى المتخلفين: {overduePatients.Count()}\n\n" +
                      string.Join("\n", overduePatients.Select(p => $"• {p.FullName} - {p.Phone}"))
                    : "لا يوجد مرضى متخلفين عن جلسات التقويم";

                MessageBox.Show(message, "المرضى المتخلفين", MessageBoxButton.OK, MessageBoxImage.Information);
            }, "جاري تحميل البيانات...");
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        public Task ExportAsync()
        {
            return ExecuteAsync(async () =>
            {
                // هنا يمكن إضافة منطق التصدير
                MessageBox.Show("سيتم إضافة ميزة التصدير قريباً", "تصدير البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
            }, "جاري التصدير...");
        }

        #endregion

        #region Private Methods

        private void OnSelectedPlanChanged()
        {
            OnPropertyChanged(nameof(HasSelectedPlan));
            OnPropertyChanged(nameof(IsSelectedPlanActive));
            OnPropertyChanged(nameof(CanGenerateAppointments));
        }

        private void OnSearchTextChanged()
        {
            // يمكن إضافة تأخير للبحث التلقائي هنا
        }

        private async void OnFilterChanged()
        {
            await LoadPlansAsync();
        }

        #endregion
    }
}