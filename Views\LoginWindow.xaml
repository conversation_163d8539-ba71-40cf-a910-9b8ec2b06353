<Window x:Class="AqlanCenterProApp.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - مركز الدكتور عقلان"
        Height="500"
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        ShowInTaskbar="True"
        Background="White">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- الهيدر -->
        <Border Grid.Row="0"
                Background="{StaticResource PrimaryBrush}"
                Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <Ellipse Width="80"
                         Height="80"
                         Margin="0,0,0,10">
                    <Ellipse.Fill>
                        <SolidColorBrush Color="White"
                                         Opacity="0.2"/>
                    </Ellipse.Fill>
                </Ellipse>
                <TextBlock Text="مركز الدكتور عقلان الكامل"
                           FontSize="18"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"/>
                <TextBlock Text="تسجيل الدخول"
                           FontSize="14"
                           Foreground="{StaticResource AccentBrush}"
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- محتوى تسجيل الدخول -->
        <StackPanel Grid.Row="1"
                    Margin="40,50,40,40"
                    VerticalAlignment="Center">

            <!-- اسم المستخدم -->
            <TextBlock Text="اسم المستخدم:"
                       FontSize="14"
                       Margin="0,0,0,5"/>
            <TextBox x:Name="UsernameTextBox"
                     Text="admin"
                     FontSize="14"
                     Padding="10"
                     Margin="0,0,0,20"
                     BorderBrush="#DDDDDD"
                     BorderThickness="1"/>

            <!-- كلمة المرور -->
            <TextBlock Text="كلمة المرور:"
                       FontSize="14"
                       Margin="0,0,0,5"/>
            <PasswordBox x:Name="PasswordBox"
                         Password="admin123"
                         FontSize="14"
                         Padding="10"
                         Margin="0,0,0,30"
                         BorderBrush="#DDDDDD"
                         BorderThickness="1"/>

            <!-- زر تسجيل الدخول -->
            <Button Content="تسجيل الدخول"
                    Click="OnLoginClick"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    FontSize="16"
                    Padding="20,12"
                    Margin="0,0,0,20"/>

            <!-- رسالة الخطأ -->
            <TextBlock x:Name="ErrorMessageTextBlock"
                       FontSize="12"
                       Foreground="Red"
                       HorizontalAlignment="Center"
                       Visibility="Collapsed"/>

        </StackPanel>
    </Grid>
</Window>
