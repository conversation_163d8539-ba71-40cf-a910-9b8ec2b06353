using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.AccountStatements;

namespace AqlanCenterProApp.Views.AccountStatements
{
    /// <summary>
    /// Interaction logic for AccountStatementsView.xaml
    /// </summary>
    public partial class AccountStatementsView : UserControl
    {
        public AccountStatementsView()
        {
            InitializeComponent();
        }

        public AccountStatementsView(AccountStatementsListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += AccountStatementsView_Loaded;
        }

        private void AccountStatementsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is AccountStatementsListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadAccountStatementsAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadAccountStatementsAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات كشف الحساب: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في AccountStatementsView_Loaded: {ex.Message}");
            }
        }
    }
}