<Window x:Class="AqlanCenterProApp.Views.Appointments.AddEditAppointmentView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AqlanCenterProApp.Views.Appointments"
        mc:Ignorable="d"
        Title="إضافة موعد جديد" Height="700" Width="900"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        Background="#FAFAFA"
        ResizeMode="CanResize"
        WindowStyle="None"
        AllowsTransparency="True">
    
    <Window.Resources>
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Background" Value="#2980B9"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#3498DB"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Background" Value="#95A5A6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#7F8C8D"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="10" BorderThickness="1" BorderBrush="#E0E0E0">
        <Grid Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="#2C3E50" CornerRadius="10,10,0,0">
                <DockPanel LastChildFill="True" VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right" Margin="20,0">
                        <TextBlock Text="📅" FontSize="24" Margin="0,0,15,0" VerticalAlignment="Center"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إضافة موعد جديد" FontSize="18" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="إدخال بيانات الموعد الجديد" FontSize="12" Foreground="#BDC3C7" Margin="0,2,0,0"/>
                        </StackPanel>
                    </StackPanel>
                    <Button Content="✕" Width="40" Height="40" 
                            Style="{StaticResource SecondaryButton}" 
                            BorderThickness="0"
                            Command="{Binding CancelCommand}" Margin="10"/>
                </DockPanel>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
                <StackPanel>
                    <!-- Patient -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="👤 المريض" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                            <ComboBox ItemsSource="{Binding Patients}" 
                                      SelectedItem="{Binding SelectedPatient}"
                                      DisplayMemberPath="FullName"
                                      Style="{StaticResource ModernComboBox}"
                                      Tag="اختر المريض..."/>
                        </StackPanel>
                    </Border>

                    <!-- Orthodontic Plan -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="🦷 خطة التقويم (اختياري)" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                            <ComboBox ItemsSource="{Binding OrthodonticPlans}"
                                      SelectedItem="{Binding SelectedOrthodonticPlan}"
                                      DisplayMemberPath="PlanName"
                                      Style="{StaticResource ModernComboBox}"
                                      Tag="اختر خطة التقويم (اختياري)"/>
                        </StackPanel>
                    </Border>

                    <!-- Doctor -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="👨‍⚕️ الطبيب" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                            <ComboBox ItemsSource="{Binding Doctors}" 
                                      SelectedItem="{Binding SelectedDoctor}"
                                      DisplayMemberPath="FullName"
                                      Style="{StaticResource ModernComboBox}"
                                      Tag="اختر الطبيب..."/>
                        </StackPanel>
                    </Border>

                    <!-- Service Type & Appointment Type -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <TextBlock Text="🏥 نوع الخدمة" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                                <ComboBox ItemsSource="{Binding ServiceTypes}" 
                                          SelectedItem="{Binding ServiceType}"
                                          Style="{StaticResource ModernComboBox}"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#F8F9FA" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <TextBlock Text="📋 نوع الموعد" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                                <ComboBox ItemsSource="{Binding AppointmentTypes}" 
                                          SelectedItem="{Binding AppointmentType}"
                                          Style="{StaticResource ModernComboBox}"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- Date, Time, Duration -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <TextBlock Text="📅 التاريخ" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                                <DatePicker SelectedDate="{Binding AppointmentDate}" 
                                           Style="{StaticResource ModernDatePicker}"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#F8F9FA" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <TextBlock Text="🕐 الوقت" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                                <TextBox Text="{Binding AppointmentTime, StringFormat=HH:mm}" 
                                         Style="{StaticResource ModernTextBox}"
                                         Tag="HH:MM"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="4" Background="#F8F9FA" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <TextBlock Text="⏱️ المدة (دقيقة)" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                                <TextBox Text="{Binding DurationMinutes}" 
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- Notes -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات" FontSize="15" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,10"/>
                            <TextBox Text="{Binding Notes}" 
                                     Style="{StaticResource ModernTextBox}"
                                     Height="80"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     Tag="أدخل الملاحظات هنا..."/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer -->
            <Border Grid.Row="2" Background="#ECF0F1" CornerRadius="0,0,10,10" Padding="20">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="💾 حفظ الموعد" 
                            Command="{Binding SaveCommand}"
                            Style="{StaticResource PrimaryButton}"
                            Margin="0,0,10,0"/>
                    <Button Content="❌ إلغاء" 
                            Command="{Binding CancelCommand}"
                            Style="{StaticResource SecondaryButton}"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window> 