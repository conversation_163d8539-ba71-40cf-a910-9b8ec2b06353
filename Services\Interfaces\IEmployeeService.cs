using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الموظفين
    /// </summary>
    public interface IEmployeeService
    {
        // إدارة الموظفين الأساسية
        Task<List<Employee>> GetAllEmployeesAsync();
        Task<List<Employee>> GetActiveEmployeesAsync();
        Task<Employee?> GetEmployeeByIdAsync(int employeeId);
        Task<List<Employee>> SearchEmployeesAsync(string searchTerm);
        Task<List<Employee>> GetEmployeesByJobTitleAsync(string jobTitle);
        Task<List<Employee>> GetEmployeesByStatusAsync(string status);
        Task<Employee> AddEmployeeAsync(Employee employee, string createdBy);
        Task<bool> UpdateEmployeeAsync(Employee employee, string updatedBy);
        Task<bool> DeleteEmployeeAsync(int employeeId);
        Task<bool> ToggleEmployeeStatusAsync(int employeeId, string updatedBy);

        // إدارة الحضور والانصراف
        Task<List<EmployeeAttendance>> GetEmployeeAttendanceAsync(int employeeId, DateTime startDate, DateTime endDate);
        Task<List<EmployeeAttendance>> GetAttendanceByDateAsync(DateTime date);
        Task<bool> AddAttendanceAsync(EmployeeAttendance attendance);
        Task<bool> UpdateAttendanceAsync(EmployeeAttendance attendance);
        Task<bool> DeleteAttendanceAsync(int attendanceId);
        Task<EmployeeAttendance?> GetAttendanceByIdAsync(int attendanceId);

        // إدارة الرواتب
        Task<List<EmployeeSalary>> GetEmployeeSalariesAsync(int employeeId, int year);
        Task<EmployeeSalary?> GetSalaryByMonthAsync(int employeeId, int month, int year);
        Task<bool> AddSalaryAsync(EmployeeSalary salary);
        Task<bool> UpdateSalaryAsync(EmployeeSalary salary);
        Task<bool> DeleteSalaryAsync(int salaryId);
        Task<List<EmployeeSalary>> GetSalariesByMonthAsync(int month, int year);

        // إدارة الإجازات
        Task<List<EmployeeLeave>> GetEmployeeLeavesAsync(int employeeId);
        Task<List<EmployeeLeave>> GetLeavesByStatusAsync(string status);
        Task<bool> AddLeaveAsync(EmployeeLeave leave);
        Task<bool> UpdateLeaveAsync(EmployeeLeave leave);
        Task<bool> DeleteLeaveAsync(int leaveId);
        Task<bool> ApproveLeaveAsync(int leaveId, string approvedBy, string notes);
        Task<bool> RejectLeaveAsync(int leaveId, string rejectedBy, string notes);

        // إدارة المستندات
        Task<List<EmployeeDocument>> GetEmployeeDocumentsAsync(int employeeId);
        Task<List<EmployeeDocument>> GetDocumentsByTypeAsync(string documentType);
        Task<bool> AddDocumentAsync(EmployeeDocument document);
        Task<bool> UpdateDocumentAsync(EmployeeDocument document);
        Task<bool> DeleteDocumentAsync(int documentId);
        Task<List<EmployeeDocument>> GetExpiringDocumentsAsync(int daysBeforeExpiry = 30);

        // الإحصائيات والتقارير
        Task<EmployeeStatistics> GetEmployeeStatisticsAsync(int employeeId);
        Task<GeneralEmployeeStatistics> GetGeneralStatisticsAsync();
        Task<AttendanceStatistics> GetAttendanceStatisticsAsync(DateTime startDate, DateTime endDate);
        Task<SalaryStatistics> GetSalaryStatisticsAsync(int month, int year);
    }
} 