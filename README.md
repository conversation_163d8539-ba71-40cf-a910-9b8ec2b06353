# مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان
## AqlanCenterProApp

### نظرة عامة
نظام إدارة عيادة أسنان متكامل مطور باستخدام WPF .NET 8 مع دعم كامل للغة العربية وواجهة RTL.

### المتطلبات التقنية
- **.NET 8.0 LTS** (Long Term Support)
- **Windows 10/11** أو أحدث
- **Visual Studio 2022** أو **Visual Studio Code**
- **SQL Server LocalDB** أو **SQLite** (افتراضي)

### الحزم البرمجية المستخدمة
```xml
<!-- Entity Framework Core for .NET 8 LTS -->
Microsoft.EntityFrameworkCore.Sqlite v8.0.11
Microsoft.EntityFrameworkCore.Tools v8.0.11

<!-- MVVM Community Toolkit -->
CommunityToolkit.Mvvm v8.4.0

<!-- Microsoft Extensions for .NET 8 LTS -->
Microsoft.Extensions.Hosting v8.0.1
Microsoft.Extensions.Configuration.Json v8.0.1
Microsoft.Extensions.DependencyInjection v8.0.1

<!-- Security & Cryptography -->
BCrypt.Net-Next v4.0.3
```

### هيكلية المشروع
```
AqlanCenterProApp/
├── Data/              # قاعدة البيانات وإعداداتها
├── Models/            # النماذج البرمجية (Entities)
├── Repositories/      # طبقة الوصول للبيانات
├── Services/          # منطق الأعمال
├── ViewModels/        # نماذج العرض (MVVM)
├── Views/             # واجهات المستخدم
│   └── Controls/      # عناصر التحكم المخصصة
├── Helpers/           # أدوات مساعدة
├── Resources/         # الموارد (صور، خطوط، ملفات)
└── App.xaml          # إعدادات التطبيق العامة
```

### المميزات الرئيسية
- ✅ **واجهة عربية كاملة** مع دعم RTL
- ✅ **تصميم عصري** بألوان المركز الرسمية
- ✅ **أمان متقدم** مع تشفير كلمات المرور
- ✅ **قاعدة بيانات محلية** SQLite
- ✅ **نسخ احتياطي** تلقائي
- ✅ **تقارير شاملة** قابلة للتصدير
- ✅ **إدارة صلاحيات** متدرجة

### الوحدات المطورة
1. **إدارة المرضى** - ملفات كاملة مع التاريخ الطبي
2. **إدارة الأطباء** - معلومات ونسب الأطباء
3. **إدارة الموظفين** - الحضور والرواتب
4. **إدارة المواعيد** - تقويم ذكي مع التنبيهات
5. **الفواتير والمدفوعات** - نظام مالي متكامل
6. **المعامل والمختبرات** - تتبع الطلبات
7. **المشتريات والمخزون** - إدارة المواد
8. **المستخدمين والصلاحيات** - أمان النظام
9. **التقارير الذكية** - تحليلات شاملة
10. **النسخ الاحتياطي** - حماية البيانات

### تشغيل المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd AqlanCenterProApp

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

### إعدادات قاعدة البيانات
يتم إنشاء قاعدة البيانات تلقائياً في المجلد `Data/AqlanCenter.db` عند أول تشغيل.

### المستخدم الافتراضي
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحية:** مدير النظام

### الدعم والتطوير
تم تطوير هذا النظام خصيصاً لمركز الدكتور عقلان مع إمكانية التخصيص والتوسع حسب الحاجة.

### الترخيص
جميع الحقوق محفوظة © 2024 مركز الدكتور عقلان الكامل
