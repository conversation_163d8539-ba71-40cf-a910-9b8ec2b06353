<UserControl x:Class="AqlanCenterProApp.Views.OrthodonticPlans.OrthodonticPlansView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             Background="#F5F6FA"
             d:DesignWidth="1200"
             d:DesignHeight="800">

    <UserControl.Resources>
        <converters:BoolToTextConverter x:Key="BoolToTextConverter" TrueText="مُنشأة" FalseText="غير مُنشأة"/>
        
        <!-- أنماط الألوان حسب حالة الخطة -->
        <Style x:Key="StatusStyle" TargetType="TextBlock">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="نشطة">
                    <Setter Property="Foreground" Value="#27AE60"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="مكتملة">
                    <Setter Property="Foreground" Value="#2980B9"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="متأخرة">
                    <Setter Property="Foreground" Value="#E74C3C"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="ملغية">
                    <Setter Property="Foreground" Value="#95A5A6"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- نمط شريط التقدم -->
        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="8"/>
            <Setter Property="Background" Value="#ECF0F1"/>
            <Setter Property="Foreground" Value="#27AE60"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20" Orientation="Vertical">
            
            <!-- العنوان الرئيسي -->
            <Border Background="White" CornerRadius="10" Padding="20" Margin="0,0,0,20" 
                    Effect="{StaticResource CardShadow}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="🦷" FontSize="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إدارة خطط علاج التقويم" FontSize="24" FontWeight="Bold" Foreground="#2D3E50"/>
                            <TextBlock Text="إدارة وتنظيم خطط علاج التقويم للمرضى" FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Content="➕ إضافة خطة" Command="{Binding AddPlanCommand}" 
                                Height="36" Width="120" Style="{StaticResource PrimaryButton}" Margin="0,0,10,0"/>
                        <Button Content="🔄 تحديث" Command="{Binding RefreshCommand}" 
                                Height="36" Width="80" Style="{StaticResource AccentButton}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- كروت الإحصائيات -->
            <UniformGrid Columns="4" Margin="0,0,0,20" HorizontalAlignment="Center">
                <Border Background="#E3F0FF" CornerRadius="10" Margin="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="إجمالي الخطط" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding Statistics.TotalPlans}" FontSize="30" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#2980B9"/>
                    </StackPanel>
                </Border>
                
                <Border Background="#E3FFD3" CornerRadius="10" Margin="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="الخطط النشطة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding Statistics.ActivePlans}" FontSize="30" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#27AE60"/>
                    </StackPanel>
                </Border>
                
                <Border Background="#FFF9D3" CornerRadius="10" Margin="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="الجلسات المكتملة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding Statistics.CompletedSessions}" FontSize="30" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#F39C12"/>
                    </StackPanel>
                </Border>
                
                <Border Background="#FFD7D7" CornerRadius="10" Margin="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="الخطط المتأخرة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding Statistics.OverduePlans}" FontSize="30" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#E74C3C"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <!-- شريط البحث والفلترة -->
            <Border Background="White" CornerRadius="10" Padding="15" Margin="0,0,0,20" 
                    Effect="{StaticResource CardShadow}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" Height="36" Margin="0,0,10,0" VerticalAlignment="Center"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBox}"
                             Tag="بحث بالاسم أو نوع العلاج..."/>

                    <ComboBox Grid.Column="1" Width="120" Height="36" Margin="0,0,10,0" VerticalAlignment="Center"
                              ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding SelectedStatus}"
                              Style="{StaticResource ModernComboBox}"/>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Content="🔍" Command="{Binding SearchCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="بحث"/>
                        <Button Content="📊" Command="{Binding ShowStatisticsCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="الإحصائيات" Margin="5,0,0,0"/>
                        <Button Content="⚠️" Command="{Binding ShowOverduePatientsCommand}" Height="36" Width="40" 
                                Style="{StaticResource AccentButton}" ToolTip="المرضى المتخلفين" Margin="5,0,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- جدول خطط التقويم -->
            <Border Background="White" CornerRadius="10" Margin="0,0,0,20" 
                    Effect="{StaticResource CardShadow}">
                <DataGrid ItemsSource="{Binding Plans}" 
                          SelectedItem="{Binding SelectedPlan, Mode=TwoWay}"
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          IsReadOnly="True" 
                          RowHeight="60"
                          HeadersVisibility="Column" 
                          FontSize="14" 
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal" 
                          Style="{StaticResource ModernDataGrid}"
                          AlternatingRowBackground="#F8F9FA">
                    
                    <DataGrid.Columns>
                        <!-- المريض -->
                        <DataGridTextColumn Header="المريض" Binding="{Binding Patient.FullName}" Width="150"/>
                        
                        <!-- الطبيب -->
                        <DataGridTextColumn Header="الطبيب" Binding="{Binding Doctor.FullName}" Width="150"/>
                        
                        <!-- نوع العلاج -->
                        <DataGridTextColumn Header="نوع العلاج" Binding="{Binding TreatmentType}" Width="150"/>
                        
                        <!-- التقدم -->
                        <DataGridTemplateColumn Header="التقدم" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Vertical" Margin="5">
                                        <ProgressBar Value="{Binding ProgressPercentage}" 
                                                     Maximum="100" Style="{StaticResource ProgressBarStyle}"/>
                                        <TextBlock Text="{Binding ProgressPercentage, StringFormat={}{0:F1}%}" 
                                                   FontSize="12" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- الجلسات -->
                        <DataGridTextColumn Header="الجلسات" Width="100">
                            <DataGridTextColumn.Binding>
                                <MultiBinding StringFormat="{}{0}/{1}">
                                    <Binding Path="CompletedSessions"/>
                                    <Binding Path="TotalSessions"/>
                                </MultiBinding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        
                        <!-- تاريخ البداية -->
                        <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat=yyyy/MM/dd}" Width="120"/>
                        
                        <!-- تاريخ النهاية -->
                        <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding ExpectedEndDate, StringFormat=yyyy/MM/dd}" Width="120"/>
                        
                        <!-- الحالة -->
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <StaticResource ResourceKey="StatusStyle"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- المواعيد -->
                        <DataGridTextColumn Header="المواعيد" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="AppointmentsGenerated" Converter="{StaticResource BoolToTextConverter}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- شريط الأزرار -->
            <Border Background="White" CornerRadius="10" Padding="15" 
                    Effect="{StaticResource CardShadow}">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="✏️ تعديل" Command="{Binding EditPlanCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="🗑️ حذف" Command="{Binding DeletePlanCommand}" Height="36" Width="100" 
                            Style="{StaticResource DangerButton}" Margin="0,0,10,0"/>
                    <Button Content="📅 إنشاء مواعيد" Command="{Binding GenerateAppointmentsCommand}" Height="36" Width="140" 
                            Style="{StaticResource PrimaryButton}" Margin="0,0,10,0"/>
                    <Button Content="➕ موعد واحد" Command="{Binding CreateNextAppointmentCommand}" Height="36" Width="120" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="🔄 إعادة جدولة" Command="{Binding RescheduleAppointmentsCommand}" Height="36" Width="130" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="📈 تحديث التقدم" Command="{Binding UpdateProgressCommand}" Height="36" Width="130" 
                            Style="{StaticResource AccentButton}" Margin="0,0,10,0"/>
                    <Button Content="📤 تصدير" Command="{Binding ExportCommand}" Height="36" Width="100" 
                            Style="{StaticResource AccentButton}"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl> 