﻿<Window x:Class="AqlanCenterProApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AqlanCenterProApp"
        xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
        mc:Ignorable="d"
        Title="مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان"
        Height="900"
        Width="1400"
        MinHeight="700"
        MinWidth="1200"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- Header -->
            <RowDefinition Height="*"/>
            <!-- Main Content -->
            <RowDefinition Height="Auto"/>
            <!-- Footer -->
        </Grid.RowDefinitions>

        <!-- الهيدر -->
        <controls:HeaderControl Grid.Row="0"
                                x:Name="HeaderControl"/>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="210"/>
                <!-- Main Sidebar -->
                <ColumnDefinition Width="*"/>
                <!-- Content Area -->
            </Grid.ColumnDefinitions>

            <!-- السايدبار الرئيسي -->
            <controls:SidebarControl Grid.Column="0"
                                     x:Name="SidebarControl"
                                     MenuItemSelected="OnMenuItemSelected"/>

            <!-- منطقة المحتوى -->
            <Border Grid.Column="1"
                    Background="White"
                    Margin="10"
                    CornerRadius="8"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">

                <!-- محتوى الصفحة مباشرة بدون عنوان -->
                <ContentControl x:Name="MainContentControl"
                                Margin="20"/>

            </Border>
        </Grid>

        <!-- الفوتر -->
        <controls:FooterControl Grid.Row="2"
                                x:Name="FooterControl"/>
    </Grid>
</Window>
