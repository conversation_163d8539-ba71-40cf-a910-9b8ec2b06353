using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج الحركة المالية
    /// </summary>
    public class FinancialTransaction
    {
        public int Id { get; set; }

        [Required]
        public DateTime TransactionDate { get; set; }

        [Required]
        [StringLength(100)]
        public string TransactionType { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public decimal DebitAmount { get; set; }

        [Required]
        public decimal CreditAmount { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
} 