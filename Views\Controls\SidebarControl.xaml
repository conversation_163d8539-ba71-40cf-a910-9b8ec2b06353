<UserControl x:Class="AqlanCenterProApp.Views.Controls.SidebarControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Controls"
             mc:Ignorable="d"
             d:DesignHeight="800"
             d:DesignWidth="250"
             FlowDirection="RightToLeft">

        <Border BorderBrush="#E0E0E0"
                BorderThickness="0,0,2,0">
                <Border.Background>
                        <LinearGradientBrush StartPoint="0,0"
                                             EndPoint="0,1">
                                <GradientStop Color="#3498DB"
                                              Offset="0"/>
                                <GradientStop Color="#2980B9"
                                              Offset="1"/>
                        </LinearGradientBrush>
                </Border.Background>
                <Grid>
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- هيدر السايدبار -->
                        <StackPanel Grid.Row="0"
                                    Orientation="Horizontal"
                                    Margin="15,20,15,20"
                                    FlowDirection="RightToLeft">
                                <Ellipse Width="32"
                                         Height="32"
                                         Fill="#E67E22"
                                         Margin="0,0,6,0"
                                         VerticalAlignment="Center"/>
                                <TextBlock Text="القائمة الرئيسية"
                                           FontFamily="Segoe UI, Tahoma, Arial"
                                           FontSize="13"
                                           FontWeight="Bold"
                                           Foreground="White"
                                           VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- الأزرار الرئيسية -->
                        <ScrollViewer Grid.Row="1"
                                      VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Disabled">
                                <StackPanel Margin="5,1,5,0">

                                        <!-- الداشبورد -->
                                        <Button x:Name="DashboardButton"
                                                Background="Transparent"
                                                Foreground="White"
                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                FontSize="12"
                                                FontWeight="SemiBold"
                                                Padding="10,8"
                                                Margin="2"
                                                BorderThickness="0"
                                                Cursor="Hand"
                                                Click="OnMenuItemClick"
                                                Tag="Dashboard">
                                                <StackPanel Orientation="Horizontal"
                                                            FlowDirection="RightToLeft">
                                                        <TextBlock Text="📊"
                                                                   FontSize="14"
                                                                   Margin="8,0,0,0"
                                                                   VerticalAlignment="Center"/>
                                                        <TextBlock Text="لوحة التحكم"
                                                                   VerticalAlignment="Center"/>
                                                </StackPanel>
                                        </Button>

                                        <!-- المرضى - مع قائمة فرعية -->
                                        <Expander x:Name="PatientsExpander"
                                                  Header="👥 المرضى"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Patients">
                                                                <TextBlock Text="📋 قائمة المرضى"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AddPatient">
                                                                <TextBlock Text="➕ إضافة مريض جديد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="SearchPatients">
                                                                <TextBlock Text="🔍 البحث في المرضى"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="MedicalRecords">
                                                                <TextBlock Text="📋 السجلات الطبية"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- الأطباء - مع قائمة فرعية -->
                                        <Expander x:Name="DoctorsExpander"
                                                  Header="👨‍⚕️ الأطباء"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Doctors">
                                                                <TextBlock Text="📋 قائمة الأطباء"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AddDoctor">
                                                                <TextBlock Text="➕ إضافة طبيب جديد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="DoctorSchedule">
                                                                <TextBlock Text="📅 جدول الأطباء"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- الموظفين - مع قائمة فرعية -->
                                        <Expander x:Name="EmployeesExpander"
                                                  Header="👷 الموظفين"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Employees">
                                                                <TextBlock Text="📋 قائمة الموظفين"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="EmployeeAttendance">
                                                                <TextBlock Text="⏰ الحضور والانصراف"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="EmployeeLeaves">
                                                                <TextBlock Text="🏖️ الإجازات"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="EmployeeSalaries">
                                                                <TextBlock Text="💰 الرواتب"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="EmployeeReports">
                                                                <TextBlock Text="📊 تقارير الموظفين"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- المواعيد - مع قائمة فرعية -->
                                        <Expander x:Name="AppointmentsExpander"
                                                  Header="📅 المواعيد"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Appointments">
                                                                <TextBlock Text="📋 قائمة المواعيد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AddAppointment">
                                                                <TextBlock Text="➕ إضافة موعد جديد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AppointmentCalendar">
                                                                <TextBlock Text="📅 جدول المواعيد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- المالية - مع قائمة فرعية -->
                                        <Expander x:Name="FinancialExpander"
                                                  Header="💰 المالية"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Invoices">
                                                                <TextBlock Text="💵 الفواتير"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Receipts">
                                                                <TextBlock Text="🧾 الإيصالات"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="PaymentVouchers">
                                                                <TextBlock Text="📄 سندات الصرف"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AccountStatements">
                                                                <TextBlock Text="📊 كشف الحساب"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- المعامل - مع قائمة فرعية -->
                                        <Expander x:Name="LabsExpander"
                                                  Header="🧪 المعامل"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Labs">
                                                                <TextBlock Text="🏥 قائمة المعامل"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="LabOrders">
                                                                <TextBlock Text="📋 طلبات المعمل"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="OrthodonticPlans">
                                                                <TextBlock Text="🦷 مخططات تقويم الأسنان"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Shades">
                                                                <TextBlock Text="🎨 الألوان والظلال"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- المخزون والمشتريات - مع قائمة فرعية -->
                                        <Expander x:Name="InventoryExpander"
                                                  Header="📦 المخزون"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Inventory">
                                                                <TextBlock Text="📦 إدارة المخزون"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Purchases">
                                                                <TextBlock Text="🛒 المشتريات"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Suppliers">
                                                                <TextBlock Text="🏢 الموردين"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- التقارير - مع قائمة فرعية -->
                                        <Expander x:Name="ReportsExpander"
                                                  Header="📈 التقارير"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="ReportsMain">
                                                                <TextBlock Text="📊 التقارير الرئيسية"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="PatientReports">
                                                                <TextBlock Text="👥 تقارير المرضى"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="AppointmentReports">
                                                                <TextBlock Text="📅 تقارير المواعيد"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="FinancialReports">
                                                                <TextBlock Text="💰 التقارير المالية"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="InventoryReports">
                                                                <TextBlock Text="📦 تقارير المخزون"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- المستخدمين - مع قائمة فرعية -->
                                        <Expander x:Name="UsersExpander"
                                                  Header="👤 المستخدمين"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Users">
                                                                <TextBlock Text="👤 إدارة المستخدمين"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Roles">
                                                                <TextBlock Text="🔐 إدارة الصلاحيات"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="ActivityLog">
                                                                <TextBlock Text="📝 سجل النشاط"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- النسخ الاحتياطي - مع قائمة فرعية -->
                                        <Expander x:Name="BackupExpander"
                                                  Header="💾 النسخ الاحتياطي"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Backup">
                                                                <TextBlock Text="💾 إدارة النسخ الاحتياطية"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Restore">
                                                                <TextBlock Text="🔄 استعادة نسخة احتياطية"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                        <!-- الإعدادات - مع قائمة فرعية -->
                                        <Expander x:Name="SettingsExpander"
                                                  Header="⚙️ الإعدادات"
                                                  Foreground="White"
                                                  FontFamily="Segoe UI, Tahoma, Arial"
                                                  FontSize="12"
                                                  FontWeight="SemiBold"
                                                  Margin="2"
                                                  IsExpanded="False">
                                                <StackPanel Margin="10,0,0,0">
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="Settings">
                                                                <TextBlock Text="⚙️ الإعدادات العامة"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="PrintSettings">
                                                                <TextBlock Text="🖨️ إعدادات الطباعة"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                        <Button Background="Transparent"
                                                                Foreground="White"
                                                                FontFamily="Segoe UI, Tahoma, Arial"
                                                                FontSize="11"
                                                                FontWeight="Normal"
                                                                Padding="15,6"
                                                                Margin="2"
                                                                BorderThickness="0"
                                                                Cursor="Hand"
                                                                Click="OnMenuItemClick"
                                                                Tag="NotificationSettings">
                                                                <TextBlock Text="🔔 إعدادات الإشعارات"
                                                                           HorizontalAlignment="Right"/>
                                                        </Button>
                                                </StackPanel>
                                        </Expander>

                                </StackPanel>
                        </ScrollViewer>
                </Grid>
        </Border>
</UserControl>
