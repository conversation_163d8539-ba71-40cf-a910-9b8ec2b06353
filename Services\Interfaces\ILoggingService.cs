using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التسجيل المتقدمة
    /// </summary>
    public interface ILoggingService
    {
        /// <summary>
        /// تسجيل معلومات عامة
        /// </summary>
        Task LogInfoAsync(string message, string category = "General", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل تحذير
        /// </summary>
        Task LogWarningAsync(string message, string category = "General", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل خطأ
        /// </summary>
        Task LogErrorAsync(Exception exception, string message = "", string category = "General", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل خطأ حرج
        /// </summary>
        Task LogCriticalAsync(Exception exception, string message = "", string category = "General", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل بداية عملية
        /// </summary>
        Task<string> LogOperationStartAsync(string operationName, string category = "Operation", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل انتهاء عملية
        /// </summary>
        Task LogOperationEndAsync(string operationId, bool success = true, string? errorMessage = null, Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل أداء العملية
        /// </summary>
        Task LogPerformanceAsync(string operationName, TimeSpan duration, string category = "Performance", Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل عملية قاعدة البيانات
        /// </summary>
        Task LogDatabaseOperationAsync(string operation, string tableName, TimeSpan? duration = null, bool success = true, string? errorMessage = null);

        /// <summary>
        /// تسجيل تفاعل المستخدم
        /// </summary>
        Task LogUserInteractionAsync(string action, string module, string? userId = null, Dictionary<string, object>? properties = null);

        /// <summary>
        /// تسجيل حالة النظام
        /// </summary>
        Task LogSystemStatusAsync(string component, string status, Dictionary<string, object>? metrics = null);

        /// <summary>
        /// الحصول على سجلات حسب الفترة الزمنية
        /// </summary>
        Task<IEnumerable<LogEntry>> GetLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? category = null, LogLevel? level = null);

        /// <summary>
        /// تنظيف السجلات القديمة
        /// </summary>
        Task CleanupOldLogsAsync(int daysToKeep = 30);

        /// <summary>
        /// تصدير السجلات
        /// </summary>
        Task<byte[]> ExportLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string format = "CSV");
    }

    /// <summary>
    /// مستوى السجل
    /// </summary>
    public enum LogLevel
    {
        Info,
        Warning,
        Error,
        Critical,
        Performance,
        Database,
        UserInteraction,
        SystemStatus
    }

    /// <summary>
    /// إدخال السجل
    /// </summary>
    public class LogEntry
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public LogLevel Level { get; set; }
        public string Category { get; set; } = "";
        public string Message { get; set; } = "";
        public string? Exception { get; set; }
        public string? StackTrace { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
        public string? UserId { get; set; }
        public string? SessionId { get; set; }
        public string MachineName { get; set; } = Environment.MachineName;
        public string ApplicationVersion { get; set; } = "";
    }
}
