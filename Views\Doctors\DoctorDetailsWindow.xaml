<Window x:Class="AqlanCenterProApp.Views.Doctors.DoctorDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل الطبيب"
        Height="600"
        Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0"
                Background="#2196F3"
                CornerRadius="8"
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="👨‍⚕️"
                           FontSize="24"
                           Margin="0,0,10,0"/>
                <TextBlock Text="{Binding Doctor.FullName, StringFormat='تفاصيل الطبيب: {0}'}"
                           FontSize="20"
                           FontWeight="Bold"
                           Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1"
                VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العمود الأيسر -->
                <StackPanel Grid.Column="0">
                    <!-- البيانات الأساسية -->
                    <GroupBox Header="البيانات الأساسية"
                              Style="{DynamicResource ModernGroupBoxStyle}"
                              Margin="0,0,0,15">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0"
                                    Grid.Column="0"
                                    Text="الاسم الكامل:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="0"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.FullName}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="1"
                                    Grid.Column="0"
                                    Text="التخصص:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="1"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Specialization}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="2"
                                    Grid.Column="0"
                                    Text="رقم الهاتف:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="2"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Phone}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="3"
                                    Grid.Column="0"
                                    Text="رقم الجوال:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="3"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Mobile}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="4"
                                    Grid.Column="0"
                                    Text="البريد الإلكتروني:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="4"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Email}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="5"
                                    Grid.Column="0"
                                    Text="العنوان:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="5"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Address}"
                                    TextWrapping="Wrap"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="6"
                                    Grid.Column="0"
                                    Text="الحالة:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <StackPanel Grid.Row="6"
                                    Grid.Column="1"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <Ellipse Width="10"
                                        Height="10"
                                         Fill="{Binding Doctor.IsActive, Converter={StaticResource BoolToStatusColorConverter}}"
                                         Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding Doctor.Status}"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات التعاقد -->
                    <GroupBox Header="معلومات التعاقد"
                              Style="{DynamicResource ModernGroupBoxStyle}"
                              Margin="0,0,0,15">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0"
                                    Grid.Column="0"
                                    Text="نوع التعاقد:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="0"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.ContractType}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="1"
                                    Grid.Column="0"
                                    Text="تاريخ الانضمام:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="1"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.JoinDate, StringFormat='{}{0:yyyy-MM-dd}'}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="2"
                                    Grid.Column="0"
                                    Text="نسبة العمولة:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="2"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.CommissionPercentage, StringFormat='{}{0}%'}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="3"
                                    Grid.Column="0"
                                    Text="الراتب الثابت:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="3"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.FixedSalary, StringFormat='{}{0:N0} {1}', ConverterParameter={Binding Doctor.SalaryCurrency}}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="4"
                                    Grid.Column="0"
                                    Text="عملة العمولة:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="4"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.CommissionCurrency}"
                                    Margin="10,5"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>

                <!-- العمود الأيمن -->
                <StackPanel Grid.Column="2">
                    <!-- الإحصائيات السريعة -->
                    <GroupBox Header="الإحصائيات السريعة"
                              Style="{DynamicResource ModernGroupBoxStyle}"
                              Margin="0,0,0,15">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0"
                                    Grid.Column="0"
                                    Text="إجمالي المرضى:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="0"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.TotalPatientsCount}"
                                    Margin="10,5"
                                    Foreground="#2196F3"
                                    FontWeight="Bold"/>

                            <TextBlock Grid.Row="1"
                                    Grid.Column="0"
                                    Text="الجلسات المكتملة:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="1"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.CompletedSessionsCount}"
                                    Margin="10,5"
                                    Foreground="#4CAF50"
                                    FontWeight="Bold"/>

                            <TextBlock Grid.Row="2"
                                    Grid.Column="0"
                                    Text="إجمالي الأرباح:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="2"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.TotalEarnings, StringFormat='{}{0:N0} ريال'}"
                                    Margin="10,5"
                                    Foreground="#FF9800"
                                    FontWeight="Bold"/>

                            <TextBlock Grid.Row="3"
                                    Grid.Column="0"
                                    Text="التقييم:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <StackPanel Grid.Row="3"
                                    Grid.Column="1"
                                    Orientation="Horizontal"
                                    Margin="10,5">
                                <TextBlock Text="{Binding Doctor.Rating, StringFormat='{}{0:F1}'}"
                                        FontWeight="Bold"
                                        Foreground="#FFC107"/>
                                <TextBlock Text="/5"
                                        Margin="2,0,5,0"/>
                                <TextBlock Text="⭐"
                                        FontSize="16"/>
                            </StackPanel>

                            <TextBlock Grid.Row="4"
                                    Grid.Column="0"
                                    Text="عدد التقييمات:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="4"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.RatingCount}"
                                    Margin="10,5"/>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات إضافية -->
                    <GroupBox Header="معلومات إضافية"
                              Style="{DynamicResource ModernGroupBoxStyle}"
                              Margin="0,0,0,15">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0"
                                    Grid.Column="0"
                                    Text="الجنس:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="0"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Gender}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="1"
                                    Grid.Column="0"
                                    Text="تاريخ الميلاد:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="1"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.DateOfBirth, StringFormat='{}{0:yyyy-MM-dd}'}"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="2"
                                    Grid.Column="0"
                                    Text="المؤهلات:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="2"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.Qualifications}"
                                    TextWrapping="Wrap"
                                    Margin="10,5"/>

                            <TextBlock Grid.Row="3"
                                    Grid.Column="0"
                                    Text="ملاحظات:"
                                    FontWeight="Bold"
                                    Margin="0,5"/>
                            <TextBlock Grid.Row="3"
                                    Grid.Column="1"
                                    Text="{Binding Doctor.AdditionalNotes}"
                                    TextWrapping="Wrap"
                                    Margin="10,5"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            <Button Content="📝 تعديل"
                    Command="{Binding EditCommand}"
                    Background="#4CAF50"
                    Foreground="White"
                    BorderThickness="0"
                    Padding="20,10"
                    Margin="10,0"
                    FontWeight="Bold"
                    FontSize="14"
                    MinWidth="120"/>

            <Button Content="📊 إحصائيات مفصلة"
                    Command="{Binding ShowStatisticsCommand}"
                    Background="#FF9800"
                    Foreground="White"
                    BorderThickness="0"
                    Padding="20,10"
                    Margin="10,0"
                    FontWeight="Bold"
                    FontSize="14"
                    MinWidth="150"/>

            <Button Content="🩺 الجلسات"
                    Command="{Binding ShowSessionsCommand}"
                    Background="#4CAF50"
                    Foreground="White"
                    BorderThickness="0"
                    Padding="20,10"
                    Margin="10,0"
                    FontWeight="Bold"
                    FontSize="14"
                    MinWidth="120"/>

            <Button Content="🖨️ طباعة"
                    Command="{Binding PrintCommand}"
                    Background="#9C27B0"
                    Foreground="White"
                    BorderThickness="0"
                    Padding="20,10"
                    Margin="10,0"
                    FontWeight="Bold"
                    FontSize="14"
                    MinWidth="120"/>

            <Button Content="❌ إغلاق"
                    Command="{Binding CloseCommand}"
                    Background="#F44336"
                    Foreground="White"
                    BorderThickness="0"
                    Padding="20,10"
                    Margin="10,0"
                    FontWeight="Bold"
                    FontSize="14"
                    MinWidth="120"/>
        </StackPanel>
    </Grid>

    <Window.Resources>
        <Style x:Key="ModernGroupBoxStyle"
                TargetType="GroupBox">
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="BorderBrush"
                    Value="#E0E0E0"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Padding"
                    Value="10"/>
            <Setter Property="Margin"
                    Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0"
                                        Background="#2196F3"
                                        CornerRadius="8,8,0,0"
                                        Padding="15,8">
                                    <ContentPresenter ContentSource="Header"
                                                      TextBlock.Foreground="White"
                                                      TextBlock.FontWeight="Bold"/>
                                </Border>
                                <ContentPresenter Grid.Row="1"
                                                  Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>
