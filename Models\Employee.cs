using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج الموظف
    /// </summary>
    public class Employee : BaseEntity
    {
        /// <summary>
        /// الرقم الوظيفي (تسلسل تلقائي)
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// الرقم الوظيفي (مخصص)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الأول
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// اسم العائلة
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// اسم الموظف الكامل
        /// </summary>
        [StringLength(100)]
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// رقم الهوية الوطنية
        /// </summary>
        [StringLength(50)]
        public string? NationalId { get; set; }

        /// <summary>
        /// الجنس
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// الحالة الاجتماعية
        /// </summary>
        [StringLength(20)]
        public string? MaritalStatus { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// رقم الهوية/الجواز
        /// </summary>
        [StringLength(50)]
        public string? IdentityNumber { get; set; }

        /// <summary>
        /// القسم
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// المنصب/الوظيفة
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Position { get; set; } = string.Empty;

        /// <summary>
        /// مسمى الوظيفة (للتوافق مع الكود القديم)
        /// </summary>
        [StringLength(100)]
        public string JobTitle => Position;

        /// <summary>
        /// تاريخ التعيين
        /// </summary>
        [Required]
        public DateTime HireDate { get; set; } = DateTime.Now;

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// رقم الهاتف (للتوافق مع الكود القديم)
        /// </summary>
        [StringLength(20)]
        public string? Phone => PhoneNumber;

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [StringLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// صورة الموظف
        /// </summary>
        [StringLength(500)]
        public string? EmployeeImage { get; set; }

        /// <summary>
        /// حالة الموظف (نشط/موقوف/مستقيل/مؤرشف)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "نشط";

        /// <summary>
        /// هل الموظف نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } = 0;

        /// <summary>
        /// بدل السكن
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal HousingAllowance { get; set; } = 0;

        /// <summary>
        /// بدل النقل
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TransportationAllowance { get; set; } = 0;

        /// <summary>
        /// بدل الطعام
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal FoodAllowance { get; set; } = 0;

        /// <summary>
        /// بدلات أخرى
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherAllowances { get; set; } = 0;

        /// <summary>
        /// خصم التأمين
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal InsuranceDeduction { get; set; } = 0;

        /// <summary>
        /// خصم الضريبة
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxDeduction { get; set; } = 0;

        /// <summary>
        /// خصومات أخرى
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherDeductions { get; set; } = 0;

        /// <summary>
        /// ملاحظات الراتب
        /// </summary>
        [StringLength(500)]
        public string? SalaryNotes { get; set; }

        /// <summary>
        /// إجمالي الراتب (محسوب)
        /// </summary>
        [NotMapped]
        public decimal TotalSalary => BasicSalary + HousingAllowance + TransportationAllowance + FoodAllowance + OtherAllowances - InsuranceDeduction - TaxDeduction - OtherDeductions;

        /// <summary>
        /// عملة الراتب
        /// </summary>
        [MaxLength(10)]
        public string? SalaryCurrency { get; set; } = "ر.ي";

        /// <summary>
        /// رصيد الإجازات السنوية
        /// </summary>
        public int AnnualLeaveBalance { get; set; } = 30;

        /// <summary>
        /// رصيد الإجازات الطارئة
        /// </summary>
        public int EmergencyLeaveBalance { get; set; } = 10;

        /// <summary>
        /// رصيد الإجازات غير المدفوعة
        /// </summary>
        public int UnpaidLeaveBalance { get; set; } = 0;

        /// <summary>
        /// دور الموظف في النظام
        /// </summary>
        [StringLength(50)]
        public string? SystemRole { get; set; }

        /// <summary>
        /// اسم المستخدم في النظام (إذا كان مستخدماً)
        /// </summary>
        [StringLength(50)]
        public string? Username { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        public new string? Notes { get; set; }

        // Navigation Properties
        public virtual ICollection<EmployeeAttendance> Attendances { get; set; } = new List<EmployeeAttendance>();
        public virtual ICollection<EmployeeSalary> Salaries { get; set; } = new List<EmployeeSalary>();
        public virtual ICollection<EmployeeLeave> Leaves { get; set; } = new List<EmployeeLeave>();
        public virtual ICollection<EmployeeDocument> Documents { get; set; } = new List<EmployeeDocument>();
    }
}
