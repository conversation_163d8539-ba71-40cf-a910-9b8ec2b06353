using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    public class PurchaseService : IPurchaseService
    {
        private readonly AqlanCenterDbContext _context;
        private readonly IInventoryItemService _inventoryItemService;

        public PurchaseService(AqlanCenterDbContext context, IInventoryItemService inventoryItemService)
        {
            _context = context;
            _inventoryItemService = inventoryItemService;
        }

        public async Task<IEnumerable<Purchase>> GetAllPurchasesAsync()
        {
            return await _context.Purchases
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<Purchase?> GetPurchaseByIdAsync(int id)
        {
            return await _context.Purchases
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .Include(p => p.PaymentVouchers)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Purchase?> GetPurchaseByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _context.Purchases
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .FirstOrDefaultAsync(p => p.InvoiceNumber == invoiceNumber);
        }

        public async Task<Purchase> CreatePurchaseAsync(Purchase purchase, List<PurchaseItem> items)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // إنشاء رقم الفاتورة إذا لم يكن موجوداً
                if (string.IsNullOrEmpty(purchase.InvoiceNumber))
                {
                    purchase.InvoiceNumber = await GenerateInvoiceNumberAsync();
                }

                purchase.CreatedAt = DateTime.Now;
                purchase.PurchaseStatus = "Pending";
                purchase.PaymentStatus = "Pending";
                purchase.RemainingAmount = purchase.TotalAmount - purchase.PaidAmount;

                _context.Purchases.Add(purchase);
                await _context.SaveChangesAsync();

                // إضافة عناصر المشتريات
                foreach (var item in items)
                {
                    item.PurchaseId = purchase.Id;
                    item.CreatedAt = DateTime.Now;
                    item.RemainingQuantity = item.Quantity;
                    _context.PurchaseItems.Add(item);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return purchase;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Purchase> UpdatePurchaseAsync(Purchase purchase, List<PurchaseItem> items)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingPurchase = await _context.Purchases
                    .Include(p => p.PurchaseItems)
                    .FirstOrDefaultAsync(p => p.Id == purchase.Id);

                if (existingPurchase == null)
                    throw new ArgumentException("المشتريات غير موجودة");

                // تحديث بيانات المشتريات
                existingPurchase.InvoiceNumber = purchase.InvoiceNumber;
                existingPurchase.PurchaseDate = purchase.PurchaseDate;
                existingPurchase.SupplierId = purchase.SupplierId;
                existingPurchase.SupplierInvoiceNumber = purchase.SupplierInvoiceNumber;
                existingPurchase.SubTotal = purchase.SubTotal;
                existingPurchase.TaxAmount = purchase.TaxAmount;
                existingPurchase.DiscountAmount = purchase.DiscountAmount;
                existingPurchase.TotalAmount = purchase.TotalAmount;
                existingPurchase.PaidAmount = purchase.PaidAmount;
                existingPurchase.RemainingAmount = purchase.TotalAmount - purchase.PaidAmount;
                existingPurchase.ExpectedDeliveryDate = purchase.ExpectedDeliveryDate;
                existingPurchase.Notes = purchase.Notes;
                existingPurchase.UpdatedAt = DateTime.Now;

                // حذف العناصر القديمة
                _context.PurchaseItems.RemoveRange(existingPurchase.PurchaseItems);

                // إضافة العناصر الجديدة
                foreach (var item in items)
                {
                    item.PurchaseId = purchase.Id;
                    item.CreatedAt = DateTime.Now;
                    item.RemainingQuantity = item.Quantity;
                    _context.PurchaseItems.Add(item);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return existingPurchase;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> DeletePurchaseAsync(int id)
        {
            var purchase = await _context.Purchases.FindAsync(id);
            if (purchase == null)
                return false;

            purchase.IsDeleted = true;
            purchase.DeletedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> CancelPurchaseAsync(int id)
        {
            var purchase = await _context.Purchases.FindAsync(id);
            if (purchase == null)
                return false;

            purchase.PurchaseStatus = "Cancelled";
            purchase.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> ReceivePurchaseAsync(int id, string receivedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var purchase = await _context.Purchases
                    .Include(p => p.PurchaseItems)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (purchase == null)
                    return false;

                purchase.PurchaseStatus = "Received";
                purchase.ReceivedBy = receivedBy;
                purchase.ReceivedDate = DateTime.Now;
                purchase.UpdatedAt = DateTime.Now;

                // تحديث المخزون
                foreach (var item in purchase.PurchaseItems)
                {
                    await _inventoryItemService.UpdateItemQuantityAsync(
                        item.InventoryItemId, 
                        item.Quantity, 
                        "add");

                    await _inventoryItemService.UpdateItemAverageCostAsync(
                        item.InventoryItemId,
                        item.UnitPrice,
                        item.Quantity);

                    item.ReceivedQuantity = item.Quantity;
                    item.RemainingQuantity = 0;
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<IEnumerable<Purchase>> GetPurchasesBySupplierAsync(int supplierId)
        {
            return await _context.Purchases
                .Where(p => p.SupplierId == supplierId)
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Purchase>> GetPurchasesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Purchases
                .Where(p => p.PurchaseDate >= startDate && p.PurchaseDate <= endDate)
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Purchase>> GetPendingPurchasesAsync()
        {
            return await _context.Purchases
                .Where(p => p.PurchaseStatus == "Pending")
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderBy(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Purchase>> GetReceivedPurchasesAsync()
        {
            return await _context.Purchases
                .Where(p => p.PurchaseStatus == "Received")
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderByDescending(p => p.ReceivedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Purchase>> SearchPurchasesAsync(string searchTerm)
        {
            return await _context.Purchases
                .Where(p => (p.InvoiceNumber != null && p.InvoiceNumber.Contains(searchTerm)) ||
                           (p.SupplierInvoiceNumber != null && p.SupplierInvoiceNumber.Contains(searchTerm)) ||
                           (p.Supplier != null && p.Supplier.Name != null && p.Supplier.Name.Contains(searchTerm)))
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseItems)
                    .ThenInclude(pi => pi.InventoryItem)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalPurchasesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Purchases.AsQueryable();
            
            if (startDate.HasValue)
                query = query.Where(p => p.PurchaseDate >= startDate.Value);
            
            if (endDate.HasValue)
                query = query.Where(p => p.PurchaseDate <= endDate.Value);

            return await query.SumAsync(p => p.TotalAmount);
        }

        public async Task<decimal> GetTotalPurchasesBySupplierAsync(int supplierId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Purchases.Where(p => p.SupplierId == supplierId);
            
            if (startDate.HasValue)
                query = query.Where(p => p.PurchaseDate >= startDate.Value);
            
            if (endDate.HasValue)
                query = query.Where(p => p.PurchaseDate <= endDate.Value);

            return await query.SumAsync(p => p.TotalAmount);
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var lastPurchase = await _context.Purchases
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastPurchase != null)
            {
                if (int.TryParse(lastPurchase.InvoiceNumber.Replace("PUR-", ""), out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"PUR-{nextNumber:D6}";
        }

        public async Task<bool> UpdatePurchaseStatusAsync(int id, string status)
        {
            var purchase = await _context.Purchases.FindAsync(id);
            if (purchase == null)
                return false;

            purchase.PurchaseStatus = status;
            purchase.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> UpdatePaymentStatusAsync(int id, string status, decimal paidAmount)
        {
            var purchase = await _context.Purchases.FindAsync(id);
            if (purchase == null)
                return false;

            purchase.PaymentStatus = status;
            purchase.PaidAmount = paidAmount;
            purchase.RemainingAmount = purchase.TotalAmount - paidAmount;
            purchase.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<IEnumerable<PurchaseItem>> GetPurchaseItemsAsync(int purchaseId)
        {
            return await _context.PurchaseItems
                .Where(pi => pi.PurchaseId == purchaseId)
                .Include(pi => pi.InventoryItem)
                .ToListAsync();
        }

        public async Task<decimal> CalculatePurchaseTotalAsync(List<PurchaseItem> items)
        {
            decimal subtotal = 0;
            foreach (var item in items)
            {
                item.TotalPrice = item.Quantity * item.UnitPrice;
                item.DiscountAmount = item.TotalPrice * (item.DiscountPercentage / 100);
                item.NetPrice = item.TotalPrice - item.DiscountAmount;
                subtotal += item.NetPrice;
            }
            return subtotal;
        }
    }
} 