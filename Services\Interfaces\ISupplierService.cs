using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<IEnumerable<Supplier>> GetActiveSuppliersAsync();
        Task<Supplier?> GetSupplierByIdAsync(int id);
        Task<Supplier?> GetSupplierByTaxNumberAsync(string taxNumber);
        Task<Supplier> CreateSupplierAsync(Supplier supplier);
        Task<Supplier> UpdateSupplierAsync(Supplier supplier);
        Task<bool> DeleteSupplierAsync(int id);
        Task<bool> DeactivateSupplierAsync(int id);
        Task<bool> ActivateSupplierAsync(int id);
        Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm);
        Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId);
        Task<decimal> GetSupplierTotalPaymentsAsync(int supplierId);
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<IEnumerable<Purchase>> GetSupplierPurchasesAsync(int supplierId);
        Task<IEnumerable<PaymentVoucher>> GetSupplierPaymentVouchersAsync(int supplierId);
    }
} 