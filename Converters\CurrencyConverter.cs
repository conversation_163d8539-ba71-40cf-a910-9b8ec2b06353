using System;
using System.Globalization;
using System.Windows.Data;

namespace AqlanCenterProApp.Converters
{
    public class CurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "";
            if (value is decimal dec)
                return dec.ToString("N0", culture);
            if (value is double d)
                return d.ToString("N0", culture);
            if (value is float f)
                return f.ToString("N0", culture);
            if (value is int i)
                return i.ToString("N0", culture);
            if (value is long l)
                return l.ToString("N0", culture);
            return value.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 