using AqlanCenterProApp.Models.Reports;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التقارير
    /// </summary>
    public interface IReportService
    {
        // تقارير المرضى
        Task<PatientReport> GeneratePatientReportAsync();

        // تقارير المواعيد
        Task<AppointmentReport> GenerateAppointmentReportAsync();

        // التقارير المالية
        Task<FinancialReport> GenerateFinancialReportAsync();

        // تقارير المخزون
        Task<InventoryReport> GenerateInventoryReportAsync();

        // تقارير الأداء
        Task<PerformanceReport> GeneratePerformanceReportAsync();

        // إدارة التقارير
        Task<List<ReportBase>> GetSavedReportsAsync(ReportType? type = null);
        Task<ReportBase?> GetReportByIdAsync(int reportId);
        Task<int> SaveReportAsync(ReportBase report);
        Task<bool> DeleteReportAsync(int reportId);
        Task<bool> ExportReportAsync(int reportId, string format, string filePath);
        Task<byte[]> GenerateReportPdfAsync(ReportBase report);
        Task<byte[]> GenerateReportExcelAsync(ReportBase report);
    }
} 