using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace AqlanCenterProApp.ViewModels.Users
{
    public class AddEditUserViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;
        private readonly IActivityLogService _activityLogService;

        private User _user;
        private string _password = string.Empty;
        private string _confirmPassword = string.Empty;
        private bool _isEditMode;
        private string _originalUsername = string.Empty;
        private string _originalEmail = string.Empty;

        public AddEditUserViewModel(IUserService userService, IRoleService roleService, IActivityLogService activityLogService, User? userToEdit = null)
        {
            _userService = userService;
            _roleService = roleService;
            _activityLogService = activityLogService;

            User = userToEdit ?? new User
            {
                IsActive = true,
                Language = "ar",
                Theme = "Light"
            };

            IsEditMode = userToEdit != null;
            if (IsEditMode && userToEdit != null)
            {
                _originalUsername = userToEdit.Username;
                _originalEmail = userToEdit.Email;
            }

            Roles = new ObservableCollection<Role>();

            SaveCommand = new RelayCommand(async () => await SaveAsync(), () => CanSave);
            CancelCommand = new RelayCommand(() => Cancel());
            ValidateUsernameCommand = new RelayCommand(async () => await ValidateUsernameAsync());
            ValidateEmailCommand = new RelayCommand(async () => await ValidateEmailAsync());

            _ = LoadRolesAsync();
        }

        public User User
        {
            get => _user;
            set
            {
                SetProperty(ref _user, value);
                OnPropertyChanged(nameof(CanSave));
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                SetProperty(ref _password, value);
                OnPropertyChanged(nameof(CanSave));
                OnPropertyChanged(nameof(PasswordValidationMessage));
            }
        }

        public string ConfirmPassword
        {
            get => _confirmPassword;
            set
            {
                SetProperty(ref _confirmPassword, value);
                OnPropertyChanged(nameof(CanSave));
                OnPropertyChanged(nameof(PasswordValidationMessage));
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public ObservableCollection<Role> Roles { get; }

        public bool CanSave => IsValid();
        public string PasswordValidationMessage => GetPasswordValidationMessage();

        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ValidateUsernameCommand { get; }
        public ICommand ValidateEmailCommand { get; }

        public event Action<bool>? SaveCompleted;

        private async Task LoadRolesAsync()
        {
            IsBusy = true;
            try
            {
                var roles = await _roleService.GetActiveRolesAsync();
                Roles.Clear();
                foreach (var role in roles)
                {
                    Roles.Add(role);
                }

                // Set default role if not in edit mode
                if (!IsEditMode && Roles.Any())
                {
                    User.RoleId = Roles.First().Id;
                }
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading roles: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(User.Username) || 
                string.IsNullOrWhiteSpace(User.FullName) || 
                string.IsNullOrWhiteSpace(User.Email) || 
                User.RoleId == 0)
                return false;

            if (!IsEditMode && string.IsNullOrWhiteSpace(Password))
                return false;

            if (!string.IsNullOrWhiteSpace(Password) && Password != ConfirmPassword)
                return false;

            if (Password.Length > 0 && Password.Length < 6)
                return false;

            return true;
        }

        private string GetPasswordValidationMessage()
        {
            if (IsEditMode && string.IsNullOrWhiteSpace(Password))
                return string.Empty;

            if (string.IsNullOrWhiteSpace(Password))
                return "كلمة المرور مطلوبة";

            if (Password.Length < 6)
                return "كلمة المرور يجب أن تكون 6 أحرف على الأقل";

            if (Password != ConfirmPassword)
                return "كلمة المرور غير متطابقة";

            return string.Empty;
        }

        private async Task ValidateUsernameAsync()
        {
            if (string.IsNullOrWhiteSpace(User.Username)) return;

            try
            {
                var isUnique = await _userService.IsUsernameUniqueAsync(User.Username, IsEditMode ? User.Id : null);
                if (!isUnique)
                {
                    // TODO: Show validation error
                    System.Diagnostics.Debug.WriteLine("Username is not unique");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating username: {ex.Message}");
            }
        }

        private async Task ValidateEmailAsync()
        {
            if (string.IsNullOrWhiteSpace(User.Email)) return;

            try
            {
                var isUnique = await _userService.IsEmailUniqueAsync(User.Email, IsEditMode ? User.Id : null);
                if (!isUnique)
                {
                    // TODO: Show validation error
                    System.Diagnostics.Debug.WriteLine("Email is not unique");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating email: {ex.Message}");
            }
        }

        private async Task SaveAsync()
        {
            if (!IsValid()) return;

            IsBusy = true;
            try
            {
                if (IsEditMode)
                {
                    var updatedUser = await _userService.UpdateUserAsync(User);
                    
                    // Update password if provided
                    if (!string.IsNullOrWhiteSpace(Password))
                    {
                        await _userService.ResetPasswordAsync(User.Id, Password);
                    }

                    await _activityLogService.LogActivityAsync(1, "Update", "User", User.Id, $"Updated user: {User.FullName}");
                }
                else
                {
                    var newUser = await _userService.CreateUserAsync(User, Password);
                    await _activityLogService.LogActivityAsync(1, "Create", "User", newUser.Id, $"Created user: {User.FullName}");
                }

                SaveCompleted?.Invoke(true);
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error saving user: {ex.Message}");
                SaveCompleted?.Invoke(false);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void Cancel()
        {
            SaveCompleted?.Invoke(false);
        }
    }
} 