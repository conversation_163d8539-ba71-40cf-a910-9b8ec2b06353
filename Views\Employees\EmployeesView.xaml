<UserControl x:Class="AqlanCenterProApp.Views.Employees.EmployeesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Employees"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             Background="#F5F5F5">

    <UserControl.Resources>
        <!-- Button Styles -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#B71C1C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#2E7D32"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E65100"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- TextBox Style -->
        <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <TextBox Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Text, UpdateSourceTrigger=PropertyChanged}"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         Padding="{TemplateBinding Padding}"
                                         FontSize="{TemplateBinding FontSize}"
                                         VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="120"/>
        </Style>

        <!-- DataGrid Style -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#EEEEEE"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="True"/>
            <Setter Property="CanUserResizeColumns" Value="True"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
        </Style>

        <!-- DataGridColumnHeader Style -->
        <Style x:Key="ModernColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20" Orientation="Vertical" FlowDirection="RightToLeft">
            <!-- Main Title -->
            <TextBlock Text="إدارة الموظفين" FontSize="36" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,18"/>

            <!-- Toolbar Buttons -->
            <WrapPanel HorizontalAlignment="Stretch" Margin="0,0,0,14" ItemWidth="135" ItemHeight="40" FlowDirection="RightToLeft">
                <Button Content="➕ إضافة موظف" Command="{Binding AddEmployeeCommand}" Background="#47BE3E" Foreground="White" Margin="5"/>
                <Button Content="✏️ تعديل" Command="{Binding EditEmployeeCommand}" Background="#FFC107" Foreground="Black" Margin="5"/>
                <Button Content="🗑️ حذف" Command="{Binding DeleteEmployeeCommand}" Background="#F44336" Foreground="White" Margin="5"/>
                <Button Content="🔄 تحديث" Command="{Binding RefreshCommand}" Background="#2196F3" Foreground="White" Margin="5"/>
                <Button Content="📤 تصدير" Command="{Binding ExportCommand}" Margin="5"/>
                <Button Content="🖨️ طباعة" Command="{Binding PrintCommand}" Margin="5"/>
                <Button Content="⏰ حضور" Command="{Binding ViewAttendanceCommand}" Margin="5"/>
                <Button Content="💰 الرواتب" Command="{Binding ViewSalariesCommand}" Margin="5"/>
                <Button Content="🌴 الإجازات" Command="{Binding ViewLeavesCommand}" Margin="5"/>
                <Button Content="📝 طلب إجازة" Command="{Binding RequestLeaveCommand}" Background="#9C27B0" Foreground="White" Margin="5"/>
                <Button Content="📁 المستندات" Command="{Binding ViewDocumentsCommand}" Background="#607D8B" Foreground="White" Margin="5"/>
                <Button Content="📄 نسخ" Command="{Binding CopyCommand}" Margin="5"/>
                <Button Content="📊 إحصائيات" Command="{Binding ShowStatisticsCommand}" Background="#FF9800" Foreground="White" Margin="5"/>
                <Button Content="📋 تقارير" Command="{Binding ShowReportsCommand}" Background="#795548" Foreground="White" Margin="5"/>
            </WrapPanel>

            <!-- Search and Filter -->
            <DockPanel Margin="0,0,0,10" LastChildFill="True" FlowDirection="RightToLeft">
                <TextBox x:Name="SearchBox" Width="320" Height="32" VerticalAlignment="Center" Margin="5" FontSize="16" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                <Button Content="🔍" Width="40" Height="34" Margin="4,0,0,0" Command="{Binding SearchCommand}"/>
                <ComboBox ItemsSource="{Binding Departments}" SelectedItem="{Binding SelectedDepartment}" Width="150" Height="32" Margin="5" FontSize="14"/>
                <ComboBox ItemsSource="{Binding Positions}" SelectedItem="{Binding SelectedPosition}" Width="150" Height="32" Margin="5" FontSize="14"/>
                <ComboBox ItemsSource="{Binding Statuses}" SelectedItem="{Binding SelectedStatus}" Width="120" Height="32" Margin="5" FontSize="14"/>
            </DockPanel>

            <!-- DataGrid -->
            <DataGrid x:Name="EmployeesDataGrid"
                      ItemsSource="{Binding Employees}"
                      SelectedItem="{Binding SelectedEmployee}"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      HeadersVisibility="Column"
                      Margin="0,0,0,8"
                      SelectionMode="Single"
                      CanUserAddRows="False"
                      RowHeight="34"
                      EnableRowVirtualization="True"
                      EnableColumnVirtualization="True"
                      VirtualizingPanel.IsVirtualizing="True"
                      VirtualizingPanel.VirtualizationMode="Recycling"
                      MaxHeight="500"
                      MinHeight="200">
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="✏️ تعديل" Command="{Binding EditEmployeeCommand}"/>
                        <MenuItem Header="🗑️ حذف" Command="{Binding DeleteEmployeeCommand}"/>
                        <MenuItem Header="⏰ حضور" Command="{Binding ViewAttendanceCommand}"/>
                        <MenuItem Header="💰 الرواتب" Command="{Binding ViewSalariesCommand}"/>
                        <MenuItem Header="🌴 الإجازات" Command="{Binding ViewLeavesCommand}"/>
                        <MenuItem Header="📝 طلب إجازة" Command="{Binding RequestLeaveCommand}"/>
                        <MenuItem Header="📁 المستندات" Command="{Binding ViewDocumentsCommand}"/>
                        <MenuItem Header="📄 نسخ" Command="{Binding CopyCommand}"/>
                        <MenuItem Header="📊 إحصائيات" Command="{Binding ShowStatisticsCommand}"/>
                        <MenuItem Header="📋 تقارير" Command="{Binding ShowReportsCommand}"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
                <!-- Define columns as needed -->
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم الوظيفي" Binding="{Binding EmployeeNumber}" Width="*"/>
                    <DataGridTextColumn Header="الاسم" Binding="{Binding FullName}" Width="2*"/>
                    <DataGridTextColumn Header="القسم" Binding="{Binding Department}" Width="*"/>
                    <DataGridTextColumn Header="الوظيفة" Binding="{Binding Position}" Width="*"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="*"/>
                    <DataGridTextColumn Header="الهاتف" Binding="{Binding PhoneNumber}" Width="*"/>
                    <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary}" Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </ScrollViewer>
</UserControl>