<UserControl x:Class="AqlanCenterProApp.Views.Labs.LabsListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إدارة المعامل والمختبرات" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="20,10"
                   Foreground="#FF2196F3"/>

        <!-- شريط الأدوات -->
        <Border Grid.Row="1" 
                Background="#FFF5F5F5" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="0,1" 
                Padding="20,10">
            <StackPanel Orientation="Horizontal">
                <Button Content="➕ إضافة معمل"
                        Command="{Binding AddLabCommand}"
                        Background="#FF4CAF50"
                        Foreground="White"
                        Padding="15,8"
                        Margin="5"/>

                <Button Content="✏️ تعديل"
                        Command="{Binding EditLabCommand}"
                        Background="#FF2196F3"
                        Foreground="White"
                        Padding="15,8"
                        Margin="5"/>

                <Button Content="🗑️ حذف"
                        Command="{Binding DeleteLabCommand}"
                        Background="#FFF44336"
                        Foreground="White"
                        Padding="15,8"
                        Margin="5"/>

                <Button Content="🔄 تحديث"
                        Command="{Binding RefreshCommand}"
                        Background="#FF757575"
                        Foreground="White"
                        Padding="15,8"
                        Margin="5"/>
            </StackPanel>
        </Border>

        <!-- قائمة المعامل -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding Labs}"
                  SelectedItem="{Binding SelectedLab}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  AlternatingRowBackground="#FFF9F9F9"
                  RowBackground="White"
                  Margin="20,0,20,10"
                  FontSize="13">

            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المعمل" 
                                    Binding="{Binding Name}" 
                                    Width="200"
                                    FontWeight="SemiBold"/>

                <DataGridTextColumn Header="العنوان" 
                                    Binding="{Binding Address}" 
                                    Width="250"/>

                <DataGridTextColumn Header="الهاتف" 
                                    Binding="{Binding Phone}" 
                                    Width="120"/>

                <DataGridTextColumn Header="الواتساب" 
                                    Binding="{Binding WhatsApp}" 
                                    Width="120"/>

                <DataGridTextColumn Header="البريد الإلكتروني" 
                                    Binding="{Binding Email}" 
                                    Width="200"/>

                <DataGridTemplateColumn Header="الحالة" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{Binding IsActive, Converter={StaticResource BoolToStatusColorConverter}}"
                                    CornerRadius="10"
                                    Padding="8,3">
                                <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusTextConverter}}"
                                           Foreground="White"
                                           FontSize="11"
                                           FontWeight="SemiBold"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTextColumn Header="تاريخ الإنشاء" 
                                    Binding="{Binding CreatedAt, StringFormat='{}{0:yyyy/MM/dd}'}" 
                                    Width="100"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- شريط الحالة -->
        <Border Grid.Row="3" 
                Background="#FF2196F3" 
                Padding="20,8">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="إجمالي المعامل: " Foreground="White" FontWeight="SemiBold"/>
                <TextBlock Text="{Binding TotalLabs}" Foreground="White" FontWeight="Bold"/>
                
                <TextBlock Text=" | النشطة: " Foreground="White" FontWeight="SemiBold" Margin="20,0,0,0"/>
                <TextBlock Text="{Binding ActiveLabs}" Foreground="White" FontWeight="Bold"/>
                
                <TextBlock Text=" | غير النشطة: " Foreground="White" FontWeight="SemiBold" Margin="20,0,0,0"/>
                <TextBlock Text="{Binding InactiveLabs}" Foreground="White" FontWeight="Bold"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
