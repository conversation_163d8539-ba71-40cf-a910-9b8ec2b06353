using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Services.Interfaces;
using System.IO;

namespace AqlanCenterProApp.Helpers
{
    public static class DatabaseManager
    {
        /// <summary>
        /// إنشاء قاعدة بيانات واحدة موحدة في المجلد الرئيسي
        /// </summary>
        /// <param name="loggingService">خدمة التسجيل الاختيارية</param>
        public static async Task<bool> CreateUnifiedDatabaseAsync(ILoggingService? loggingService = null)
        {
            try
            {
                if (loggingService != null)
                {
                    await loggingService.LogInfoAsync("بدء إنشاء قاعدة البيانات الموحدة", "DatabaseManager");
                }
                Console.WriteLine("🔄 بدء إنشاء قاعدة البيانات الموحدة...");

                // حذف قواعد البيانات القديمة
                await CleanupOldDatabasesAsync();

                // إنشاء قاعدة البيانات الجديدة
                var dbPath = DatabasePathHelper.GetDatabasePath();
                var connectionString = DatabasePathHelper.GetConnectionString();

                Console.WriteLine($"📁 مسار قاعدة البيانات الجديدة: {dbPath}");

                Console.WriteLine("🏗️ إنشاء DbContextOptionsBuilder...");
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);
                Console.WriteLine("✅ تم إنشاء DbContextOptionsBuilder");

                Console.WriteLine("🔧 إنشاء AqlanCenterDbContext...");
                using var context = new AqlanCenterDbContext(optionsBuilder.Options);
                Console.WriteLine("✅ تم إنشاء AqlanCenterDbContext");

                // التحقق من صحة السياق
                if (context == null)
                {
                    throw new InvalidOperationException("فشل في إنشاء سياق قاعدة البيانات");
                }
                Console.WriteLine("✅ تم التحقق من صحة السياق");

                // حذف قاعدة البيانات إذا كانت موجودة
                Console.WriteLine("🗑️ حذف قاعدة البيانات القديمة...");
                await context.Database.EnsureDeletedAsync();
                Console.WriteLine("✅ تم حذف قاعدة البيانات القديمة");

                // إنشاء قاعدة البيانات الجديدة
                Console.WriteLine("🏗️ إنشاء قاعدة البيانات الجديدة...");
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("✅ تم إنشاء قاعدة البيانات الجديدة");

                // تهيئة البيانات الأساسية
                Console.WriteLine("📊 بدء تهيئة البيانات الأساسية...");
                await DatabaseInitializer.InitializeAsync(context);
                Console.WriteLine("✅ تم تهيئة البيانات الأساسية");

                Console.WriteLine("🎉 تم إنشاء قاعدة البيانات الموحدة بنجاح!");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"🔍 الخطأ الداخلي: {ex.InnerException.Message}");
                    Console.WriteLine($"📋 تفاصيل الخطأ الداخلي: {ex.InnerException.StackTrace}");
                }

                return false;
            }
        }

        /// <summary>
        /// حذف قواعد البيانات القديمة من جميع المواقع
        /// </summary>
        private static async Task CleanupOldDatabasesAsync()
        {
            try
            {
                Console.WriteLine("🧹 بدء تنظيف قواعد البيانات القديمة...");

                var locationsToCheck = new[]
                {
                    AppDomain.CurrentDomain.BaseDirectory,
                    Directory.GetCurrentDirectory(),
                    Path.Combine(Directory.GetCurrentDirectory(), "bin", "Debug", "net8.0-windows"),
                    Path.Combine(Directory.GetCurrentDirectory(), "bin", "Release", "net8.0-windows")
                };

                var dbFilesToDelete = new[]
                {
                    "AqlanCenter.db",
                    "AqlanCenter.db-shm",
                    "AqlanCenter.db-wal",
                    "AqlanCenterDatabase.db",
                    "AqlanCenterDatabase.db-shm",
                    "AqlanCenterDatabase.db-wal"
                };

                foreach (var location in locationsToCheck)
                {
                    if (!Directory.Exists(location)) continue;

                    foreach (var dbFile in dbFilesToDelete)
                    {
                        var filePath = Path.Combine(location, dbFile);
                        if (File.Exists(filePath))
                        {
                            try
                            {
                                File.Delete(filePath);
                                Console.WriteLine($"🗑️ تم حذف: {filePath}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"⚠️ لا يمكن حذف {filePath}: {ex.Message}");
                            }
                        }
                    }
                }

                Console.WriteLine("✅ تم تنظيف قواعد البيانات القديمة");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ خطأ في تنظيف قواعد البيانات القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات الموحدة
        /// </summary>
        public static bool IsUnifiedDatabaseExists()
        {
            return DatabasePathHelper.DatabaseExists();
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات الموحدة
        /// </summary>
        public static string GetUnifiedDatabasePath()
        {
            return DatabasePathHelper.GetDatabasePath();
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال لقاعدة البيانات الموحدة
        /// </summary>
        public static string GetUnifiedConnectionString()
        {
            return DatabasePathHelper.GetConnectionString();
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات الموحدة
        /// </summary>
        public static async Task<bool> BackupUnifiedDatabaseAsync()
        {
            try
            {
                var dbPath = GetUnifiedDatabasePath();
                if (!File.Exists(dbPath))
                {
                    Console.WriteLine("⚠️ قاعدة البيانات غير موجودة للنسخ الاحتياطي");
                    return false;
                }

                var backupPath = DatabasePathHelper.CreateBackupPath();
                DatabasePathHelper.EnsureBackupDirectory();

                await Task.Run(() => File.Copy(dbPath, backupPath));
                Console.WriteLine($"💾 تم إنشاء نسخة احتياطية: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// فحص صحة قاعدة البيانات الموحدة
        /// </summary>
        public static async Task<(bool IsValid, string Message)> ValidateUnifiedDatabaseAsync()
        {
            try
            {
                var dbPath = GetUnifiedDatabasePath();
                if (!File.Exists(dbPath))
                {
                    return (false, "قاعدة البيانات غير موجودة");
                }

                var connectionString = GetUnifiedConnectionString();
                var optionsBuilder = new DbContextOptionsBuilder<AqlanCenterDbContext>();
                optionsBuilder.UseSqlite(connectionString);

                using var context = new AqlanCenterDbContext(optionsBuilder.Options);

                // فحص الاتصال
                var canConnect = await context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    return (false, "لا يمكن الاتصال بقاعدة البيانات");
                }

                // فحص الجداول الأساسية
                var hasUsers = await context.Users.AnyAsync();

                return (true, "قاعدة البيانات صحيحة وجاهزة للاستخدام");
            }
            catch (Exception ex)
            {
                return (false, $"خطأ في فحص قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين قاعدة البيانات الموحدة
        /// </summary>
        public static async Task<bool> ResetUnifiedDatabaseAsync()
        {
            try
            {
                Console.WriteLine("🔄 بدء إعادة تعيين قاعدة البيانات...");

                // إنشاء نسخة احتياطية أولاً
                await BackupUnifiedDatabaseAsync();

                // إعادة إنشاء قاعدة البيانات
                var success = await CreateUnifiedDatabaseAsync();

                if (success)
                {
                    Console.WriteLine("🎉 تم إعادة تعيين قاعدة البيانات بنجاح!");
                }

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إعادة تعيين قاعدة البيانات: {ex.Message}");
                return false;
            }
        }
    }
}
