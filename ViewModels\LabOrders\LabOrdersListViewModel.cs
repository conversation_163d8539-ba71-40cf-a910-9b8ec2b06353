using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Services;
using Microsoft.Win32;
using System.IO;
using System.Linq;

namespace AqlanCenterProApp.ViewModels.LabOrders
{
    public class LabOrdersListViewModel : BaseViewModel
    {
        private readonly ILabOrderService _labOrderService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;
        private readonly ILabService _labService;
        private readonly IShadeService _shadeService;

        private ObservableCollection<LabOrder> _labOrders = new();
        public ObservableCollection<LabOrder> LabOrders
        {
            get => _labOrders;
            set => SetProperty(ref _labOrders, value);
        }

        private ObservableCollection<Patient> _patients = new();
        public ObservableCollection<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }

        private ObservableCollection<Doctor> _doctors = new();
        public ObservableCollection<Doctor> Doctors
        {
            get => _doctors;
            set => SetProperty(ref _doctors, value);
        }

        private ObservableCollection<Lab> _labs = new();
        public ObservableCollection<Lab> Labs
        {
            get => _labs;
            set => SetProperty(ref _labs, value);
        }

        private ObservableCollection<Shade> _shades = new();
        public ObservableCollection<Shade> Shades
        {
            get => _shades;
            set => SetProperty(ref _shades, value);
        }

        private LabOrder? _selectedLabOrder;
        public LabOrder? SelectedLabOrder
        {
            get => _selectedLabOrder;
            set => SetProperty(ref _selectedLabOrder, value);
        }

        private Patient? _selectedPatient;
        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set
            {
                if (SetProperty(ref _selectedPatient, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private Doctor? _selectedDoctor;
        public Doctor? SelectedDoctor
        {
            get => _selectedDoctor;
            set
            {
                if (SetProperty(ref _selectedDoctor, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private Lab? _selectedLab;
        public Lab? SelectedLab
        {
            get => _selectedLab;
            set
            {
                if (SetProperty(ref _selectedLab, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private string _statusFilter = string.Empty;
        public string StatusFilter
        {
            get => _statusFilter;
            set
            {
                if (SetProperty(ref _statusFilter, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private DateTime? _startDate;
        public DateTime? StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private DateTime? _endDate;
        public DateTime? EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private string _searchTerm = string.Empty;
        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                if (SetProperty(ref _searchTerm, value))
                {
                    FilterLabOrders();
                }
            }
        }

        private List<LabOrder> _allLabOrders = new();

        public LabOrdersListViewModel(
            ILabOrderService labOrderService,
            IPatientService patientService,
            IDoctorService doctorService,
            ILabService labService,
            IShadeService shadeService)
        {
            _labOrderService = labOrderService;
            _patientService = patientService;
            _doctorService = doctorService;
            _labService = labService;
            _shadeService = shadeService;

            InitializeCommands();
            _ = LoadDataAsync();
        }

        #region Commands

        public RelayCommand AddCommand { get; private set; } = null!;
        public RelayCommand EditCommand { get; private set; } = null!;
        public RelayCommand DeleteCommand { get; private set; } = null!;
        public RelayCommand RefreshCommand { get; private set; } = null!;
        public RelayCommand ExportCommand { get; private set; } = null!;
        public RelayCommand PrintCommand { get; private set; } = null!;
        public RelayCommand ClearFiltersCommand { get; private set; } = null!;
        public RelayCommand LoadDataCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            AddCommand = new RelayCommand(async _ => await AddLabOrderAsync());
            EditCommand = new RelayCommand(async _ => await EditLabOrderAsync(), _ => SelectedLabOrder != null);
            DeleteCommand = new RelayCommand(async _ => await DeleteLabOrderAsync(), _ => SelectedLabOrder != null);
            RefreshCommand = new RelayCommand(async _ => await LoadDataAsync());
            ExportCommand = new RelayCommand(async _ => await ExportDataAsync());
            PrintCommand = new RelayCommand(async _ => await PrintDataAsync());
            ClearFiltersCommand = new RelayCommand(_ => ClearFilters());
            LoadDataCommand = new RelayCommand(async _ => await LoadDataAsync());
        }

        #endregion

        #region Methods

        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                await Task.WhenAll(
                    LoadLabOrdersAsync(),
                    LoadPatientsAsync(),
                    LoadDoctorsAsync(),
                    LoadLabsAsync(),
                    LoadShadesAsync()
                );
            }, "جاري تحميل البيانات...");
        }

        public async Task LoadLabOrdersAsync()
        {
            var labOrders = await _labOrderService.GetAllLabOrdersAsync()
                .ConfigureAwait(false);
            _allLabOrders = labOrders.ToList();
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                FilterLabOrders();
            });
        }

        public async Task LoadPatientsAsync()
        {
            var patients = await _patientService.GetAllPatientsAsync();
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Patients.Clear();
                Patients.Add(new Patient { Id = 0, FullName = "جميع المرضى" });
                foreach (var patient in patients)
                {
                    Patients.Add(patient);
                }
            });
        }

        public async Task LoadDoctorsAsync()
        {
            var doctors = await _doctorService.GetAllDoctorsAsync();
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Doctors.Clear();
                Doctors.Add(new Doctor { Id = 0, FullName = "جميع الأطباء" });
                foreach (var doctor in doctors)
                {
                    Doctors.Add(doctor);
                }
            });
        }

        public async Task LoadLabsAsync()
        {
            var labs = await _labService.GetActiveLabsAsync();
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Labs.Clear();
                Labs.Add(new Lab { LabId = 0, Name = "جميع المعامل" });
                foreach (var lab in labs)
                {
                    Labs.Add(lab);
                }
            });
        }

        public async Task LoadShadesAsync()
        {
            var shades = await _shadeService.GetAllShadesAsync();
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Shades.Clear();
                foreach (var shade in shades)
                {
                    Shades.Add(shade);
                }
            });
        }

        private void FilterLabOrders()
        {
            var filtered = _allLabOrders.AsEnumerable();

            if (SelectedPatient != null && SelectedPatient.Id != 0)
            {
                filtered = filtered.Where(lo => lo.PatientId == SelectedPatient.Id);
            }

            if (SelectedDoctor != null && SelectedDoctor.Id != 0)
            {
                filtered = filtered.Where(lo => lo.DoctorId == SelectedDoctor.Id);
            }

            if (SelectedLab != null && SelectedLab.LabId != 0)
            {
                filtered = filtered.Where(lo => lo.LabId == SelectedLab.LabId);
            }

            if (!string.IsNullOrWhiteSpace(StatusFilter))
            {
                filtered = filtered.Where(lo => lo.Status.Contains(StatusFilter));
            }

            if (StartDate.HasValue)
            {
                filtered = filtered.Where(lo => lo.SendDate >= StartDate.Value);
            }

            if (EndDate.HasValue)
            {
                filtered = filtered.Where(lo => lo.SendDate <= EndDate.Value);
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                filtered = filtered.Where(lo =>
                    lo.OrderNumber.Contains(SearchTerm) ||
                    lo.WorkType.Contains(SearchTerm) ||
                    lo.Patient.FullName.Contains(SearchTerm) ||
                    lo.Doctor.FullName.Contains(SearchTerm) ||
                    lo.Lab.Name.Contains(SearchTerm)
                );
            }

            LabOrders.Clear();
            foreach (var labOrder in filtered.OrderByDescending(lo => lo.SendDate))
            {
                LabOrders.Add(labOrder);
            }
        }

        private void ClearFilters()
        {
            SelectedPatient = null;
            SelectedDoctor = null;
            SelectedLab = null;
            StatusFilter = string.Empty;
            StartDate = null;
            EndDate = null;
            SearchTerm = string.Empty;
        }

        private async Task AddLabOrderAsync()
        {
            await ExecuteAsync(async () =>
            {
                // This would open the Add/Edit dialog
                // For now, we'll just show a message
                MessageBox.Show("سيتم فتح نافذة إضافة طلب معمل جديد", "إضافة طلب معمل", MessageBoxButton.OK, MessageBoxImage.Information);
            });
        }

        private async Task EditLabOrderAsync()
        {
            if (SelectedLabOrder == null) return;

            await ExecuteAsync(async () =>
            {
                // This would open the Add/Edit dialog with the selected lab order
                MessageBox.Show($"سيتم فتح نافذة تعديل طلب المعمل رقم {SelectedLabOrder.OrderNumber}", "تعديل طلب معمل", MessageBoxButton.OK, MessageBoxImage.Information);
            });
        }

        private async Task DeleteLabOrderAsync()
        {
            if (SelectedLabOrder == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف طلب المعمل رقم {SelectedLabOrder.OrderNumber}؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ExecuteAsync(async () =>
                {
                    await _labOrderService.DeleteLabOrderAsync(SelectedLabOrder.LabOrderId);
                    await LoadDataAsync();
                }, "جاري حذف طلب المعمل...");
            }
        }

        private async Task ExportDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "ملف Excel (*.xlsx)|*.xlsx|ملف CSV (*.csv)|*.csv",
                    FileName = $"طلبات_المعامل_{DateTime.Now:yyyyMMdd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Export logic would go here
                    MessageBox.Show($"تم تصدير البيانات إلى {saveFileDialog.FileName}", "تصدير البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            });
        }

        private async Task PrintDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Print logic would go here
                MessageBox.Show("سيتم طباعة تقرير طلبات المعامل", "طباعة التقرير", MessageBoxButton.OK, MessageBoxImage.Information);
            });
        }

        #endregion
    }
}