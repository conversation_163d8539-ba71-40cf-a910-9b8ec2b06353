<Window x:Class="AqlanCenterProApp.Views.Doctors.DoctorSessionsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="جلسات الطبيب"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" 
                Background="#4CAF50" 
                CornerRadius="8" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🩺" 
                           FontSize="24" 
                           Margin="0,0,10,0"/>
                <TextBlock Text="{Binding Doctor.FullName, StringFormat='جلسات الطبيب: {0}'}" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- شريط البحث والفلاتر -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="SearchTextBox"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         Width="300"
                         Height="35"
                         Padding="10,8"
                         FontSize="14"
                         VerticalContentAlignment="Center"
                         Background="White"
                         BorderBrush="#E0E0E0"
                         BorderThickness="1">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Style.Triggers>
                                <Trigger Property="Text" Value="">
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <VisualBrush AlignmentX="Right" AlignmentY="Center" Stretch="None">
                                                <VisualBrush.Visual>
                                                    <TextBlock Text="🔍 البحث في الجلسات..." 
                                                               Foreground="#999" 
                                                               FontSize="14"/>
                                                </VisualBrush.Visual>
                                            </VisualBrush>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <ComboBox x:Name="StatusFilterComboBox"
                          SelectedItem="{Binding SelectedStatus}"
                          Width="150"
                          Height="35"
                          Margin="10,0,0,0"
                          FontSize="14"
                          VerticalContentAlignment="Center">
                    <ComboBoxItem Content="جميع الحالات"/>
                    <ComboBoxItem Content="مكتملة"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="ملغية"/>
                    <ComboBoxItem Content="مؤجلة"/>
                </ComboBox>
            </StackPanel>

            <!-- الأزرار -->
            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <Button Content="➕ جلسة جديدة"
                        Command="{Binding AddSessionCommand}"
                        Background="#4CAF50"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="15,8"
                        Margin="10,0,0,0"
                        FontWeight="Bold"
                        FontSize="14"/>

                <Button Content="🔄 تحديث"
                        Command="{Binding RefreshCommand}"
                        Background="#2196F3"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="15,8"
                        Margin="10,0,0,0"
                        FontWeight="Bold"
                        FontSize="14"/>
            </StackPanel>
        </Grid>

        <!-- قائمة الجلسات -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding Sessions}"
                  SelectedItem="{Binding SelectedSession}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  Background="White"
                  BorderBrush="#E0E0E0"
                  BorderThickness="1"
                  RowHeight="40"
                  FontSize="14"
                  AlternatingRowBackground="#F9F9F9">

            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الجلسة" 
                                    Binding="{Binding SessionId}" 
                                    Width="100"/>
                
                <DataGridTextColumn Header="اسم المريض" 
                                    Binding="{Binding PatientName}" 
                                    Width="200"/>
                
                <DataGridTextColumn Header="نوع العلاج" 
                                    Binding="{Binding TreatmentType}" 
                                    Width="150"/>
                
                <DataGridTextColumn Header="تاريخ الجلسة" 
                                    Binding="{Binding SessionDate, StringFormat='{}{0:yyyy-MM-dd}'}" 
                                    Width="120"/>
                
                <DataGridTextColumn Header="وقت البداية" 
                                    Binding="{Binding StartTime, StringFormat='{}{0:HH:mm}'}" 
                                    Width="100"/>
                
                <DataGridTextColumn Header="المدة (دقيقة)" 
                                    Binding="{Binding Duration}" 
                                    Width="100"/>
                
                <DataGridTextColumn Header="التكلفة" 
                                    Binding="{Binding Cost, StringFormat='{}{0:N0} ريال'}" 
                                    Width="120"/>
                
                <DataGridTemplateColumn Header="الحالة" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}" 
                                    CornerRadius="12" 
                                    Padding="8,4">
                                <TextBlock Text="{Binding Status}" 
                                           Foreground="White" 
                                           FontWeight="Bold" 
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTextColumn Header="ملاحظات" 
                                    Binding="{Binding Notes}" 
                                    Width="*"/>
            </DataGrid.Columns>

            <!-- قائمة السياق -->
            <DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="📝 تعديل الجلسة"
                              Command="{Binding EditSessionCommand}"
                              Icon="✏️"/>
                    <MenuItem Header="👁️ عرض التفاصيل"
                              Command="{Binding ViewSessionDetailsCommand}"
                              Icon="👁️"/>
                    <Separator/>
                    <MenuItem Header="✅ تأكيد اكتمال"
                              Command="{Binding CompleteSessionCommand}"
                              Icon="✅"/>
                    <MenuItem Header="❌ إلغاء الجلسة"
                              Command="{Binding CancelSessionCommand}"
                              Icon="❌"/>
                    <Separator/>
                    <MenuItem Header="🖨️ طباعة الجلسة"
                              Command="{Binding PrintSessionCommand}"
                              Icon="🖨️"/>
                </ContextMenu>
            </DataGrid.ContextMenu>
        </DataGrid>

        <!-- الإحصائيات السريعة -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الجلسات -->
            <Border Grid.Column="0" Background="#2196F3" CornerRadius="8" Padding="15,10" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="{Binding TotalSessions}" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي الجلسات" 
                               FontSize="12" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- الجلسات المكتملة -->
            <Border Grid.Column="1" Background="#4CAF50" CornerRadius="8" Padding="15,10" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="{Binding CompletedSessions}" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="مكتملة" 
                               FontSize="12" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- الجلسات المعلقة -->
            <Border Grid.Column="2" Background="#FF9800" CornerRadius="8" Padding="15,10" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="{Binding PendingSessions}" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="معلقة" 
                               FontSize="12" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- إجمالي الأرباح -->
            <Border Grid.Column="3" Background="#9C27B0" CornerRadius="8" Padding="15,10" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="{Binding TotalEarnings, StringFormat='{}{0:N0}'}" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي الأرباح (ريال)" 
                               FontSize="12" 
                               Foreground="White" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- زر الإغلاق -->
            <Button Grid.Column="4" 
                    Content="❌ إغلاق" 
                    Command="{Binding CloseCommand}"
                    Background="#F44336" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Padding="20,10" 
                    Margin="20,0,0,0" 
                    FontWeight="Bold" 
                    FontSize="14" 
                    MinWidth="120"/>
        </Grid>
    </Grid>
</Window>
