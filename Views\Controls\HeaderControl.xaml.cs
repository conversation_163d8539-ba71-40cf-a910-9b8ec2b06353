using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace AqlanCenterProApp.Views.Controls
{
    public partial class HeaderControl : UserControl
    {
        private DispatcherTimer _clockTimer;

        public HeaderControl()
        {
            InitializeComponent();
            Loaded += HeaderControl_Loaded;
        }

        private void HeaderControl_Loaded(object sender, RoutedEventArgs e)
        {
            // ساعة رقمية حية
            _clockTimer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(1) };
            _clockTimer.Tick += (s, ev) =>
            {
                ClockText.Text = DateTime.Now.ToString("HH:mm:ss");
            };
            _clockTimer.Start();

            // اسم المستخدم الحالي (مثال ثابت، عدل حسب النظام)
            UserNameText.Text = "مرحباً، د. عقلان";
        }

        private void Notifications_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("نافذة التنبيهات ستظهر هنا.", "التنبيهات");
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("نافذة الإعدادات ستظهر هنا.", "الإعدادات");
        }

        private void Language_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تغيير اللغة (عربي/إنجليزي) سيتم من هنا.", "تغيير اللغة");
        }

        private void Theme_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تغيير الثيم (ليلي/نهاري) سيتم من هنا.", "تغيير الثيم");
        }

        private void Logout_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تسجيل الخروج.", "تسجيل الخروج");
        }
    }
}
