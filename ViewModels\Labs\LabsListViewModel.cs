using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using AqlanCenterProApp.Views.Labs;


namespace AqlanCenterProApp.ViewModels.Labs
{
    /// <summary>
    /// ViewModel لإدارة قائمة المعامل والمختبرات
    /// </summary>
    public class LabsListViewModel : BaseViewModel
    {
        private readonly ILabService _labService;
        private readonly ILabOrderService _labOrderService;

        /// <summary>
        /// مجموعة المعامل
        /// </summary>
        public ObservableCollection<Lab> Labs { get; set; } = new();

        /// <summary>
        /// عرض المجموعة مع إمكانية الفلترة والبحث
        /// </summary>
        public ICollectionView LabsView { get; set; }

        private Lab? _selectedLab;
        /// <summary>
        /// المعمل المحدد
        /// </summary>
        public Lab? SelectedLab
        {
            get => _selectedLab;
            set
            {
                SetProperty(ref _selectedLab, value);
                OnPropertyChanged(nameof(IsLabSelected));
            }
        }

        private string _searchText = string.Empty;
        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                ApplyFilter();
            }
        }

        private string _statusFilter = "الكل";
        /// <summary>
        /// فلتر الحالة
        /// </summary>
        public string StatusFilter
        {
            get => _statusFilter;
            set
            {
                SetProperty(ref _statusFilter, value);
                ApplyFilter();
            }
        }

        /// <summary>
        /// خيارات فلتر الحالة
        /// </summary>
        public List<string> StatusOptions { get; } = new() { "الكل", "نشط", "غير نشط" };

        /// <summary>
        /// هل يوجد معمل محدد
        /// </summary>
        public bool IsLabSelected => SelectedLab != null;

        /// <summary>
        /// عدد المعامل
        /// </summary>
        public int TotalLabs => Labs.Count;

        /// <summary>
        /// عدد المعامل النشطة
        /// </summary>
        public int ActiveLabs => Labs.Count(l => l.IsActive);

        /// <summary>
        /// عدد المعامل غير النشطة
        /// </summary>
        public int InactiveLabs => Labs.Count(l => !l.IsActive);

        // Commands
        /// <summary>
        /// أمر تحديث القائمة
        /// </summary>
        public ICommand RefreshCommand { get; private set; } = null!;

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; private set; } = null!;

        /// <summary>
        /// أمر إضافة معمل جديد
        /// </summary>
        public ICommand AddLabCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تعديل المعمل
        /// </summary>
        public ICommand EditLabCommand { get; private set; } = null!;

        /// <summary>
        /// أمر حذف المعمل
        /// </summary>
        public ICommand DeleteLabCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض تفاصيل المعمل
        /// </summary>
        public ICommand ViewLabDetailsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض طلبات المعمل
        /// </summary>
        public ICommand ViewLabOrdersCommand { get; private set; } = null!;

        /// <summary>
        /// أمر طباعة قائمة المعامل
        /// </summary>
        public ICommand PrintCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تصدير البيانات
        /// </summary>
        public ICommand ExportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر نسخ البيانات
        /// </summary>
        public ICommand CopyCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الإحصائيات
        /// </summary>
        public ICommand ShowStatisticsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض التقارير
        /// </summary>
        public ICommand ShowReportsCommand { get; private set; } = null!;

        public LabsListViewModel(ILabService labService, ILabOrderService labOrderService)
        {
            _labService = labService;
            _labOrderService = labOrderService;

            // إعداد العرض
            LabsView = CollectionViewSource.GetDefaultView(Labs);
            LabsView.Filter = FilterLabs;

            // إعداد الأوامر
            InitializeCommands();
        }

        /// <summary>
        /// تهيئة الأوامر
        /// </summary>
        private void InitializeCommands()
        {
            RefreshCommand = new RelayCommand(async () => await LoadLabsAsync());
            SearchCommand = new RelayCommand(() => ApplyFilter());
            AddLabCommand = new RelayCommand(AddLab);
            EditLabCommand = new RelayCommand(EditLab, () => IsLabSelected);
            DeleteLabCommand = new RelayCommand(async () => await DeleteLabAsync(), () => IsLabSelected);
            ViewLabDetailsCommand = new RelayCommand(ViewLabDetails, () => IsLabSelected);
            ViewLabOrdersCommand = new RelayCommand(ViewLabOrders, () => IsLabSelected);
            PrintCommand = new RelayCommand(PrintLabs);
            ExportCommand = new RelayCommand(ExportLabs);
            CopyCommand = new RelayCommand(CopyLabData, () => IsLabSelected);
            ShowStatisticsCommand = new RelayCommand(ShowStatistics);
            ShowReportsCommand = new RelayCommand(ShowReports);
        }

        /// <summary>
        /// تحميل المعامل
        /// </summary>
        public async Task LoadLabsAsync()
        {
            await ExecuteAsync(async () =>
            {
                var labs = await _labService.GetAllLabsAsync()
                    .ConfigureAwait(false);
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Labs.Clear();
                    foreach (var lab in labs)
                    {
                        Labs.Add(lab);
                    }
                    LabsView.Refresh();
                });
            });
        }

        /// <summary>
        /// تطبيق الفلتر
        /// </summary>
        private void ApplyFilter()
        {
            LabsView.Refresh();
        }

        /// <summary>
        /// فلترة المعامل
        /// </summary>
        private bool FilterLabs(object obj)
        {
            if (obj is not Lab lab)
                return false;

            // فلتر البحث
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                if (!lab.Name.ToLower().Contains(searchLower) &&
                    !lab.Address?.ToLower().Contains(searchLower) == true &&
                    !lab.Phone?.Contains(SearchText) == true &&
                    !lab.Email?.ToLower().Contains(searchLower) == true)
                {
                    return false;
                }
            }

            // فلتر الحالة
            if (StatusFilter != "الكل")
            {
                bool isActive = StatusFilter == "نشط";
                if (lab.IsActive != isActive)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// إضافة معمل جديد
        /// </summary>
        private void AddLab()
        {
            try
            {
                var viewModel = new AddEditLabViewModel(_labService);
                var dialog = new AddEditLabView(viewModel);

                if (dialog.ShowDialog() == true)
                {
                    // تحديث القائمة
                    _ = LoadLabsAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في فتح نافذة إضافة المعمل: {ex.Message}";
            }
        }

        /// <summary>
        /// تعديل المعمل
        /// </summary>
        private void EditLab()
        {
            if (SelectedLab == null) return;

            try
            {
                var viewModel = new AddEditLabViewModel(_labService, SelectedLab);
                var dialog = new AddEditLabView(viewModel);

                if (dialog.ShowDialog() == true)
                {
                    // تحديث القائمة
                    _ = LoadLabsAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في فتح نافذة تعديل المعمل: {ex.Message}";
            }
        }

        /// <summary>
        /// حذف المعمل
        /// </summary>
        private async Task DeleteLabAsync()
        {
            if (SelectedLab == null) return;

            await ExecuteAsync(async () =>
            {
                await _labService.DeleteLabAsync(SelectedLab.Id);
                Labs.Remove(SelectedLab);
            }, "جاري حذف المعمل...");
        }

        /// <summary>
        /// عرض تفاصيل المعمل
        /// </summary>
        private void ViewLabDetails()
        {
            // سيتم تنفيذ هذا من خلال الواجهة
        }

        /// <summary>
        /// عرض طلبات المعمل
        /// </summary>
        private void ViewLabOrders()
        {
            // سيتم تنفيذ هذا من خلال الواجهة
        }

        /// <summary>
        /// طباعة قائمة المعامل
        /// </summary>
        private void PrintLabs()
        {
            // سيتم تنفيذ هذا لاحقاً
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void ExportLabs()
        {
            // سيتم تنفيذ هذا لاحقاً
        }

        /// <summary>
        /// نسخ بيانات المعمل
        /// </summary>
        private void CopyLabData()
        {
            if (SelectedLab == null) return;

            // نسخ بيانات المعمل إلى الحافظة
            var labInfo = $"اسم المعمل: {SelectedLab.Name}\n" +
                         $"العنوان: {SelectedLab.Address}\n" +
                         $"الهاتف: {SelectedLab.Phone}\n" +
                         $"الواتساب: {SelectedLab.WhatsApp}\n" +
                         $"البريد الإلكتروني: {SelectedLab.Email}";

            System.Windows.Clipboard.SetText(labInfo);
        }

        /// <summary>
        /// عرض الإحصائيات
        /// </summary>
        private void ShowStatistics()
        {
            // سيتم تنفيذ هذا لاحقاً
        }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        private void ShowReports()
        {
            // سيتم تنفيذ هذا لاحقاً
        }
    }
}
