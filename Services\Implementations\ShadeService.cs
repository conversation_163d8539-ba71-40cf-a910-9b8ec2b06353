using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Services.Implementations
{
    public class ShadeService : IShadeService
    {
        private readonly AqlanCenterDbContext _context;

        public ShadeService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Shade>> GetAllShadesAsync()
        {
            return await _context.Shades
                .Include(s => s.LabOrders)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Shade>> GetActiveShadesAsync()
        {
            return await _context.Shades
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Shade?> GetShadeByIdAsync(int id)
        {
            return await _context.Shades
                .Include(s => s.LabOrders)
                .FirstOrDefaultAsync(s => s.ShadeId == id);
        }

        public async Task<Shade> AddShadeAsync(Shade shade)
        {
            shade.CreatedAt = DateTime.Now;
            shade.UpdatedAt = DateTime.Now;
            _context.Shades.Add(shade);
            await _context.SaveChangesAsync();
            return shade;
        }

        public async Task<Shade> UpdateShadeAsync(Shade shade)
        {
            shade.UpdatedAt = DateTime.Now;
            _context.Shades.Update(shade);
            await _context.SaveChangesAsync();
            return shade;
        }

        public async Task<bool> DeleteShadeAsync(int id)
        {
            var shade = await _context.Shades.FindAsync(id);
            if (shade == null) return false;

            // Check if shade is used in any lab orders
            var hasOrders = await _context.LabOrders.AnyAsync(lo => lo.ShadeId == id);
            if (hasOrders)
            {
                // Soft delete - mark as inactive
                shade.IsActive = false;
                shade.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
                return true;
            }

            _context.Shades.Remove(shade);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ShadeExistsAsync(int id)
        {
            return await _context.Shades.AnyAsync(s => s.ShadeId == id);
        }

        public async Task<IEnumerable<Shade>> SearchShadesAsync(string searchTerm)
        {
            return await _context.Shades
                .Where(s => s.IsActive && (s.Name.Contains(searchTerm) || 
                                         s.Description!.Contains(searchTerm) || 
                                         s.ColorCode!.Contains(searchTerm)))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Shade> GetShadeByNameAsync(string name)
        {
            return await _context.Shades
                .FirstOrDefaultAsync(s => s.Name == name && s.IsActive);
        }
    }
} 