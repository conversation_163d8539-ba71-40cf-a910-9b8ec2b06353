using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows;
using CommunityToolkit.Mvvm.Input;

namespace AqlanCenterProApp.ViewModels.Reports
{
    public class ReportViewModel : INotifyPropertyChanged
    {
        private string _reportTitle;
        private string _reportDate;
        private string _reportDetails;
        private string _notes;
        private ObservableCollection<QuickStat> _quickStats;
        private ObservableCollection<ReportDataItem> _reportData;
        private DateTime _reportDateValue = DateTime.Now;

        public ReportViewModel()
        {
            SaveCommand = new RelayCommand(SaveReport);
            ResetCommand = new RelayCommand(ResetReport);
            ExportCommand = new RelayCommand(ExportReport);
            PrintCommand = new RelayCommand(PrintReport);
            
            InitializeData();
            _originalCopy = this.MemberwiseClone() as ReportViewModel;
        }

        public string ReportTitle
        {
            get => _reportTitle;
            set
            {
                _reportTitle = value;
                OnPropertyChanged();
            }
        }

        public string ReportDate
        {
            get => _reportDate;
            set
            {
                _reportDate = value;
                OnPropertyChanged();
            }
        }

        public string ReportDetails
        {
            get => _reportDetails;
            set
            {
                _reportDetails = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<QuickStat> QuickStats
        {
            get => _quickStats;
            set
            {
                _quickStats = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ReportDataItem> ReportData
        {
            get => _reportData;
            set
            {
                _reportData = value;
                OnPropertyChanged();
            }
        }

        public DateTime ReportDateValue
        {
            get => _reportDateValue;
            set
            {
                _reportDateValue = value;
                ReportDate = $"تاريخ التقرير: {value:dd/MM/yyyy}";
                OnPropertyChanged();
            }
        }

        public ICommand SaveCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }

        private ReportViewModel _originalCopy;

        private async void InitializeData()
        {
            ReportTitle = "تقرير الأداء الشامل";
            ReportDate = $"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy}";
            ReportDetails = "هذا التقرير يعرض الأداء الشامل للمركز خلال الفترة المحددة، ويتضمن إحصائيات المرضى والمواعيد والإيرادات والمصروفات.";
            Notes = "• تم إعداد هذا التقرير بناءً على البيانات المتوفرة في النظام\n• جميع الأرقام صحيحة حتى تاريخ إعداد التقرير\n• يرجى مراجعة البيانات قبل اتخاذ أي قرارات مالية";

            // سيتم تحديث هذه البيانات من قاعدة البيانات لاحقاً
            QuickStats = new ObservableCollection<QuickStat>
            {
                new QuickStat { Icon = "👥", Value = "0", Label = "إجمالي المرضى" },
                new QuickStat { Icon = "📅", Value = "0", Label = "المواعيد" },
                new QuickStat { Icon = "💰", Value = "0", Label = "الإيرادات (ريال)" },
                new QuickStat { Icon = "💊", Value = "0", Label = "العلاجات المكتملة" },
                new QuickStat { Icon = "⭐", Value = "0", Label = "تقييم المرضى" },
                new QuickStat { Icon = "📈", Value = "0%", Label = "نمو الإيرادات" }
            };

            ReportData = new ObservableCollection<ReportDataItem>
            {
                new ReportDataItem { Label = "المرضى الجدد", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "المواعيد المكتملة", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "المواعيد الملغية", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "الإيرادات من العلاج", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "الإيرادات من الاستشارات", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "المصروفات التشغيلية", Value = "0", Percentage = "0%" },
                new ReportDataItem { Label = "صافي الربح", Value = "0", Percentage = "0%" }
            };

            // تحميل البيانات من قاعدة البيانات
            await LoadDataFromDatabase();
        }

        private async Task LoadDataFromDatabase()
        {
            try
            {
                // هنا يمكن إضافة منطق لتحميل البيانات من قاعدة البيانات
                // سيتم تنفيذ ذلك لاحقاً عند ربط ReportViewModel بقاعدة البيانات
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void SaveReport()
        {
            _originalCopy = this.MemberwiseClone() as ReportViewModel;
            MessageBox.Show("تم حفظ التعديلات بنجاح!", "حفظ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetReport()
        {
            if (_originalCopy != null)
            {
                this.ReportTitle = _originalCopy.ReportTitle;
                this.ReportDateValue = _originalCopy.ReportDateValue;
                this.ReportDetails = _originalCopy.ReportDetails;
                this.Notes = _originalCopy.Notes;
                this.QuickStats = new ObservableCollection<QuickStat>(_originalCopy.QuickStats.Select(q => new QuickStat { Icon = q.Icon, Value = q.Value, Label = q.Label }));
                this.ReportData = new ObservableCollection<ReportDataItem>(_originalCopy.ReportData.Select(d => new ReportDataItem { Label = d.Label, Value = d.Value, Percentage = d.Percentage }));
            }
        }

        private void ExportReport()
        {
            MessageBox.Show("سيتم تنفيذ منطق التصدير إلى PDF هنا.", "تصدير PDF", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintReport()
        {
            MessageBox.Show("سيتم تنفيذ منطق الطباعة هنا.", "طباعة التقرير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class QuickStat
    {
        public string Icon { get; set; }
        public string Value { get; set; }
        public string Label { get; set; }
    }

    public class ReportDataItem
    {
        public string Label { get; set; }
        public string Value { get; set; }
        public string Percentage { get; set; }
    }
} 