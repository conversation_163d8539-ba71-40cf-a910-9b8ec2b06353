using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;
using System.Threading;

namespace AqlanCenterProApp.ViewModels.Doctors
{
    /// <summary>
    /// ViewModel لإدارة قائمة الأطباء
    /// </summary>
    public class DoctorsListViewModel : BaseViewModel
    {
        private readonly IDoctorService _doctorService = null!;

        #region Properties

        /// <summary>
        /// قائمة الأطباء
        /// </summary>
        public ObservableCollection<Doctor> Doctors { get; } = new();

        private Doctor? _selectedDoctor;
        private string _searchText = string.Empty;
        private string _selectedSpecialization = "الكل";
        private string _selectedStatus = "الكل";
        private bool _showInactiveDoctors = true;

        /// <summary>
        /// الطبيب المحدد
        /// </summary>
        public Doctor? SelectedDoctor
        {
            get => _selectedDoctor;
            set
            {
                if (SetProperty(ref _selectedDoctor, value))
                {
                    // تحديث حالة الأوامر عند تغيير الطبيب المحدد
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// التخصص المحدد للتصفية
        /// </summary>
        public string SelectedSpecialization
        {
            get => _selectedSpecialization;
            set
            {
                if (SetProperty(ref _selectedSpecialization, value))
                {
                    Application.Current.Dispatcher.InvokeAsync(async () => await SearchAsync());
                }
            }
        }

        /// <summary>
        /// الحالة المحددة للتصفية
        /// </summary>
        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    Application.Current.Dispatcher.InvokeAsync(async () => await SearchAsync());
                }
            }
        }

        /// <summary>
        /// هل يتم عرض الأطباء غير النشطين
        /// </summary>
        public bool ShowInactiveDoctors
        {
            get => _showInactiveDoctors;
            set
            {
                if (SetProperty(ref _showInactiveDoctors, value))
                {
                    Application.Current.Dispatcher.InvokeAsync(async () => await SearchAsync());
                }
            }
        }

        private int _totalDoctors = 0;
        private int _activeDoctors = 0;
        private int _inactiveDoctors = 0;
        private int _newDoctorsThisMonth = 0;

        /// <summary>
        /// إجمالي عدد الأطباء
        /// </summary>
        public int TotalDoctors
        {
            get => _totalDoctors;
            set => SetProperty(ref _totalDoctors, value);
        }

        /// <summary>
        /// عدد الأطباء النشطين
        /// </summary>
        public int ActiveDoctors
        {
            get => _activeDoctors;
            set => SetProperty(ref _activeDoctors, value);
        }

        /// <summary>
        /// عدد الأطباء غير النشطين
        /// </summary>
        public int InactiveDoctors
        {
            get => _inactiveDoctors;
            set => SetProperty(ref _inactiveDoctors, value);
        }

        /// <summary>
        /// عدد الأطباء الجدد هذا الشهر
        /// </summary>
        public int NewDoctorsThisMonth
        {
            get => _newDoctorsThisMonth;
            set => SetProperty(ref _newDoctorsThisMonth, value);
        }

        /// <summary>
        /// قائمة التخصصات
        /// </summary>
        public List<string> Specializations { get; } = new List<string>
        {
            "الكل",
            "أخصائي تقويم أسنان",
            "طبيب أسنان عام",
            "أخصائي جراحة الفم والوجه والفكين",
            "أخصائي طب أسنان الأطفال",
            "أخصائي علاج الجذور",
            "أخصائي التركيبات السنية",
            "أخصائي أمراض اللثة",
            "أخصائي طب الأسنان التجميلي",
            "أخصائي زراعة الأسنان",
            "أخصائي الأشعة السنية"
        };

        /// <summary>
        /// قائمة الحالات
        /// </summary>
        public List<string> StatusList { get; } = new List<string>
        {
            "الكل",
            "نشط",
            "غير نشط",
            "في إجازة",
            "منتهي العقد"
        };

        /// <summary>
        /// قائمة أنواع التعاقد
        /// </summary>
        public List<string> ContractTypes { get; } = new List<string>
        {
            "دائم",
            "دوام جزئي",
            "بالنسبة",
            "استشاري",
            "متدرب"
        };

        #endregion

        #region Commands

        /// <summary>
        /// أمر تحديث القائمة
        /// </summary>
        public ICommand RefreshCommand { get; private set; } = null!;

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; private set; } = null!;

        /// <summary>
        /// أمر إضافة طبيب جديد
        /// </summary>
        public ICommand AddDoctorCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تعديل الطبيب
        /// </summary>
        public ICommand EditDoctorCommand { get; private set; } = null!;

        /// <summary>
        /// أمر حذف الطبيب
        /// </summary>
        public ICommand DeleteDoctorCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تفعيل/تعطيل الطبيب
        /// </summary>
        public ICommand ToggleStatusCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض تفاصيل الطبيب
        /// </summary>
        public ICommand ViewDetailsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض إحصائيات الطبيب
        /// </summary>
        public ICommand ViewStatisticsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر طباعة قائمة الأطباء
        /// </summary>
        public ICommand PrintCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تصدير البيانات
        /// </summary>
        public ICommand ExportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر إرسال واتساب
        /// </summary>
        public ICommand SendWhatsAppCommand { get; private set; } = null!;

        /// <summary>
        /// أمر عرض الإحصائيات العامة
        /// </summary>
        public ICommand ShowGeneralStatisticsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر نسخ البيانات
        /// </summary>
        public ICommand CopyCommand { get; private set; } = null!;

        #endregion

        #region Events

        /// <summary>
        /// حدث تحميل الصفحة
        /// </summary>
        public event EventHandler? Loaded;

        /// <summary>
        /// إثارة حدث التحميل
        /// </summary>
        protected virtual void OnLoaded()
        {
            Loaded?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Constructor

        public DoctorsListViewModel(IDoctorService doctorService)
        {
            try
            {
                _doctorService = doctorService;

                // تهيئة الأوامر
                RefreshCommand = new RelayCommand(async () => await RefreshAsync());
                SearchCommand = new RelayCommand(async () => await SearchAsync());
                AddDoctorCommand = new RelayCommand(AddDoctor);
                EditDoctorCommand = new RelayCommand(EditDoctor, () => SelectedDoctor != null);
                DeleteDoctorCommand = new RelayCommand(DeleteDoctor, () => SelectedDoctor != null);
                ToggleStatusCommand = new RelayCommand(ToggleStatus, () => SelectedDoctor != null);
                ViewDetailsCommand = new RelayCommand(ViewDetails, () => SelectedDoctor != null);
                ViewStatisticsCommand = new RelayCommand(ViewStatistics, () => SelectedDoctor != null);
                PrintCommand = new RelayCommand(Print);
                ExportCommand = new RelayCommand(Export);
                SendWhatsAppCommand = new RelayCommand<object>(param => SendWhatsApp(param as Doctor), param => param is Doctor);
                ShowGeneralStatisticsCommand = new RelayCommand(ShowGeneralStatistics);
                CopyCommand = new RelayCommand(Copy, () => SelectedDoctor != null);

                Console.WriteLine("DoctorsListViewModel initialized successfully");

                // لا نحمل البيانات هنا لتجنب التعليق
                // سيتم تحميل البيانات عند تحميل الواجهة
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تهيئة DoctorsListViewModel: {ex.Message}");
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحديث آمن للبيانات
        /// </summary>
        public async Task SafeRefreshAsync()
        {
            try
            {
                await RefreshAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SafeRefreshAsync: {ex.Message}");
                // إضافة بيانات وهمية في حالة الفشل
                LoadSampleData();
            }
        }

        /// <summary>
        /// بحث آمن
        /// </summary>
        public async Task SafeSearchAsync()
        {
            try
            {
                await SearchAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SafeSearchAsync: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات وهمية للاختبار
        /// </summary>
        private void LoadSampleData()
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Doctors.Clear();

                    var sampleDoctors = new List<Doctor>
                    {
                        new Doctor
                        {
                            Id = 1,
                            FullName = "د. أحمد محمد",
                            Specialization = "طبيب أسنان عام",
                            Phone = "01234567890",
                            Mobile = "01234567890",
                            Email = "<EMAIL>",
                            IsActive = true,
                            Status = "نشط",
                            JoinDate = DateTime.Now.AddMonths(-6),
                            ContractType = "دائم",
                            CommissionPercentage = 30,
                            TotalPatientsCount = 25,
                            CompletedSessionsCount = 45,
                            TotalEarnings = 15000,
                            Rating = 4.5m
                        },
                        new Doctor
                        {
                            Id = 2,
                            FullName = "د. فاطمة علي",
                            Specialization = "أخصائي تقويم أسنان",
                            Phone = "01234567891",
                            Mobile = "01234567891",
                            Email = "<EMAIL>",
                            IsActive = true,
                            Status = "نشط",
                            JoinDate = DateTime.Now.AddMonths(-12),
                            ContractType = "دائم",
                            CommissionPercentage = 35,
                            TotalPatientsCount = 40,
                            CompletedSessionsCount = 80,
                            TotalEarnings = 28000,
                            Rating = 4.8m
                        },
                        new Doctor
                        {
                            Id = 3,
                            FullName = "د. محمد سالم",
                            Specialization = "أخصائي جراحة الفم والوجه والفكين",
                            Phone = "01234567892",
                            Mobile = "01234567892",
                            Email = "<EMAIL>",
                            IsActive = true,
                            Status = "نشط",
                            JoinDate = DateTime.Now.AddMonths(-3),
                            ContractType = "دوام جزئي",
                            CommissionPercentage = 25,
                            TotalPatientsCount = 15,
                            CompletedSessionsCount = 30,
                            TotalEarnings = 12000,
                            Rating = 4.2m
                        }
                    };

                    foreach (var doctor in sampleDoctors)
                    {
                        Doctors.Add(doctor);
                    }

                    UpdateStatistics();
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل البيانات الوهمية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قائمة الأطباء
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                IsLoading = true;

                // إضافة timeout للعملية
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(25));

                // تحميل البيانات في الخلفية مع timeout
                var doctors = await Task.Run(async () =>
                {
                    return await _doctorService.GetAllDoctorsAsync();
                }, cts.Token);

                // تحديث القائمة في الخيط الرئيسي
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    Doctors.Clear();
                    foreach (var doctor in doctors)
                    {
                        Doctors.Add(doctor);
                    }
                    UpdateStatistics();
                });
            }
            catch (OperationCanceledException)
            {
                System.Windows.MessageBox.Show("انتهت مهلة تحميل بيانات الأطباء. يرجى المحاولة مرة أخرى.", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل بيانات الأطباء: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// البحث عن الأطباء
        /// </summary>
        private async Task SearchAsync()
        {
            try
            {
                // التأكد من أننا في الخيط الرئيسي
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    await Application.Current.Dispatcher.InvokeAsync(async () => await SearchAsync());
                    return;
                }

                IsLoading = true;

                // تحميل البيانات في خيط خلفي
                var doctors = await Task.Run(async () =>
                {
                    List<Doctor> result;
                    if (string.IsNullOrWhiteSpace(SearchText))
                    {
                        result = (await _doctorService.GetAllDoctorsAsync()).ToList();
                    }
                    else
                    {
                        result = (await _doctorService.SearchDoctorsAsync(SearchText)).ToList();
                    }

                    // تطبيق التصفية
                    return ApplyFilters(result);
                });

                // تحديث القائمة في الخيط الرئيسي
                Doctors.Clear();
                foreach (var doctor in doctors)
                {
                    Doctors.Add(doctor);
                }
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في SearchAsync: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// تطبيق المرشحات
        /// </summary>
        private List<Doctor> ApplyFilters(List<Doctor> doctors)
        {
            // تصفية حسب التخصص
            if (SelectedSpecialization != "الكل")
            {
                doctors = doctors.Where(d => d.Specialization == SelectedSpecialization).ToList();
            }

            // تصفية حسب الحالة
            if (SelectedStatus != "الكل")
            {
                doctors = doctors.Where(d => d.Status == SelectedStatus).ToList();
            }

            // تصفية الأطباء غير النشطين
            if (!ShowInactiveDoctors)
            {
                doctors = doctors.Where(d => d.IsActive).ToList();
            }

            return doctors;
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                TotalDoctors = Doctors.Count;
                ActiveDoctors = Doctors.Count(d => d.IsActive && d.Status == "نشط");
                InactiveDoctors = Doctors.Count(d => !d.IsActive || d.Status != "نشط");

                // حساب الأطباء الجدد هذا الشهر
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                NewDoctorsThisMonth = Doctors.Count(d => d.JoinDate.Month == currentMonth && d.JoinDate.Year == currentYear);

                // تحديث خصائص إضافية للأداء
                OnPropertyChanged(nameof(TotalDoctors));
                OnPropertyChanged(nameof(ActiveDoctors));
                OnPropertyChanged(nameof(InactiveDoctors));
                OnPropertyChanged(nameof(NewDoctorsThisMonth));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة طبيب جديد
        /// </summary>
        private void AddDoctor()
        {
            try
            {
                // إنشاء نافذة إضافة طبيب جديد
                var addDoctorWindow = new Views.Doctors.AddEditDoctorWindow();
                var result = addDoctorWindow.ShowDialog();

                if (result == true)
                {
                    // تحديث القائمة بعد الإضافة
                    RefreshCommand.Execute(null);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة طبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل الطبيب
        /// </summary>
        private void EditDoctor()
        {
            if (SelectedDoctor == null) return;

            try
            {
                // إنشاء نافذة تعديل الطبيب
                var editDoctorWindow = new Views.Doctors.AddEditDoctorWindow(SelectedDoctor);
                var result = editDoctorWindow.ShowDialog();

                if (result == true)
                {
                    // تحديث القائمة بعد التعديل
                    RefreshCommand.Execute(null);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة تعديل الطبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف الطبيب
        /// </summary>
        private async void DeleteDoctor()
        {
            if (SelectedDoctor == null) return;

            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف الطبيب: {SelectedDoctor.FullName}؟\n\nملاحظة: إذا كان للطبيب جلسات أو مواعيد، سيتم تعطيله بدلاً من الحذف.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    var doctorToDelete = SelectedDoctor; // حفظ مرجع للطبيب

                    var success = await _doctorService.DeleteDoctorAsync(doctorToDelete.Id, "System");
                    if (success)
                    {
                        // إزالة الطبيب من القائمة مباشرة لتحسين الأداء
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            Doctors.Remove(doctorToDelete);
                            SelectedDoctor = null;
                            UpdateStatistics();
                        });

                        System.Windows.MessageBox.Show("تم حذف/تعطيل الطبيب بنجاح", "نجح",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("فشل في حذف الطبيب. قد يكون للطبيب جلسات مرتبطة.", "خطأ",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في حذف الطبيب: {ex.Message}");
                    System.Windows.MessageBox.Show($"خطأ في حذف الطبيب: {ex.Message}", "خطأ",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        /// <summary>
        /// تفعيل/تعطيل الطبيب
        /// </summary>
        private async void ToggleStatus()
        {
            if (SelectedDoctor == null) return;

            try
            {
                var success = await _doctorService.ToggleDoctorStatusAsync(SelectedDoctor.Id, "System");
                if (success)
                {
                    await RefreshAsync();
                    var status = SelectedDoctor.IsActive ? "تفعيل" : "تعطيل";
                    System.Windows.MessageBox.Show($"تم {status} الطبيب بنجاح", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show("فشل في تغيير حالة الطبيب", "خطأ",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تغيير حالة الطبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الطبيب
        /// </summary>
        private void ViewDetails()
        {
            if (SelectedDoctor == null) return;

            try
            {
                var detailsWindow = new Views.Doctors.DoctorDetailsWindow(SelectedDoctor);
                detailsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة تفاصيل الطبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض إحصائيات الطبيب
        /// </summary>
        private void ViewStatistics()
        {
            if (SelectedDoctor == null) return;

            try
            {
                var statisticsWindow = new Views.Doctors.DoctorStatisticsWindow(SelectedDoctor);
                statisticsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إحصائيات الطبيب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة قائمة الأطباء
        /// </summary>
        private async void Print()
        {
            try
            {
                var exportService = new Services.ExportService();
                var success = await exportService.PrintDoctorsListAsync(Doctors.ToList());

                if (success)
                {
                    System.Windows.MessageBox.Show("تم فتح التقرير للطباعة", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show("فشل في إنشاء تقرير الطباعة", "خطأ",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير قائمة الأطباء
        /// </summary>
        private async void Export()
        {
            try
            {
                var result = System.Windows.MessageBox.Show(
                    "اختر نوع التصدير:\n\nنعم = CSV (Excel)\nلا = HTML (تقرير)",
                    "نوع التصدير",
                    System.Windows.MessageBoxButton.YesNoCancel,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Cancel)
                    return;

                var exportService = new Services.ExportService();
                bool success = false;

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // تصدير CSV
                    success = await exportService.ExportDoctorsToCsvAsync(Doctors.ToList());
                }
                else
                {
                    // تصدير HTML
                    success = await exportService.ExportDoctorsToHtmlAsync(Doctors.ToList());
                }

                if (success)
                {
                    System.Windows.MessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show("فشل في تصدير البيانات", "خطأ",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إرسال رسالة واتساب للطبيب
        /// </summary>
        private void SendWhatsApp(Doctor? doctor)
        {
            if (doctor == null) return;

            try
            {
                if (string.IsNullOrEmpty(doctor.Mobile))
                {
                    System.Windows.MessageBox.Show("لا يوجد رقم هاتف للطبيب", "تنبيه",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // فتح واتساب
                var phoneNumber = doctor.Mobile.Replace("+", "").Replace(" ", "").Replace("-", "");
                var message = $"مرحباً د. {doctor.FullName}";
                var whatsappUrl = $"https://wa.me/{phoneNumber}?text={Uri.EscapeDataString(message)}";

                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح واتساب: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض الإحصائيات العامة
        /// </summary>
        private void ShowGeneralStatistics()
        {
            try
            {
                var totalDoctors = Doctors.Count;
                var activeDoctors = Doctors.Count(d => d.IsActive);
                var totalEarnings = Doctors.Sum(d => d.TotalEarnings);
                var totalPatients = Doctors.Sum(d => d.TotalPatientsCount);

                System.Windows.MessageBox.Show($"الإحصائيات العامة للأطباء:\n" +
                    $"إجمالي الأطباء: {totalDoctors}\n" +
                    $"الأطباء النشطون: {activeDoctors}\n" +
                    $"إجمالي الأرباح: {totalEarnings:C}\n" +
                    $"إجمالي المرضى: {totalPatients}", "الإحصائيات العامة",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في عرض الإحصائيات العامة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
        /// <summary>
        /// نسخ بيانات الطبيب
        /// </summary>
        private void Copy()
        {
            if (SelectedDoctor == null) return;

            try
            {
                var doctorInfo = $"اسم الطبيب: {SelectedDoctor.FullName}\n" +
                               $"التخصص: {SelectedDoctor.Specialization}\n" +
                               $"الهاتف: {SelectedDoctor.Mobile}\n" +
                               $"البريد الإلكتروني: {SelectedDoctor.Email}\n" +
                               $"الحالة: {SelectedDoctor.Status}\n" +
                               $"تاريخ الانضمام: {SelectedDoctor.JoinDate:yyyy-MM-dd}";

                System.Windows.Clipboard.SetText(doctorInfo);
                System.Windows.MessageBox.Show("تم نسخ بيانات الطبيب إلى الحافظة", "نجح",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في نسخ البيانات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
