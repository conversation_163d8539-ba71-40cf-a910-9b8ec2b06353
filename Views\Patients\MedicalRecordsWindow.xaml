<Window x:Class="AqlanCenterProApp.Views.Patients.MedicalRecordsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="السجلات الطبية"
        Width="1200"
        Height="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F6FA">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="السجلات الطبية للمرضى"
                   FontSize="28"
                   FontWeight="Bold"
                   Foreground="#3498DB"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- شريط البحث والفلاتر -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <TextBox Grid.Column="0"
                     x:Name="SearchTextBox"
                     Height="35"
                     FontSize="14"
                     VerticalContentAlignment="Center"
                     Margin="0,0,10,0"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>

            <!-- فلتر المريض -->
            <ComboBox Grid.Column="1"
                      x:Name="PatientFilterComboBox"
                      Width="200"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع المرضى"/>
            </ComboBox>

            <!-- فلتر التاريخ -->
            <ComboBox Grid.Column="2"
                      x:Name="DateFilterComboBox"
                      Width="150"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع التواريخ"/>
                <ComboBoxItem Content="آخر أسبوع"/>
                <ComboBoxItem Content="آخر شهر"/>
                <ComboBoxItem Content="آخر 3 أشهر"/>
                <ComboBoxItem Content="آخر سنة"/>
            </ComboBox>

            <!-- زر البحث -->
            <Button Grid.Column="3"
                    Content="🔍 بحث"
                    Width="100"
                    Height="35"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- قائمة السجلات الطبية -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <DataGrid x:Name="MedicalRecordsDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      HeadersVisibility="Column"
                      CanUserAddRows="False"
                      RowHeight="40"
                      EnableRowVirtualization="True"
                      EnableColumnVirtualization="True"
                      VirtualizingPanel.IsVirtualizing="True"
                      VirtualizingPanel.VirtualizationMode="Recycling">

                <DataGrid.Columns>
                    <!-- رقم السجل -->
                    <DataGridTextColumn Header="رقم السجل" 
                                        Binding="{Binding Id}" 
                                        Width="80"/>

                    <!-- اسم المريض -->
                    <DataGridTextColumn Header="اسم المريض" 
                                        Binding="{Binding PatientName}" 
                                        Width="200"/>

                    <!-- رقم الملف -->
                    <DataGridTextColumn Header="رقم الملف" 
                                        Binding="{Binding FileNumber}" 
                                        Width="100"/>

                    <!-- تاريخ السجل -->
                    <DataGridTextColumn Header="تاريخ السجل" 
                                        Binding="{Binding RecordDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>

                    <!-- نوع السجل -->
                    <DataGridTextColumn Header="نوع السجل" 
                                        Binding="{Binding RecordType}" 
                                        Width="150"/>

                    <!-- التشخيص -->
                    <DataGridTextColumn Header="التشخيص" 
                                        Binding="{Binding Diagnosis}" 
                                        Width="250"/>

                    <!-- العلاج -->
                    <DataGridTextColumn Header="العلاج المقترح" 
                                        Binding="{Binding Treatment}" 
                                        Width="200"/>

                    <!-- الطبيب -->
                    <DataGridTextColumn Header="الطبيب المعالج" 
                                        Binding="{Binding DoctorName}" 
                                        Width="150"/>

                    <!-- الحالة -->
                    <DataGridTextColumn Header="الحالة" 
                                        Binding="{Binding Status}" 
                                        Width="100"/>

                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="👁️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="عرض التفاصيل"
                                            Click="ViewDetailsButton_Click"/>
                                    <Button Content="✏️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="تعديل"
                                            Click="EditRecordButton_Click"/>
                                    <Button Content="🖨️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="طباعة"
                                            Click="PrintRecordButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <!-- قائمة السياق -->
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="👁️ عرض التفاصيل" Click="ViewDetailsMenuItem_Click"/>
                        <MenuItem Header="✏️ تعديل السجل" Click="EditRecordMenuItem_Click"/>
                        <MenuItem Header="🖨️ طباعة السجل" Click="PrintRecordMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="📋 نسخ المعلومات" Click="CopyRecordMenuItem_Click"/>
                        <MenuItem Header="📤 تصدير السجل" Click="ExportRecordMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="🗑️ حذف السجل" Click="DeleteRecordMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>
        </ScrollViewer>

        <!-- الأزرار السفلية -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="AddRecordButton"
                    Content="➕ إضافة سجل جديد"
                    Width="150"
                    Height="40"
                    Background="#27AE60"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="AddRecordButton_Click"/>
            
            <Button x:Name="RefreshButton"
                    Content="🔄 تحديث"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="RefreshButton_Click"/>
            
            <Button x:Name="ExportAllButton"
                    Content="📤 تصدير الكل"
                    Width="120"
                    Height="40"
                    Background="#F39C12"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="ExportAllButton_Click"/>
            
            <Button x:Name="PrintAllButton"
                    Content="🖨️ طباعة الكل"
                    Width="120"
                    Height="40"
                    Background="#9B59B6"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="PrintAllButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="❌ إغلاق"
                    Width="120"
                    Height="40"
                    Background="#E74C3C"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
