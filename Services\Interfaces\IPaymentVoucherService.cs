using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة سندات الصرف
    /// </summary>
    public interface IPaymentVoucherService
    {
        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// جلب جميع سندات الصرف
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetAllVouchersAsync();

        /// <summary>
        /// جلب سند صرف بالمعرف
        /// </summary>
        Task<PaymentVoucher?> GetVoucherByIdAsync(int id);

        /// <summary>
        /// جلب سند صرف برقم السند
        /// </summary>
        Task<PaymentVoucher?> GetVoucherByNumberAsync(string voucherNumber);

        /// <summary>
        /// جلب سندات صرف موظف معين
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByEmployeeAsync(int employeeId);

        /// <summary>
        /// جلب سندات صرف مورد معين
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersBySupplierAsync(int supplierId);

        /// <summary>
        /// جلب سندات صرف معمل معين
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByLabAsync(int labId);

        /// <summary>
        /// إنشاء سند صرف جديد
        /// </summary>
        Task<PaymentVoucher> CreateVoucherAsync(PaymentVoucher voucher);

        /// <summary>
        /// تحديث سند صرف
        /// </summary>
        Task<PaymentVoucher> UpdateVoucherAsync(PaymentVoucher voucher);

        /// <summary>
        /// حذف سند صرف
        /// </summary>
        Task<bool> DeleteVoucherAsync(int id);

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في سندات الصرف
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> SearchVouchersAsync(string searchTerm);

        /// <summary>
        /// جلب سندات الصرف حسب التاريخ
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByDateAsync(DateTime date);

        /// <summary>
        /// جلب سندات الصرف في فترة زمنية
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب سندات الصرف حسب نوع المصروف
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByExpenseTypeAsync(string expenseType);

        /// <summary>
        /// جلب سندات الصرف حسب نوع المستفيد
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByBeneficiaryTypeAsync(string beneficiaryType);

        /// <summary>
        /// جلب سندات الصرف حسب الحالة
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetVouchersByStatusAsync(string status);

        #endregion

        #region الترقيم التلقائي

        /// <summary>
        /// الحصول على رقم سند الصرف التالي
        /// </summary>
        Task<string> GetNextVoucherNumberAsync();

        /// <summary>
        /// التحقق من توفر رقم سند صرف
        /// </summary>
        Task<bool> IsVoucherNumberAvailableAsync(string voucherNumber);

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// جلب إحصائيات سندات الصرف
        /// </summary>
        Task<PaymentVoucherStatistics> GetVoucherStatisticsAsync();

        /// <summary>
        /// جلب إجمالي المصروفات في فترة زمنية
        /// </summary>
        Task<decimal> GetTotalExpensesAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إحصائيات المصروفات حسب النوع
        /// </summary>
        Task<Dictionary<string, decimal>> GetExpensesByTypeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إحصائيات المصروفات حسب المستفيد
        /// </summary>
        Task<Dictionary<string, decimal>> GetExpensesByBeneficiaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// جلب إحصائيات المصروفات حسب الشهر
        /// </summary>
        Task<Dictionary<string, decimal>> GetMonthlyExpensesAsync(int year);

        #endregion

        #region الطباعة والتصدير

        /// <summary>
        /// طباعة سند صرف
        /// </summary>
        Task<bool> PrintVoucherAsync(int voucherId);

        /// <summary>
        /// تصدير سند صرف إلى PDF
        /// </summary>
        Task<byte[]> ExportVoucherToPdfAsync(int voucherId);

        /// <summary>
        /// تصدير سند صرف إلى Excel
        /// </summary>
        Task<byte[]> ExportVoucherToExcelAsync(int voucherId);

        #endregion

        #region الموافقة والإدارة

        /// <summary>
        /// الموافقة على سند صرف
        /// </summary>
        Task<bool> ApproveVoucherAsync(int voucherId, string approvedBy, string? notes = null);

        /// <summary>
        /// رفض سند صرف
        /// </summary>
        Task<bool> RejectVoucherAsync(int voucherId, string rejectedBy, string reason);

        /// <summary>
        /// جلب سندات الصرف المعلقة
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetPendingVouchersAsync();

        /// <summary>
        /// جلب سندات الصرف المعتمدة
        /// </summary>
        Task<IEnumerable<PaymentVoucher>> GetApprovedVouchersAsync();

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة بيانات سند الصرف
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateVoucherAsync(PaymentVoucher voucher);

        /// <summary>
        /// التحقق من صلاحية الموافقة على سند صرف
        /// </summary>
        Task<bool> CanApproveVoucherAsync(int voucherId, string approver);

        #endregion
    }

    /// <summary>
    /// نموذج إحصائيات سندات الصرف
    /// </summary>
    public class PaymentVoucherStatistics
    {
        public int TotalVouchers { get; set; }
        public int ApprovedVouchers { get; set; }
        public int PendingVouchers { get; set; }
        public int RejectedVouchers { get; set; }
        public int CancelledVouchers { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal RejectedAmount { get; set; }
        public Dictionary<string, int> VouchersByStatus { get; set; } = new();
        public Dictionary<string, decimal> ExpensesByType { get; set; } = new();
        public Dictionary<string, decimal> ExpensesByBeneficiary { get; set; } = new();
        public Dictionary<string, int> VouchersByMonth { get; set; } = new();
    }
} 