using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models.Reports;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Reports
{
    /// <summary>
    /// ViewModel الرئيسي للتقارير
    /// </summary>
    public class ReportsMainViewModel : BaseViewModel
    {
        private readonly IReportService _reportService;

        /// <summary>
        /// التقارير المحفوظة
        /// </summary>
        public ObservableCollection<ReportBase> SavedReports { get; set; } = new();

        /// <summary>
        /// التقارير السريعة
        /// </summary>
        public ObservableCollection<QuickReport> QuickReports { get; set; } = new();

        /// <summary>
        /// التقرير المحدد
        /// </summary>
        private ReportBase? _selectedReport;
        public ReportBase? SelectedReport
        {
            get => _selectedReport;
            set => SetProperty(ref _selectedReport, value);
        }

        /// <summary>
        /// نوع التقرير المحدد
        /// </summary>
        private ReportType _selectedReportType = ReportType.PatientReport;
        public ReportType SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        private DateTime _startDate = DateTime.Now.AddMonths(-1);
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        private DateTime _endDate = DateTime.Now;
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        /// <summary>
        /// الفلاتر المطبقة
        /// </summary>
        private string? _appliedFilters;
        public string? AppliedFilters
        {
            get => _appliedFilters;
            set => SetProperty(ref _appliedFilters, value);
        }

        // Commands
        /// <summary>
        /// أمر إنشاء تقرير جديد
        /// </summary>
        public ICommand CreateReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تحميل التقارير المحفوظة
        /// </summary>
        public ICommand LoadSavedReportsCommand { get; private set; } = null!;

        /// <summary>
        /// أمر حذف التقرير
        /// </summary>
        public ICommand DeleteReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تصدير التقرير
        /// </summary>
        public ICommand ExportReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر طباعة التقرير
        /// </summary>
        public ICommand PrintReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر تحديث التقرير
        /// </summary>
        public ICommand RefreshReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر فتح التقرير السريع
        /// </summary>
        public ICommand OpenQuickReportCommand { get; private set; } = null!;

        /// <summary>
        /// أمر فتح التقرير المخصص
        /// </summary>
        public ICommand OpenCustomReportCommand { get; private set; } = null!;

        public ReportsMainViewModel(IReportService reportService)
        {
            _reportService = reportService;
            InitializeCommands();
            InitializeQuickReports();
            _ = LoadSavedReportsAsync();
        }

        /// <summary>
        /// تهيئة الأوامر
        /// </summary>
        private void InitializeCommands()
        {
            CreateReportCommand = new RelayCommand(async () => await CreateReportAsync());
            LoadSavedReportsCommand = new RelayCommand(async () => await LoadSavedReportsAsync());
            DeleteReportCommand = new RelayCommand(async () => await DeleteReportAsync(), () => SelectedReport != null);
            ExportReportCommand = new RelayCommand(async () => await ExportReportAsync(), () => SelectedReport != null);
            PrintReportCommand = new RelayCommand(async () => await PrintReportAsync(), () => SelectedReport != null);
            RefreshReportCommand = new RelayCommand(async () => await RefreshReportAsync(), () => SelectedReport != null);
            OpenQuickReportCommand = new RelayCommand<QuickReport>(async (report) => await OpenQuickReportAsync(report));
            OpenCustomReportCommand = new RelayCommand(async () => await OpenCustomReportAsync());
        }

        /// <summary>
        /// تهيئة التقارير السريعة
        /// </summary>
        private void InitializeQuickReports()
        {
            QuickReports.Clear();
            QuickReports.Add(new QuickReport { Title = "تقرير المرضى الشهري", Type = ReportType.PatientReport, Icon = "👥", Description = "إحصائيات المرضى للشهر الحالي" });
            QuickReports.Add(new QuickReport { Title = "تقرير المواعيد اليومي", Type = ReportType.AppointmentReport, Icon = "📅", Description = "مواعيد اليوم وحالة الحضور" });
            QuickReports.Add(new QuickReport { Title = "التقرير المالي الشهري", Type = ReportType.FinancialReport, Icon = "💰", Description = "الإيرادات والمصروفات للشهر الحالي" });
            QuickReports.Add(new QuickReport { Title = "تقرير المخزون", Type = ReportType.InventoryReport, Icon = "📦", Description = "الأصناف منخفضة المخزون وقريبة انتهاء الصلاحية" });
            QuickReports.Add(new QuickReport { Title = "تقرير الأداء", Type = ReportType.PerformanceReport, Icon = "📊", Description = "أداء الأطباء والموظفين" });
        }

        /// <summary>
        /// إنشاء تقرير جديد
        /// </summary>
        public async Task CreateReportAsync()
        {
            await ExecuteAsync(async () =>
            {
                var report = await GenerateReportAsync();
                if (report != null)
                {
                    // حفظ التقرير
                    var reportId = await _reportService.SaveReportAsync(report);
                    report.Id = reportId;

                    // إضافة إلى القائمة
                    SavedReports.Insert(0, report);
                    SelectedReport = report;
                }
            }, "جاري إنشاء التقرير...");
        }

        /// <summary>
        /// تحميل التقارير المحفوظة
        /// </summary>
        public async Task LoadSavedReportsAsync()
        {
            await ExecuteAsync(async () =>
            {
                var reports = await _reportService.GetSavedReportsAsync();
                SavedReports.Clear();
                foreach (var report in reports.OrderByDescending(r => r.CreatedAt))
                {
                    SavedReports.Add(report);
                }
            }, "جاري تحميل التقارير...");
        }

        /// <summary>
        /// حذف التقرير
        /// </summary>
        public async Task DeleteReportAsync()
        {
            if (SelectedReport == null) return;

            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف التقرير '{SelectedReport.Title}'؟",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                await ExecuteAsync(async () =>
                {
                    await _reportService.DeleteReportAsync(SelectedReport.Id);
                    SavedReports.Remove(SelectedReport);
                    SelectedReport = null;
                }, "جاري حذف التقرير...");
            }
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        public async Task ExportReportAsync()
        {
            if (SelectedReport == null) return;

            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf|Excel files (*.xlsx)|*.xlsx",
                DefaultExt = "pdf",
                FileName = $"{SelectedReport.Title}_{DateTime.Now:yyyyMMdd}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                await ExecuteAsync(async () =>
                {
                    var format = saveFileDialog.FileName.EndsWith(".pdf") ? "pdf" : "excel";
                    await _reportService.ExportReportAsync(SelectedReport.Id, format, saveFileDialog.FileName);
                }, "جاري تصدير التقرير...");
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        public async Task PrintReportAsync()
        {
            if (SelectedReport == null) return;

            await ExecuteAsync(async () =>
            {
                var pdfBytes = await _reportService.GenerateReportPdfAsync(SelectedReport);
                // هنا يمكن إضافة منطق الطباعة
                System.Windows.MessageBox.Show("تم إنشاء ملف PDF للتقرير. يمكنك طباعته الآن.", "طباعة التقرير");
            }, "جاري إعداد التقرير للطباعة...");
        }

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        public async Task RefreshReportAsync()
        {
            if (SelectedReport == null) return;

            await ExecuteAsync(async () =>
            {
                var updatedReport = await GenerateReportAsync();
                if (updatedReport != null)
                {
                    updatedReport.Id = SelectedReport.Id;
                    updatedReport.CreatedAt = SelectedReport.CreatedAt;
                    updatedReport.CreatedBy = SelectedReport.CreatedBy;

                    await _reportService.SaveReportAsync(updatedReport);

                    var index = SavedReports.IndexOf(SelectedReport);
                    SavedReports[index] = updatedReport;
                    SelectedReport = updatedReport;
                }
            }, "جاري تحديث التقرير...");
        }

        /// <summary>
        /// فتح التقرير السريع
        /// </summary>
        public async Task OpenQuickReportAsync(QuickReport? quickReport)
        {
            if (quickReport == null) return;

            SelectedReportType = quickReport.Type;
            StartDate = DateTime.Now.AddDays(-30);
            EndDate = DateTime.Now;

            await CreateReportAsync();
        }

        /// <summary>
        /// إنشاء تقرير حسب النوع
        /// </summary>
        public async Task<ReportBase?> GenerateReportAsync()
        {
            if (SelectedReportType == null) return null;

            IsBusy = true;
            try
            {
                switch (SelectedReportType)
                {
                    case ReportType.PatientReport:
                        return await _reportService.GeneratePatientReportAsync();
                    case ReportType.AppointmentReport:
                        return await _reportService.GenerateAppointmentReportAsync();
                    case ReportType.FinancialReport:
                        return await _reportService.GenerateFinancialReportAsync();
                    case ReportType.InventoryReport:
                        return await _reportService.GenerateInventoryReportAsync();
                    case ReportType.PerformanceReport:
                        return await _reportService.GeneratePerformanceReportAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
            return null;
        }

        /// <summary>
        /// فتح التقرير المخصص
        /// </summary>
        public async Task OpenCustomReportAsync()
        {
            await ExecuteAsync(async () =>
            {
                // إنشاء نافذة جديدة لعرض التقرير المخصص
                var customReportWindow = new System.Windows.Window
                {
                    Title = "التقرير المخصص - مركز الدكتور عقلان الكامل",
                    Width = 1200,
                    Height = 800,
                    WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen,
                    ResizeMode = System.Windows.ResizeMode.CanResize,
                    Icon = new System.Windows.Media.Imaging.BitmapImage(new Uri("/Resources/logo.png", UriKind.Relative))
                };

                var customReportView = new Views.Reports.ReportView();
                var customReportViewModel = new ReportViewModel();
                customReportView.DataContext = customReportViewModel;

                customReportWindow.Content = customReportView;
                customReportWindow.Show();
            }, "جاري فتح التقرير المخصص...");
        }

        /// <summary>
        /// تحميل التقارير
        /// </summary>
        public async Task LoadReportsAsync()
        {
            try
            {
                IsLoading = true;

                // تحميل التقارير السريعة
                QuickReports.Clear();
                var quickReports = new List<QuickReport>
                {
                    new QuickReport { Title = "تقرير المرضى", Icon = "👥", Description = "إحصائيات شاملة للمرضى" },
                    new QuickReport { Title = "تقرير الأطباء", Icon = "👨‍⚕️", Description = "إحصائيات الأطباء والأداء" },
                    new QuickReport { Title = "تقرير المواعيد", Icon = "📅", Description = "تقرير المواعيد والجلسات" },
                    new QuickReport { Title = "تقرير الإيرادات", Icon = "💰", Description = "التقارير المالية والإيرادات" }
                };

                foreach (var report in quickReports)
                {
                    QuickReports.Add(report);
                }

                // تحميل التقارير المفصلة
                SavedReports.Clear();

                await Task.Delay(100); // محاكاة تحميل البيانات
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل التقارير: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }

    /// <summary>
    /// التقرير السريع
    /// </summary>
    public class QuickReport
    {
        public string Title { get; set; } = string.Empty;
        public ReportType Type { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}