using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IActivityLogService
    {
        Task<ActivityLog> LogActivityAsync(int userId, string action, string entityType, int? entityId = null, string? description = null, string? oldValues = null, string? newValues = null);
        Task<IEnumerable<ActivityLog>> GetUserActivityLogsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<ActivityLog>> GetEntityActivityLogsAsync(string entityType, int entityId);
        Task<IEnumerable<ActivityLog>> GetAllActivityLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? action = null, string? entityType = null);
        Task<IEnumerable<ActivityLog>> GetLoginLogsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<ActivityLog>> GetFailedLoginAttemptsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<ActivityLog?> GetActivityLogByIdAsync(int id);
        Task<bool> DeleteActivityLogAsync(int id);
        Task<bool> DeleteOldActivityLogsAsync(DateTime cutoffDate);
        Task<int> GetActivityLogsCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<ActivityLog>> GetRecentActivityLogsAsync(int count = 50);
        Task LogLoginAsync(int userId, string ipAddress, string userAgent);
        Task LogLogoutAsync(int userId, string ipAddress);
        Task LogFailedLoginAsync(string username, string ipAddress, string userAgent, string reason);
    }
} 