<Window x:Class="AqlanCenterProApp.Views.Patients.AddEditPatientView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="700"
        Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFontFamily}">

    <Window.Resources>
        <!-- تصميم حقول الإدخال -->
        <Style x:Key="InputLabelStyle"
               TargetType="TextBlock">
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Margin"
                    Value="0,0,0,5"/>
            <Setter Property="Foreground"
                    Value="{StaticResource DarkGrayBrush}"/>
        </Style>

        <Style x:Key="InputTextBoxStyle"
               TargetType="TextBox"
               BasedOn="{StaticResource ModernTextBoxStyle}">
            <Setter Property="Margin"
                    Value="0,0,0,15"/>
            <Setter Property="Height"
                    Value="35"/>
        </Style>

        <Style x:Key="InputComboBoxStyle"
               TargetType="ComboBox">
            <Setter Property="Margin"
                    Value="0,0,0,15"/>
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="FontFamily"
                    Value="{StaticResource ArabicFontFamily}"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <!-- تصميم رسائل الخطأ -->
        <Style x:Key="ErrorTextStyle"
               TargetType="TextBlock">
            <Setter Property="Foreground"
                    Value="{StaticResource DangerBrush}"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="Margin"
                    Value="0,-10,0,10"/>
            <Setter Property="TextWrapping"
                    Value="Wrap"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="0"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly"
                      ScrollViewer.CanContentScroll="False"
                      ScrollViewer.IsDeferredScrollingEnabled="False">
            <TabControl Margin="20">
                <!-- تبويب البيانات الأساسية -->
                <TabItem Header="البيانات الأساسية">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- العمود الأيمن -->
                        <StackPanel Grid.Column="0">
                            <!-- رقم الملف -->
                            <TextBlock Text="رقم الملف *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding FileNumber, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     IsReadOnly="{Binding IsEditMode}"/>
                            <TextBlock Text="{Binding ValidationErrors[FileNumber]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[FileNumber], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- الاسم الأول -->
                            <TextBlock Text="الاسم الأول *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[FirstName]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[FirstName], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- الاسم الأوسط -->
                            <TextBlock Text="الاسم الأوسط"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding MiddleName, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>

                            <!-- الاسم الأخير -->
                            <TextBlock Text="الاسم الأخير *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding LastName, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[LastName]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[LastName], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- الجنس -->
                            <TextBlock Text="الجنس *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <ComboBox ItemsSource="{Binding Genders}"
                                      SelectedValue="{Binding Gender}"
                                      Style="{StaticResource InputComboBoxStyle}"/>

                            <!-- تاريخ الميلاد -->
                            <TextBlock Text="تاريخ الميلاد *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <DatePicker SelectedDate="{Binding DateOfBirth}"
                                        FontFamily="{StaticResource ArabicFontFamily}"
                                        FontSize="14"
                                        Margin="0,0,0,15"
                                        Height="35"/>
                            <TextBlock Text="{Binding ValidationErrors[DateOfBirth]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[DateOfBirth], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- العمر (للقراءة فقط) -->
                            <TextBlock Text="العمر"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Age, StringFormat='{}{0} سنة'}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     IsReadOnly="True"
                                     Background="{StaticResource LightGrayBrush}"/>
                        </StackPanel>

                        <!-- العمود الأيسر -->
                        <StackPanel Grid.Column="2">
                            <!-- رقم الهاتف الأساسي -->
                            <TextBlock Text="رقم الهاتف الأساسي *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[Phone]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[Phone], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- رقم الهاتف المحمول -->
                            <TextBlock Text="رقم الهاتف المحمول"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Mobile, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[Mobile]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[Mobile], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- العنوان -->
                            <TextBlock Text="العنوان"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"/>

                            <!-- تصنيف المريض -->
                            <TextBlock Text="تصنيف المريض *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ComboBox Grid.Column="0"
                                          ItemsSource="{Binding Categories}"
                                          SelectedValue="{Binding PatientCategory}"
                                          Style="{StaticResource InputComboBoxStyle}"
                                          IsEditable="True"/>
                                <Button Grid.Column="1"
                                        Content="➕"
                                        Command="{Binding AddCategoryCommand}"
                                        Width="35"
                                        Height="35"
                                        Margin="5,0,0,15"
                                        ToolTip="إضافة تصنيف جديد"/>
                            </Grid>

                            <!-- حالة الملف -->
                            <TextBlock Text="حالة الملف *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <ComboBox ItemsSource="{Binding FileStatuses}"
                                      SelectedValue="{Binding FileStatus}"
                                      Style="{StaticResource InputComboBoxStyle}"/>

                            <!-- مبلغ المعاينة -->
                            <TextBlock Text="مبلغ المعاينة *"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding ConsultationFee, StringFormat=N2, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[ConsultationFee]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[ConsultationFee], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- الصورة الشخصية -->
                            <TextBlock Text="الصورة الشخصية"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- عرض الصورة -->
                                <Border Grid.Row="0"
                                        Width="120"
                                        Height="120"
                                        BorderBrush="{StaticResource BorderBrush}"
                                        BorderThickness="2"
                                        CornerRadius="5"
                                        Margin="0,0,0,10">
                                    <Image Source="{Binding PatientImage}"
                                           Stretch="UniformToFill"
                                           RenderOptions.BitmapScalingMode="HighQuality"/>
                                </Border>

                                <!-- أزرار الصورة -->
                                <StackPanel Grid.Row="1"
                                            Orientation="Horizontal">
                                    <Button Content="اختيار صورة"
                                            Command="{Binding SelectImageCommand}"
                                            Style="{StaticResource SecondaryButtonStyle}"
                                            Margin="0,0,5,15"/>
                                    <Button Content="إزالة"
                                            Command="{Binding RemoveImageCommand}"
                                            Style="{StaticResource DangerButtonStyle}"
                                            Margin="0,0,0,15"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <!-- تبويب البيانات الطبية -->
                <TabItem Header="البيانات الطبية">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- العمود الأيمن -->
                        <StackPanel Grid.Column="0">
                            <!-- التاريخ الطبي -->
                            <TextBlock Text="التاريخ الطبي"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding MedicalHistory, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Height="120"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>

                            <!-- الحساسية -->
                            <TextBlock Text="الحساسية"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Allergies, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Height="80"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"/>
                        </StackPanel>

                        <!-- العمود الأيسر -->
                        <StackPanel Grid.Column="2">
                            <!-- جهة الاتصال الطارئة -->
                            <TextBlock Text="جهة الاتصال الطارئة"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding EmergencyContact, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>

                            <!-- هاتف الطوارئ -->
                            <TextBlock Text="هاتف الطوارئ"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding EmergencyPhone, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                            <TextBlock Text="{Binding ValidationErrors[EmergencyPhone]}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       Visibility="{Binding ValidationErrors[EmergencyPhone], Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- ملاحظات -->
                            <TextBlock Text="ملاحظات"
                                       Style="{StaticResource InputLabelStyle}"/>
                            <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Height="120"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- شريط الأزرار -->
        <Border Grid.Row="1"
                Background="{StaticResource LightGrayBrush}"
                Padding="20,15">
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Left"
                        FlowDirection="LeftToRight">

                <Button Content="💾 حفظ"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        IsDefault="True"
                        Margin="0,0,10,0"/>

                <Button Content="🖨️ طباعة سند المعاينة"
                        Command="{Binding PrintConsultationReceiptCommand}"
                        Style="{StaticResource SecondaryButtonStyle}"
                        Margin="0,0,10,0"
                        Visibility="{Binding IsEditMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                <Button Content="❌ إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource SecondaryButtonStyle}"
                        IsCancel="True"/>
            </StackPanel>
        </Border>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="{StaticResource WhiteBrush}"
                    Padding="30,20"
                    CornerRadius="10">
                <StackPanel>
                    <ProgressBar IsIndeterminate="True"
                                 Width="200"
                                 Height="10"
                                 Margin="0,0,0,15"/>
                    <TextBlock Text="{Binding LoadingMessage}"
                               FontFamily="{StaticResource ArabicFontFamily}"
                               FontSize="14"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
