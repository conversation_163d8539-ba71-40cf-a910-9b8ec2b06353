using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Models;
using System.IO;

namespace AqlanCenterProApp.Data
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeAsync(AqlanCenterDbContext context)
        {
            try
            {
                Console.WriteLine("🔍 بدء تهيئة قاعدة البيانات...");

                // التحقق من صحة السياق
                if (context == null)
                {
                    throw new ArgumentNullException(nameof(context), "سياق قاعدة البيانات فارغ");
                }

                Console.WriteLine("✅ سياق قاعدة البيانات صحيح");

                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataDirectory))
                {
                    Directory.CreateDirectory(dataDirectory);
                }
                Console.WriteLine($"📁 مجلد البيانات: {dataDirectory}");

                // Ensure database is created
                Console.WriteLine("🔄 التأكد من إنشاء قاعدة البيانات...");
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("✅ تم التأكد من إنشاء قاعدة البيانات");

                // التحقق من وجود DbSet للمستخدمين
                if (context.Users == null)
                {
                    throw new InvalidOperationException("DbSet للمستخدمين غير مهيأ");
                }
                Console.WriteLine("✅ DbSet للمستخدمين مهيأ بشكل صحيح");

                // Check if database needs seeding
                Console.WriteLine("🔍 فحص الحاجة لإضافة البيانات الأساسية...");
                var userCount = await context.Users.CountAsync();
                Console.WriteLine($"📊 عدد المستخدمين الحالي: {userCount}");

                if (userCount == 0)
                {
                    Console.WriteLine("📝 إضافة البيانات الأساسية...");
                    await SeedDataAsync(context);
                    Console.WriteLine("✅ تم إضافة البيانات الأساسية");
                }
                else
                {
                    Console.WriteLine("ℹ️ البيانات الأساسية موجودة مسبقاً");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"🔍 الخطأ الداخلي: {ex.InnerException.Message}");
                }

                // Log error
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        private static async Task SeedDataAsync(AqlanCenterDbContext context)
        {
            // Seed essential data first
            await SeedRolesAsync(context);
            await SeedUsersAsync(context);

            // إضافة البيانات الأساسية
            await SeedProsthesisTypesAsync(context);
            await SeedSampleEmployeesAsync(context);

            // إضافة بيانات تجريبية للداشبورد
            await SeedSampleDoctorsAsync(context);
            await SeedSamplePatientsAsync(context);
            await SeedSampleAppointmentsAsync(context);
            await SeedSampleSessionsAsync(context);
            await SeedSamplePaymentsAsync(context);
            await SeedSampleInventoryAsync(context);

            await context.SaveChangesAsync();
        }

        private static async Task SeedRolesAsync(AqlanCenterDbContext context)
        {
            var adminRole = new Role
            {
                RoleName = "مدير النظام",
                Description = "صلاحيات كاملة لإدارة النظام",
                IsActive = true,
                CanViewPatients = true,
                CanAddPatients = true,
                CanEditPatients = true,
                CanDeletePatients = true,
                CanViewDoctors = true,
                CanAddDoctors = true,
                CanEditDoctors = true,
                CanDeleteDoctors = true,
                CanViewEmployees = true,
                CanAddEmployees = true,
                CanEditEmployees = true,
                CanDeleteEmployees = true,
                CanViewAppointments = true,
                CanAddAppointments = true,
                CanEditAppointments = true,
                CanDeleteAppointments = true,
                CanViewInvoices = true,
                CanAddInvoices = true,
                CanEditInvoices = true,
                CanDeleteInvoices = true,
                CanViewPayments = true,
                CanAddPayments = true,
                CanEditPayments = true,
                CanDeletePayments = true,
                CanViewReports = true,
                CanExportReports = true,
                CanViewSettings = true,
                CanEditSettings = true,
                CanBackupRestore = true,
                CreatedAt = DateTime.Now
            };

            var doctorRole = new Role
            {
                RoleName = "طبيب",
                Description = "صلاحيات الطبيب للوصول للمرضى والجلسات",
                IsActive = true,
                CanViewPatients = true,
                CanAddPatients = true,
                CanEditPatients = true,
                CanViewAppointments = true,
                CanAddAppointments = true,
                CanEditAppointments = true,
                CanViewInvoices = true,
                CanViewPayments = true,
                CreatedAt = DateTime.Now
            };

            var receptionRole = new Role
            {
                RoleName = "موظف استقبال",
                Description = "صلاحيات موظف الاستقبال",
                IsActive = true,
                CanViewPatients = true,
                CanAddPatients = true,
                CanEditPatients = true,
                CanViewAppointments = true,
                CanAddAppointments = true,
                CanEditAppointments = true,
                CanViewPayments = true,
                CanAddPayments = true,
                CreatedAt = DateTime.Now
            };

            var roles = new[] { adminRole, doctorRole, receptionRole };
            await context.Roles.AddRangeAsync(roles);
        }

        private static async Task SeedUsersAsync(AqlanCenterDbContext context)
        {
            var users = new[]
            {
                new User
                {
                    Username = "admin",
                    PasswordHash = "admin123", // في التطبيق الحقيقي يجب تشفير كلمة المرور
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Phone = "777-000000",
                    RoleId = 1, // مدير النظام
                    IsActive = true,
                    LastLoginDate = DateTime.Now,
                    CreatedAt = DateTime.Now
                },
                new User
                {
                    Username = "doctor",
                    PasswordHash = "doctor123",
                    FullName = "د. عقلان محمد الحكيمي",
                    Email = "<EMAIL>",
                    Phone = "777-111111",
                    RoleId = 2, // طبيب
                    IsActive = true,
                    LastLoginDate = DateTime.Now,
                    CreatedAt = DateTime.Now
                },
                new User
                {
                    Username = "reception",
                    PasswordHash = "reception123",
                    FullName = "موظف الاستقبال",
                    Email = "<EMAIL>",
                    Phone = "777-222222",
                    RoleId = 3, // موظف استقبال
                    IsActive = true,
                    LastLoginDate = DateTime.Now,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Users.AddRangeAsync(users);
        }

        private static async Task SeedProsthesisTypesAsync(AqlanCenterDbContext context)
        {
            var prosthesisTypes = new[]
            {
                new ProsthesisType
                {
                    TypeName = "تقويم أسنان معدني",
                    Description = "تقويم أسنان تقليدي معدني",
                    DefaultCost = 150000,
                    DefaultDeliveryDays = 1,
                    Category = "تقويم",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new ProsthesisType
                {
                    TypeName = "تقويم أسنان شفاف",
                    Description = "تقويم أسنان شفاف (إنفزلاين)",
                    DefaultCost = 300000,
                    DefaultDeliveryDays = 7,
                    Category = "تقويم",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new ProsthesisType
                {
                    TypeName = "زراعة سن واحد",
                    Description = "زراعة سن واحد مع التاج",
                    DefaultCost = 200000,
                    DefaultDeliveryDays = 14,
                    Category = "زراعة",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new ProsthesisType
                {
                    TypeName = "تركيبة ثابتة",
                    Description = "تركيبة أسنان ثابتة (جسر)",
                    DefaultCost = 100000,
                    DefaultDeliveryDays = 7,
                    Category = "تركيبات",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new ProsthesisType
                {
                    TypeName = "تركيبة متحركة",
                    Description = "طقم أسنان متحرك",
                    DefaultCost = 80000,
                    DefaultDeliveryDays = 10,
                    Category = "تركيبات",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new ProsthesisType
                {
                    TypeName = "تبييض أسنان",
                    Description = "جلسة تبييض أسنان",
                    DefaultCost = 50000,
                    DefaultDeliveryDays = 1,
                    Category = "تجميل",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.ProsthesisTypes.AddRangeAsync(prosthesisTypes);
        }

        private static async Task SeedSampleEmployeesAsync(AqlanCenterDbContext context)
        {
            var employees = new[]
            {
                new Employee
                {
                    EmployeeNumber = "EMP001",
                    FirstName = "أحمد",
                    LastName = "محمد الحكيمي",
                    NationalId = "1234567890",
                    PhoneNumber = "777-123456",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "الإدارة",
                    Position = "مدير",
                    HireDate = new DateTime(2020, 1, 15),
                    BasicSalary = 500000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP002",
                    FirstName = "فاطمة",
                    LastName = "علي أحمد",
                    NationalId = "1234567891",
                    PhoneNumber = "777-123457",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "التمريض",
                    Position = "ممرض",
                    HireDate = new DateTime(2020, 3, 10),
                    BasicSalary = 300000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP003",
                    FirstName = "محمد",
                    LastName = "عبدالله سالم",
                    NationalId = "1234567892",
                    PhoneNumber = "777-123458",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "المحاسبة",
                    Position = "محاسب",
                    HireDate = new DateTime(2020, 2, 20),
                    BasicSalary = 350000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP004",
                    FirstName = "سارة",
                    LastName = "يوسف محمد",
                    NationalId = "1234567893",
                    PhoneNumber = "777-123459",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "الاستقبال",
                    Position = "موظف استقبال",
                    HireDate = new DateTime(2020, 4, 5),
                    BasicSalary = 250000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP005",
                    FirstName = "علي",
                    LastName = "حسن عبدالله",
                    NationalId = "1234567894",
                    PhoneNumber = "777-123460",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "الصيانة",
                    Position = "فني صيانة",
                    HireDate = new DateTime(2020, 5, 12),
                    BasicSalary = 200000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP006",
                    FirstName = "خديجة",
                    LastName = "أحمد علي",
                    NationalId = "1234567895",
                    PhoneNumber = "777-123461",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "التمريض",
                    Position = "ممرض",
                    HireDate = new DateTime(2020, 6, 8),
                    BasicSalary = 300000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP007",
                    FirstName = "عبدالله",
                    LastName = "محمد سالم",
                    NationalId = "1234567896",
                    PhoneNumber = "777-123462",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "الأمن",
                    Position = "حارس",
                    HireDate = new DateTime(2020, 7, 15),
                    BasicSalary = 180000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                },
                new Employee
                {
                    EmployeeNumber = "EMP008",
                    FirstName = "مريم",
                    LastName = "علي أحمد",
                    NationalId = "1234567897",
                    PhoneNumber = "777-123463",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    Department = "النظافة",
                    Position = "عامل نظافة",
                    HireDate = new DateTime(2020, 8, 20),
                    BasicSalary = 150000,
                    Status = "نشط",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Employees.AddRangeAsync(employees);
        }

        private static async Task SeedSampleDoctorsAsync(AqlanCenterDbContext context)
        {
            var doctors = new[]
            {
                new Doctor
                {
                    DoctorId = 1,
                    FullName = "د. عقلان محمد الحكيمي",
                    Specialization = "طب الأسنان العام",
                    Phone = "777-111111",
                    Mobile = "770-111111",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    ContractType = "دائم",
                    JoinDate = new DateTime(2020, 1, 1),
                    FixedSalary = 800000,
                    SalaryCurrency = "ر.ي",
                    Status = "نشط",
                    IsActive = true,
                    Qualifications = "بكالوريوس طب الأسنان - جامعة صنعاء",
                    IsAvailableForAppointments = true,
                    Rating = 4.8m,
                    RatingCount = 25,
                    TotalEarnings = 2500000,
                    CreatedAt = DateTime.Now
                },
                new Doctor
                {
                    DoctorId = 2,
                    FullName = "د. سارة أحمد علي",
                    Specialization = "تقويم الأسنان",
                    Phone = "777-222222",
                    Mobile = "770-222222",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    ContractType = "دائم",
                    JoinDate = new DateTime(2020, 6, 1),
                    FixedSalary = 700000,
                    SalaryCurrency = "ر.ي",
                    Status = "نشط",
                    IsActive = true,
                    Qualifications = "ماجستير تقويم الأسنان",
                    IsAvailableForAppointments = true,
                    Rating = 4.9m,
                    RatingCount = 18,
                    TotalEarnings = 1800000,
                    CreatedAt = DateTime.Now
                },
                new Doctor
                {
                    DoctorId = 3,
                    FullName = "د. محمد عبدالله سالم",
                    Specialization = "جراحة الفم والأسنان",
                    Phone = "777-333333",
                    Mobile = "770-333333",
                    Email = "<EMAIL>",
                    Address = "صنعاء، اليمن",
                    ContractType = "جزئي",
                    JoinDate = new DateTime(2021, 3, 1),
                    CommissionPercentage = 40,
                    SalaryCurrency = "ر.ي",
                    Status = "نشط",
                    IsActive = true,
                    Qualifications = "دكتوراه جراحة الفم والأسنان",
                    IsAvailableForAppointments = true,
                    Rating = 4.7m,
                    RatingCount = 12,
                    TotalEarnings = 1200000,
                    CreatedAt = DateTime.Now
                }
            };

            await context.Doctors.AddRangeAsync(doctors);
        }

        private static async Task SeedSamplePatientsAsync(AqlanCenterDbContext context)
        {
            var patients = new[]
            {
                new Patient
                {
                    FileNumber = 8501,
                    FullName = "أحمد محمد علي الحكيمي",
                    Gender = "ذكر",
                    DateOfBirth = new DateTime(1990, 5, 15),
                    Phone = "777-444444",
                    Mobile = "770-444444",
                    Email = "<EMAIL>",
                    Address = "شارع الزبيري، صنعاء",
                    PatientCategory = "جديد",
                    RegistrationDate = DateTime.Now.AddDays(-30),
                    OpeningBalance = 0,
                    CurrentBalance = -50000,
                    FileStatus = "نشط",
                    ConsultationFee = 5000,
                    MedicalHistory = "لا يوجد تاريخ مرضي مهم",
                    CreatedAt = DateTime.Now.AddDays(-30)
                },
                new Patient
                {
                    FileNumber = 8502,
                    FullName = "فاطمة علي أحمد الشامي",
                    Gender = "أنثى",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    Phone = "777-555555",
                    Mobile = "770-555555",
                    Email = "<EMAIL>",
                    Address = "شارع الستين، صنعاء",
                    PatientCategory = "متابع",
                    RegistrationDate = DateTime.Now.AddDays(-45),
                    OpeningBalance = 0,
                    CurrentBalance = -120000,
                    FileStatus = "نشط",
                    ConsultationFee = 5000,
                    MedicalHistory = "حساسية من البنسلين",
                    Allergies = "البنسلين",
                    CreatedAt = DateTime.Now.AddDays(-45)
                },
                new Patient
                {
                    FileNumber = 8503,
                    FullName = "محمد سالم عبدالله الأهدل",
                    Gender = "ذكر",
                    DateOfBirth = new DateTime(1995, 12, 10),
                    Phone = "777-666666",
                    Mobile = "770-666666",
                    Email = "<EMAIL>",
                    Address = "شارع الحصبة، صنعاء",
                    PatientCategory = "VIP",
                    RegistrationDate = DateTime.Now.AddDays(-60),
                    OpeningBalance = 0,
                    CurrentBalance = -200000,
                    FileStatus = "نشط",
                    ConsultationFee = 10000,
                    MedicalHistory = "مريض سكري",
                    CreatedAt = DateTime.Now.AddDays(-60)
                }
            };

            await context.Patients.AddRangeAsync(patients);
        }

        private static async Task SeedSampleAppointmentsAsync(AqlanCenterDbContext context)
        {
            var appointments = new[]
            {
                new Appointment
                {
                    PatientId = 1,
                    DoctorId = 1,
                    AppointmentDate = DateTime.Today,
                    AppointmentTime = TimeSpan.FromHours(10),
                    ServiceType = "فحص عام",
                    AppointmentType = "فحص",
                    Status = "مؤكد",
                    Notes = "فحص دوري",
                    CreatedAt = DateTime.Now.AddDays(-2)
                },
                new Appointment
                {
                    PatientId = 2,
                    DoctorId = 2,
                    AppointmentDate = DateTime.Today,
                    AppointmentTime = TimeSpan.FromHours(14),
                    ServiceType = "تقويم أسنان",
                    AppointmentType = "علاج",
                    Status = "مؤكد",
                    Notes = "متابعة تقويم",
                    CreatedAt = DateTime.Now.AddDays(-1)
                },
                new Appointment
                {
                    PatientId = 3,
                    DoctorId = 3,
                    AppointmentDate = DateTime.Today.AddDays(1),
                    AppointmentTime = TimeSpan.FromHours(9),
                    ServiceType = "خلع ضرس",
                    AppointmentType = "جراحة",
                    Status = "مؤكد",
                    Notes = "خلع ضرس العقل",
                    CreatedAt = DateTime.Now
                }
            };

            await context.Appointments.AddRangeAsync(appointments);
        }

        private static async Task SeedSampleSessionsAsync(AqlanCenterDbContext context)
        {
            var sessions = new[]
            {
                new Session
                {
                    PatientId = 1,
                    DoctorId = 1,
                    SessionDate = DateTime.Now.AddDays(-5),
                    TreatmentType = "تنظيف أسنان",
                    TreatmentDescription = "تنظيف شامل للأسنان وإزالة الجير",
                    Amount = 25000,
                    Status = "مكتملة",
                    Notes = "تم التنظيف بنجاح",
                    CreatedAt = DateTime.Now.AddDays(-5)
                },
                new Session
                {
                    PatientId = 2,
                    DoctorId = 2,
                    SessionDate = DateTime.Now.AddDays(-3),
                    TreatmentType = "تركيب تقويم",
                    TreatmentDescription = "تركيب تقويم أسنان معدني",
                    Amount = 150000,
                    Status = "مكتملة",
                    Notes = "تم تركيب التقويم بنجاح",
                    CreatedAt = DateTime.Now.AddDays(-3)
                },
                new Session
                {
                    PatientId = 3,
                    DoctorId = 1,
                    SessionDate = DateTime.Now.AddDays(-1),
                    TreatmentType = "حشو أسنان",
                    TreatmentDescription = "حشو ضرس بالكمبوزيت",
                    Amount = 35000,
                    Status = "مكتملة",
                    Notes = "تم الحشو بنجاح",
                    CreatedAt = DateTime.Now.AddDays(-1)
                }
            };

            await context.Sessions.AddRangeAsync(sessions);
        }

        private static async Task SeedSamplePaymentsAsync(AqlanCenterDbContext context)
        {
            var payments = new[]
            {
                new Payment
                {
                    PatientId = 1,
                    Amount = 25000,

                    PaymentMethod = "نقدي",
                    PaymentDate = DateTime.Now.AddDays(-5),
                    PaymentType = "دفع",
                    Status = "مكتمل",
                    PaymentNotes = "دفع تنظيف الأسنان",
                    CreatedAt = DateTime.Now.AddDays(-5)
                },
                new Payment
                {
                    PatientId = 2,
                    Amount = 50000,
                    PaymentMethod = "نقدي",
                    PaymentDate = DateTime.Now.AddDays(-3),
                    PaymentType = "دفع",
                    Status = "مكتمل",
                    PaymentNotes = "دفعة أولى للتقويم",
                    CreatedAt = DateTime.Now.AddDays(-3)
                },
                new Payment
                {
                    PatientId = 3,
                    Amount = 35000,
                    PaymentMethod = "بطاقة ائتمان",
                    PaymentDate = DateTime.Now.AddDays(-1),
                    PaymentType = "دفع",
                    Status = "مكتمل",
                    PaymentNotes = "دفع حشو الأسنان",
                    CreatedAt = DateTime.Now.AddDays(-1)
                }
            };

            await context.Payments.AddRangeAsync(payments);
        }

        private static async Task SeedSampleInventoryAsync(AqlanCenterDbContext context)
        {
            var inventoryItems = new[]
            {
                new InventoryItem
                {
                    Name = "أدوات تنظيف الأسنان",
                    Category = "أدوات طبية",
                    CurrentQuantity = 50,
                    MinimumQuantity = 10,
                    MaximumQuantity = 100,
                    LastPurchasePrice = 5000,
                    ExpiryDate = DateTime.Now.AddMonths(12),
                    CreatedAt = DateTime.Now.AddDays(-30)
                },
                new InventoryItem
                {
                    Name = "مواد حشو الأسنان",
                    Category = "مواد طبية",
                    CurrentQuantity = 5,
                    MinimumQuantity = 10,
                    MaximumQuantity = 50,
                    LastPurchasePrice = 15000,
                    ExpiryDate = DateTime.Now.AddMonths(6),
                    CreatedAt = DateTime.Now.AddDays(-20)
                },
                new InventoryItem
                {
                    Name = "أقواس التقويم",
                    Category = "تقويم الأسنان",
                    CurrentQuantity = 0,
                    MinimumQuantity = 5,
                    MaximumQuantity = 25,
                    LastPurchasePrice = 25000,
                    ExpiryDate = DateTime.Now.AddMonths(24),
                    CreatedAt = DateTime.Now.AddDays(-10)
                }
            };

            await context.InventoryItems.AddRangeAsync(inventoryItems);
        }

        /// <summary>
        /// إعادة تعبئة قاعدة البيانات بالبيانات التجريبية لاختبار الداشبورد
        /// </summary>
        public static async Task ReseedDashboardDataAsync(AqlanCenterDbContext context)
        {
            try
            {
                Console.WriteLine("🔄 بدء إعادة تعبئة البيانات التجريبية للداشبورد...");

                // حذف البيانات الموجودة (عدا المستخدمين والأدوار)
                await context.Payments.ExecuteDeleteAsync();
                await context.Sessions.ExecuteDeleteAsync();
                await context.Appointments.ExecuteDeleteAsync();
                await context.InventoryItems.ExecuteDeleteAsync();
                await context.Patients.ExecuteDeleteAsync();
                await context.Doctors.ExecuteDeleteAsync();

                Console.WriteLine("🗑️ تم حذف البيانات القديمة");

                // إضافة البيانات التجريبية الجديدة
                await SeedSampleDoctorsAsync(context);
                await SeedSamplePatientsAsync(context);
                await SeedSampleAppointmentsAsync(context);
                await SeedSampleSessionsAsync(context);
                await SeedSamplePaymentsAsync(context);
                await SeedSampleInventoryAsync(context);

                await context.SaveChangesAsync();
                Console.WriteLine("✅ تم إضافة البيانات التجريبية بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إعادة تعبئة البيانات: {ex.Message}");
                throw;
            }
        }
    }
}
