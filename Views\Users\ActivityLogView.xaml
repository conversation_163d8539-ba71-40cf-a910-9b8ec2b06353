<UserControl x:Class="AqlanCenterProApp.Views.Users.ActivityLogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             Background="#F5F5F5">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,10">
            <TextBlock Text="سجل النشاطات" FontSize="24" FontWeight="Bold" Foreground="#333"/>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <ComboBox ItemsSource="{Binding Users}" SelectedItem="{Binding SelectedUser}" DisplayMemberPath="FullName" Width="180" Margin="5" ToolTip="المستخدم"/>
                <ComboBox ItemsSource="{Binding Actions}" SelectedItem="{Binding SelectedAction}" Width="120" Margin="5" ToolTip="العملية"/>
                <ComboBox ItemsSource="{Binding EntityTypes}" SelectedItem="{Binding SelectedEntityType}" Width="120" Margin="5" ToolTip="نوع الكيان"/>
                <DatePicker SelectedDate="{Binding FromDate}" Width="120" Margin="5" ToolTip="من تاريخ"/>
                <DatePicker SelectedDate="{Binding ToDate}" Width="120" Margin="5" ToolTip="إلى تاريخ"/>
                <TextBox Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}" Width="200" Margin="5" ToolTip="بحث في التفاصيل"/>
                <Button Content="بحث" Command="{Binding SearchCommand}" Margin="5"/>
                <Button Content="مسح" Command="{Binding ClearFiltersCommand}" Margin="5"/>
            </StackPanel>
        </Border>

        <!-- DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="10">
            <DataGrid ItemsSource="{Binding ActivityLogs}" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionUnit="FullRow">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding ActionDate, StringFormat='dd/MM/yyyy HH:mm'}" Width="150"/>
                    <DataGridTextColumn Header="المستخدم" Binding="{Binding User.FullName}" Width="150"/>
                    <DataGridTextColumn Header="العملية" Binding="{Binding Action}" Width="100"/>
                    <DataGridTextColumn Header="الكيان" Binding="{Binding EntityType}" Width="100"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                    <DataGridTextColumn Header="IP" Binding="{Binding IpAddress}" Width="100"/>
                    <DataGridTextColumn Header="المتصفح/الجهاز" Binding="{Binding UserAgent}" Width="150"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</UserControl> 