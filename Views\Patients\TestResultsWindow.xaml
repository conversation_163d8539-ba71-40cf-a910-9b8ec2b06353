<Window x:Class="AqlanCenterProApp.Views.Patients.TestResultsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نتائج الفحوصات"
        Width="1200"
        Height="750"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F6FA">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="نتائج الفحوصات والتحاليل"
                   FontSize="28"
                   FontWeight="Bold"
                   Foreground="#3498DB"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- شريط البحث والفلاتر -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <TextBox Grid.Column="0"
                     x:Name="SearchTextBox"
                     Height="35"
                     FontSize="14"
                     VerticalContentAlignment="Center"
                     Margin="0,0,10,0"/>

            <!-- فلتر المريض -->
            <ComboBox Grid.Column="1"
                      x:Name="PatientFilterComboBox"
                      Width="180"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع المرضى"/>
            </ComboBox>

            <!-- فلتر نوع الفحص -->
            <ComboBox Grid.Column="2"
                      x:Name="TestTypeFilterComboBox"
                      Width="150"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع الفحوصات"/>
                <ComboBoxItem Content="أشعة سينية"/>
                <ComboBoxItem Content="أشعة بانوراما"/>
                <ComboBoxItem Content="فحص دم"/>
                <ComboBoxItem Content="مزرعة بكتيريا"/>
                <ComboBoxItem Content="فحص لعاب"/>
            </ComboBox>

            <!-- فلتر الحالة -->
            <ComboBox Grid.Column="3"
                      x:Name="StatusFilterComboBox"
                      Width="120"
                      Height="35"
                      FontSize="14"
                      Margin="0,0,10,0">
                <ComboBoxItem Content="جميع الحالات"/>
                <ComboBoxItem Content="مكتمل"/>
                <ComboBoxItem Content="قيد الانتظار"/>
                <ComboBoxItem Content="مرفوض"/>
            </ComboBox>

            <!-- زر البحث -->
            <Button Grid.Column="4"
                    Content="🔍 بحث"
                    Width="100"
                    Height="35"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- قائمة نتائج الفحوصات -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <DataGrid x:Name="TestResultsDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      HeadersVisibility="Column"
                      CanUserAddRows="False"
                      RowHeight="40"
                      EnableRowVirtualization="True">

                <DataGrid.Columns>
                    <!-- رقم الفحص -->
                    <DataGridTextColumn Header="رقم الفحص" 
                                        Binding="{Binding Id}" 
                                        Width="80"/>

                    <!-- اسم المريض -->
                    <DataGridTextColumn Header="اسم المريض" 
                                        Binding="{Binding PatientName}" 
                                        Width="180"/>

                    <!-- نوع الفحص -->
                    <DataGridTextColumn Header="نوع الفحص" 
                                        Binding="{Binding TestType}" 
                                        Width="150"/>

                    <!-- تاريخ الفحص -->
                    <DataGridTextColumn Header="تاريخ الفحص" 
                                        Binding="{Binding TestDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>

                    <!-- تاريخ النتيجة -->
                    <DataGridTextColumn Header="تاريخ النتيجة" 
                                        Binding="{Binding ResultDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>

                    <!-- النتيجة -->
                    <DataGridTextColumn Header="النتيجة" 
                                        Binding="{Binding Result}" 
                                        Width="250"/>

                    <!-- الطبيب المطلوب -->
                    <DataGridTextColumn Header="الطبيب المطلوب" 
                                        Binding="{Binding RequestingDoctor}" 
                                        Width="150"/>

                    <!-- المختبر -->
                    <DataGridTextColumn Header="المختبر" 
                                        Binding="{Binding Laboratory}" 
                                        Width="120"/>

                    <!-- الحالة -->
                    <DataGridTextColumn Header="الحالة" 
                                        Binding="{Binding Status}" 
                                        Width="100"/>

                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="👁️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="عرض التفاصيل"
                                            Click="ViewDetailsButton_Click"/>
                                    <Button Content="🖨️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="طباعة"
                                            Click="PrintButton_Click"/>
                                    <Button Content="📎" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="المرفقات"
                                            Click="AttachmentsButton_Click"/>
                                    <Button Content="✏️" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="2"
                                            ToolTip="تعديل"
                                            Click="EditButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <!-- قائمة السياق -->
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="👁️ عرض التفاصيل" Click="ViewDetailsMenuItem_Click"/>
                        <MenuItem Header="🖨️ طباعة النتيجة" Click="PrintMenuItem_Click"/>
                        <MenuItem Header="📎 عرض المرفقات" Click="AttachmentsMenuItem_Click"/>
                        <MenuItem Header="✏️ تعديل النتيجة" Click="EditMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="📋 نسخ المعلومات" Click="CopyMenuItem_Click"/>
                        <MenuItem Header="📤 تصدير النتيجة" Click="ExportMenuItem_Click"/>
                        <Separator/>
                        <MenuItem Header="🗑️ حذف النتيجة" Click="DeleteMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>
        </ScrollViewer>

        <!-- الأزرار السفلية -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="AddTestResultButton"
                    Content="➕ إضافة نتيجة جديدة"
                    Width="150"
                    Height="40"
                    Background="#27AE60"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="AddTestResultButton_Click"/>
            
            <Button x:Name="RefreshButton"
                    Content="🔄 تحديث"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="RefreshButton_Click"/>
            
            <Button x:Name="ExportAllButton"
                    Content="📤 تصدير الكل"
                    Width="120"
                    Height="40"
                    Background="#F39C12"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="ExportAllButton_Click"/>
            
            <Button x:Name="StatisticsButton"
                    Content="📊 إحصائيات"
                    Width="120"
                    Height="40"
                    Background="#9B59B6"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="StatisticsButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="❌ إغلاق"
                    Width="120"
                    Height="40"
                    Background="#E74C3C"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
