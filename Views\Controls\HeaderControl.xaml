<UserControl x:Class="AqlanCenterProApp.Views.Controls.HeaderControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="90"
             d:DesignWidth="1200">

    <Border BorderBrush="#E0E0E0"
            BorderThickness="0,0,0,2"
            Padding="20,10">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0"
                                 EndPoint="1,0">
                <GradientStop Color="White"
                              Offset="0"/>
                <GradientStop Color="#F8F9FA"
                              Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Grid FlowDirection="RightToLeft">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <!-- شعار -->
                <ColumnDefinition Width="2*"/>
                <!-- اسم المركز والعبارة -->
                <ColumnDefinition Width="Auto"/>
                <!-- الأيقونات واسم المستخدم -->
            </Grid.ColumnDefinitions>

            <!-- شعار العيادة -->
            <Image Source="/Resources/logo.png"
                   Height="56"
                   Grid.Column="0"
                   VerticalAlignment="Center"
                   Margin="0,0,16,0"/>

            <!-- اسم المركز والعبارة التسويقية -->
            <StackPanel Grid.Column="1"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center">
                <TextBlock Text="مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان"
                           FontSize="18"
                           FontWeight="Bold"
                           Foreground="#3498DB"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"
                           MaxWidth="600"
                           Margin="0,0,0,3"/>
                <TextBlock Text="معنا تصبح ابتسامتك كما تحب أن تكون"
                           FontSize="14"
                           Foreground="#E67E22"
                           FontWeight="SemiBold"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"
                           MaxWidth="500"/>
            </StackPanel>

            <!-- مجموعة الأيقونات واسم المستخدم -->
            <StackPanel Grid.Column="2"
                        Orientation="Horizontal"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left">

                <!-- مرحبًا د. عقلان -->
                <Border Background="#3498DB"
                        CornerRadius="15"
                        Padding="12,6"
                        Margin="0,0,10,0">
                    <TextBlock x:Name="UserNameText"
                               Text="مرحبًا د. عقلان"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Border>

                <!-- الساعة الرقمية -->
                <Border Background="#E67E22"
                        CornerRadius="15"
                        Padding="12,6"
                        Margin="0,0,10,0">
                    <TextBlock x:Name="ClockText"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Border>
                <!-- مجموعة الأزرار -->
                <StackPanel Orientation="Horizontal"
                            Margin="0,0,10,0">
                    <!-- أيقونة التنبيهات -->
                    <Border Background="#95A5A6"
                            CornerRadius="17"
                            Width="35"
                            Height="35"
                            Margin="0,0,5,0">
                        <Button x:Name="NotificationsButton"
                                ToolTip="التنبيهات"
                                Background="Transparent"
                                BorderThickness="0"
                                VerticalAlignment="Center"
                                Click="Notifications_Click">
                            <TextBlock FontFamily="Segoe MDL2 Assets"
                                       Text="&#xE7F4;"
                                       FontSize="16"
                                       Foreground="White"/>
                        </Button>
                    </Border>

                    <!-- أيقونة الإعدادات -->
                    <Border Background="#95A5A6"
                            CornerRadius="17"
                            Width="35"
                            Height="35"
                            Margin="0,0,5,0">
                        <Button x:Name="SettingsButton"
                                ToolTip="الإعدادات"
                                Background="Transparent"
                                BorderThickness="0"
                                VerticalAlignment="Center"
                                Click="Settings_Click">
                            <TextBlock FontFamily="Segoe MDL2 Assets"
                                       Text="&#xE713;"
                                       FontSize="16"
                                       Foreground="White"/>
                        </Button>
                    </Border>

                    <!-- أيقونة اللغة -->
                    <Border Background="#95A5A6"
                            CornerRadius="17"
                            Width="35"
                            Height="35"
                            Margin="0,0,5,0">
                        <Button x:Name="LanguageButton"
                                ToolTip="تغيير اللغة"
                                Background="Transparent"
                                BorderThickness="0"
                                VerticalAlignment="Center"
                                Click="Language_Click">
                            <TextBlock FontFamily="Segoe MDL2 Assets"
                                       Text="&#xE775;"
                                       FontSize="16"
                                       Foreground="White"/>
                        </Button>
                    </Border>

                    <!-- أيقونة الثيم -->
                    <Border Background="#95A5A6"
                            CornerRadius="17"
                            Width="35"
                            Height="35"
                            Margin="0,0,5,0">
                        <Button x:Name="ThemeButton"
                                ToolTip="تغيير الثيم"
                                Background="Transparent"
                                BorderThickness="0"
                                VerticalAlignment="Center"
                                Click="Theme_Click">
                            <TextBlock FontFamily="Segoe MDL2 Assets"
                                       Text="&#xE706;"
                                       FontSize="16"
                                       Foreground="White"/>
                        </Button>
                    </Border>
                </StackPanel>

                <!-- زر الخروج منفصل -->
                <Border Background="#E74C3C"
                        CornerRadius="17"
                        Width="35"
                        Height="35">
                    <Button x:Name="LogoutButton"
                            ToolTip="تسجيل الخروج"
                            Background="Transparent"
                            BorderThickness="0"
                            VerticalAlignment="Center"
                            Click="Logout_Click">
                        <TextBlock FontFamily="Segoe MDL2 Assets"
                                   Text="&#xE8AC;"
                                   FontSize="16"
                                   Foreground="White"/>
                    </Button>
                </Border>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
