<UserControl x:Class="AqlanCenterProApp.Views.Controls.HeaderControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="90"
             d:DesignWidth="1200">

    <Border BorderBrush="#E0E0E0"
            BorderThickness="0,0,0,2"
            Padding="15,8"
            Height="80">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0"
                                 EndPoint="1,0">
                <GradientStop Color="White"
                              Offset="0"/>
                <GradientStop Color="#F8F9FA"
                              Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Grid FlowDirection="RightToLeft">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <!-- شعار -->
                <ColumnDefinition Width="*"/>
                <!-- اسم المركز والعبارة -->
                <ColumnDefinition Width="Auto"/>
                <!-- الأيقونات واسم المستخدم -->
            </Grid.ColumnDefinitions>

            <!-- شعار العيادة -->
            <Image Source="/Resources/logo.png"
                   Height="50"
                   Grid.Column="0"
                   VerticalAlignment="Center"
                   Margin="0,0,20,0"/>

            <!-- اسم المركز والعبارة التسويقية -->
            <StackPanel Grid.Column="1"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center">
                <TextBlock Text="مركز الدكتور عقلان الكامل لتقويم وزراعة وتجميل الأسنان"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#3498DB"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"
                           MaxWidth="500"
                           Margin="0,0,0,2"/>
                <TextBlock Text="معنا تصبح ابتسامتك كما تحب أن تكون"
                           FontSize="12"
                           Foreground="#E67E22"
                           FontWeight="SemiBold"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"
                           MaxWidth="400"/>
            </StackPanel>

            <!-- مجموعة الأيقونات واسم المستخدم -->
            <StackPanel Grid.Column="2"
                        Orientation="Horizontal"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Margin="20,0,0,0">

                <!-- مرحبًا د. عقلان -->
                <Border Background="#3498DB"
                        CornerRadius="12"
                        Padding="10,5"
                        Margin="0,0,8,0">
                    <TextBlock x:Name="UserNameText"
                               Text="مرحبًا د. عقلان"
                               FontSize="12"
                               FontWeight="SemiBold"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Border>

                <!-- الساعة الرقمية -->
                <Border Background="#E67E22"
                        CornerRadius="12"
                        Padding="10,5"
                        Margin="0,0,8,0">
                    <TextBlock x:Name="ClockText"
                               FontSize="12"
                               FontWeight="SemiBold"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Border>
                <!-- مجموعة الأزرار -->
                <StackPanel Orientation="Horizontal"
                            Margin="0,0,8,0">
                    <!-- أيقونة التنبيهات -->
                    <Button x:Name="NotificationsButton"
                            ToolTip="التنبيهات"
                            Background="#95A5A6"
                            BorderThickness="0"
                            Width="30"
                            Height="30"
                            Margin="0,0,4,0"
                            Click="Notifications_Click">
                        <TextBlock FontFamily="Segoe MDL2 Assets"
                                   Text="&#xE7F4;"
                                   FontSize="14"
                                   Foreground="White"/>
                    </Button>

                    <!-- أيقونة الإعدادات -->
                    <Button x:Name="SettingsButton"
                            ToolTip="الإعدادات"
                            Background="#95A5A6"
                            BorderThickness="0"
                            Width="30"
                            Height="30"
                            Margin="0,0,4,0"
                            Click="Settings_Click">
                        <TextBlock FontFamily="Segoe MDL2 Assets"
                                   Text="&#xE713;"
                                   FontSize="14"
                                   Foreground="White"/>
                    </Button>

                    <!-- أيقونة اللغة -->
                    <Button x:Name="LanguageButton"
                            ToolTip="تغيير اللغة"
                            Background="#95A5A6"
                            BorderThickness="0"
                            Width="30"
                            Height="30"
                            Margin="0,0,4,0"
                            Click="Language_Click">
                        <TextBlock FontFamily="Segoe MDL2 Assets"
                                   Text="&#xE775;"
                                   FontSize="14"
                                   Foreground="White"/>
                    </Button>

                    <!-- أيقونة الثيم -->
                    <Button x:Name="ThemeButton"
                            ToolTip="تغيير الثيم"
                            Background="#95A5A6"
                            BorderThickness="0"
                            Width="30"
                            Height="30"
                            Margin="0,0,4,0"
                            Click="Theme_Click">
                        <TextBlock FontFamily="Segoe MDL2 Assets"
                                   Text="&#xE706;"
                                   FontSize="14"
                                   Foreground="White"/>
                    </Button>
                </StackPanel>

                <!-- زر الخروج منفصل -->
                <Button x:Name="LogoutButton"
                        ToolTip="تسجيل الخروج"
                        Background="#E74C3C"
                        BorderThickness="0"
                        Width="30"
                        Height="30"
                        Click="Logout_Click">
                    <TextBlock FontFamily="Segoe MDL2 Assets"
                               Text="&#xE8AC;"
                               FontSize="14"
                               Foreground="White"/>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
