using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    /// <summary>
    /// نموذج سند القبض للمدفوعات المستلمة
    /// </summary>
    public class Receipt : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ReceiptNumber { get; set; } = string.Empty;

        [Required]
        public DateTime ReceiptDate { get; set; } = DateTime.Now;

        [Required]
        public int PatientId { get; set; }

        public int? SessionId { get; set; } // إذا كان الدفع لجلسة معينة

        public int? InvoiceId { get; set; } // إذا كان الدفع لفاتورة معينة

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [Required]
        [StringLength(20)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، بطاقة، تحويل، شيك

        [StringLength(100)]
        public string? ReferenceNumber { get; set; } // رقم المرجع (رقم البطاقة، رقم التحويل)

        [Required]
        [StringLength(100)]
        public string Purpose { get; set; } = string.Empty; // الغرض من الدفع

        [StringLength(500)]
        public string Description { get; set; } = string.Empty; // وصف الدفع

        [StringLength(500)]
        public new string? Notes { get; set; }

        [StringLength(100)]
        public string? ReceivedBy { get; set; } // من استلم المبلغ

        [StringLength(100)]
        public string? IssuedBy { get; set; } // من أصدر السند

        [StringLength(20)]
        public string Status { get; set; } = "مكتمل"; // مكتمل، معلق، ملغي

        [StringLength(100)]
        public string? Signature { get; set; } // توقيع المستلم

        // Navigation Properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("SessionId")]
        public virtual Session? Session { get; set; }

        [ForeignKey("InvoiceId")]
        public virtual Invoice? Invoice { get; set; }
    }
}