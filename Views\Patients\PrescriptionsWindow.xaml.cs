using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace AqlanCenterProApp.Views.Patients
{
    public partial class PrescriptionsWindow : Window
    {
        public ObservableCollection<Prescription> Prescriptions { get; set; }

        public PrescriptionsWindow()
        {
            InitializeComponent();
            Prescriptions = new ObservableCollection<Prescription>();
            LoadSampleData();
            PrescriptionsDataGrid.ItemsSource = Prescriptions;
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للعرض
            Prescriptions.Add(new Prescription
            {
                Id = 1,
                PatientName = "أحمد محمد علي",
                PrescriptionDate = DateTime.Now.AddDays(-2),
                DoctorName = "د. سارة أحمد",
                Medications = "أموكسيسيلين 500 مجم، إيبوبروفين 400 مجم",
                Dosage = "أموكسيسيلين: 3 مرات يومياً لمدة 7 أيام، إيبوبروفين: عند الحاجة",
                Status = "نشطة"
            });

            Prescriptions.Add(new Prescription
            {
                Id = 2,
                PatientName = "فاطمة حسن محمد",
                PrescriptionDate = DateTime.Now.AddDays(-5),
                DoctorName = "د. محمد عبدالله",
                Medications = "باراسيتامول 500 مجم، غسول فم مطهر",
                Dosage = "باراسيتامول: 4 مرات يومياً، الغسول: مرتين يومياً",
                Status = "مكتملة"
            });

            Prescriptions.Add(new Prescription
            {
                Id = 3,
                PatientName = "خالد عبدالرحمن",
                PrescriptionDate = DateTime.Now.AddDays(-1),
                DoctorName = "د. أمل سالم",
                Medications = "مضاد حيوي موضعي، مسكن للألم",
                Dosage = "المضاد الحيوي: 3 مرات يومياً، المسكن: عند الحاجة",
                Status = "نشطة"
            });
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق البحث والفلاتر", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Prescription prescription)
            {
                ShowPrescriptionDetails(prescription);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Prescription prescription)
            {
                PrintPrescription(prescription);
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Prescription prescription)
            {
                EditPrescription(prescription);
            }
        }

        private void ViewDetailsMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (PrescriptionsDataGrid.SelectedItem is Prescription prescription)
            {
                ShowPrescriptionDetails(prescription);
            }
        }

        private void PrintMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (PrescriptionsDataGrid.SelectedItem is Prescription prescription)
            {
                PrintPrescription(prescription);
            }
        }

        private void EditMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (PrescriptionsDataGrid.SelectedItem is Prescription prescription)
            {
                EditPrescription(prescription);
            }
        }

        private void CopyMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (PrescriptionsDataGrid.SelectedItem is Prescription prescription)
            {
                var prescriptionInfo = $"الوصفة الطبية رقم: {prescription.Id}\n" +
                                     $"المريض: {prescription.PatientName}\n" +
                                     $"التاريخ: {prescription.PrescriptionDate:dd/MM/yyyy}\n" +
                                     $"الطبيب: {prescription.DoctorName}\n" +
                                     $"الأدوية: {prescription.Medications}\n" +
                                     $"الجرعة: {prescription.Dosage}";
                
                Clipboard.SetText(prescriptionInfo);
                MessageBox.Show("تم نسخ معلومات الوصفة", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportMenuItem_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير وظيفة التصدير قريباً", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (PrescriptionsDataGrid.SelectedItem is Prescription prescription)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الوصفة الطبية للمريض {prescription.PatientName}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    Prescriptions.Remove(prescription);
                    MessageBox.Show("تم حذف الوصفة بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void AddPrescriptionButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة وصفة طبية جديدة", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportAllButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير جميع الوصفات الطبية", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ShowPrescriptionDetails(Prescription prescription)
        {
            var details = $"تفاصيل الوصفة الطبية\n\n" +
                         $"رقم الوصفة: {prescription.Id}\n" +
                         $"اسم المريض: {prescription.PatientName}\n" +
                         $"تاريخ الوصفة: {prescription.PrescriptionDate:dd/MM/yyyy}\n" +
                         $"الطبيب المعالج: {prescription.DoctorName}\n" +
                         $"الأدوية المقررة: {prescription.Medications}\n" +
                         $"الجرعة والتعليمات: {prescription.Dosage}\n" +
                         $"الحالة: {prescription.Status}";

            MessageBox.Show(details, "تفاصيل الوصفة الطبية", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditPrescription(Prescription prescription)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الوصفة الطبية للمريض: {prescription.PatientName}", 
                "تعديل الوصفة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintPrescription(Prescription prescription)
        {
            MessageBox.Show($"سيتم طباعة الوصفة الطبية للمريض: {prescription.PatientName}", 
                "طباعة الوصفة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // نموذج الوصفة الطبية
    public class Prescription
    {
        public int Id { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public DateTime PrescriptionDate { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string Medications { get; set; } = string.Empty;
        public string Dosage { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}
