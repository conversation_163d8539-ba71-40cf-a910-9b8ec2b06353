using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace AqlanCenterProApp.Views.Controls
{
    /// <summary>
    /// Interaction logic for StatisticCard.xaml
    /// </summary>
    public partial class StatisticCard : UserControl
    {
        public StatisticCard()
        {
            InitializeComponent();
        }

        // خصائص البطاقة
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register("Value", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string Value
        {
            get { return (string)GetValue(ValueProperty); }
            set { SetValue(ValueProperty, value); }
        }

        public static readonly DependencyProperty IconProperty =
            DependencyProperty.Register("Icon", typeof(string), typeof(StatisticCard), new PropertyMetadata("📊"));

        public string Icon
        {
            get { return (string)GetValue(IconProperty); }
            set { SetValue(IconProperty, value); }
        }

        public static readonly DependencyProperty SubTextProperty =
            DependencyProperty.Register("SubText", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string SubText
        {
            get { return (string)GetValue(SubTextProperty); }
            set { SetValue(SubTextProperty, value); }
        }

        public static readonly DependencyProperty CardBackgroundProperty =
            DependencyProperty.Register("CardBackground", typeof(Brush), typeof(StatisticCard),
                new PropertyMetadata(new SolidColorBrush(Colors.White)));

        public Brush CardBackground
        {
            get { return (Brush)GetValue(CardBackgroundProperty); }
            set { SetValue(CardBackgroundProperty, value); }
        }

        public static readonly DependencyProperty IconBackgroundProperty =
            DependencyProperty.Register("IconBackground", typeof(Brush), typeof(StatisticCard),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(52, 152, 219))));

        public Brush IconBackground
        {
            get { return (Brush)GetValue(IconBackgroundProperty); }
            set { SetValue(IconBackgroundProperty, value); }
        }

        public static readonly DependencyProperty ValueColorProperty =
            DependencyProperty.Register("ValueColor", typeof(Brush), typeof(StatisticCard),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(44, 62, 80))));

        public Brush ValueColor
        {
            get { return (Brush)GetValue(ValueColorProperty); }
            set { SetValue(ValueColorProperty, value); }
        }

        // خصائص مؤشر التغيير
        public static readonly DependencyProperty ShowTrendProperty =
            DependencyProperty.Register("ShowTrend", typeof(bool), typeof(StatisticCard), new PropertyMetadata(false));

        public bool ShowTrend
        {
            get { return (bool)GetValue(ShowTrendProperty); }
            set { SetValue(ShowTrendProperty, value); }
        }

        public static readonly DependencyProperty TrendValueProperty =
            DependencyProperty.Register("TrendValue", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string TrendValue
        {
            get { return (string)GetValue(TrendValueProperty); }
            set { SetValue(TrendValueProperty, value); }
        }

        public static readonly DependencyProperty TrendIconProperty =
            DependencyProperty.Register("TrendIcon", typeof(string), typeof(StatisticCard), new PropertyMetadata("↗"));

        public string TrendIcon
        {
            get { return (string)GetValue(TrendIconProperty); }
            set { SetValue(TrendIconProperty, value); }
        }

        public static readonly DependencyProperty TrendColorProperty =
            DependencyProperty.Register("TrendColor", typeof(Brush), typeof(StatisticCard),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(46, 204, 113))));

        public Brush TrendColor
        {
            get { return (Brush)GetValue(TrendColorProperty); }
            set { SetValue(TrendColorProperty, value); }
        }

        // خصائص جديدة محسنة
        public static readonly DependencyProperty UnitProperty =
            DependencyProperty.Register("Unit", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string Unit
        {
            get { return (string)GetValue(UnitProperty); }
            set { SetValue(UnitProperty, value); }
        }

        public static readonly DependencyProperty ShowUnitProperty =
            DependencyProperty.Register("ShowUnit", typeof(bool), typeof(StatisticCard), new PropertyMetadata(false));

        public bool ShowUnit
        {
            get { return (bool)GetValue(ShowUnitProperty); }
            set { SetValue(ShowUnitProperty, value); }
        }

        public static readonly DependencyProperty ProgressValueProperty =
            DependencyProperty.Register("ProgressValue", typeof(double), typeof(StatisticCard), new PropertyMetadata(0.0));

        public double ProgressValue
        {
            get { return (double)GetValue(ProgressValueProperty); }
            set { SetValue(ProgressValueProperty, value); }
        }

        public static readonly DependencyProperty ShowProgressProperty =
            DependencyProperty.Register("ShowProgress", typeof(bool), typeof(StatisticCard), new PropertyMetadata(false));

        public bool ShowProgress
        {
            get { return (bool)GetValue(ShowProgressProperty); }
            set { SetValue(ShowProgressProperty, value); }
        }

        public static readonly DependencyProperty TrendBackgroundColorProperty =
            DependencyProperty.Register("TrendBackgroundColor", typeof(Brush), typeof(StatisticCard),
                new PropertyMetadata(new SolidColorBrush(Color.FromArgb(30, 46, 204, 113))));

        public Brush TrendBackgroundColor
        {
            get { return (Brush)GetValue(TrendBackgroundColorProperty); }
            set { SetValue(TrendBackgroundColorProperty, value); }
        }

        public static readonly DependencyProperty IsClickableProperty =
            DependencyProperty.Register("IsClickable", typeof(bool), typeof(StatisticCard), new PropertyMetadata(false));

        public bool IsClickable
        {
            get { return (bool)GetValue(IsClickableProperty); }
            set { SetValue(IsClickableProperty, value); }
        }

        public static readonly DependencyProperty ClickCommandProperty =
            DependencyProperty.Register("ClickCommand", typeof(ICommand), typeof(StatisticCard), new PropertyMetadata(null));

        public ICommand ClickCommand
        {
            get { return (ICommand)GetValue(ClickCommandProperty); }
            set { SetValue(ClickCommandProperty, value); }
        }

        // طرق مساعدة لتعيين الألوان المحددة مسبقاً
        public void SetSuccessStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(232, 245, 233));
            IconBackground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            ValueColor = new SolidColorBrush(Color.FromRgb(56, 142, 60));
        }

        public void SetWarningStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(255, 243, 224));
            IconBackground = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            ValueColor = new SolidColorBrush(Color.FromRgb(245, 124, 0));
        }

        public void SetDangerStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(255, 235, 238));
            IconBackground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            ValueColor = new SolidColorBrush(Color.FromRgb(211, 47, 47));
        }

        public void SetInfoStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(227, 242, 253));
            IconBackground = new SolidColorBrush(Color.FromRgb(33, 150, 243));
            ValueColor = new SolidColorBrush(Color.FromRgb(25, 118, 210));
        }

        public void SetPrimaryStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(232, 234, 246));
            IconBackground = new SolidColorBrush(Color.FromRgb(103, 58, 183));
            ValueColor = new SolidColorBrush(Color.FromRgb(81, 45, 168));
        }

        public void SetTrendUp(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↗";
            TrendColor = new SolidColorBrush(Color.FromRgb(46, 204, 113));
        }

        public void SetTrendDown(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↘";
            TrendColor = new SolidColorBrush(Color.FromRgb(231, 76, 60));
        }

        public void SetTrendFlat(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "→";
            TrendColor = new SolidColorBrush(Color.FromRgb(149, 165, 166));
            TrendBackgroundColor = new SolidColorBrush(Color.FromArgb(30, 149, 165, 166));
        }

        // طرق محسنة جديدة
        public void SetFinancialStyle(decimal value, string currency = "ر.ي")
        {
            Value = $"{value:N0}";
            Unit = currency;
            ShowUnit = true;
            CardBackground = new SolidColorBrush(Color.FromRgb(232, 245, 233));
            IconBackground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            ValueColor = new SolidColorBrush(Color.FromRgb(56, 142, 60));
        }

        public void SetPercentageStyle(double percentage)
        {
            Value = $"{percentage:F1}";
            Unit = "%";
            ShowUnit = true;
            ProgressValue = percentage;
            ShowProgress = true;
        }

        public void SetCountStyle(int count)
        {
            Value = count.ToString("N0");
            ShowUnit = false;
            ShowProgress = false;
        }

        public void SetTrendUpImproved(string value, double percentage = 0)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↗";
            TrendColor = new SolidColorBrush(Color.FromRgb(46, 204, 113));
            TrendBackgroundColor = new SolidColorBrush(Color.FromArgb(30, 46, 204, 113));

            if (percentage > 0)
            {
                ProgressValue = Math.Min(percentage, 100);
                ShowProgress = true;
            }
        }

        public void SetTrendDownImproved(string value, double percentage = 0)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↘";
            TrendColor = new SolidColorBrush(Color.FromRgb(231, 76, 60));
            TrendBackgroundColor = new SolidColorBrush(Color.FromArgb(30, 231, 76, 60));

            if (percentage > 0)
            {
                ProgressValue = Math.Min(percentage, 100);
                ShowProgress = true;
            }
        }

        public void SetClickable(ICommand command)
        {
            IsClickable = true;
            ClickCommand = command;
            Cursor = System.Windows.Input.Cursors.Hand;
        }

        public void SetCustomColors(string cardColor, string iconColor, string valueColor)
        {
            CardBackground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(cardColor));
            IconBackground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(iconColor));
            ValueColor = new SolidColorBrush((Color)ColorConverter.ConvertFromString(valueColor));
        }

        // معالج النقر
        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);
            if (IsClickable && ClickCommand?.CanExecute(null) == true)
            {
                ClickCommand.Execute(null);
            }
        }
    }
}
