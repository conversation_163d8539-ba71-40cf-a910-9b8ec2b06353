using System.Collections.Generic;
using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة إدارة الخدمات/المنتجات
    /// </summary>
    public interface IServiceService
    {
        List<Service> GetAllServices();
        Service? GetServiceById(int id);
        bool AddService(Service service);
        bool UpdateService(Service service);
        bool DeleteService(int id);
    }
} 