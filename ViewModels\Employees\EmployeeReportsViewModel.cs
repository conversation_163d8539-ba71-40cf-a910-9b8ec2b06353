using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class EmployeeReportsViewModel : INotifyPropertyChanged
    {
        private string _selectedReportType = string.Empty;
        private string _selectedDateRange = string.Empty;
        private string _selectedDepartment = string.Empty;
        private string _selectedStatus = string.Empty;
        private string _reportPreview = string.Empty;

        public ObservableCollection<string> ReportTypes { get; } = new ObservableCollection<string>
        {
            "تقرير عام للموظفين",
            "تقرير الحضور والانصراف",
            "تقرير الرواتب",
            "تقرير الإجازات",
            "تقرير المستندات",
            "تقرير الأداء"
        };

        public ObservableCollection<string> DateRanges { get; } = new ObservableCollection<string>
        {
            "هذا الشهر",
            "الشهر الماضي",
            "هذا العام",
            "العام الماضي",
            "آخر 3 أشهر",
            "آخر 6 أشهر",
            "آخر سنة"
        };

        public ObservableCollection<string> Departments { get; } = new ObservableCollection<string>
        {
            "الكل",
            "الإدارة",
            "الموارد البشرية",
            "المالية",
            "التقنية",
            "التسويق",
            "خدمة العملاء"
        };

        public ObservableCollection<string> Statuses { get; } = new ObservableCollection<string>
        {
            "الكل",
            "نشط",
            "غير نشط"
        };

        public string SelectedReportType
        {
            get => _selectedReportType;
            set { _selectedReportType = value; OnPropertyChanged(nameof(SelectedReportType)); }
        }

        public string SelectedDateRange
        {
            get => _selectedDateRange;
            set { _selectedDateRange = value; OnPropertyChanged(nameof(SelectedDateRange)); }
        }

        public string SelectedDepartment
        {
            get => _selectedDepartment;
            set { _selectedDepartment = value; OnPropertyChanged(nameof(SelectedDepartment)); }
        }

        public string SelectedStatus
        {
            get => _selectedStatus;
            set { _selectedStatus = value; OnPropertyChanged(nameof(SelectedStatus)); }
        }

        public string ReportPreview
        {
            get => _reportPreview;
            set { _reportPreview = value; OnPropertyChanged(nameof(ReportPreview)); }
        }

        public ICommand GenerateReportCommand { get; }

        public EmployeeReportsViewModel()
        {
            GenerateReportCommand = new RelayCommand(_ => GenerateReport());
            
            // Set default values
            if (ReportTypes.Count > 0) SelectedReportType = ReportTypes[0];
            if (DateRanges.Count > 0) SelectedDateRange = DateRanges[0];
            if (Departments.Count > 0) SelectedDepartment = Departments[0];
            if (Statuses.Count > 0) SelectedStatus = Statuses[0];
        }

        private void GenerateReport()
        {
            if (string.IsNullOrEmpty(SelectedReportType))
            {
                MessageBox.Show("يرجى اختيار نوع التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Generate report preview based on selections
            var preview = $"نوع التقرير: {SelectedReportType}\n" +
                         $"الفترة الزمنية: {SelectedDateRange}\n" +
                         $"القسم: {SelectedDepartment}\n" +
                         $"الحالة: {SelectedStatus}\n\n" +
                         "سيتم إنشاء التقرير بناءً على المعايير المحددة...";

            ReportPreview = preview;

            MessageBox.Show("تم إنشاء التقرير بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 