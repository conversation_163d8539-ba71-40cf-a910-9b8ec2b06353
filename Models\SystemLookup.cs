using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class SystemLookup : BaseEntity
    {
        [Required(ErrorMessage = "نوع القائمة مطلوب")]
        [StringLength(50, ErrorMessage = "نوع القائمة لا يمكن أن يتجاوز 50 حرف")]
        public string LookupType { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "اسم العنصر مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العنصر لا يمكن أن يتجاوز 100 حرف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(200, ErrorMessage = "الوصف لا يمكن أن يتجاوز 200 حرف")]
        public string? Description { get; set; }
        
        public int SortOrder { get; set; } = 0;
        
        public bool IsActive { get; set; } = true;
        
        [StringLength(50, ErrorMessage = "القيمة الإضافية لا يمكن أن تتجاوز 50 حرف")]
        public string? ExtraValue { get; set; }
        
        // أنواع القوائم المحتملة:
        // TreatmentType - أنواع العلاجات
        // ServiceType - أنواع الخدمات
        // MedicalCategory - الأصناف الطبية
        // PaymentMethod - طرق الدفع
        // AppointmentStatus - حالات المواعيد
        // PatientStatus - حالات المرضى
        // EmployeeStatus - حالات الموظفين
        // DocumentType - أنواع المستندات
        // LeaveType - أنواع الإجازات
        // SalaryType - أنواع الرواتب
        // InventoryCategory - فئات المخزون
        // LabType - أنواع المختبرات
        // ProsthesisType - أنواع التركيبات
        // ShadeType - أنواع الألوان
    }
} 