using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.ViewModels.Purchases;

namespace AqlanCenterProApp.Views.Purchases
{
    /// <summary>
    /// Interaction logic for PurchasesListView.xaml
    /// </summary>
    public partial class PurchasesListView : UserControl
    {
        public PurchasesListView()
        {
            InitializeComponent();
        }

        public PurchasesListView(PurchasesListViewModel viewModel) : this()
        {
            DataContext = viewModel;
            Loaded += PurchasesListView_Loaded;
        }

        private void PurchasesListView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is PurchasesListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل
                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.LoadPurchasesAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في LoadPurchasesAsync: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل بيانات المشتريات: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في PurchasesListView_Loaded: {ex.Message}");
            }
        }
    }
}