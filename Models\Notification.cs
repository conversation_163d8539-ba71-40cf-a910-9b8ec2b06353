using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class Notification : BaseEntity
    {
        [Key]
        public new int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string NotificationType { get; set; } = "عام"; // عام، موعد، دفع، إشعار

        [StringLength(20)]
        public string Priority { get; set; } = "عادي"; // عادي، مهم، عاجل

        public bool IsRead { get; set; } = false;

        public bool IsSent { get; set; } = false;

        public DateTime? ReadAt { get; set; }

        public DateTime? SentAt { get; set; }

        public DateTime? ScheduledDate { get; set; }

        [StringLength(50)]
        public string DeliveryMethod { get; set; } = "داخلي"; // داخلي، إيميل، واتساب، SMS

        // Foreign Keys
        public int? UserId { get; set; }
        public int? PatientId { get; set; }
        public int? AppointmentId { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        [ForeignKey("PatientId")]
        public virtual Patient? Patient { get; set; }

        [ForeignKey("AppointmentId")]
        public virtual Appointment? Appointment { get; set; }
    }
}