using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة إدارة سندات الصرف
    /// </summary>
    public class PaymentVoucherService : IPaymentVoucherService
    {
        private readonly AqlanCenterDbContext _context;

        public PaymentVoucherService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        #region العمليات الأساسية (CRUD)

        public async Task<IEnumerable<PaymentVoucher>> GetAllVouchersAsync()
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<PaymentVoucher?> GetVoucherByIdAsync(int id)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .FirstOrDefaultAsync(pv => pv.Id == id);
        }

        public async Task<PaymentVoucher?> GetVoucherByNumberAsync(string voucherNumber)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .FirstOrDefaultAsync(pv => pv.VoucherNumber == voucherNumber);
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByEmployeeAsync(int employeeId)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.EmployeeId == employeeId)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersBySupplierAsync(int supplierId)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.SupplierId == supplierId)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByLabAsync(int labId)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.LabId == labId)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<PaymentVoucher> CreateVoucherAsync(PaymentVoucher voucher)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateVoucherAsync(voucher);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // تعيين رقم السند إذا لم يكن محدداً
            if (string.IsNullOrEmpty(voucher.VoucherNumber))
                voucher.VoucherNumber = await GetNextVoucherNumberAsync();

            _context.PaymentVouchers.Add(voucher);
            await _context.SaveChangesAsync();

            return voucher;
        }

        public async Task<PaymentVoucher> UpdateVoucherAsync(PaymentVoucher voucher)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateVoucherAsync(voucher);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            _context.PaymentVouchers.Update(voucher);
            await _context.SaveChangesAsync();

            return voucher;
        }

        public async Task<bool> DeleteVoucherAsync(int id)
        {
            var voucher = await _context.PaymentVouchers.FindAsync(id);
            if (voucher == null)
                return false;

            voucher.IsDeleted = true;
            voucher.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return true;
        }

        #endregion

        #region البحث والفلترة

        public async Task<IEnumerable<PaymentVoucher>> SearchVouchersAsync(string searchTerm)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.VoucherNumber.Contains(searchTerm) ||
                           pv.BeneficiaryName.Contains(searchTerm) ||
                           pv.ExpenseType.Contains(searchTerm) ||
                           pv.Description.Contains(searchTerm) ||
                           (pv.IssuedBy != null && pv.IssuedBy.Contains(searchTerm)))
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByDateAsync(DateTime date)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.VoucherDate.Date == date.Date)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.VoucherDate.Date >= startDate.Date && pv.VoucherDate.Date <= endDate.Date)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByExpenseTypeAsync(string expenseType)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.ExpenseType == expenseType)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByBeneficiaryTypeAsync(string beneficiaryType)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.BeneficiaryType == beneficiaryType)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetVouchersByStatusAsync(string status)
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.Status == status)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        #endregion

        #region الترقيم التلقائي

        public async Task<string> GetNextVoucherNumberAsync()
        {
            var lastVoucher = await _context.PaymentVouchers
                .OrderByDescending(pv => pv.VoucherNumber)
                .FirstOrDefaultAsync();

            if (lastVoucher == null)
                return "PV-0001";

            var lastNumber = lastVoucher.VoucherNumber;
            if (lastNumber.StartsWith("PV-") && int.TryParse(lastNumber.Substring(3), out int number))
            {
                return $"PV-{(number + 1):D4}";
            }

            return $"PV-{DateTime.Now:yyyyMMdd}-{1:D3}";
        }

        public async Task<bool> IsVoucherNumberAvailableAsync(string voucherNumber)
        {
            return !await _context.PaymentVouchers.AnyAsync(pv => pv.VoucherNumber == voucherNumber);
        }

        #endregion

        #region الإحصائيات والتقارير

        public async Task<PaymentVoucherStatistics> GetVoucherStatisticsAsync()
        {
            var vouchers = await _context.PaymentVouchers.ToListAsync();
            var statistics = new PaymentVoucherStatistics
            {
                TotalVouchers = vouchers.Count,
                TotalAmount = vouchers.Sum(pv => pv.Amount),
                ApprovedAmount = vouchers.Where(pv => pv.Status == "مكتمل").Sum(pv => pv.Amount),
                PendingAmount = vouchers.Where(pv => pv.Status == "معلق").Sum(pv => pv.Amount),
                RejectedAmount = vouchers.Where(pv => pv.Status == "ملغي").Sum(pv => pv.Amount)
            };

            // إحصائيات حسب الحالة
            statistics.ApprovedVouchers = vouchers.Count(pv => pv.Status == "مكتمل");
            statistics.PendingVouchers = vouchers.Count(pv => pv.Status == "معلق");
            statistics.RejectedVouchers = vouchers.Count(pv => pv.Status == "ملغي");
            statistics.CancelledVouchers = vouchers.Count(pv => pv.Status == "ملغي");

            // إحصائيات حسب نوع المصروف
            var expenseTypes = vouchers.GroupBy(pv => pv.ExpenseType);
            foreach (var type in expenseTypes)
            {
                statistics.ExpensesByType[type.Key] = type.Sum(pv => pv.Amount);
            }

            // إحصائيات حسب المستفيد
            var beneficiaries = vouchers.GroupBy(pv => pv.BeneficiaryType);
            foreach (var beneficiary in beneficiaries)
            {
                var key = beneficiary.Key ?? "غير محدد";
                statistics.ExpensesByBeneficiary[key] = beneficiary.Sum(pv => pv.Amount);
            }

            // إحصائيات حسب الشهر
            var currentYear = DateTime.Now.Year;
            for (int month = 1; month <= 12; month++)
            {
                var monthVouchers = vouchers.Where(pv => pv.VoucherDate.Year == currentYear && pv.VoucherDate.Month == month);
                var monthKey = $"{currentYear}-{month:D2}";
                statistics.VouchersByMonth[monthKey] = monthVouchers.Count();
            }

            return statistics;
        }

        public async Task<decimal> GetTotalExpensesAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.VoucherDate.Date >= startDate.Date && pv.VoucherDate.Date <= endDate.Date)
                .SumAsync(pv => pv.Amount);
        }

        public async Task<Dictionary<string, decimal>> GetExpensesByTypeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.VoucherDate.Date >= startDate.Date && pv.VoucherDate.Date <= endDate.Date)
                .GroupBy(pv => pv.ExpenseType)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(pv => pv.Amount));
        }

        public async Task<Dictionary<string, decimal>> GetExpensesByBeneficiaryAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _context.PaymentVouchers
                .Where(pv => pv.VoucherDate.Date >= startDate.Date && pv.VoucherDate.Date <= endDate.Date)
                .GroupBy(pv => pv.BeneficiaryType)
                .Select(g => new { Key = g.Key ?? "غير محدد", Value = g.Sum(pv => pv.Amount) })
                .ToListAsync();

            return result.ToDictionary(x => x.Key, x => x.Value);
        }

        public async Task<Dictionary<string, decimal>> GetMonthlyExpensesAsync(int year)
        {
            var vouchers = await _context.PaymentVouchers
                .Where(pv => pv.VoucherDate.Year == year)
                .ToListAsync();

            var monthlyExpenses = new Dictionary<string, decimal>();
            for (int month = 1; month <= 12; month++)
            {
                var monthVouchers = vouchers.Where(pv => pv.VoucherDate.Month == month);
                var monthKey = $"{year}-{month:D2}";
                monthlyExpenses[monthKey] = monthVouchers.Sum(pv => pv.Amount);
            }

            return monthlyExpenses;
        }

        #endregion

        #region الطباعة والتصدير

        public Task<bool> PrintVoucherAsync(int voucherId)
        {
            // تنفيذ الطباعة (سيتم تنفيذها لاحقاً)
            return Task.FromResult(true);
        }

        public Task<byte[]> ExportVoucherToPdfAsync(int voucherId)
        {
            // تنفيذ التصدير إلى PDF (سيتم تنفيذها لاحقاً)
            return Task.FromResult(new byte[0]);
        }

        public Task<byte[]> ExportVoucherToExcelAsync(int voucherId)
        {
            // تنفيذ التصدير إلى Excel (سيتم تنفيذها لاحقاً)
            return Task.FromResult(new byte[0]);
        }

        #endregion

        #region الموافقة والإدارة

        public async Task<bool> ApproveVoucherAsync(int voucherId, string approvedBy, string? notes = null)
        {
            var voucher = await _context.PaymentVouchers.FindAsync(voucherId);
            if (voucher == null)
                return false;

            voucher.Status = "مكتمل";
            voucher.ApprovedBy = approvedBy;
            voucher.ApprovalDate = DateTime.Now;
            voucher.Notes = notes;
            voucher.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RejectVoucherAsync(int voucherId, string rejectedBy, string reason)
        {
            var voucher = await _context.PaymentVouchers.FindAsync(voucherId);
            if (voucher == null)
                return false;

            voucher.Status = "ملغي";
            voucher.Notes = reason;
            voucher.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<PaymentVoucher>> GetPendingVouchersAsync()
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.Status == "معلق")
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetApprovedVouchersAsync()
        {
            return await _context.PaymentVouchers
                .Include(pv => pv.Employee)
                .Include(pv => pv.Supplier)
                .Include(pv => pv.Lab)
                .Where(pv => pv.Status == "مكتمل")
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }

        #endregion

        #region التحقق من صحة البيانات

        public async Task<(bool IsValid, string ErrorMessage)> ValidateVoucherAsync(PaymentVoucher voucher)
        {
            if (voucher == null)
                return (false, "سند الصرف مطلوب");

            if (string.IsNullOrEmpty(voucher.VoucherNumber))
                return (false, "رقم السند مطلوب");

            if (string.IsNullOrEmpty(voucher.BeneficiaryName))
                return (false, "اسم المستفيد مطلوب");

            if (string.IsNullOrEmpty(voucher.ExpenseType))
                return (false, "نوع المصروف مطلوب");

            if (string.IsNullOrEmpty(voucher.Description))
                return (false, "وصف المصروف مطلوب");

            if (voucher.VoucherDate == default)
                return (false, "تاريخ السند مطلوب");

            if (voucher.Amount <= 0)
                return (false, "المبلغ يجب أن يكون أكبر من صفر");

            if (string.IsNullOrEmpty(voucher.PaymentMethod))
                return (false, "طريقة الدفع مطلوبة");

            // التحقق من عدم تكرار رقم السند
            if (await _context.PaymentVouchers.AnyAsync(pv => pv.VoucherNumber == voucher.VoucherNumber && pv.Id != voucher.Id))
                return (false, "رقم السند مستخدم بالفعل");

            return (true, string.Empty);
        }

        public async Task<bool> CanApproveVoucherAsync(int voucherId, string approver)
        {
            var voucher = await _context.PaymentVouchers.FindAsync(voucherId);
            if (voucher == null)
                return false;

            // يمكن إضافة منطق التحقق من الصلاحيات هنا
            return voucher.Status == "معلق";
        }

        #endregion
    }
}