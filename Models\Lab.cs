using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class Lab : BaseEntity
    {
        [Key]
        public int LabId { get; set; }

        [Required(ErrorMessage = "اسم المعمل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المعمل لا يمكن أن يتجاوز 100 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "العنوان لا يمكن أن يتجاوز 200 حرف")]
        public string? Address { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرف")]
        public string? Phone { get; set; }

        [StringLength(20, ErrorMessage = "رقم الواتساب لا يمكن أن يتجاوز 20 حرف")]
        public string? WhatsApp { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        [Display(Name = "ملاحظات")]
        public new string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<LabOrder> LabOrders { get; set; } = new List<LabOrder>();
    }
}
