using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    public class InventoryItemService : IInventoryItemService
    {
        private readonly AqlanCenterDbContext _context;

        public InventoryItemService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<InventoryItem>> GetAllInventoryItemsAsync()
        {
            return await _context.InventoryItems
                .Include(ii => ii.PurchaseItems)
                .Include(ii => ii.InvoiceItems)
                .OrderBy(ii => ii.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryItem>> GetActiveInventoryItemsAsync()
        {
            return await _context.InventoryItems
                .Where(ii => ii.IsActive)
                .Include(ii => ii.PurchaseItems)
                .Include(ii => ii.InvoiceItems)
                .OrderBy(ii => ii.Name)
                .ToListAsync();
        }

        public async Task<InventoryItem?> GetInventoryItemByIdAsync(int id)
        {
            return await _context.InventoryItems
                .Include(ii => ii.PurchaseItems)
                .Include(ii => ii.InvoiceItems)
                .FirstOrDefaultAsync(ii => ii.Id == id);
        }

        public async Task<InventoryItem?> GetInventoryItemByCodeAsync(string code)
        {
            return await _context.InventoryItems
                .FirstOrDefaultAsync(ii => ii.Code == code);
        }

        public async Task<InventoryItem?> GetInventoryItemByBarcodeAsync(string barcode)
        {
            return await _context.InventoryItems
                .FirstOrDefaultAsync(ii => ii.Barcode == barcode);
        }

        public async Task<InventoryItem> CreateInventoryItemAsync(InventoryItem item)
        {
            item.CreatedAt = DateTime.Now;
            item.IsActive = true;
            
            _context.InventoryItems.Add(item);
            await _context.SaveChangesAsync();
            
            return item;
        }

        public async Task<InventoryItem> UpdateInventoryItemAsync(InventoryItem item)
        {
            var existingItem = await _context.InventoryItems.FindAsync(item.Id);
            if (existingItem == null)
                throw new ArgumentException("عنصر المخزون غير موجود");

            existingItem.Name = item.Name;
            existingItem.Description = item.Description;
            existingItem.Code = item.Code;
            existingItem.Barcode = item.Barcode;
            existingItem.Category = item.Category;
            existingItem.Unit = item.Unit;
            existingItem.CurrentQuantity = item.CurrentQuantity;
            existingItem.MinimumQuantity = item.MinimumQuantity;
            existingItem.MaximumQuantity = item.MaximumQuantity;
            existingItem.AverageCost = item.AverageCost;
            existingItem.LastPurchasePrice = item.LastPurchasePrice;
            existingItem.ExpiryDate = item.ExpiryDate;
            existingItem.ExpiryWarningDays = item.ExpiryWarningDays;
            existingItem.Location = item.Location;
            existingItem.IsActive = item.IsActive;
            existingItem.RequiresExpiryTracking = item.RequiresExpiryTracking;
            existingItem.Notes = item.Notes;
            existingItem.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return existingItem;
        }

        public async Task<bool> DeleteInventoryItemAsync(int id)
        {
            var item = await _context.InventoryItems.FindAsync(id);
            if (item == null)
                return false;

            // التحقق من عدم وجود مشتريات أو فواتير مرتبطة
            var hasPurchaseItems = await _context.PurchaseItems.AnyAsync(pi => pi.InventoryItemId == id);
            var hasInvoiceItems = await _context.InvoiceItems.AnyAsync(ii => ii.InventoryItemId == id);
            
            if (hasPurchaseItems || hasInvoiceItems)
                throw new InvalidOperationException("لا يمكن حذف عنصر المخزون لوجود عمليات مرتبطة به");

            item.IsDeleted = true;
            item.DeletedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> DeactivateInventoryItemAsync(int id)
        {
            var item = await _context.InventoryItems.FindAsync(id);
            if (item == null)
                return false;

            item.IsActive = false;
            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> ActivateInventoryItemAsync(int id)
        {
            var item = await _context.InventoryItems.FindAsync(id);
            if (item == null)
                return false;

            item.IsActive = true;
            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<IEnumerable<InventoryItem>> SearchInventoryItemsAsync(string searchTerm)
        {
            return await _context.InventoryItems
                .Where(ii => (ii.Name != null && ii.Name.Contains(searchTerm)) || 
                            (ii.Code != null && ii.Code.Contains(searchTerm)) || 
                            (ii.Barcode != null && ii.Barcode.Contains(searchTerm)) ||
                            (ii.Category != null && ii.Category.Contains(searchTerm)))
                .Include(ii => ii.PurchaseItems)
                .Include(ii => ii.InvoiceItems)
                .OrderBy(ii => ii.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryItem>> GetInventoryItemsByCategoryAsync(string category)
        {
            return await _context.InventoryItems
                .Where(ii => ii.Category == category && ii.IsActive)
                .OrderBy(ii => ii.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryItem>> GetLowStockItemsAsync()
        {
            return await _context.InventoryItems
                .Where(ii => ii.IsActive && ii.CurrentQuantity <= ii.MinimumQuantity)
                .OrderBy(ii => ii.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryItem>> GetExpiringItemsAsync(int daysThreshold = 30)
        {
            var thresholdDate = DateTime.Now.AddDays(daysThreshold);
            return await _context.InventoryItems
                .Where(ii => ii.IsActive && 
                            ii.RequiresExpiryTracking && 
                            ii.ExpiryDate.HasValue && 
                            ii.ExpiryDate <= thresholdDate)
                .OrderBy(ii => ii.ExpiryDate)
                .ToListAsync();
        }

        public async Task<bool> UpdateItemQuantityAsync(int itemId, decimal quantity, string operation)
        {
            var item = await _context.InventoryItems.FindAsync(itemId);
            if (item == null)
                return false;

            switch (operation.ToLower())
            {
                case "add":
                case "increase":
                    item.CurrentQuantity += quantity;
                    break;
                case "subtract":
                case "decrease":
                    if (item.CurrentQuantity < quantity)
                        throw new InvalidOperationException("الكمية المطلوبة غير متوفرة في المخزون");
                    item.CurrentQuantity -= quantity;
                    break;
                case "set":
                    item.CurrentQuantity = quantity;
                    break;
                default:
                    throw new ArgumentException("عملية غير صحيحة");
            }

            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<decimal> GetItemCurrentQuantityAsync(int itemId)
        {
            var item = await _context.InventoryItems.FindAsync(itemId);
            return item?.CurrentQuantity ?? 0;
        }

        public async Task<bool> CheckItemAvailabilityAsync(int itemId, decimal requiredQuantity)
        {
            var item = await _context.InventoryItems.FindAsync(itemId);
            return item != null && item.CurrentQuantity >= requiredQuantity;
        }

        public async Task<IEnumerable<string>> GetItemCategoriesAsync()
        {
            return await _context.InventoryItems
                .Where(ii => ii.IsActive)
                .Select(ii => ii.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();
        }

        public async Task<decimal> GetItemAverageCostAsync(int itemId)
        {
            var item = await _context.InventoryItems.FindAsync(itemId);
            return item?.AverageCost ?? 0;
        }

        public async Task UpdateItemAverageCostAsync(int itemId, decimal newPurchasePrice, decimal quantity)
        {
            var item = await _context.InventoryItems.FindAsync(itemId);
            if (item == null)
                return;

            var currentValue = item.CurrentQuantity * item.AverageCost;
            var newValue = quantity * newPurchasePrice;
            var totalQuantity = item.CurrentQuantity + quantity;

            if (totalQuantity > 0)
            {
                item.AverageCost = (currentValue + newValue) / totalQuantity;
            }

            item.LastPurchasePrice = newPurchasePrice;
            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
        }
    }
} 