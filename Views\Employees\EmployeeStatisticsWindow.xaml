<Window x:Class="AqlanCenterProApp.Views.Employees.EmployeeStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إحصائيات الموظف" Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">
    <DockPanel>
        <TextBlock Text="إحصائيات الموظف" FontSize="24" FontWeight="Bold" Margin="20,10,20,10" DockPanel.Dock="Top" HorizontalAlignment="Center"/>
        
        <ScrollViewer>
            <StackPanel Margin="20">
                <!-- Employee Info Section -->
                <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات الموظف" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="{Binding EmployeeName}" Grid.Column="0" Grid.Row="0" FontSize="16" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding EmployeeId}" Grid.Column="1" Grid.Row="0" FontSize="16"/>
                            <TextBlock Text="{Binding Department}" Grid.Column="0" Grid.Row="1" FontSize="14"/>
                            <TextBlock Text="{Binding Position}" Grid.Column="1" Grid.Row="1" FontSize="14"/>
                            <TextBlock Text="{Binding HireDate, StringFormat='تاريخ التعيين: {0:yyyy/MM/dd}'}" Grid.Column="0" Grid.Row="2" FontSize="14"/>
                            <TextBlock Text="{Binding Status}" Grid.Column="1" Grid.Row="2" FontSize="14"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Statistics Grid -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Attendance Statistics -->
                    <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,7,15" Grid.Column="0" Grid.Row="0">
                        <StackPanel>
                            <TextBlock Text="إحصائيات الحضور" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding AttendanceStats}" FontSize="14"/>
                        </StackPanel>
                    </Border>

                    <!-- Salary Statistics -->
                    <Border Background="White" CornerRadius="8" Padding="15" Margin="7,0,0,15" Grid.Column="1" Grid.Row="0">
                        <StackPanel>
                            <TextBlock Text="إحصائيات الرواتب" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding SalaryStats}" FontSize="14"/>
                        </StackPanel>
                    </Border>

                    <!-- Leave Statistics -->
                    <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,7,15" Grid.Column="0" Grid.Row="1">
                        <StackPanel>
                            <TextBlock Text="إحصائيات الإجازات" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding LeaveStats}" FontSize="14"/>
                        </StackPanel>
                    </Border>

                    <!-- Document Statistics -->
                    <Border Background="White" CornerRadius="8" Padding="15" Margin="7,0,0,15" Grid.Column="1" Grid.Row="1">
                        <StackPanel>
                            <TextBlock Text="إحصائيات المستندات" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding DocumentStats}" FontSize="14"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <Button Content="إغلاق" Width="100" Height="35" Margin="0,0,20,20" DockPanel.Dock="Bottom" HorizontalAlignment="Left" Click="Close_Click"/>
    </DockPanel>
</Window> 