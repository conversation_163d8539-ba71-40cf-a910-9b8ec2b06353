using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AqlanCenterProApp.Services
{
    /// <summary>
    /// خدمة إدارة الأطباء
    /// </summary>
    public class DoctorService : IDoctorService
    {
        private readonly AqlanCenterDbContext _context;

        public DoctorService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع الأطباء
        /// </summary>
        public async Task<List<Doctor>> GetAllDoctorsAsync()
        {
            try
            {
                // إضافة timeout للاستعلام
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(20));

                // استعلام بسيط جداً مع timeout
                var doctors = await _context.Doctors
                    .AsNoTracking() // تحسين الأداء
                    .ToListAsync(cts.Token)
                    .ConfigureAwait(false);

                return doctors ?? new List<Doctor>();
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Timeout in GetAllDoctorsAsync");
                return new List<Doctor>();
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error in GetAllDoctorsAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // إرجاع قائمة فارغة
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// الحصول على الأطباء النشطين فقط
        /// </summary>
        public async Task<List<Doctor>> GetActiveDoctorsAsync()
        {
            try
            {
                return await _context.Doctors
                    .Where(d => d.IsActive && d.Status == "نشط")
                    .Include(d => d.Sessions)
                    .Include(d => d.Appointments)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting active doctors: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// الحصول على طبيب بالمعرف
        /// </summary>
        public async Task<Doctor?> GetDoctorByIdAsync(int doctorId)
        {
            try
            {
                return await _context.Doctors
                    .Include(d => d.Sessions)
                    .Include(d => d.Appointments)
                    .FirstOrDefaultAsync(d => d.Id == doctorId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting doctor by ID: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن الأطباء
        /// </summary>
        public async Task<List<Doctor>> SearchDoctorsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllDoctorsAsync();

                searchTerm = searchTerm.Trim().ToLower();

                // استعلام مبسط بدون Include
                return await _context.Doctors
                    .Where(d => d.FullName.ToLower().Contains(searchTerm) ||
                               d.Specialization.ToLower().Contains(searchTerm) ||
                               (d.Mobile != null && d.Mobile.Contains(searchTerm)) ||
                               (d.LicenseNumber != null && d.LicenseNumber.ToLower().Contains(searchTerm)))
                    .OrderBy(d => d.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error searching doctors: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// تصفية الأطباء حسب التخصص
        /// </summary>
        public async Task<List<Doctor>> GetDoctorsBySpecializationAsync(string specialization)
        {
            try
            {
                return await _context.Doctors
                    .Where(d => d.Specialization == specialization)
                    .Include(d => d.Sessions)
                    .Include(d => d.Appointments)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting doctors by specialization: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// تصفية الأطباء حسب الحالة
        /// </summary>
        public async Task<List<Doctor>> GetDoctorsByStatusAsync(string status)
        {
            try
            {
                return await _context.Doctors
                    .Where(d => d.Status == status)
                    .Include(d => d.Sessions)
                    .Include(d => d.Appointments)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting doctors by status: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// إضافة طبيب جديد
        /// </summary>
        public async Task<bool> AddDoctorAsync(Doctor doctor, string createdBy)
        {
            try
            {
                doctor.CreatedAt = DateTime.Now;
                doctor.CreatedBy = createdBy;
                doctor.IsActive = true;
                doctor.Status = "نشط";

                _context.Doctors.Add(doctor);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding doctor: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث بيانات طبيب
        /// </summary>
        public async Task<bool> UpdateDoctorAsync(Doctor doctor, string updatedBy)
        {
            try
            {
                var existingDoctor = await _context.Doctors.FindAsync(doctor.Id);
                if (existingDoctor == null)
                    return false;

                // تحديث البيانات
                existingDoctor.FullName = doctor.FullName;
                existingDoctor.Specialization = doctor.Specialization;
                existingDoctor.Mobile = doctor.Mobile;
                existingDoctor.Phone = doctor.Phone;
                existingDoctor.Email = doctor.Email;
                existingDoctor.Address = doctor.Address;
                existingDoctor.ContractType = doctor.ContractType;
                existingDoctor.CommissionPercentage = doctor.CommissionPercentage;
                existingDoctor.FixedSalary = doctor.FixedSalary;
                existingDoctor.Status = doctor.Status;
                existingDoctor.LicenseNumber = doctor.LicenseNumber;
                existingDoctor.LicenseExpiryDate = doctor.LicenseExpiryDate;
                existingDoctor.ContractEndDate = doctor.ContractEndDate;
                existingDoctor.DateOfBirth = doctor.DateOfBirth;
                existingDoctor.Gender = doctor.Gender;
                existingDoctor.Nationality = doctor.Nationality;
                existingDoctor.Qualifications = doctor.Qualifications;
                existingDoctor.AdditionalNotes = doctor.AdditionalNotes;
                existingDoctor.IsAvailableForAppointments = doctor.IsAvailableForAppointments;
                existingDoctor.DoctorImage = doctor.DoctorImage;
                existingDoctor.UpdatedAt = DateTime.Now;
                existingDoctor.UpdatedBy = updatedBy;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating doctor: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف طبيب
        /// </summary>
        public async Task<bool> DeleteDoctorAsync(int doctorId, string deletedBy)
        {
            try
            {
                var doctor = await _context.Doctors.FindAsync(doctorId);
                if (doctor == null)
                    return false;

                // التحقق من وجود جلسات أو مواعيد مرتبطة
                var hasSessions = await _context.Sessions.AnyAsync(s => s.DoctorId == doctorId);
                var hasAppointments = await _context.Appointments.AnyAsync(a => a.DoctorId == doctorId);

                if (hasSessions || hasAppointments)
                {
                    // تعطيل الطبيب بدلاً من الحذف
                    doctor.IsActive = false;
                    doctor.Status = "غير نشط";
                    doctor.UpdatedAt = DateTime.Now;
                    doctor.UpdatedBy = deletedBy;
                }
                else
                {
                    _context.Doctors.Remove(doctor);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting doctor: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تفعيل/تعطيل طبيب
        /// </summary>
        public async Task<bool> ToggleDoctorStatusAsync(int doctorId, string updatedBy)
        {
            try
            {
                var doctor = await _context.Doctors.FindAsync(doctorId);
                if (doctor == null)
                    return false;

                doctor.IsActive = !doctor.IsActive;
                doctor.Status = doctor.IsActive ? "نشط" : "غير نشط";
                doctor.UpdatedAt = DateTime.Now;
                doctor.UpdatedBy = updatedBy;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error toggling doctor status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على الأطباء المتاحين للمواعيد
        /// </summary>
        public async Task<List<Doctor>> GetAvailableDoctorsAsync()
        {
            try
            {
                return await _context.Doctors
                    .Where(d => d.IsActive && d.Status == "نشط" && d.IsAvailableForAppointments)
                    .OrderBy(d => d.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting available doctors: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// التحقق من انتهاء تراخيص الأطباء
        /// </summary>
        public async Task<List<Doctor>> GetDoctorsWithExpiringLicensesAsync(int daysBeforeExpiry = 30)
        {
            try
            {
                var expiryDate = DateTime.Now.AddDays(daysBeforeExpiry);
                return await _context.Doctors
                    .Where(d => d.IsActive && d.LicenseExpiryDate.HasValue && d.LicenseExpiryDate <= expiryDate)
                    .OrderBy(d => d.LicenseExpiryDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting doctors with expiring licenses: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// التحقق من انتهاء عقود الأطباء
        /// </summary>
        public async Task<List<Doctor>> GetDoctorsWithExpiringContractsAsync(int daysBeforeExpiry = 30)
        {
            try
            {
                var expiryDate = DateTime.Now.AddDays(daysBeforeExpiry);
                return await _context.Doctors
                    .Where(d => d.IsActive && d.ContractEndDate.HasValue && d.ContractEndDate <= expiryDate)
                    .OrderBy(d => d.ContractEndDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting doctors with expiring contracts: {ex.Message}");
                return new List<Doctor>();
            }
        }

        /// <summary>
        /// الحصول على أفضل الأطباء حسب التقييم
        /// </summary>
        public async Task<List<Doctor>> GetTopRatedDoctorsAsync(int count = 10)
        {
            try
            {
                return await _context.Doctors
                    .Where(d => d.IsActive && d.Rating.HasValue)
                    .OrderByDescending(d => d.Rating)
                    .ThenByDescending(d => d.RatingCount)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting top rated doctors: {ex.Message}");
                return new List<Doctor>();
            }
        }

        // سيتم إضافة باقي الدوال في الجزء التالي...
        public Task<DoctorStatistics> GetDoctorStatisticsAsync(int doctorId)
        {
            // سيتم تنفيذها لاحقاً
            return Task.FromResult(new DoctorStatistics());
        }

        public Task<GeneralDoctorStatistics> GetGeneralStatisticsAsync()
        {
            // سيتم تنفيذها لاحقاً
            return Task.FromResult(new GeneralDoctorStatistics());
        }

        public Task<bool> UpdateDoctorRatingAsync(int doctorId, decimal rating)
        {
            // سيتم تنفيذها لاحقاً
            return Task.FromResult(true);
        }

        public Task<bool> UpdateDoctorStatisticsAsync(int doctorId)
        {
            // سيتم تنفيذها لاحقاً
            return Task.FromResult(true);
        }
    }
}
