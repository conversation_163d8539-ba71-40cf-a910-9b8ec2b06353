using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Dashboard
{
    /// <summary>
    /// نموذج مكونات لوحة التحكم القابلة للتخصيص
    /// </summary>
    public class DashboardWidget
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // StatisticCard, Chart, Alert, Table
        public int Position { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool IsResizable { get; set; } = true;
        public int Width { get; set; } = 1;
        public int Height { get; set; } = 1;
        public Dictionary<string, object> Settings { get; set; } = new();
        public string Permission { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إعدادات المخطط البياني
    /// </summary>
    public class ChartSettings
    {
        public string ChartType { get; set; } = "Line"; // Line, Bar, Pie, Area
        public string Title { get; set; } = string.Empty;
        public bool ShowLegend { get; set; } = true;
        public bool ShowGrid { get; set; } = true;
        public bool IsAnimated { get; set; } = true;
        public string ColorScheme { get; set; } = "Default";
        public List<string> Colors { get; set; } = new();
        public string XAxisTitle { get; set; } = string.Empty;
        public string YAxisTitle { get; set; } = string.Empty;
        public int DataPoints { get; set; } = 12;
        public string Period { get; set; } = "Monthly"; // Daily, Weekly, Monthly, Yearly
    }

    /// <summary>
    /// نموذج إعدادات بطاقة الإحصائيات
    /// </summary>
    public class StatisticCardSettings
    {
        public string Title { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = "#3498DB";
        public string ValueFormat { get; set; } = "{0:N0}";
        public bool ShowTrend { get; set; } = true;
        public bool ShowSubText { get; set; } = true;
        public string DataSource { get; set; } = string.Empty;
        public string Currency { get; set; } = "YER";
    }

    /// <summary>
    /// نموذج إعدادات التنبيهات
    /// </summary>
    public class AlertSettings
    {
        public bool ShowPaymentAlerts { get; set; } = true;
        public bool ShowInventoryAlerts { get; set; } = true;
        public bool ShowAppointmentAlerts { get; set; } = true;
        public bool ShowSystemAlerts { get; set; } = true;
        public int MaxAlertsToShow { get; set; } = 10;
        public bool AutoRefreshAlerts { get; set; } = true;
        public int AlertRefreshInterval { get; set; } = 30; // seconds
        public List<AlertType> EnabledAlertTypes { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات الأداء
    /// </summary>
    public class PerformanceSettings
    {
        public bool EnableCaching { get; set; } = true;
        public int CacheExpirationMinutes { get; set; } = 5;
        public bool EnableAutoRefresh { get; set; } = true;
        public int AutoRefreshInterval { get; set; } = 300; // seconds
        public bool EnableLazyLoading { get; set; } = true;
        public int MaxConcurrentRequests { get; set; } = 5;
        public bool EnablePerformanceMonitoring { get; set; } = true;
    }

    /// <summary>
    /// نموذج إعدادات العرض
    /// </summary>
    public class DisplaySettings
    {
        public string Theme { get; set; } = "Light"; // Light, Dark
        public string Language { get; set; } = "ar-YE";
        public bool IsRTL { get; set; } = true;
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        public string TimeFormat { get; set; } = "HH:mm";
        public string NumberFormat { get; set; } = "N0";
        public bool ShowAnimations { get; set; } = true;
        public int AnimationDuration { get; set; } = 300; // milliseconds
    }

    /// <summary>
    /// نموذج إعدادات التصدير
    /// </summary>
    public class ExportSettings
    {
        public bool EnablePDFExport { get; set; } = true;
        public bool EnableExcelExport { get; set; } = true;
        public bool EnableImageExport { get; set; } = true;
        public string DefaultExportFormat { get; set; } = "PDF";
        public bool IncludeCharts { get; set; } = true;
        public bool IncludeStatistics { get; set; } = true;
        public bool IncludeAlerts { get; set; } = false;
        public string ExportPath { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إعدادات الأمان
    /// </summary>
    public class SecuritySettings
    {
        public bool RequireAuthentication { get; set; } = true;
        public List<string> AllowedRoles { get; set; } = new();
        public bool EnableAuditLog { get; set; } = true;
        public bool HideFinancialData { get; set; } = false;
        public bool HidePatientData { get; set; } = false;
        public int SessionTimeoutMinutes { get; set; } = 60;
    }

    /// <summary>
    /// نموذج إعدادات الإشعارات
    /// </summary>
    public class NotificationSettings
    {
        public bool EnableEmailNotifications { get; set; } = false;
        public bool EnableSMSNotifications { get; set; } = false;
        public bool EnablePushNotifications { get; set; } = true;
        public bool EnableSoundNotifications { get; set; } = true;
        public List<string> NotificationRecipients { get; set; } = new();
        public Dictionary<AlertType, bool> AlertTypeNotifications { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات التكامل
    /// </summary>
    public class IntegrationSettings
    {
        public bool EnableWhatsAppIntegration { get; set; } = false;
        public bool EnableEmailIntegration { get; set; } = false;
        public bool EnableSMSIntegration { get; set; } = false;
        public string WhatsAppAPIKey { get; set; } = string.Empty;
        public string EmailSMTPServer { get; set; } = string.Empty;
        public string SMSProvider { get; set; } = string.Empty;
        public Dictionary<string, string> APIKeys { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات النسخ الاحتياطي
    /// </summary>
    public class BackupSettings
    {
        public bool EnableAutoBackup { get; set; } = true;
        public int BackupIntervalHours { get; set; } = 24;
        public string BackupPath { get; set; } = string.Empty;
        public int MaxBackupFiles { get; set; } = 30;
        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = false;
        public List<string> BackupIncludes { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات المراقبة
    /// </summary>
    public class MonitoringSettings
    {
        public bool EnableSystemMonitoring { get; set; } = true;
        public bool EnablePerformanceMonitoring { get; set; } = true;
        public bool EnableErrorTracking { get; set; } = true;
        public bool EnableUsageAnalytics { get; set; } = false;
        public int MonitoringInterval { get; set; } = 60; // seconds
        public string LogLevel { get; set; } = "Information";
        public bool EnableRemoteMonitoring { get; set; } = false;
    }

    /// <summary>
    /// نموذج إعدادات التخصيص
    /// </summary>
    public class CustomizationSettings
    {
        public string LogoPath { get; set; } = string.Empty;
        public string CenterName { get; set; } = string.Empty;
        public string PrimaryColor { get; set; } = "#FF8C00"; // Orange
        public string SecondaryColor { get; set; } = "#87CEEB"; // Sky Blue
        public string AccentColor { get; set; } = "#3498DB";
        public string BackgroundColor { get; set; } = "#FAFBFC";
        public string FontFamily { get; set; } = "Segoe UI";
        public int FontSize { get; set; } = 14;
        public Dictionary<string, string> CustomTexts { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات لوحة التحكم الشاملة
    /// </summary>
    public class ComprehensiveDashboardSettings
    {
        public DisplaySettings Display { get; set; } = new();
        public PerformanceSettings Performance { get; set; } = new();
        public AlertSettings Alerts { get; set; } = new();
        public ExportSettings Export { get; set; } = new();
        public SecuritySettings Security { get; set; } = new();
        public NotificationSettings Notifications { get; set; } = new();
        public IntegrationSettings Integration { get; set; } = new();
        public BackupSettings Backup { get; set; } = new();
        public MonitoringSettings Monitoring { get; set; } = new();
        public CustomizationSettings Customization { get; set; } = new();
        public List<DashboardWidget> Widgets { get; set; } = new();
        public Dictionary<string, object> UserPreferences { get; set; } = new();
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string ModifiedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج حالة لوحة التحكم
    /// </summary>
    public class DashboardState
    {
        public bool IsInitialized { get; set; } = false;
        public bool IsLoading { get; set; } = false;
        public bool HasError { get; set; } = false;
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime LastRefresh { get; set; } = DateTime.Now;
        public TimeSpan LoadTime { get; set; }
        public int ActiveUsers { get; set; }
        public string Version { get; set; } = "1.0.0";
        public Dictionary<string, object> SystemInfo { get; set; } = new();
    }
}
