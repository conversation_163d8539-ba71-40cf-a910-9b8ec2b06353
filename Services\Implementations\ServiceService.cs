using System.Collections.Generic;
using System.Linq;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة إدارة الخدمات/المنتجات (تخزين مؤقت في الذاكرة)
    /// </summary>
    public class ServiceService : IServiceService
    {
        private readonly List<Service> _services;
        public ServiceService()
        {
            // بيانات تجريبية
            _services = new List<Service>
            {
                new Service { Id = 1, Name = "استشارة طبية", Price = 5000, Description = "استشارة أولية مع الطبيب" },
                new Service { Id = 2, Name = "تنظيف أسنان", Price = 8000, Description = "تنظيف وتلميع الأسنان" },
                new Service { Id = 3, Name = "حشو عصب", Price = 15000, Description = "علاج عصب سن واحد" },
                new Service { Id = 4, Name = "تركيب زيركون", Price = 25000, Description = "تركيب تاج زيركون للأسنان" },
                new Service { Id = 5, Name = "خلع سن", Price = 6000, Description = "خلع سن دائم أو لبني" }
            };
        }
        public List<Service> GetAllServices() => _services.ToList();
        public Service? GetServiceById(int id) => _services.FirstOrDefault(s => s.Id == id);
        public bool AddService(Service service)
        {
            service.Id = _services.Count > 0 ? _services.Max(s => s.Id) + 1 : 1;
            _services.Add(service);
            return true;
        }
        public bool UpdateService(Service service)
        {
            var existing = GetServiceById(service.Id);
            if (existing == null) return false;
            existing.Name = service.Name;
            existing.Description = service.Description;
            existing.Price = service.Price;
            existing.Code = service.Code;
            existing.IsActive = service.IsActive;
            return true;
        }
        public bool DeleteService(int id)
        {
            var service = GetServiceById(id);
            if (service == null) return false;
            _services.Remove(service);
            return true;
        }
    }
} 