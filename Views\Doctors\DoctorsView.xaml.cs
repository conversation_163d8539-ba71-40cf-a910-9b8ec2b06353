using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Doctors;

namespace AqlanCenterProApp.Views.Doctors
{
    /// <summary>
    /// Interaction logic for DoctorsView.xaml
    /// </summary>
    public partial class DoctorsView : UserControl
    {
        public DoctorsView()
        {
            InitializeComponent();
        }

        public DoctorsView(DoctorsListViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        private void DoctorsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is DoctorsListViewModel viewModel)
                {
                    // تحميل البيانات بشكل آمن مع timeout
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            using var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(10));
                            await Task.Delay(100, cts.Token); // تأخير قصير للسماح للواجهة بالتحميل

                            await Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.RefreshAsync();
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في RefreshAsync: {ex.Message}");
                                    // إضافة بيانات وهمية في حالة الفشل
                                    AddSampleData(viewModel);
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
                            await Dispatcher.InvokeAsync(() => AddSampleData(viewModel));
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في DoctorsView_Loaded: {ex.Message}");
            }
        }

        private void AddSampleData(DoctorsListViewModel viewModel)
        {
            try
            {
                // إضافة بيانات وهمية للاختبار
                var sampleDoctors = new List<Doctor>
                {
                    new Doctor
                    {
                        Id = 1,
                        FullName = "د. أحمد محمد",
                        Specialization = "طبيب أسنان عام",
                        Phone = "01234567890",
                        Mobile = "01234567890",
                        Email = "<EMAIL>",
                        IsActive = true,
                        Status = "نشط",
                        JoinDate = DateTime.Now.AddMonths(-6),
                        ContractType = "دائم",
                        CommissionPercentage = 30,
                        TotalPatientsCount = 25,
                        CompletedSessionsCount = 45,
                        TotalEarnings = 15000,
                        Rating = 4.5m
                    },
                    new Doctor
                    {
                        Id = 2,
                        FullName = "د. فاطمة علي",
                        Specialization = "أخصائي تقويم أسنان",
                        Phone = "01234567891",
                        Mobile = "01234567891",
                        Email = "<EMAIL>",
                        IsActive = true,
                        Status = "نشط",
                        JoinDate = DateTime.Now.AddMonths(-12),
                        ContractType = "دائم",
                        CommissionPercentage = 35,
                        TotalPatientsCount = 40,
                        CompletedSessionsCount = 80,
                        TotalEarnings = 28000,
                        Rating = 4.8m
                    }
                };

                viewModel.Doctors.Clear();
                foreach (var doctor in sampleDoctors)
                {
                    viewModel.Doctors.Add(doctor);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إضافة البيانات الوهمية: {ex.Message}");
            }
        }
    }
}
