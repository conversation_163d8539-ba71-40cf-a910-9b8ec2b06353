# دليل إضافة وحدات جديدة للسلايد بار الفرعي
## How to Add New Modules to Sub-Sidebar System

### 🎯 **الهدف:**
شرح كيفية إضافة وحدات جديدة وعمليات فرعية لنظام السلايد بار بطريقة سهلة ومنظمة.

---

## 📋 **خطوات إضافة وحدة جديدة:**

### 1. **إضافة الوحدة في السايدبار الرئيسي:**

#### في `SidebarControl.xaml`:
```xml
<!-- إضافة زر الوحدة الجديدة -->
<Button x:Name="NewModuleButton"
        Style="{StaticResource SidebarButtonStyle}"
        Click="OnMenuItemClick"
        Tag="NewModule">
    <StackPanel Orientation="Horizontal">
        <TextBlock Text="🆕" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
        <TextBlock Text="الوحدة الجديدة" VerticalAlignment="Center"/>
    </StackPanel>
</Button>
```

### 2. **إضافة معلومات الوحدة:**

#### في `MainWindow.xaml.cs` - وظيفة `InitializePages()`:
```csharp
private void InitializePages()
{
    _pages = new Dictionary<string, (string Title, UserControl? Control)>
    {
        // الوحدات الموجودة...
        { "NewModule", ("الوحدة الجديدة", null) }, // إضافة هنا
    };
}
```

#### في وظيفة `GetModuleInfo()`:
```csharp
private ModuleInfo? GetModuleInfo(string moduleKey)
{
    return moduleKey switch
    {
        // الوحدات الموجودة...
        "NewModule" => new ModuleInfo("الوحدة الجديدة", "🆕"), // إضافة هنا
        _ => null
    };
}
```

### 3. **إضافة أزرار الوحدة الفرعية:**

#### في `SubSidebarControl.xaml.cs` - وظيفة `GetModuleButtons()`:
```csharp
private List<ButtonGroup> GetModuleButtons(string moduleKey)
{
    return moduleKey switch
    {
        // الوحدات الموجودة...
        "NewModule" => GetNewModuleButtons(), // إضافة هنا
        _ => new List<ButtonGroup>()
    };
}
```

#### إنشاء وظيفة الأزرار الجديدة:
```csharp
private List<ButtonGroup> GetNewModuleButtons()
{
    return new List<ButtonGroup>
    {
        new ButtonGroup
        {
            SectionTitle = "العمليات الأساسية",
            Buttons = new List<ButtonInfo>
            {
                new ButtonInfo("➕", "إضافة جديد", "Add"),
                new ButtonInfo("📋", "عرض القائمة", "List"),
                new ButtonInfo("🔍", "البحث", "Search"),
                new ButtonInfo("📊", "الإحصائيات", "Statistics")
            }
        },
        new ButtonGroup
        {
            SectionTitle = "التقارير",
            Buttons = new List<ButtonInfo>
            {
                new ButtonInfo("📈", "التقارير", "Reports"),
                new ButtonInfo("📤", "التصدير", "Export")
            }
        }
    };
}
```

### 4. **إضافة عناوين العمليات:**

#### في `MainWindow.xaml.cs` - وظيفة `GetActionTitle()`:
```csharp
private string GetActionTitle(string action)
{
    return action switch
    {
        // العمليات الموجودة...
        
        // عمليات الوحدة الجديدة
        "Add" => "إضافة جديد",
        "List" => "عرض القائمة", 
        "Search" => "البحث",
        "Statistics" => "الإحصائيات",
        "Reports" => "التقارير",
        "Export" => "التصدير",
        
        _ => action
    };
}
```

### 5. **إضافة محتوى الوحدة:**

#### في وظيفة `GetSubPageContent()`:
```csharp
private UserControl GetSubPageContent(string module, string action)
{
    return module switch
    {
        // الوحدات الموجودة...
        "NewModule" => CreateNewModuleSubContent(action), // إضافة هنا
        _ => CreateGenericSubContent(module, action)
    };
}
```

#### إنشاء وظيفة المحتوى:
```csharp
private UserControl CreateNewModuleSubContent(string action)
{
    var control = new UserControl();
    var grid = new Grid();

    var content = action switch
    {
        "Add" => "نموذج إضافة عنصر جديد",
        "List" => "قائمة جميع العناصر",
        "Search" => "البحث المتقدم",
        "Statistics" => "إحصائيات الوحدة",
        "Reports" => "تقارير الوحدة",
        "Export" => "تصدير البيانات",
        _ => $"محتوى {action}"
    };

    var textBlock = new TextBlock
    {
        Text = $"{content}\n\nسيتم تطوير هذا القسم قريباً",
        FontFamily = (System.Windows.Media.FontFamily)FindResource("ArabicFontFamily"),
        FontSize = 16,
        TextAlignment = TextAlignment.Center,
        VerticalAlignment = VerticalAlignment.Center,
        HorizontalAlignment = HorizontalAlignment.Center,
        Foreground = (System.Windows.Media.Brush)FindResource("DarkGrayBrush")
    };

    grid.Children.Add(textBlock);
    control.Content = grid;
    return control;
}
```

---

## 🎨 **تخصيص التصميم:**

### إضافة أيقونات مخصصة:
```csharp
// استخدام أيقونات Unicode
"📊" // للإحصائيات
"👥" // للمستخدمين  
"💰" // للمالية
"🔧" // للإعدادات
"📋" // للقوائم
"🔍" // للبحث
"➕" // للإضافة
"📤" // للتصدير
```

### تخصيص الألوان:
```xml
<!-- في SubSidebarControl.xaml -->
<Setter Property="Foreground" Value="{StaticResource CustomColorBrush}"/>
```

---

## 📝 **مثال كامل - وحدة المخزون:**

### 1. في SidebarControl.xaml:
```xml
<Button x:Name="InventoryButton"
        Style="{StaticResource SidebarButtonStyle}"
        Click="OnMenuItemClick"
        Tag="Inventory">
    <StackPanel Orientation="Horizontal">
        <TextBlock Text="📦" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
        <TextBlock Text="المخزون" VerticalAlignment="Center"/>
    </StackPanel>
</Button>
```

### 2. في MainWindow.xaml.cs:
```csharp
// في InitializePages()
{ "Inventory", ("إدارة المخزون", null) },

// في GetModuleInfo()
"Inventory" => new ModuleInfo("المخزون", "📦"),
```

### 3. في SubSidebarControl.xaml.cs:
```csharp
// في GetModuleButtons()
"Inventory" => GetInventoryButtons(),

// الوظيفة الجديدة
private List<ButtonGroup> GetInventoryButtons()
{
    return new List<ButtonGroup>
    {
        new ButtonGroup
        {
            SectionTitle = "إدارة المخزون",
            Buttons = new List<ButtonInfo>
            {
                new ButtonInfo("📦", "عرض المخزون", "ViewStock"),
                new ButtonInfo("➕", "إضافة صنف", "AddItem"),
                new ButtonInfo("🔄", "حركة المخزون", "StockMovement"),
                new ButtonInfo("⚠️", "تنبيهات النفاد", "LowStockAlerts")
            }
        },
        new ButtonGroup
        {
            SectionTitle = "المشتريات",
            Buttons = new List<ButtonInfo>
            {
                new ButtonInfo("🛒", "طلبات الشراء", "PurchaseOrders"),
                new ButtonInfo("📋", "الموردين", "Suppliers"),
                new ButtonInfo("💰", "فواتير الشراء", "PurchaseInvoices")
            }
        }
    };
}
```

---

## ⚡ **نصائح للتطوير:**

### ✅ **أفضل الممارسات:**
1. **استخدم أسماء واضحة:** للوحدات والعمليات
2. **نظم الأزرار منطقياً:** في مجموعات ذات معنى
3. **اختر أيقونات مناسبة:** تعبر عن الوظيفة
4. **اكتب أوصاف واضحة:** للعمليات والأزرار

### ✅ **تجنب الأخطاء الشائعة:**
1. **لا تنس إضافة الوحدة في جميع الأماكن المطلوبة**
2. **تأكد من تطابق أسماء المفاتيح (Keys)**
3. **اختبر الوحدة بعد الإضافة مباشرة**
4. **استخدم نفس نمط التسمية للاتساق**

### ✅ **للتطوير المستقبلي:**
1. **فكر في التوسعات المحتملة**
2. **اجعل الكود قابل للصيانة**
3. **وثق التغييرات التي تقوم بها**
4. **اختبر على أحجام شاشة مختلفة**

---

## 🔧 **اختبار الوحدة الجديدة:**

### خطوات الاختبار:
1. **البناء:** `dotnet build`
2. **التشغيل:** `dotnet run`
3. **النقر على الوحدة:** في السايدبار الرئيسي
4. **التحقق من ظهور السلايد بار الفرعي**
5. **اختبار جميع الأزرار الفرعية**
6. **التحقق من تحديث المحتوى**

### علامات النجاح:
- ✅ الوحدة تظهر في السايدبار الرئيسي
- ✅ السلايد بار الفرعي يظهر عند النقر
- ✅ جميع الأزرار الفرعية تعمل
- ✅ المحتوى يتحدث حسب الاختيار
- ✅ العناوين تظهر بشكل صحيح

---

**🎯 باتباع هذا الدليل، يمكنك إضافة أي وحدة جديدة بسهولة ومرونة!**

**آخر تحديث:** 2024-12-23
