<UserControl x:Class="AqlanCenterProApp.Views.Roles.RolesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Roles"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft"
             Background="#F5F5F5">

    <UserControl.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="True"/>
            <Setter Property="CanUserResizeColumns" Value="True"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
        </Style>

        <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,12"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="إدارة الأدوار والصلاحيات" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Foreground="#333333"
                             VerticalAlignment="Center"/>
                    <TextBlock Text="|" 
                             FontSize="24" 
                             Foreground="#CCCCCC" 
                             Margin="15,0"
                             VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة الأدوار وتوزيع الصلاحيات" 
                             FontSize="16" 
                             Foreground="#666666"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="إضافة دور جديد" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Command="{Binding AddRoleCommand}"/>
                    <Button Content="تحديث" 
                            Style="{StaticResource ModernButtonStyle}"
                            Command="{Binding LoadRolesCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}" 
                             Style="{StaticResource ModernTextBoxStyle}"
                             Width="300"
                             VerticalAlignment="Center"
                             ToolTip="البحث في اسم الدور أو الوصف"/>
                    <Button Content="بحث" 
                            Style="{StaticResource ModernButtonStyle}"
                            Command="{Binding SearchCommand}"
                            Margin="10,5,5,5"/>
                </StackPanel>

                <CheckBox Grid.Column="1" 
                          Content="عرض الأدوار النشطة فقط" 
                          IsChecked="{Binding ShowActiveOnly}"
                          VerticalAlignment="Center"
                          Margin="20,0,0,0"
                          FontSize="14"/>
            </Grid>
        </Border>

        <!-- DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="20">
            <DataGrid ItemsSource="{Binding Roles}" 
                      SelectedItem="{Binding SelectedRole}"
                      Style="{StaticResource ModernDataGridStyle}"
                      ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="اسم الدور" 
                                        Binding="{Binding RoleName}" 
                                        Width="*"/>
                    <DataGridTextColumn Header="الوصف" 
                                        Binding="{Binding Description}" 
                                        Width="250"/>
                    <DataGridTextColumn Header="عدد المستخدمين" 
                                        Binding="{Binding Users.Count}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="تاريخ الإنشاء" 
                                        Binding="{Binding CreatedAt, StringFormat='dd/MM/yyyy'}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="الحالة" 
                                        Binding="{Binding IsActive}" 
                                        Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Text" Value="{Binding IsActive, Converter={StaticResource BoolToStringConverter}}"/>
                                <Setter Property="Foreground" Value="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Footer Actions -->
        <Border Grid.Row="3" Background="White" Padding="20" Margin="0,10,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="تعديل" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding EditRoleCommand}"
                        IsEnabled="{Binding CanEdit}"/>
                <Button Content="حذف" 
                        Style="{StaticResource DangerButtonStyle}"
                        Command="{Binding DeleteRoleCommand}"
                        IsEnabled="{Binding CanDelete}"/>
                <Button Content="تفعيل" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Command="{Binding ActivateRoleCommand}"
                        IsEnabled="{Binding CanActivate}"/>
                <Button Content="تعطيل" 
                        Style="{StaticResource WarningButtonStyle}"
                        Command="{Binding DeactivateRoleCommand}"
                        IsEnabled="{Binding CanDeactivate}"/>
                <Button Content="إدارة الصلاحيات" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding ManagePermissionsCommand}"
                        IsEnabled="{Binding CanEdit}"/>
                <Button Content="عرض المستخدمين" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding ViewUsersInRoleCommand}"
                        IsEnabled="{Binding CanViewUsers}"/>
                <Button Content="تصدير" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding ExportCommand}"/>
                <Button Content="طباعة" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding PrintCommand}"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="4" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" 
                            Width="100" 
                            Height="4" 
                            Margin="0,0,0,10"/>
                <TextBlock Text="جاري التحميل..." 
                          Foreground="White" 
                          FontSize="16" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl> 