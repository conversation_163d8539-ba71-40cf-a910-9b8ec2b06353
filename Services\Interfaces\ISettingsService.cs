using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface ISettingsService
    {
        // إعدادات العيادة
        Task<ClinicSettings> GetClinicSettingsAsync();
        Task<bool> UpdateClinicSettingsAsync(ClinicSettings settings);
        
        // الإعدادات العامة
        Task<SystemSettings> GetSystemSettingsAsync();
        Task<bool> UpdateSystemSettingsAsync(SystemSettings settings);
        
        // إعدادات الإشعارات
        Task<NotificationSettings> GetNotificationSettingsAsync();
        Task<bool> UpdateNotificationSettingsAsync(NotificationSettings settings);
        
        // القوائم المساعدة
        Task<List<SystemLookup>> GetLookupsByTypeAsync(string lookupType);
        Task<SystemLookup?> GetLookupByIdAsync(int id);
        Task<bool> AddLookupAsync(SystemLookup lookup);
        Task<bool> UpdateLookupAsync(SystemLookup lookup);
        Task<bool> DeleteLookupAsync(int id);
        Task<bool> ReorderLookupsAsync(List<int> lookupIds);
        
        // استيراد/تصدير الإعدادات
        Task<bool> ExportSettingsAsync(string filePath);
        Task<bool> ImportSettingsAsync(string filePath);
        Task<string> GetSettingsProfileAsync();
        
        // التحقق من كلمة المرور
        Task<bool> ValidateSettingsPasswordAsync(string password);
        
        // تطبيق الإعدادات
        Task<bool> ApplySettingsAsync();
        
        // إعادة تعيين الإعدادات
        Task<bool> ResetToDefaultsAsync();
        
        // التحقق من التحديثات
        Task<bool> CheckForUpdatesAsync();
        Task<bool> DownloadUpdateAsync(string updateUrl);
        
        // إعدادات النسخ الاحتياطي
        Task<string> GetBackupPathAsync();
        Task<bool> SetBackupPathAsync(string path);
        Task<bool> TestBackupPathAsync(string path);
    }
} 