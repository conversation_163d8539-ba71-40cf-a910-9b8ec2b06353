<Window x:Class="AqlanCenterProApp.Views.Invoices.AddEditInvoiceView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AqlanCenterProApp.Views.Invoices"
        xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
        xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- المحولات -->
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط DataGrid -->
        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#EEEEEE"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
        </Style>

        <!-- أنماط DataGridColumnHeader -->
        <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#1976D2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- ملخص مختصر للفاتورة -->
        <Border Grid.Row="0" Background="#E3F2FD" CornerRadius="8" Padding="16" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="المريض:" FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedPatient.FullName}" Foreground="#1976D2" FontWeight="Bold" Margin="8,0,0,0"/>
                <TextBlock Text="| رقم الفاتورة:" FontWeight="Bold" Margin="16,0,0,0"/>
                <TextBlock Text="{Binding Invoice.InvoiceNumber}" Margin="8,0,0,0"/>
                <TextBlock Text="| التاريخ:" FontWeight="Bold" Margin="16,0,0,0"/>
                <TextBlock Text="{Binding Invoice.InvoiceDate, StringFormat=dd/MM/yyyy}" Margin="8,0,0,0"/>
                <TextBlock Text="| الإجمالي:" FontWeight="Bold" Margin="16,0,0,0"/>
                <TextBlock Text="{Binding Invoice.TotalAmount, StringFormat=N0}" Foreground="#388E3C" FontWeight="Bold" Margin="8,0,0,0"/>
            </StackPanel>
        </Border>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" PanningMode="VerticalOnly">
            <StackPanel>
                <!-- معلومات الفاتورة الأساسية -->
                <Border Background="White" BorderBrush="#DDDDDD" BorderThickness="0,0,0,1" Padding="20,15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <!-- المريض مع زر إضافة -->
                        <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,20,0" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="المريض:" FontWeight="Bold" Margin="0,0,5,0"/>
                            <ComboBox x:Name="PatientsComboBox"
                                      ItemsSource="{Binding Patients}"
                                      SelectedItem="{Binding SelectedPatient, Mode=TwoWay}"
                                      DisplayMemberPath="FullName"
                                      IsEditable="True"
                                      IsTextSearchEnabled="True"
                                      Height="35" Width="220" Padding="10,5"
                                      BorderBrush="#DDDDDD" BorderThickness="1"
                                      Margin="0,0,5,0"
                                      ToolTip="ابحث باسم المريض أو رقم الملف"/>
                            <Button Content="➕" Width="32" Height="32" Margin="0,0,0,0" Padding="0"
                                    Command="{Binding AddNewPatientCommand}"
                                    ToolTip="إضافة مريض جديد"
                                    Background="#4CAF50" Foreground="White" FontWeight="Bold"
                                    BorderThickness="0"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="0" Margin="0,0,20,0">
                            <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Invoice.InvoiceNumber}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1" IsReadOnly="True"/>
                        </StackPanel>
                        <StackPanel Grid.Column="2" Grid.Row="0">
                            <TextBlock Text="تاريخ الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding Invoice.InvoiceDate}" Height="35" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <!-- العمود الثاني -->
                        <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,20,20,0">
                            <TextBlock Text="تاريخ الاستحقاق:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding Invoice.DueDate}" Height="35" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="1" Grid.Row="1" Margin="0,20,20,0">
                            <TextBlock Text="طريقة الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding PaymentMethods}" SelectedItem="{Binding Invoice.PaymentMethod}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                        </StackPanel>
                        <StackPanel Grid.Column="2" Grid.Row="1" Margin="0,20,0,0">
                            <TextBlock Text="الحالة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Invoice.InvoiceStatus}" Height="35" Padding="10,5" BorderBrush="#DDDDDD" BorderThickness="1" IsReadOnly="True"/>
                        </StackPanel>
                    </Grid>
                </Border>
                <!-- عناصر الفاتورة -->
                <Border Background="White" Margin="20,10" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <!-- شريط أدوات العناصر -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,10">
                            <Button Content="🖨️ طباعة" Command="{Binding PrintCommand}" Style="{StaticResource ActionButtonStyle}" Background="#1976D2" Width="100" Margin="0,0,10,0"/>
                            <Button Content="👁️ معاينة" Command="{Binding PreviewCommand}" Style="{StaticResource ActionButtonStyle}" Background="#607D8B" Width="100" Margin="0,0,10,0"/>
                            <Button Content="➕ إضافة عنصر" Command="{Binding AddItemCommand}" Style="{StaticResource ActionButtonStyle}" Background="#4CAF50" Width="120"/>
                            <Button Content="🗑️ حذف عنصر" Command="{Binding RemoveItemCommand}" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Width="120"/>
                            <Button Content="↩️ تراجع" Command="{Binding UndoRemoveItemCommand}" Style="{StaticResource ActionButtonStyle}" Background="#FFA000" Width="100" Visibility="{Binding CanUndoRemove, Converter={StaticResource BoolToVisibilityConverter}}"/>
                        </StackPanel>
                        <!-- شريط البحث عن الخدمات -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="🔍 البحث عن خدمة/منتج:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox x:Name="ServicesComboBox" ItemsSource="{Binding AvailableServices}" SelectedItem="{Binding SelectedService, Mode=TwoWay}" DisplayMemberPath="Name" IsEditable="True" IsTextSearchEnabled="True" Width="250" Height="32" Padding="8,2" ToolTip="ابحث باسم الخدمة أو المنتج"/>
                            <Button Content="إضافة للخدمات" Command="{Binding AddServiceToInvoiceCommand}" Margin="8,0,0,0" Width="90" Height="32"/>
                        </StackPanel>
                        <!-- جدول العناصر -->
                        <DataGrid Grid.Row="2" ItemsSource="{Binding InvoiceItems}" SelectedItem="{Binding SelectedItem}" Style="{StaticResource DataGridStyle}" ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}" RowHeight="35" MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding ItemDescription}" Width="*"/>
                                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}" Width="100"/>
                                <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, UpdateSourceTrigger=PropertyChanged, StringFormat=N0}" Width="120"/>
                                <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalPrice, StringFormat=N0}" Width="120"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
                <!-- المبالغ -->
                <Border Background="White" Margin="20,0,20,10" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <!-- المبالغ التفصيلية -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <StackPanel Margin="0,0,50,0">
                                <TextBlock Text="المجموع الفرعي:" FontWeight="Bold"/>
                                <TextBlock Text="{Binding Invoice.SubTotal, StringFormat=N0}" FontSize="16" Foreground="#2196F3"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,50,0">
                                <TextBlock Text="الخصم:" FontWeight="Bold"/>
                                <TextBox Text="{Binding DiscountAmount, UpdateSourceTrigger=PropertyChanged}" Width="100" Height="30" Padding="5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,50,0">
                                <TextBlock Text="الضريبة:" FontWeight="Bold"/>
                                <TextBox Text="{Binding TaxAmount, UpdateSourceTrigger=PropertyChanged}" Width="100" Height="30" Padding="5" BorderBrush="#DDDDDD" BorderThickness="1"/>
                            </StackPanel>
                            <StackPanel>
                                <TextBlock Text="الإجمالي:" FontWeight="Bold" FontSize="16"/>
                                <TextBlock Text="{Binding Invoice.TotalAmount, StringFormat=N0}" FontSize="18" Foreground="#4CAF50" FontWeight="Bold"/>
                            </StackPanel>
                        </StackPanel>
                        <!-- أزرار الحفظ والإلغاء -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button Content="{Binding SaveButtonText}" Command="{Binding SaveCommand}" Style="{StaticResource ActionButtonStyle}" Background="#4CAF50" Width="100"/>
                            <Button Content="إلغاء" Command="{Binding CancelCommand}" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Width="100"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>
        <!-- مؤشر التحميل -->
        <Grid Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="10" Margin="0,0,0,10"/>
                <TextBlock Text="جاري الحفظ..." Foreground="White" HorizontalAlignment="Center" FontSize="16"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 