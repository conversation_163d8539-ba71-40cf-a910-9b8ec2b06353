<Window x:Class="AqlanCenterProApp.Views.Users.ChangePasswordWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تغيير كلمة المرور" 
        Height="400" 
        Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" CornerRadius="8" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="تغيير كلمة المرور" 
                         FontSize="20" 
                         FontWeight="Bold" 
                         Foreground="#333333"
                         HorizontalAlignment="Center"/>
                <TextBlock Text="أدخل كلمة المرور الحالية والجديدة" 
                         FontSize="14" 
                         Foreground="#666666"
                         HorizontalAlignment="Center"
                         Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Background="White" Padding="20" CornerRadius="8">
            <StackPanel>
                <TextBlock Text="كلمة المرور الحالية:" 
                         FontWeight="SemiBold" 
                         Margin="0,0,0,5"/>
                <PasswordBox x:Name="CurrentPasswordBox" 
                           Style="{StaticResource ModernTextBoxStyle}"
                           Margin="0,0,0,15"/>

                <TextBlock Text="كلمة المرور الجديدة:" 
                         FontWeight="SemiBold" 
                         Margin="0,0,0,5"/>
                <PasswordBox x:Name="NewPasswordBox" 
                           Style="{StaticResource ModernTextBoxStyle}"
                           Margin="0,0,0,15"/>

                <TextBlock Text="تأكيد كلمة المرور الجديدة:" 
                         FontWeight="SemiBold" 
                         Margin="0,0,0,5"/>
                <PasswordBox x:Name="ConfirmPasswordBox" 
                           Style="{StaticResource ModernTextBoxStyle}"
                           Margin="0,0,0,15"/>

                <!-- Password Requirements -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="4" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="متطلبات كلمة المرور:" 
                                 FontWeight="SemiBold" 
                                 Foreground="#333333"
                                 Margin="0,0,0,5"/>
                        <TextBlock Text="• 8 أحرف على الأقل" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                        <TextBlock Text="• حرف كبير واحد على الأقل" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                        <TextBlock Text="• حرف صغير واحد على الأقل" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                        <TextBlock Text="• رقم واحد على الأقل" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                        <TextBlock Text="• رمز خاص واحد على الأقل" 
                                 FontSize="12" 
                                 Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                         Foreground="#F44336" 
                         FontSize="12" 
                         TextWrapping="Wrap"
                         Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                         Margin="0,0,0,10"/>
            </StackPanel>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20" CornerRadius="8" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تغيير كلمة المرور" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Command="{Binding ChangePasswordCommand}"
                        IsEnabled="{Binding CanChangePassword}"/>
                <Button Content="إلغاء" 
                        Style="{StaticResource ModernButtonStyle}"
                        Command="{Binding CancelCommand}"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" 
                            Width="100" 
                            Height="4" 
                            Margin="0,0,0,10"/>
                <TextBlock Text="جاري تغيير كلمة المرور..." 
                          Foreground="White" 
                          FontSize="16" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window> 