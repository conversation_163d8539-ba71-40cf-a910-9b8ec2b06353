using System;
using System.Windows;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Doctors;
using AqlanCenterProApp.Views.Doctors;

namespace AqlanCenterProApp.Views.Doctors
{
    /// <summary>
    /// Interaction logic for DoctorDetailsWindow.xaml
    /// </summary>
    public partial class DoctorDetailsWindow : Window
    {
        public DoctorDetailsWindow(Doctor doctor)
        {
            InitializeComponent();

            try
            {
                // إنشاء ViewModel
                var viewModel = new DoctorDetailsViewModel(doctor);
                DataContext = viewModel;

                // ربط الأحداث
                viewModel.CloseRequested += (sender, result) =>
                {
                    DialogResult = result;
                    Close();
                };

                viewModel.EditRequested += (sender, doctorToEdit) =>
                {
                    try
                    {
                        var editWindow = new AddEditDoctorWindow(doctorToEdit);
                        var result = editWindow.ShowDialog();

                        if (result == true)
                        {
                            // تحديث البيانات المعروضة
                            // يمكن إضافة منطق تحديث البيانات هنا
                            System.Windows.MessageBox.Show("تم تحديث بيانات الطبيب بنجاح", "نجح",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Windows.MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                };

                viewModel.StatisticsRequested += (sender, doctorForStats) =>
                {
                    try
                    {
                        var statisticsWindow = new DoctorStatisticsWindow(doctorForStats);
                        statisticsWindow.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        System.Windows.MessageBox.Show($"خطأ في فتح نافذة الإحصائيات: {ex.Message}", "خطأ",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                };

                viewModel.SessionsRequested += (sender, doctorForSessions) =>
                {
                    try
                    {
                        var sessionsWindow = new DoctorSessionsWindow(doctorForSessions);
                        sessionsWindow.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        System.Windows.MessageBox.Show($"خطأ في فتح نافذة الجلسات: {ex.Message}", "خطأ",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                };
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                Close();
            }
        }
    }
}
