using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AqlanCenterProApp.Helpers
{
    /// <summary>
    /// مساعد للتسلسل الآمن باستخدام JSON بدلاً من BinaryFormatter
    /// </summary>
    public static class SerializationHelper
    {
        /// <summary>
        /// إعدادات JSON الافتراضية للنظام
        /// </summary>
        public static JsonSerializerOptions DefaultOptions => new()
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            AllowTrailingCommas = true,
            PropertyNameCaseInsensitive = true,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            Converters =
            {
                new JsonStringEnumConverter(),
                new DateTimeConverter(),
                new TimeSpanConverter()
            }
        };

        /// <summary>
        /// تسلسل كائن إلى JSON
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="obj">الكائن المراد تسلسله</param>
        /// <param name="options">إعدادات التسلسل (اختيارية)</param>
        /// <returns>JSON string</returns>
        public static string SerializeToJson<T>(T obj, JsonSerializerOptions? options = null)
        {
            try
            {
                return JsonSerializer.Serialize(obj, options ?? DefaultOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تسلسل الكائن: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// إلغاء تسلسل JSON إلى كائن
        /// </summary>
        /// <typeparam name="T">نوع الكائن المطلوب</typeparam>
        /// <param name="json">JSON string</param>
        /// <param name="options">إعدادات إلغاء التسلسل (اختيارية)</param>
        /// <returns>الكائن أو null في حالة الفشل</returns>
        public static T? DeserializeFromJson<T>(string json, JsonSerializerOptions? options = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(json))
                    return default;

                return JsonSerializer.Deserialize<T>(json, options ?? DefaultOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إلغاء تسلسل JSON: {ex.Message}");
                return default;
            }
        }

        /// <summary>
        /// تسلسل كائن إلى ملف JSON
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="obj">الكائن المراد تسلسله</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="options">إعدادات التسلسل (اختيارية)</param>
        /// <returns>true إذا نجح التسلسل</returns>
        public static async Task<bool> SerializeToFileAsync<T>(T obj, string filePath, JsonSerializerOptions? options = null)
        {
            try
            {
                var json = SerializeToJson(obj, options);
                if (string.IsNullOrEmpty(json))
                    return false;

                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حفظ الملف: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إلغاء تسلسل ملف JSON إلى كائن
        /// </summary>
        /// <typeparam name="T">نوع الكائن المطلوب</typeparam>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="options">إعدادات إلغاء التسلسل (اختيارية)</param>
        /// <returns>الكائن أو null في حالة الفشل</returns>
        public static async Task<T?> DeserializeFromFileAsync<T>(string filePath, JsonSerializerOptions? options = null)
        {
            try
            {
                if (!File.Exists(filePath))
                    return default;

                var json = await File.ReadAllTextAsync(filePath);
                return DeserializeFromJson<T>(json, options);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في قراءة الملف: {ex.Message}");
                return default;
            }
        }

        /// <summary>
        /// تحويل كائن إلى byte array باستخدام JSON
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="obj">الكائن المراد تحويله</param>
        /// <param name="options">إعدادات التسلسل (اختيارية)</param>
        /// <returns>byte array</returns>
        public static byte[] SerializeToBytes<T>(T obj, JsonSerializerOptions? options = null)
        {
            try
            {
                return JsonSerializer.SerializeToUtf8Bytes(obj, options ?? DefaultOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحويل الكائن إلى bytes: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// تحويل byte array إلى كائن باستخدام JSON
        /// </summary>
        /// <typeparam name="T">نوع الكائن المطلوب</typeparam>
        /// <param name="bytes">byte array</param>
        /// <param name="options">إعدادات إلغاء التسلسل (اختيارية)</param>
        /// <returns>الكائن أو null في حالة الفشل</returns>
        public static T? DeserializeFromBytes<T>(byte[] bytes, JsonSerializerOptions? options = null)
        {
            try
            {
                if (bytes == null || bytes.Length == 0)
                    return default;

                return JsonSerializer.Deserialize<T>(bytes, options ?? DefaultOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحويل bytes إلى كائن: {ex.Message}");
                return default;
            }
        }
    }

    /// <summary>
    /// محول مخصص للتاريخ والوقت
    /// </summary>
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return DateTime.Parse(reader.GetString()!);
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }

    /// <summary>
    /// محول مخصص للمدة الزمنية
    /// </summary>
    public class TimeSpanConverter : JsonConverter<TimeSpan>
    {
        public override TimeSpan Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return TimeSpan.Parse(reader.GetString()!);
        }

        public override void Write(Utf8JsonWriter writer, TimeSpan value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }
}