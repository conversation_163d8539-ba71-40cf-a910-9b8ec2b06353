using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة إدارة سندات القبض
    /// </summary>
    public class ReceiptService : IReceiptService
    {
        private readonly AqlanCenterDbContext _context;

        public ReceiptService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        #region العمليات الأساسية (CRUD)

        public async Task<IEnumerable<Receipt>> GetAllReceiptsAsync()
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Include(r => r.Session)
                .Include(r => r.Invoice)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<Receipt?> GetReceiptByIdAsync(int id)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Include(r => r.Session)
                .Include(r => r.Invoice)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<Receipt?> GetReceiptByNumberAsync(string receiptNumber)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Include(r => r.Session)
                .Include(r => r.Invoice)
                .FirstOrDefaultAsync(r => r.ReceiptNumber == receiptNumber);
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByPatientAsync(int patientId)
        {
            return await _context.Receipts
                .Include(r => r.Session)
                .Include(r => r.Invoice)
                .Where(r => r.PatientId == patientId)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<Receipt> CreateReceiptAsync(Receipt receipt)
        {
            // حماية من الازدواجية: لا تسمح بإضافة إيصال مكرر لنفس المريض ونفس النوع والمبلغ في نفس اليوم
            bool exists = await _context.Receipts.AnyAsync(r =>
                r.PatientId == receipt.PatientId &&
                r.Amount == receipt.Amount &&
                r.Purpose == receipt.Purpose &&
                r.ReceiptDate.Date == receipt.ReceiptDate.Date &&
                r.Status != "ملغي");
            if (exists)
                throw new InvalidOperationException("يوجد إيصال مالي بنفس البيانات لهذا المريض في نفس اليوم.");

            // التحقق من صحة البيانات
            var validation = await ValidateReceiptAsync(receipt);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // تعيين رقم السند إذا لم يكن محدداً
            if (string.IsNullOrEmpty(receipt.ReceiptNumber))
                receipt.ReceiptNumber = await GetNextReceiptNumberAsync();

            // التحقق من صحة المبلغ
            var amountValidation = await ValidatePaymentAmountAsync(receipt.PatientId, receipt.Amount);
            if (!amountValidation.IsValid)
                throw new InvalidOperationException(amountValidation.ErrorMessage);

            _context.Receipts.Add(receipt);
            await _context.SaveChangesAsync();

            // توثيق العملية في سجل التدقيق (بسيط)
            try
            {
                var log = $"[إضافة إيصال] للمريض: {receipt.PatientId} - المبلغ: {receipt.Amount} - النوع: {receipt.Purpose} - المستخدم: {receipt.IssuedBy} - التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm}";
                System.IO.File.AppendAllText("audit_log.txt", log + "\n");
            }
            catch { }

            // تحديث رصيد المريض إذا كان مرتبطاً بفاتورة
            if (receipt.InvoiceId.HasValue)
            {
                await UpdateInvoicePaymentAsync(receipt.InvoiceId.Value, receipt.Amount);
            }

            return receipt;
        }

        public async Task<Receipt> UpdateReceiptAsync(Receipt receipt)
        {
            // التحقق من صحة البيانات
            var validation = await ValidateReceiptAsync(receipt);
            if (!validation.IsValid)
                throw new InvalidOperationException(validation.ErrorMessage);

            // جلب الإيصال الأصلي من قاعدة البيانات
            var original = await _context.Receipts.AsNoTracking().FirstOrDefaultAsync(r => r.Id == receipt.Id);
            bool wasCancelled = original != null && original.Status != "ملغي" && receipt.Status == "ملغي";

            _context.Receipts.Update(receipt);
            await _context.SaveChangesAsync();

            // توثيق عملية الإلغاء في سجل التدقيق
            if (wasCancelled)
            {
                try
                {
                    var log = $"[إلغاء إيصال] رقم: {receipt.ReceiptNumber} للمريض: {receipt.PatientId} - المبلغ: {receipt.Amount} - النوع: {receipt.Purpose} - المستخدم: {receipt.IssuedBy} - التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm} - ملاحظات: {receipt.Description}";
                    System.IO.File.AppendAllText("audit_log.txt", log + "\n");
                }
                catch { }
            }

            return receipt;
        }

        public async Task<bool> DeleteReceiptAsync(int id)
        {
            var receipt = await _context.Receipts.FindAsync(id);
            if (receipt == null)
                return false;

            receipt.IsDeleted = true;
            receipt.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return true;
        }

        #endregion

        #region البحث والفلترة

        public async Task<IEnumerable<Receipt>> SearchReceiptsAsync(string searchTerm)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.ReceiptNumber.Contains(searchTerm) ||
                           (r.Patient != null && r.Patient.FullName != null && r.Patient.FullName.Contains(searchTerm)) ||
                           (r.Purpose != null && r.Purpose.Contains(searchTerm)) ||
                           (r.ReceivedBy != null && r.ReceivedBy.Contains(searchTerm)))
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByDateAsync(DateTime date)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.ReceiptDate.Date == date.Date)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.ReceiptDate.Date >= startDate.Date && r.ReceiptDate.Date <= endDate.Date)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByPaymentMethodAsync(string paymentMethod)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.PaymentMethod == paymentMethod)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByStatusAsync(string status)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.Status == status)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        #endregion

        #region الترقيم التلقائي

        public async Task<string> GetNextReceiptNumberAsync()
        {
            var lastReceipt = await _context.Receipts
                .OrderByDescending(r => r.ReceiptNumber)
                .FirstOrDefaultAsync();

            if (lastReceipt == null)
                return "RCP-0001";

            var lastNumber = lastReceipt.ReceiptNumber;
            if (lastNumber.StartsWith("RCP-") && int.TryParse(lastNumber.Substring(4), out int number))
            {
                return $"RCP-{(number + 1):D4}";
            }

            return $"RCP-{DateTime.Now:yyyyMMdd}-{1:D3}";
        }

        public async Task<bool> IsReceiptNumberAvailableAsync(string receiptNumber)
        {
            return !await _context.Receipts.AnyAsync(r => r.ReceiptNumber == receiptNumber);
        }

        #endregion

        #region الإحصائيات والتقارير

        public async Task<ReceiptStatistics> GetReceiptStatisticsAsync()
        {
            var receipts = await _context.Receipts.ToListAsync();
            var statistics = new ReceiptStatistics
            {
                TotalReceipts = receipts.Count,
                TotalAmount = receipts.Sum(r => r.Amount),
                CompletedAmount = receipts.Where(r => r.Status == "مكتمل").Sum(r => r.Amount),
                PendingAmount = receipts.Where(r => r.Status == "معلق").Sum(r => r.Amount)
            };

            // إحصائيات حسب الحالة
            statistics.CompletedReceipts = receipts.Count(r => r.Status == "مكتمل");
            statistics.PendingReceipts = receipts.Count(r => r.Status == "معلق");
            statistics.CancelledReceipts = receipts.Count(r => r.Status == "ملغي");

            // إحصائيات حسب طريقة الدفع
            var paymentMethods = receipts.GroupBy(r => r.PaymentMethod);
            foreach (var method in paymentMethods)
            {
                statistics.PaymentsByMethod[method.Key] = method.Sum(r => r.Amount);
            }

            // إحصائيات حسب الشهر
            var currentYear = DateTime.Now.Year;
            for (int month = 1; month <= 12; month++)
            {
                var monthReceipts = receipts.Where(r => r.ReceiptDate.Year == currentYear && r.ReceiptDate.Month == month);
                var monthKey = $"{currentYear}-{month:D2}";
                statistics.ReceiptsByMonth[monthKey] = monthReceipts.Count();
            }

            return statistics;
        }

        public async Task<decimal> GetTotalPaymentsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Receipts
                .Where(r => r.ReceiptDate.Date >= startDate.Date && r.ReceiptDate.Date <= endDate.Date)
                .SumAsync(r => r.Amount);
        }

        public async Task<Dictionary<string, decimal>> GetPaymentsByMethodAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Receipts
                .Where(r => r.ReceiptDate.Date >= startDate.Date && r.ReceiptDate.Date <= endDate.Date)
                .GroupBy(r => r.PaymentMethod)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(r => r.Amount));
        }

        public async Task<Dictionary<DateTime, decimal>> GetDailyPaymentsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Receipts
                .Where(r => r.ReceiptDate.Date >= startDate.Date && r.ReceiptDate.Date <= endDate.Date)
                .GroupBy(r => r.ReceiptDate.Date)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(r => r.Amount));
        }

        #endregion

        #region الطباعة والتصدير

        public Task<bool> PrintReceiptAsync(int receiptId)
        {
            // تنفيذ الطباعة (سيتم تنفيذها لاحقاً)
            return Task.FromResult(true);
        }

        public Task<byte[]> ExportReceiptToPdfAsync(int receiptId)
        {
            // تنفيذ التصدير إلى PDF (سيتم تنفيذها لاحقاً)
            return Task.FromResult(new byte[0]);
        }

        public Task<byte[]> ExportReceiptToExcelAsync(int receiptId)
        {
            // تنفيذ التصدير إلى Excel (سيتم تنفيذها لاحقاً)
            return Task.FromResult(new byte[0]);
        }

        #endregion

        #region الربط مع الفواتير والجلسات

        public async Task<bool> LinkReceiptToInvoiceAsync(int receiptId, int invoiceId)
        {
            var receipt = await _context.Receipts.FindAsync(receiptId);
            if (receipt == null)
                return false;

            receipt.InvoiceId = invoiceId;
            await _context.SaveChangesAsync();

            // تحديث مبالغ الفاتورة
            await UpdateInvoicePaymentAsync(invoiceId, receipt.Amount);

            return true;
        }

        public async Task<bool> LinkReceiptToSessionAsync(int receiptId, int sessionId)
        {
            var receipt = await _context.Receipts.FindAsync(receiptId);
            if (receipt == null)
                return false;

            receipt.SessionId = sessionId;
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsByInvoiceAsync(int invoiceId)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.InvoiceId == invoiceId)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetReceiptsBySessionAsync(int sessionId)
        {
            return await _context.Receipts
                .Include(r => r.Patient)
                .Where(r => r.SessionId == sessionId)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        #endregion

        #region التحقق من صحة البيانات

        public async Task<(bool IsValid, string ErrorMessage)> ValidateReceiptAsync(Receipt receipt)
        {
            if (receipt == null)
                return (false, "سند القبض مطلوب");

            if (string.IsNullOrEmpty(receipt.ReceiptNumber))
                return (false, "رقم السند مطلوب");

            if (receipt.PatientId <= 0)
                return (false, "المريض مطلوب");

            if (receipt.ReceiptDate == default)
                return (false, "تاريخ السند مطلوب");

            if (receipt.Amount <= 0)
                return (false, "المبلغ يجب أن يكون أكبر من صفر");

            if (string.IsNullOrEmpty(receipt.Purpose))
                return (false, "الغرض من الدفع مطلوب");

            if (string.IsNullOrEmpty(receipt.PaymentMethod))
                return (false, "طريقة الدفع مطلوبة");

            // التحقق من عدم تكرار رقم السند
            if (await _context.Receipts.AnyAsync(r => r.ReceiptNumber == receipt.ReceiptNumber && r.Id != receipt.Id))
                return (false, "رقم السند مستخدم بالفعل");

            return (true, string.Empty);
        }

        public Task<(bool IsValid, string ErrorMessage)> ValidatePaymentAmountAsync(int patientId, decimal amount)
        {
            if (amount <= 0)
                return Task.FromResult((false, "المبلغ يجب أن يكون أكبر من صفر"));

            // يمكن إضافة المزيد من التحققات هنا مثل الحد الأقصى للمبلغ
            return Task.FromResult((true, string.Empty));
        }

        #endregion

        #region الطرق المساعدة

        private async Task UpdateInvoicePaymentAsync(int invoiceId, decimal amount)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice != null)
            {
                invoice.PaidAmount += amount;
                invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

                if (invoice.RemainingAmount <= 0)
                    invoice.InvoiceStatus = "مدفوعة";

                await _context.SaveChangesAsync();
            }
        }

        #endregion
    }
}