using System;
using System.Windows;
using System.Windows.Controls;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Views.Patients
{
    /// <summary>
    /// نافذة حديثة لإضافة مريض جديد بتصميم مشابه لنافذة إضافة الجلسة
    /// </summary>
    public partial class ModernAddPatientWindow : Window
    {
        private readonly IPatientService _patientService;
        private Patient? _savedPatient;

        public ModernAddPatientWindow()
        {
            InitializeComponent();

            // إنشاء خدمة مؤقتة للاختبار
            _patientService = CreateTempPatientService();

            // تحميل رقم الملف التالي
            LoadNextFileNumber();
        }

        private IPatientService CreateTempPatientService()
        {
            // إنشاء خدمة مؤقتة باستخدام قاعدة البيانات الموحدة
            return Helpers.DbContextHelper.CreatePatientService();
        }

        private async void LoadNextFileNumber()
        {
            try
            {
                var nextFileNumber = await _patientService.GetNextFileNumberAsync();
                FileNumberTextBox.Text = nextFileNumber.ToString();

                // تحديث العنوان الفرعي
                PatientInfoSubtitle.Text = $"مريض جديد - رقم الملف: {nextFileNumber}";
            }
            catch
            {
                FileNumberTextBox.Text = "8500";
                PatientInfoSubtitle.Text = "مريض جديد - رقم الملف: 8500";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من البيانات المطلوبة
                if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المريض", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    FullNameTextBox.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء الحفظ
                SaveButton.IsEnabled = false;
                CancelButton.IsEnabled = false;
                SaveButton.Content = "جاري الحفظ...";

                // إنشاء كائب المريض الجديد
                var consultationFee = decimal.TryParse(ConsultationFeeTextBox.Text, out var fee) ? fee : 50.00m;

                var newPatient = new Patient
                {
                    FileNumber = int.Parse(FileNumberTextBox.Text),
                    FullName = FullNameTextBox.Text.Trim(),
                    Gender = ((ComboBoxItem)GenderComboBox.SelectedItem)?.Content?.ToString() ?? "ذكر",
                    DateOfBirth = DateOfBirthPicker.SelectedDate,
                    Phone = PhoneTextBox.Text?.Trim(),
                    Address = AddressTextBox.Text?.Trim(),
                    PatientCategory = ((ComboBoxItem)CategoryComboBox.SelectedItem)?.Content?.ToString() ?? "عادي",
                    ConsultationFee = consultationFee,
                    RegistrationDate = DateTime.Now,
                    CurrentBalance = 0,
                    FileStatus = "نشط",
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                };

                // حفظ المريض
                await _patientService.AddPatientAsync(newPatient, "System");

                // تفعيل زر طباعة سند المعاينة
                PrintReceiptButton.IsEnabled = true;

                // حفظ بيانات المريض للطباعة
                _savedPatient = newPatient;

                // إغلاق النافذة بنجاح
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المريض: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // إعادة تفعيل الأزرار
                SaveButton.IsEnabled = true;
                CancelButton.IsEnabled = true;
                SaveButton.Content = "حفظ المريض";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void PrintReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            if (_savedPatient == null)
            {
                MessageBox.Show("لا توجد بيانات مريض للطباعة", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // فتح نافذة سند المعاينة
                var receiptWindow = new ConsultationReceiptWindow(_savedPatient);
                receiptWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سند المعاينة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateConsultationReceiptHtml(Patient patient)
        {
            var html = new System.Text.StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>سند معاينة - مركز عقلان للأسنان</title>");
            html.AppendLine("<style>");

            // CSS Styles
            html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; background-color: #f8f9fa; }");
            html.AppendLine(".receipt-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; }");

            // Header Styles
            html.AppendLine(".header { background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%); color: white; padding: 30px 20px; text-align: center; position: relative; }");
            html.AppendLine(".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"40\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"2\"/></svg>') center/80px; opacity: 0.3; }");
            html.AppendLine(".logo-section { position: relative; z-index: 1; }");
            html.AppendLine(".clinic-logo { width: 60px; height: 60px; background: white; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #4A90E2; font-weight: bold; }");
            html.AppendLine(".clinic-name { font-size: 28px; font-weight: bold; margin-bottom: 5px; text-shadow: 0 2px 4px rgba(0,0,0,0.3); }");
            html.AppendLine(".clinic-subtitle { font-size: 16px; opacity: 0.9; margin-bottom: 15px; }");
            html.AppendLine(".receipt-title { font-size: 22px; font-weight: bold; background: rgba(255,255,255,0.2); padding: 10px 20px; border-radius: 25px; display: inline-block; }");

            // Content Styles
            html.AppendLine(".content { padding: 30px; }");
            html.AppendLine(".receipt-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #4A90E2; }");
            html.AppendLine(".receipt-number { font-size: 14px; color: #666; text-align: center; margin-bottom: 20px; }");

            // Patient Info Styles
            html.AppendLine(".patient-section { margin-bottom: 25px; }");
            html.AppendLine(".section-title { font-size: 18px; font-weight: bold; color: #4A90E2; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 2px solid #e9ecef; }");
            html.AppendLine(".info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }");
            html.AppendLine(".info-item { background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef; }");
            html.AppendLine(".info-label { font-size: 12px; color: #666; margin-bottom: 4px; }");
            html.AppendLine(".info-value { font-size: 16px; font-weight: 600; color: #333; }");

            // Amount Section Styles
            html.AppendLine(".amount-section { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 25px; border-radius: 10px; text-align: center; margin: 25px 0; position: relative; overflow: hidden; }");
            html.AppendLine(".amount-section::before { content: '💰'; position: absolute; top: 10px; right: 15px; font-size: 24px; opacity: 0.3; }");
            html.AppendLine(".amount-label { font-size: 16px; margin-bottom: 10px; opacity: 0.9; }");
            html.AppendLine(".amount-value { font-size: 32px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3); }");
            html.AppendLine(".amount-words { font-size: 14px; margin-top: 8px; opacity: 0.8; font-style: italic; }");

            // Signature Section
            html.AppendLine(".signature-section { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; }");
            html.AppendLine(".signature-title { font-size: 16px; font-weight: bold; color: #4A90E2; margin-bottom: 15px; }");
            html.AppendLine(".signature-line { border-bottom: 2px solid #333; width: 200px; margin: 20px auto; }");
            html.AppendLine(".signature-label { text-align: center; font-size: 14px; color: #666; margin-top: 8px; }");

            // Footer Styles
            html.AppendLine(".footer { background: #343a40; color: white; padding: 20px; text-align: center; }");
            html.AppendLine(".contact-info { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px; }");
            html.AppendLine(".contact-item { font-size: 14px; }");
            html.AppendLine(".contact-icon { margin-left: 8px; }");
            html.AppendLine(".footer-note { font-size: 12px; opacity: 0.8; border-top: 1px solid #495057; padding-top: 15px; margin-top: 15px; }");

            // Print Styles
            html.AppendLine("@media print { ");
            html.AppendLine("  body { background: white; padding: 0; }");
            html.AppendLine("  .receipt-container { box-shadow: none; border-radius: 0; }");
            html.AppendLine("  .no-print { display: none; }");
            html.AppendLine("}");

            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // Receipt Container
            html.AppendLine("<div class='receipt-container'>");

            // Header Section
            html.AppendLine("  <div class='header'>");
            html.AppendLine("    <div class='logo-section'>");
            html.AppendLine("      <div class='clinic-logo'>🦷</div>");
            html.AppendLine("      <div class='clinic-name'>مركز عقلان للأسنان</div>");
            html.AppendLine("      <div class='clinic-subtitle'>Aqlan Dental Center</div>");
            html.AppendLine("      <div class='receipt-title'>🧾 سند معاينة</div>");
            html.AppendLine("    </div>");
            html.AppendLine("  </div>");

            // Content Section
            html.AppendLine("  <div class='content'>");

            // Receipt Info
            html.AppendLine("    <div class='receipt-info'>");
            html.AppendLine($"      <div class='receipt-number'>رقم السند: {DateTime.Now:yyyyMMddHHmmss} | التاريخ: {DateTime.Now:yyyy/MM/dd} | الوقت: {DateTime.Now:HH:mm}</div>");
            html.AppendLine("    </div>");

            // Patient Information
            html.AppendLine("    <div class='patient-section'>");
            html.AppendLine("      <div class='section-title'>📋 بيانات المريض</div>");
            html.AppendLine("      <div class='info-grid'>");
            html.AppendLine("        <div class='info-item'>");
            html.AppendLine("          <div class='info-label'>اسم المريض</div>");
            html.AppendLine($"          <div class='info-value'>{patient.FullName}</div>");
            html.AppendLine("        </div>");
            html.AppendLine("        <div class='info-item'>");
            html.AppendLine("          <div class='info-label'>رقم الملف</div>");
            html.AppendLine($"          <div class='info-value'>{patient.FileNumber}</div>");
            html.AppendLine("        </div>");
            html.AppendLine("        <div class='info-item'>");
            html.AppendLine("          <div class='info-label'>رقم الهاتف</div>");
            html.AppendLine($"          <div class='info-value'>{patient.Mobile ?? patient.Phone ?? "غير محدد"}</div>");
            html.AppendLine("        </div>");
            html.AppendLine("        <div class='info-item'>");
            html.AppendLine("          <div class='info-label'>نوع الخدمة</div>");
            html.AppendLine("          <div class='info-value'>معاينة أولية</div>");
            html.AppendLine("        </div>");
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");

            // Amount Section
            // Amount Section
            var consultationFee = patient.ConsultationFee ?? 0;
            html.AppendLine("    <div class='amount-section'>");
            html.AppendLine("      <div class='amount-label'>المبلغ المدفوع</div>");
            html.AppendLine($"      <div class='amount-value'>{consultationFee:N2} ريال يمني</div>");
            html.AppendLine($"      <div class='amount-words'>({ConvertNumberToWords(consultationFee)} ريال يمني فقط لا غير)</div>");
            html.AppendLine("    </div>");

            // Signature Section
            html.AppendLine("    <div class='signature-section'>");
            html.AppendLine("      <div class='signature-title'>✍️ توقيع المستلم</div>");
            html.AppendLine("      <div class='signature-line'></div>");
            html.AppendLine("      <div class='signature-label'>توقيع وختم المسؤول</div>");
            html.AppendLine("    </div>");

            html.AppendLine("  </div>");

            // Footer Section
            html.AppendLine("  <div class='footer'>");
            html.AppendLine("    <div class='contact-info'>");
            html.AppendLine("      <div class='contact-item'>");
            html.AppendLine("        <span class='contact-icon'>📞</span> هاتف: 01-123456 | جوال: 770-245745");
            html.AppendLine("      </div>");
            html.AppendLine("      <div class='contact-item'>");
            html.AppendLine("        <span class='contact-icon'>📍</span> العنوان: صنعاء - شارع الزبيري - مجمع الأسنان");
            html.AppendLine("      </div>");
            html.AppendLine("    </div>");
            html.AppendLine("    <div class='footer-note'>");
            html.AppendLine("      <p>🌟 شكراً لثقتكم بنا - نتمنى لكم دوام الصحة والعافية 🌟</p>");
            html.AppendLine($"      <p>تم إصدار هذا السند بتاريخ: {DateTime.Now:yyyy/MM/dd} الساعة: {DateTime.Now:HH:mm}</p>");
            html.AppendLine("    </div>");
            html.AppendLine("  </div>");

            html.AppendLine("</div>");

            // Print Button (hidden in print)
            html.AppendLine("<div class='no-print' style='text-align: center; margin: 20px;'>");
            html.AppendLine("  <button onclick='window.print()' style='background: #4A90E2; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;'>🖨️ طباعة السند</button>");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// تحويل الرقم إلى كلمات باللغة العربية
        /// </summary>
        private string ConvertNumberToWords(decimal number)
        {
            if (number == 0) return "صفر";

            var integerPart = (int)Math.Floor(number);
            var decimalPart = (int)((number - integerPart) * 100);

            var words = ConvertIntegerToWords(integerPart);

            if (decimalPart > 0)
            {
                words += $" و {ConvertIntegerToWords(decimalPart)} فلس";
            }

            return words;
        }

        /// <summary>
        /// تحويل الرقم الصحيح إلى كلمات
        /// </summary>
        private string ConvertIntegerToWords(int number)
        {
            if (number == 0) return "صفر";

            var ones = new[] { "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة" };
            var tens = new[] { "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
            var teens = new[] { "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر" };

            if (number < 10)
                return ones[number];
            else if (number < 20)
                return teens[number - 10];
            else if (number < 100)
            {
                var ten = number / 10;
                var one = number % 10;
                return tens[ten] + (one > 0 ? " " + ones[one] : "");
            }
            else if (number < 1000)
            {
                var hundred = number / 100;
                var remainder = number % 100;
                var result = ones[hundred] + " مائة";
                if (remainder > 0)
                    result += " " + ConvertIntegerToWords(remainder);
                return result;
            }
            else if (number < 1000000)
            {
                var thousand = number / 1000;
                var remainder = number % 1000;
                var result = ConvertIntegerToWords(thousand) + " ألف";
                if (remainder > 0)
                    result += " " + ConvertIntegerToWords(remainder);
                return result;
            }

            return number.ToString(); // للأرقام الكبيرة جداً
        }
    }
}
