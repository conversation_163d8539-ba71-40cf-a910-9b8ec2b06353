using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AqlanCenterProApp.Services.Implementations
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly AqlanCenterDbContext _context;

        public ActivityLogService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<ActivityLog> LogActivityAsync(int userId, string action, string entityType, int? entityId = null, string? description = null, string? oldValues = null, string? newValues = null)
        {
            var activityLog = new ActivityLog
            {
                UserId = userId,
                Action = action,
                EntityType = entityType,
                EntityId = entityId,
                Description = description,
                OldValues = oldValues,
                NewValues = newValues,
                ActionDate = DateTime.Now,
                CreatedAt = DateTime.Now
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
            return activityLog;
        }

        public async Task<IEnumerable<ActivityLog>> GetUserActivityLogsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.ActivityLogs
                .Include(al => al.User)
                .Where(al => al.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(al => al.ActionDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(al => al.ActionDate <= toDate.Value);

            return await query
                .OrderByDescending(al => al.ActionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ActivityLog>> GetEntityActivityLogsAsync(string entityType, int entityId)
        {
            return await _context.ActivityLogs
                .Include(al => al.User)
                .Where(al => al.EntityType == entityType && al.EntityId == entityId)
                .OrderByDescending(al => al.ActionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ActivityLog>> GetAllActivityLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? action = null, string? entityType = null)
        {
            var query = _context.ActivityLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(al => al.ActionDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(al => al.ActionDate <= toDate.Value);

            if (!string.IsNullOrEmpty(action))
                query = query.Where(al => al.Action == action);

            if (!string.IsNullOrEmpty(entityType))
                query = query.Where(al => al.EntityType == entityType);

            return await query
                .Include(al => al.User)
                .OrderByDescending(al => al.ActionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ActivityLog>> GetLoginLogsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.ActivityLogs
                .Where(al => al.Action == "Login" || al.Action == "Logout");

            if (fromDate.HasValue)
                query = query.Where(al => al.ActionDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(al => al.ActionDate <= toDate.Value);

            return await query
                .Include(al => al.User)
                .OrderByDescending(al => al.ActionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ActivityLog>> GetFailedLoginAttemptsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.ActivityLogs
                .Where(al => al.Action == "FailedLogin");

            if (fromDate.HasValue)
                query = query.Where(al => al.ActionDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(al => al.ActionDate <= toDate.Value);

            return await query
                .Include(al => al.User)
                .OrderByDescending(al => al.ActionDate)
                .ToListAsync();
        }

        public async Task<ActivityLog?> GetActivityLogByIdAsync(int id)
        {
            return await _context.ActivityLogs
                .Include(al => al.User)
                .FirstOrDefaultAsync(al => al.Id == id);
        }

        public async Task<bool> DeleteActivityLogAsync(int id)
        {
            var activityLog = await _context.ActivityLogs.FindAsync(id);
            if (activityLog == null) return false;

            _context.ActivityLogs.Remove(activityLog);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteOldActivityLogsAsync(DateTime cutoffDate)
        {
            var oldLogs = await _context.ActivityLogs
                .Where(al => al.ActionDate < cutoffDate)
                .ToListAsync();

            _context.ActivityLogs.RemoveRange(oldLogs);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<int> GetActivityLogsCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.ActivityLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(al => al.ActionDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(al => al.ActionDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<IEnumerable<ActivityLog>> GetRecentActivityLogsAsync(int count = 50)
        {
            return await _context.ActivityLogs
                .Include(al => al.User)
                .OrderByDescending(al => al.ActionDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task LogLoginAsync(int userId, string ipAddress, string userAgent)
        {
            await LogActivityAsync(userId, "Login", "User", userId, "User logged in successfully", null, null);
        }

        public async Task LogLogoutAsync(int userId, string ipAddress)
        {
            await LogActivityAsync(userId, "Logout", "User", userId, "User logged out", null, null);
        }

        public async Task LogFailedLoginAsync(string username, string ipAddress, string userAgent, string reason)
        {
            var activityLog = new ActivityLog
            {
                UserId = 0, // No user ID for failed login
                Action = "FailedLogin",
                EntityType = "User",
                Description = $"Failed login attempt for username: {username}. Reason: {reason}",
                IpAddress = ipAddress,
                UserAgent = userAgent,
                ActionDate = DateTime.Now,
                CreatedAt = DateTime.Now
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
        }
    }
} 