using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AqlanCenterProApp.Models
{
    public class ActivityLog : BaseEntity
    {
        [Required]
        public int UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // إضافة، تعديل، حذف، عرض، تسجيل دخول، تسجيل خروج
        
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty; // Patient, Doctor, Invoice, etc.
        
        public int? EntityId { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public string? OldValues { get; set; } // JSON
        
        public string? NewValues { get; set; } // JSON
        
        [StringLength(50)]
        public string? IpAddress { get; set; }
        
        [StringLength(200)]
        public string? UserAgent { get; set; }
        
        public DateTime ActionDate { get; set; } = DateTime.Now;
        
        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
