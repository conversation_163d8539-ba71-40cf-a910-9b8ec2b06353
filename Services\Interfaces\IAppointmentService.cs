using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IAppointmentService
    {
        Task<IEnumerable<Appointment>> GetAllAppointmentsAsync();
        Task<Appointment?> GetAppointmentByIdAsync(int id);
        Task<IEnumerable<Appointment>> GetAppointmentsByPatientAsync(int patientId);
        Task<IEnumerable<Appointment>> GetAppointmentsByDoctorAsync(int doctorId);
        Task<IEnumerable<Appointment>> GetAppointmentsByDateAsync(DateTime date);
        Task<IEnumerable<Appointment>> GetAppointmentsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Appointment>> GetUpcomingAppointmentsAsync(int days = 7);
        Task<IEnumerable<Appointment>> GetOverdueAppointmentsAsync();
        Task<Appointment> CreateAppointmentAsync(Appointment appointment);
        Task<Appointment> UpdateAppointmentAsync(Appointment appointment);
        Task<bool> DeleteAppointmentAsync(int id);
        Task<bool> CancelAppointmentAsync(int id, string reason);
        Task<bool> RescheduleAppointmentAsync(int id, DateTime newDateTime);
        Task<bool> MarkAppointmentAsCompletedAsync(int id);
        Task<bool> MarkAppointmentAsNoShowAsync(int id);
        Task<IEnumerable<Appointment>> SearchAppointmentsAsync(string searchTerm);
        Task<int> GetAppointmentsCountAsync();
        Task<Dictionary<string, int>> GetAppointmentsStatisticsAsync();
        Task<bool> IsTimeSlotAvailableAsync(int doctorId, DateTime dateTime, int durationMinutes = 30, int? excludeAppointmentId = null);
    }
} 