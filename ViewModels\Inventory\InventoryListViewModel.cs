using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Inventory
{
    public class InventoryListViewModel : BaseViewModel
    {
        private readonly IInventoryItemService _inventoryItemService;
        private ObservableCollection<InventoryItem> _inventoryItems;
        private InventoryItem? _selectedItem;
        private string _searchTerm = string.Empty;
        private string _selectedCategory = "الكل";
        private bool _showOnlyActive = true;
        private bool _showLowStock = false;
        private bool _showExpiring = false;

        public InventoryListViewModel(IInventoryItemService inventoryItemService)
        {
            _inventoryItemService = inventoryItemService;
            _inventoryItems = new ObservableCollection<InventoryItem>();

            LoadInventoryCommand = new RelayCommand(async () => await LoadInventoryAsync());
            AddItemCommand = new RelayCommand(() => AddItem());
            EditItemCommand = new RelayCommand(() => EditItem(), () => SelectedItem != null);
            DeleteItemCommand = new RelayCommand(async () => await DeleteItemAsync(), () => SelectedItem != null);
            SearchCommand = new RelayCommand(async () => await SearchItemsAsync());
            ExportCommand = new RelayCommand(async () => await ExportInventoryAsync());
            PrintCommand = new RelayCommand(async () => await PrintInventoryAsync());
            RefreshCommand = new RelayCommand(async () => await LoadInventoryAsync());

            // تحميل البيانات عند الإنشاء
            _ = LoadInventoryAsync();
        }

        public ObservableCollection<InventoryItem> InventoryItems
        {
            get => _inventoryItems;
            set => SetProperty(ref _inventoryItems, value);
        }

        public InventoryItem? SelectedItem
        {
            get => _selectedItem;
            set
            {
                SetProperty(ref _selectedItem, value);
                OnPropertyChanged(nameof(CanEdit));
                OnPropertyChanged(nameof(CanDelete));
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                SetProperty(ref _searchTerm, value);
                _ = SearchItemsAsync();
            }
        }

        public string SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                SetProperty(ref _selectedCategory, value);
                _ = LoadInventoryAsync();
            }
        }

        public bool ShowOnlyActive
        {
            get => _showOnlyActive;
            set
            {
                SetProperty(ref _showOnlyActive, value);
                _ = LoadInventoryAsync();
            }
        }

        public bool ShowLowStock
        {
            get => _showLowStock;
            set
            {
                SetProperty(ref _showLowStock, value);
                _ = LoadInventoryAsync();
            }
        }

        public bool ShowExpiring
        {
            get => _showExpiring;
            set
            {
                SetProperty(ref _showExpiring, value);
                _ = LoadInventoryAsync();
            }
        }

        public bool CanEdit => SelectedItem != null;
        public bool CanDelete => SelectedItem != null;

        public ObservableCollection<string> Categories { get; } = new ObservableCollection<string>
        {
            "الكل",
            "أدوية",
            "أدوات",
            "أجهزة",
            "مواد مختبرية",
            "مستهلكات",
            "أخرى"
        };

        // Commands
        public ICommand LoadInventoryCommand { get; }
        public ICommand AddItemCommand { get; }
        public ICommand EditItemCommand { get; }
        public ICommand DeleteItemCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand RefreshCommand { get; }

        public async Task LoadInventoryAsync()
        {
            try
            {
                IsBusy = true;
                IEnumerable<InventoryItem> items;

                if (ShowLowStock)
                {
                    items = await _inventoryItemService.GetLowStockItemsAsync();
                }
                else if (ShowExpiring)
                {
                    items = await _inventoryItemService.GetExpiringItemsAsync();
                }
                else if (SelectedCategory != "الكل")
                {
                    items = await _inventoryItemService.GetInventoryItemsByCategoryAsync(SelectedCategory);
                }
                else
                {
                    items = ShowOnlyActive 
                        ? await _inventoryItemService.GetActiveInventoryItemsAsync()
                        : await _inventoryItemService.GetAllInventoryItemsAsync();
                }

                InventoryItems.Clear();
                foreach (var item in items)
                {
                    InventoryItems.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تحميل المخزون: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddItem()
        {
            // سيتم تنفيذها لاحقاً
            MessageHelper.ShowInfo("سيتم إضافة هذه الميزة قريباً");
        }

        private void EditItem()
        {
            if (SelectedItem == null) return;
            // سيتم تنفيذها لاحقاً
            MessageHelper.ShowInfo($"تعديل العنصر: {SelectedItem.Name}");
        }

        private async Task DeleteItemAsync()
        {
            if (SelectedItem == null) return;

            var result = MessageHelper.ShowConfirmation($"هل أنت متأكد من حذف العنصر '{SelectedItem.Name}'؟");
            if (!result) return;

            try
            {
                IsBusy = true;
                var success = await _inventoryItemService.DeleteInventoryItemAsync(SelectedItem.Id);
                if (success)
                {
                    InventoryItems.Remove(SelectedItem);
                    MessageHelper.ShowSuccess("تم حذف العنصر بنجاح");
                }
                else
                {
                    MessageHelper.ShowError("فشل في حذف العنصر");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في حذف العنصر: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchItemsAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                await LoadInventoryAsync();
                return;
            }

            try
            {
                IsBusy = true;
                var items = await _inventoryItemService.SearchInventoryItemsAsync(SearchTerm);
                
                InventoryItems.Clear();
                foreach (var item in items)
                {
                    if (!ShowOnlyActive || item.IsActive)
                    {
                        InventoryItems.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExportInventoryAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                await Task.Delay(100);
                MessageHelper.ShowSuccess("تم تصدير بيانات المخزون بنجاح");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task PrintInventoryAsync()
        {
            try
            {
                IsBusy = true;
                // سيتم تنفيذها لاحقاً
                await Task.Delay(100);
                MessageHelper.ShowSuccess("تم إرسال بيانات المخزون للطباعة بنجاح");
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError($"خطأ في الطباعة: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
} 