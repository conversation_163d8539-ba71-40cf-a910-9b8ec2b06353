using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class LeaveRequestFormViewModel : INotifyPropertyChanged
    {
        private readonly Employee _employee;
        private DateTime? _startDate;
        private DateTime? _endDate;
        private string _selectedLeaveType = string.Empty;
        private string _reason = string.Empty;
        private string _addressDuringLeave = string.Empty;
        private string _contactPhone = string.Empty;
        private bool _isDeclarationAccepted;
        private bool _isTermsAccepted;

        public LeaveRequestFormViewModel(Employee employee)
        {
            _employee = employee;
            InitializeCommands();
            InitializeData();
        }

        #region Properties

        public string EmployeeName => _employee.FullName;
        public string EmployeeId => _employee.EmployeeId.ToString();
        public string Department => _employee.Department;
        public string Position => _employee.Position;
        public string RequestDate => DateTime.Now.ToString("dd/MM/yyyy");

        public List<string> LeaveTypes { get; } = new List<string>
        {
            "إجازة سنوية",
            "إجازة مرضية",
            "إجازة طارئة",
            "إجازة بدون راتب",
            "إجازة أمومة",
            "إجازة حج",
            "إجازة عيد",
            "إجازة أخرى"
        };

        public string SelectedLeaveType
        {
            get => _selectedLeaveType;
            set
            {
                _selectedLeaveType = value;
                OnPropertyChanged(nameof(SelectedLeaveType));
            }
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set
            {
                _startDate = value;
                OnPropertyChanged(nameof(StartDate));
                CalculateDaysCount();
            }
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set
            {
                _endDate = value;
                OnPropertyChanged(nameof(EndDate));
                CalculateDaysCount();
            }
        }

        public string DaysCount
        {
            get
            {
                if (_startDate.HasValue && _endDate.HasValue && _endDate >= _startDate)
                {
                    int days = (_endDate.Value - _startDate.Value).Days + 1;
                    return days.ToString();
                }
                return "0";
            }
        }

        public string Reason
        {
            get => _reason;
            set
            {
                _reason = value;
                OnPropertyChanged(nameof(Reason));
            }
        }

        public string AddressDuringLeave
        {
            get => _addressDuringLeave;
            set
            {
                _addressDuringLeave = value;
                OnPropertyChanged(nameof(AddressDuringLeave));
            }
        }

        public string ContactPhone
        {
            get => _contactPhone;
            set
            {
                _contactPhone = value;
                OnPropertyChanged(nameof(ContactPhone));
            }
        }

        public bool IsDeclarationAccepted
        {
            get => _isDeclarationAccepted;
            set
            {
                _isDeclarationAccepted = value;
                OnPropertyChanged(nameof(IsDeclarationAccepted));
            }
        }

        public bool IsTermsAccepted
        {
            get => _isTermsAccepted;
            set
            {
                _isTermsAccepted = value;
                OnPropertyChanged(nameof(IsTermsAccepted));
            }
        }

        #endregion

        #region Commands

        public ICommand PrintCommand { get; private set; } = null!;
        public ICommand SaveDraftCommand { get; private set; } = null!;
        public ICommand SubmitCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            PrintCommand = new RelayCommand(PrintForm);
            SaveDraftCommand = new RelayCommand(SaveDraft);
            SubmitCommand = new RelayCommand(SubmitRequest);
        }

        #endregion

        #region Methods

        private void InitializeData()
        {
            // Set default values
            ContactPhone = _employee?.Phone ?? string.Empty;
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
        }

        private void CalculateDaysCount()
        {
            OnPropertyChanged(nameof(DaysCount));
        }

        private void PrintForm()
        {
            if (ValidateForm())
            {
                MessageBox.Show("سيتم إضافة ميزة الطباعة في المراحل القادمة",
                    "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SaveDraft()
        {
            if (ValidateForm())
            {
                MessageBox.Show("تم حفظ المسودة بنجاح",
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SubmitRequest()
        {
            if (ValidateForm())
            {
                var result = MessageBox.Show("هل أنت متأكد من إرسال طلب الإجازة؟",
                    "تأكيد الإرسال", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Here you would typically save to database
                    MessageBox.Show("تم إرسال طلب الإجازة بنجاح",
                        "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(SelectedLeaveType))
            {
                MessageBox.Show("يرجى اختيار نوع الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!StartDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ بداية الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!EndDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ نهاية الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (EndDate < StartDate)
            {
                MessageBox.Show("تاريخ نهاية الإجازة يجب أن يكون بعد تاريخ البداية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(Reason))
            {
                MessageBox.Show("يرجى كتابة سبب الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(AddressDuringLeave))
            {
                MessageBox.Show("يرجى كتابة عنوان الإقامة أثناء الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(ContactPhone))
            {
                MessageBox.Show("يرجى كتابة رقم الهاتف للتواصل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!IsDeclarationAccepted)
            {
                MessageBox.Show("يرجى الموافقة على الإقرار", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!IsTermsAccepted)
            {
                MessageBox.Show("يرجى الموافقة على شروط وأحكام الإجازة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        #endregion
    }
}