using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models
{
    public class Permission : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [Required]
        [StringLength(50)]
        public string Module { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; }

        public bool IsActive { get; set; } = true;
    }

    public static class Permissions
    {
        // صلاحيات المرضى
        public const string ViewPatients = "ViewPatients";
        public const string AddPatient = "AddPatient";
        public const string EditPatient = "EditPatient";
        public const string DeletePatient = "DeletePatient";
        public const string ViewPatientAccount = "ViewPatientAccount";
        public const string PrintConsultationReceipt = "PrintConsultationReceipt";

        // صلاحيات المالية
        public const string ViewFinancial = "ViewFinancial";
        public const string AddReceipt = "AddReceipt";
        public const string EditReceipt = "EditReceipt";
        public const string DeleteReceipt = "DeleteReceipt";
        public const string CancelReceipt = "CancelReceipt";
        public const string ExportFinancial = "ExportFinancial";

        // صلاحيات الموظفين
        public const string ViewEmployees = "ViewEmployees";
        public const string AddEmployee = "AddEmployee";
        public const string EditEmployee = "EditEmployee";
        public const string DeleteEmployee = "DeleteEmployee";
        public const string ViewEmployeeReports = "ViewEmployeeReports";

        // صلاحيات الأطباء
        public const string ViewDoctors = "ViewDoctors";
        public const string AddDoctor = "AddDoctor";
        public const string EditDoctor = "EditDoctor";
        public const string DeleteDoctor = "DeleteDoctor";

        // صلاحيات المواعيد
        public const string ViewAppointments = "ViewAppointments";
        public const string AddAppointment = "AddAppointment";
        public const string EditAppointment = "EditAppointment";
        public const string DeleteAppointment = "DeleteAppointment";

        // صلاحيات المخزون
        public const string ViewInventory = "ViewInventory";
        public const string AddInventory = "AddInventory";
        public const string EditInventory = "EditInventory";
        public const string DeleteInventory = "DeleteInventory";

        // صلاحيات التقارير
        public const string ViewReports = "ViewReports";
        public const string ExportReports = "ExportReports";
        public const string PrintReports = "PrintReports";

        // صلاحيات النظام
        public const string ViewSettings = "ViewSettings";
        public const string EditSettings = "EditSettings";
        public const string ManageUsers = "ManageUsers";
        public const string ManageRoles = "ManageRoles";
        public const string ManagePermissions = "ManagePermissions";
        public const string ViewActivityLog = "ViewActivityLog";
        public const string BackupSystem = "BackupSystem";
        public const string RestoreSystem = "RestoreSystem";

        public static List<Permission> GetAllPermissions()
        {
            return new List<Permission>
            {
                // صلاحيات المرضى
                new Permission { Name = "عرض المرضى", Description = "إمكانية عرض قائمة المرضى", Module = "Patients", Action = ViewPatients },
                new Permission { Name = "إضافة مريض", Description = "إمكانية إضافة مريض جديد", Module = "Patients", Action = AddPatient },
                new Permission { Name = "تعديل مريض", Description = "إمكانية تعديل بيانات المريض", Module = "Patients", Action = EditPatient },
                new Permission { Name = "حذف مريض", Description = "إمكانية حذف مريض", Module = "Patients", Action = DeletePatient },
                new Permission { Name = "كشف حساب المريض", Description = "إمكانية عرض كشف حساب المريض", Module = "Patients", Action = ViewPatientAccount },
                new Permission { Name = "طباعة سند المعاينة", Description = "إمكانية طباعة سند المعاينة", Module = "Patients", Action = PrintConsultationReceipt },

                // صلاحيات المالية
                new Permission { Name = "عرض المالية", Description = "إمكانية عرض العمليات المالية", Module = "Financial", Action = ViewFinancial },
                new Permission { Name = "إضافة إيصال", Description = "إمكانية إضافة إيصال جديد", Module = "Financial", Action = AddReceipt },
                new Permission { Name = "تعديل إيصال", Description = "إمكانية تعديل الإيصال", Module = "Financial", Action = EditReceipt },
                new Permission { Name = "حذف إيصال", Description = "إمكانية حذف الإيصال", Module = "Financial", Action = DeleteReceipt },
                new Permission { Name = "إلغاء إيصال", Description = "إمكانية إلغاء الإيصال", Module = "Financial", Action = CancelReceipt },
                new Permission { Name = "تصدير المالية", Description = "إمكانية تصدير البيانات المالية", Module = "Financial", Action = ExportFinancial },

                // صلاحيات الموظفين
                new Permission { Name = "عرض الموظفين", Description = "إمكانية عرض قائمة الموظفين", Module = "Employees", Action = ViewEmployees },
                new Permission { Name = "إضافة موظف", Description = "إمكانية إضافة موظف جديد", Module = "Employees", Action = AddEmployee },
                new Permission { Name = "تعديل موظف", Description = "إمكانية تعديل بيانات الموظف", Module = "Employees", Action = EditEmployee },
                new Permission { Name = "حذف موظف", Description = "إمكانية حذف موظف", Module = "Employees", Action = DeleteEmployee },
                new Permission { Name = "تقارير الموظفين", Description = "إمكانية عرض تقارير الموظفين", Module = "Employees", Action = ViewEmployeeReports },

                // صلاحيات الأطباء
                new Permission { Name = "عرض الأطباء", Description = "إمكانية عرض قائمة الأطباء", Module = "Doctors", Action = ViewDoctors },
                new Permission { Name = "إضافة طبيب", Description = "إمكانية إضافة طبيب جديد", Module = "Doctors", Action = AddDoctor },
                new Permission { Name = "تعديل طبيب", Description = "إمكانية تعديل بيانات الطبيب", Module = "Doctors", Action = EditDoctor },
                new Permission { Name = "حذف طبيب", Description = "إمكانية حذف طبيب", Module = "Doctors", Action = DeleteDoctor },

                // صلاحيات المواعيد
                new Permission { Name = "عرض المواعيد", Description = "إمكانية عرض قائمة المواعيد", Module = "Appointments", Action = ViewAppointments },
                new Permission { Name = "إضافة موعد", Description = "إمكانية إضافة موعد جديد", Module = "Appointments", Action = AddAppointment },
                new Permission { Name = "تعديل موعد", Description = "إمكانية تعديل الموعد", Module = "Appointments", Action = EditAppointment },
                new Permission { Name = "حذف موعد", Description = "إمكانية حذف موعد", Module = "Appointments", Action = DeleteAppointment },

                // صلاحيات المخزون
                new Permission { Name = "عرض المخزون", Description = "إمكانية عرض المخزون", Module = "Inventory", Action = ViewInventory },
                new Permission { Name = "إضافة مخزون", Description = "إمكانية إضافة عنصر للمخزون", Module = "Inventory", Action = AddInventory },
                new Permission { Name = "تعديل مخزون", Description = "إمكانية تعديل عنصر المخزون", Module = "Inventory", Action = EditInventory },
                new Permission { Name = "حذف مخزون", Description = "إمكانية حذف عنصر من المخزون", Module = "Inventory", Action = DeleteInventory },

                // صلاحيات التقارير
                new Permission { Name = "عرض التقارير", Description = "إمكانية عرض التقارير", Module = "Reports", Action = ViewReports },
                new Permission { Name = "تصدير التقارير", Description = "إمكانية تصدير التقارير", Module = "Reports", Action = ExportReports },
                new Permission { Name = "طباعة التقارير", Description = "إمكانية طباعة التقارير", Module = "Reports", Action = PrintReports },

                // صلاحيات النظام
                new Permission { Name = "عرض الإعدادات", Description = "إمكانية عرض إعدادات النظام", Module = "System", Action = ViewSettings },
                new Permission { Name = "تعديل الإعدادات", Description = "إمكانية تعديل إعدادات النظام", Module = "System", Action = EditSettings },
                new Permission { Name = "إدارة المستخدمين", Description = "إمكانية إدارة المستخدمين", Module = "System", Action = ManageUsers },
                new Permission { Name = "إدارة الأدوار", Description = "إمكانية إدارة الأدوار", Module = "System", Action = ManageRoles },
                new Permission { Name = "إدارة الصلاحيات", Description = "إمكانية إدارة الصلاحيات", Module = "System", Action = ManagePermissions },
                new Permission { Name = "سجل النشاطات", Description = "إمكانية عرض سجل النشاطات", Module = "System", Action = ViewActivityLog },
                new Permission { Name = "نسخ احتياطي", Description = "إمكانية عمل نسخة احتياطية", Module = "System", Action = BackupSystem },
                new Permission { Name = "استعادة النظام", Description = "إمكانية استعادة النظام", Module = "System", Action = RestoreSystem }
            };
        }
    }
} 