using Microsoft.EntityFrameworkCore;
using AqlanCenterProApp.Data;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    public class SupplierService : ISupplierService
    {
        private readonly AqlanCenterDbContext _context;

        public SupplierService(AqlanCenterDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers
                .Include(s => s.Purchases)
                .Include(s => s.PaymentVouchers)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Supplier>> GetActiveSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .Include(s => s.Purchases)
                .Include(s => s.PaymentVouchers)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _context.Suppliers
                .Include(s => s.Purchases)
                .Include(s => s.PaymentVouchers)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Supplier?> GetSupplierByTaxNumberAsync(string taxNumber)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.TaxNumber == taxNumber);
        }

        public async Task<Supplier> CreateSupplierAsync(Supplier supplier)
        {
            supplier.CreatedAt = DateTime.Now;
            supplier.IsActive = true;
            
            _context.Suppliers.Add(supplier);
            await _context.SaveChangesAsync();
            
            return supplier;
        }

        public async Task<Supplier> UpdateSupplierAsync(Supplier supplier)
        {
            var existingSupplier = await _context.Suppliers.FindAsync(supplier.Id);
            if (existingSupplier == null)
                throw new ArgumentException("المورد غير موجود");

            existingSupplier.Name = supplier.Name;
            existingSupplier.Address = supplier.Address;
            existingSupplier.Phone = supplier.Phone;
            existingSupplier.Mobile = supplier.Mobile;
            existingSupplier.Email = supplier.Email;
            existingSupplier.TaxNumber = supplier.TaxNumber;
            existingSupplier.CommercialRecord = supplier.CommercialRecord;
            existingSupplier.ContactPerson = supplier.ContactPerson;
            existingSupplier.ContactPhone = supplier.ContactPhone;
            existingSupplier.Notes = supplier.Notes;
            existingSupplier.IsActive = supplier.IsActive;
            existingSupplier.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return existingSupplier;
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
                return false;

            // التحقق من عدم وجود مشتريات مرتبطة
            var hasPurchases = await _context.Purchases.AnyAsync(p => p.SupplierId == id);
            if (hasPurchases)
                throw new InvalidOperationException("لا يمكن حذف المورد لوجود مشتريات مرتبطة به");

            supplier.IsDeleted = true;
            supplier.DeletedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> DeactivateSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
                return false;

            supplier.IsActive = false;
            supplier.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> ActivateSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
                return false;

            supplier.IsActive = true;
            supplier.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            return await _context.Suppliers
                .Where(s => (s.Name != null && s.Name.Contains(searchTerm)) || 
                           (s.Phone != null && s.Phone.Contains(searchTerm)) || 
                           (s.Mobile != null && s.Mobile.Contains(searchTerm)) ||
                           (s.TaxNumber != null && s.TaxNumber.Contains(searchTerm)))
                .Include(s => s.Purchases)
                .Include(s => s.PaymentVouchers)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId)
        {
            return await _context.Purchases
                .Where(p => p.SupplierId == supplierId)
                .SumAsync(p => p.TotalAmount);
        }

        public async Task<decimal> GetSupplierTotalPaymentsAsync(int supplierId)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.SupplierId == supplierId)
                .SumAsync(pv => pv.Amount);
        }

        public async Task<decimal> GetSupplierBalanceAsync(int supplierId)
        {
            var totalPurchases = await GetSupplierTotalPurchasesAsync(supplierId);
            var totalPayments = await GetSupplierTotalPaymentsAsync(supplierId);
            return totalPurchases - totalPayments;
        }

        public async Task<IEnumerable<Purchase>> GetSupplierPurchasesAsync(int supplierId)
        {
            return await _context.Purchases
                .Where(p => p.SupplierId == supplierId)
                .Include(p => p.PurchaseItems)
                .OrderByDescending(p => p.PurchaseDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PaymentVoucher>> GetSupplierPaymentVouchersAsync(int supplierId)
        {
            return await _context.PaymentVouchers
                .Where(pv => pv.SupplierId == supplierId)
                .OrderByDescending(pv => pv.VoucherDate)
                .ToListAsync();
        }
    }
} 