<Window x:Class="AqlanCenterProApp.Views.Doctors.DoctorStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إحصائيات الطبيب"
        Height="700"
        Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" 
                Background="#FF9800" 
                CornerRadius="8" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊" 
                           FontSize="24" 
                           Margin="0,0,10,0"/>
                <TextBlock Text="{Binding Doctor.FullName, StringFormat='إحصائيات الطبيب: {0}'}" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- الإحصائيات السريعة -->
                <UniformGrid Grid.Row="0" Columns="4" Margin="0,0,0,20">
                    <!-- إجمالي المرضى -->
                    <Border Background="#2196F3" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding Doctor.TotalPatientsCount}" 
                                       FontSize="28" 
                                       FontWeight="Bold" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي المرضى" 
                                       FontSize="14" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- الجلسات المكتملة -->
                    <Border Background="#4CAF50" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding Doctor.CompletedSessionsCount}" 
                                       FontSize="28" 
                                       FontWeight="Bold" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="الجلسات المكتملة" 
                                       FontSize="14" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- إجمالي الأرباح -->
                    <Border Background="#FF9800" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding Doctor.TotalEarnings, StringFormat='{}{0:N0}'}" 
                                       FontSize="28" 
                                       FontWeight="Bold" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي الأرباح (ريال)" 
                                       FontSize="14" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- التقييم -->
                    <Border Background="#9C27B0" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⭐" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding Doctor.Rating, StringFormat='{}{0:F1}/5'}" 
                                       FontSize="28" 
                                       FontWeight="Bold" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding Doctor.RatingCount, StringFormat='({0} تقييم)'}" 
                                       FontSize="14" 
                                       Foreground="White" 
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- الإحصائيات التفصيلية -->
                <Grid Grid.Row="1" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الإحصائيات المالية -->
                    <GroupBox Grid.Column="0" Header="الإحصائيات المالية" Style="{DynamicResource ModernGroupBoxStyle}">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي الأرباح:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Doctor.TotalEarnings, StringFormat='{}{0:N0} ريال'}" 
                                       Margin="10,5" Foreground="#FF9800" FontWeight="Bold"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة العمولة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Doctor.CommissionPercentage, StringFormat='{}{0}%'}" Margin="10,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="الراتب الثابت:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Doctor.FixedSalary, StringFormat='{}{0:N0} {1}', ConverterParameter={Binding Doctor.SalaryCurrency}}" Margin="10,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="عملة العمولة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Doctor.CommissionCurrency}" Margin="10,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="متوسط الربح/جلسة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding AverageEarningsPerSession, StringFormat='{}{0:N0} ريال'}" 
                                       Margin="10,5" Foreground="#4CAF50" FontWeight="Bold"/>
                        </Grid>
                    </GroupBox>

                    <!-- إحصائيات الأداء -->
                    <GroupBox Grid.Column="2" Header="إحصائيات الأداء" Style="{DynamicResource ModernGroupBoxStyle}">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المرضى:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Doctor.TotalPatientsCount}" 
                                       Margin="10,5" Foreground="#2196F3" FontWeight="Bold"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="الجلسات المكتملة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Doctor.CompletedSessionsCount}" 
                                       Margin="10,5" Foreground="#4CAF50" FontWeight="Bold"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="التقييم العام:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Doctor.Rating, StringFormat='{}{0:F1}/5'}" 
                                       Margin="10,5" Foreground="#FFC107" FontWeight="Bold"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="عدد التقييمات:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Doctor.RatingCount}" Margin="10,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="متوسط المرضى/شهر:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding AveragePatientsPerMonth, StringFormat='{}{0:F1}'}" 
                                       Margin="10,5" Foreground="#9C27B0" FontWeight="Bold"/>
                        </Grid>
                    </GroupBox>
                </Grid>

                <!-- الرسوم البيانية والتقارير -->
                <GroupBox Grid.Row="2" Header="التقارير الشهرية" Style="{DynamicResource ModernGroupBoxStyle}">
                    <Grid Margin="15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الأرباح الشهرية -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="الأرباح الشهرية (آخر 6 أشهر)" 
                                       FontWeight="Bold" 
                                       FontSize="16" 
                                       Margin="0,0,0,10"
                                       Foreground="#FF9800"/>
                            <StackPanel x:Name="MonthlyEarningsPanel"/>
                        </StackPanel>

                        <!-- الجلسات الشهرية -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الجلسات الشهرية (آخر 6 أشهر)" 
                                       FontWeight="Bold" 
                                       FontSize="16" 
                                       Margin="0,0,0,10"
                                       Foreground="#4CAF50"/>
                            <StackPanel x:Name="MonthlySessionsPanel"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
            </Grid>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button Content="🔄 تحديث" 
                    Command="{Binding RefreshCommand}"
                    Background="#2196F3" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Padding="20,10" 
                    Margin="10,0" 
                    FontWeight="Bold" 
                    FontSize="14" 
                    MinWidth="120"/>
            
            <Button Content="🖨️ طباعة" 
                    Command="{Binding PrintCommand}"
                    Background="#9C27B0" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Padding="20,10" 
                    Margin="10,0" 
                    FontWeight="Bold" 
                    FontSize="14" 
                    MinWidth="120"/>
            
            <Button Content="📤 تصدير" 
                    Command="{Binding ExportCommand}"
                    Background="#607D8B" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Padding="20,10" 
                    Margin="10,0" 
                    FontWeight="Bold" 
                    FontSize="14" 
                    MinWidth="120"/>
            
            <Button Content="❌ إغلاق" 
                    Command="{Binding CloseCommand}"
                    Background="#F44336" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Padding="20,10" 
                    Margin="10,0" 
                    FontWeight="Bold" 
                    FontSize="14" 
                    MinWidth="120"/>
        </StackPanel>
    </Grid>

    <Window.Resources>
        <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0" 
                                        Background="#FF9800" 
                                        CornerRadius="8,8,0,0" 
                                        Padding="15,8">
                                    <ContentPresenter ContentSource="Header" 
                                                      TextBlock.Foreground="White" 
                                                      TextBlock.FontWeight="Bold"/>
                                </Border>
                                <ContentPresenter Grid.Row="1" 
                                                  Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>
