using System.Windows;
using AqlanCenterProApp.ViewModels.Labs;

namespace AqlanCenterProApp.Views.Labs
{
    /// <summary>
    /// Interaction logic for AddEditLabView.xaml
    /// </summary>
    public partial class AddEditLabView : Window
    {
        public AddEditLabView(AddEditLabViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // ربط حدث طلب الإغلاق
            viewModel.CloseRequested += OnCloseRequested;
        }

        private void OnCloseRequested(object? sender, bool result)
        {
            DialogResult = result;
            Close();
        }
    }
} 