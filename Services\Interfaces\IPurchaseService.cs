using AqlanCenterProApp.Models;

namespace AqlanCenterProApp.Services.Interfaces
{
    public interface IPurchaseService
    {
        Task<IEnumerable<Purchase>> GetAllPurchasesAsync();
        Task<Purchase?> GetPurchaseByIdAsync(int id);
        Task<Purchase?> GetPurchaseByInvoiceNumberAsync(string invoiceNumber);
        Task<Purchase> CreatePurchaseAsync(Purchase purchase, List<PurchaseItem> items);
        Task<Purchase> UpdatePurchaseAsync(Purchase purchase, List<PurchaseItem> items);
        Task<bool> DeletePurchaseAsync(int id);
        Task<bool> CancelPurchaseAsync(int id);
        Task<bool> ReceivePurchaseAsync(int id, string receivedBy);
        Task<IEnumerable<Purchase>> GetPurchasesBySupplierAsync(int supplierId);
        Task<IEnumerable<Purchase>> GetPurchasesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Purchase>> GetPendingPurchasesAsync();
        Task<IEnumerable<Purchase>> GetReceivedPurchasesAsync();
        Task<IEnumerable<Purchase>> SearchPurchasesAsync(string searchTerm);
        Task<decimal> GetTotalPurchasesAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<decimal> GetTotalPurchasesBySupplierAsync(int supplierId, DateTime? startDate = null, DateTime? endDate = null);
        Task<string> GenerateInvoiceNumberAsync();
        Task<bool> UpdatePurchaseStatusAsync(int id, string status);
        Task<bool> UpdatePaymentStatusAsync(int id, string status, decimal paidAmount);
        Task<IEnumerable<PurchaseItem>> GetPurchaseItemsAsync(int purchaseId);
        Task<decimal> CalculatePurchaseTotalAsync(List<PurchaseItem> items);
    }
} 