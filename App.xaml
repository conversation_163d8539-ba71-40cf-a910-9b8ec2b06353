<Application x:Class="AqlanCenterProApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:AqlanCenterProApp"
             xmlns:converters="clr-namespace:AqlanCenterProApp.Converters">
        <Application.Resources>
                <!-- المحولات (Converters) -->
                <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
                <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
                <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
                <converters:NotNullConverter x:Key="NotNullConverter"/>
                <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
                <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
                <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
                <converters:CurrencyConverter x:Key="CurrencyConverter"/>
                <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
                <converters:NotificationTypeToColorConverter x:Key="NotificationTypeToColorConverter"/>
                <converters:ColorToBrushConverter x:Key="ColorToBrushConverter"/>
                <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
                <converters:BoolToStatusColorConverter x:Key="BoolToStatusColorConverter"/>
                <converters:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>
                <converters:BoolToTextConverter x:Key="BoolToTextConverter"/>

                <!-- المحولات الإضافية من StringToVisibilityConverter.cs -->
                <converters:BooleanToVisibilityConverter x:Key="CustomBooleanToVisibilityConverter"/>
                <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
                <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
                <converters:ArabicDateConverter x:Key="ArabicDateConverter"/>

                <!-- تأثيرات الظل -->
                <DropShadowEffect x:Key="DropShadowEffect"
                                  Color="#E0E0E0"
                                  Direction="270"
                                  ShadowDepth="2"
                                  BlurRadius="8"
                                  Opacity="0.3"/>

                <DropShadowEffect x:Key="EnhancedDropShadowEffect"
                                  Color="#000000"
                                  Direction="270"
                                  ShadowDepth="4"
                                  BlurRadius="12"
                                  Opacity="0.15"/>

                <!-- الألوان الأساسية -->
                <SolidColorBrush x:Key="PrimaryBlueBrush"
                                 Color="#4472B5"/>
                <SolidColorBrush x:Key="PrimaryOrangeBrush"
                                 Color="#F7931D"/>
                <SolidColorBrush x:Key="WhiteBrush"
                                 Color="White"/>
                <SolidColorBrush x:Key="LightGrayBrush"
                                 Color="#F5F5F5"/>
                <SolidColorBrush x:Key="DarkGrayBrush"
                                 Color="#666666"/>
                <SolidColorBrush x:Key="BorderBrush"
                                 Color="#E0E0E0"/>

                <!-- ألوان إضافية للسايدبار -->
                <SolidColorBrush x:Key="DarkBlueBrush"
                                 Color="#2C3E50"/>
                <SolidColorBrush x:Key="LightBlueBrush"
                                 Color="#3498DB"/>
                <SolidColorBrush x:Key="AccentBrush"
                                 Color="#E74C3C"/>

                <!-- ألوان الحالة -->
                <SolidColorBrush x:Key="SuccessBrush"
                                 Color="#4CAF50"/>
                <SolidColorBrush x:Key="WarningBrush"
                                 Color="#FF9800"/>
                <SolidColorBrush x:Key="DangerBrush"
                                 Color="#F44336"/>
                <SolidColorBrush x:Key="InfoBrush"
                                 Color="#2196F3"/>

                <!-- الخطوط -->
                <FontFamily x:Key="ArabicFontFamily">Segoe UI, Tahoma, Arial</FontFamily>

                <!-- التأثيرات البصرية -->
                <DropShadowEffect x:Key="CardShadow"
                                  Color="Gray"
                                  Direction="270"
                                  ShadowDepth="2"
                                  BlurRadius="8"
                                  Opacity="0.2"/>

                <!-- أنماط الأزرار -->
                <Style x:Key="PrimaryButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="{StaticResource PrimaryBlueBrush}"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#3A5998"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#2C4373"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="AccentButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="{StaticResource AccentBrush}"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#C0392B"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#A93226"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="DangerButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="{StaticResource DangerBrush}"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#D32F2F"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#B71C1C"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- ستايل زر النجاح -->
                <Style x:Key="SuccessButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="{StaticResource SuccessBrush}"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="FontFamily"
                                Value="{StaticResource ArabicFontFamily}"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#2E7D32"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#1B5E20"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- أنماط TextBox -->
                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="36"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Padding"
                                Value="10,8"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#E0E0E0"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="VerticalContentAlignment"
                                Value="Center"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="6">
                                                        <ScrollViewer x:Name="PART_ContentHost"
                                                                      Margin="{TemplateBinding Padding}"
                                                                      VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsFocused"
                                                                 Value="True">
                                                                <Setter Property="BorderBrush"
                                                                        Value="{StaticResource PrimaryBlueBrush}"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- أنماط DataGrid -->
                <Style x:Key="ModernDataGrid"
                       TargetType="DataGrid">
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#E0E0E0"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="RowBackground"
                                Value="White"/>
                        <Setter Property="AlternatingRowBackground"
                                Value="#F8F9FA"/>
                        <Setter Property="GridLinesVisibility"
                                Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush"
                                Value="#E0E0E0"/>
                        <Setter Property="HeadersVisibility"
                                Value="Column"/>
                        <Setter Property="AutoGenerateColumns"
                                Value="False"/>
                        <Setter Property="CanUserAddRows"
                                Value="False"/>
                        <Setter Property="CanUserDeleteRows"
                                Value="False"/>
                        <Setter Property="IsReadOnly"
                                Value="True"/>
                        <Setter Property="SelectionMode"
                                Value="Single"/>
                        <Setter Property="FontFamily"
                                Value="{StaticResource ArabicFontFamily}"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <!-- ستايل رأس DataGrid -->
                <Style x:Key="DataGridHeaderStyle"
                       TargetType="DataGridColumnHeader">
                        <Setter Property="Background"
                                Value="#F8F9FA"/>
                        <Setter Property="Foreground"
                                Value="#2C3E50"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Height"
                                Value="45"/>
                        <Setter Property="Padding"
                                Value="15,0"/>
                        <Setter Property="BorderBrush"
                                Value="#E0E0E0"/>
                        <Setter Property="BorderThickness"
                                Value="0,0,0,1"/>
                        <Setter Property="HorizontalContentAlignment"
                                Value="Center"/>
                        <Setter Property="VerticalContentAlignment"
                                Value="Center"/>
                        <Setter Property="FontFamily"
                                Value="{StaticResource ArabicFontFamily}"/>
                </Style>

                <!-- IconButtonStyle للأزرار في الهيدر -->
                <Style x:Key="IconButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="Transparent"/>
                        <Setter Property="Foreground"
                                Value="#4472B5"/>
                        <Setter Property="Width"
                                Value="32"/>
                        <Setter Property="Height"
                                Value="32"/>
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border x:Name="border"
                                                        Background="{TemplateBinding Background}"
                                                        CornerRadius="16">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter TargetName="border"
                                                                        Property="Background"
                                                                        Value="#F0F0F0"/>
                                                                <Setter Property="Foreground"
                                                                        Value="#F7931D"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter TargetName="border"
                                                                        Property="Background"
                                                                        Value="#E0E0E0"/>
                                                                <Setter Property="Foreground"
                                                                        Value="#2C3E50"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="QuickActionButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#4472B5"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="Width"
                                Value="38"/>
                        <Setter Property="Height"
                                Value="38"/>
                        <Setter Property="FontSize"
                                Value="18"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Margin"
                                Value="4,0"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border x:Name="border"
                                                        Background="{TemplateBinding Background}"
                                                        CornerRadius="19">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter TargetName="border"
                                                                        Property="Background"
                                                                        Value="#F7931D"/>
                                                                <Setter Property="Foreground"
                                                                        Value="#4472B5"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter TargetName="border"
                                                                        Property="Background"
                                                                        Value="#333"/>
                                                                <Setter Property="Foreground"
                                                                        Value="White"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

        </Application.Resources>
</Application>
