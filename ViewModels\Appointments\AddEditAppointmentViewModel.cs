using System;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Appointments
{
    public class AddEditAppointmentViewModel : BaseViewModel
    {
        private readonly IAppointmentService _appointmentService;
        private readonly IPatientService _patientService;
        private readonly IDoctorService _doctorService;
        private readonly IOrthodonticPlanService _orthodonticPlanService;
        private readonly bool _isEditMode;

        public AddEditAppointmentViewModel(IAppointmentService appointmentService, IPatientService patientService, IDoctorService doctorService, IOrthodonticPlanService orthodonticPlanService, Appointment? appointment = null)
        {
            _appointmentService = appointmentService;
            _patientService = patientService;
            _doctorService = doctorService;
            _orthodonticPlanService = orthodonticPlanService;
            _isEditMode = appointment != null;

            // تحميل القوائم
            _ = LoadPatientsAsync();
            _ = LoadDoctorsAsync();

            if (_isEditMode && appointment != null)
            {
                AppointmentId = appointment.Id;
                SelectedPatient = appointment.Patient;
                SelectedDoctor = appointment.Doctor;
                ServiceType = appointment.ServiceType;
                AppointmentType = appointment.AppointmentType;
                AppointmentDate = appointment.AppointmentDateTime.Date;
                AppointmentTime = appointment.AppointmentDateTime.TimeOfDay;
                DurationMinutes = appointment.DurationMinutes;
                Notes = appointment.Notes;
                if (appointment.OrthodonticPlan != null)
                {
                    SelectedOrthodonticPlan = appointment.OrthodonticPlan;
                }
            }
            else
            {
                AppointmentDate = DateTime.Today;
                AppointmentTime = DateTime.Now.TimeOfDay;
                DurationMinutes = 30;
            }

            SaveCommand = new RelayCommand(async _ => await SaveAsync());
            CancelCommand = new RelayCommand(_ => Cancel());
        }

        #region Properties

        public int AppointmentId { get; set; }

        private ObservableCollection<Patient> _patients = new();
        public ObservableCollection<Patient> Patients
        {
            get => _patients;
            set => SetProperty(ref _patients, value);
        }

        private ObservableCollection<Doctor> _doctors = new();
        public ObservableCollection<Doctor> Doctors
        {
            get => _doctors;
            set => SetProperty(ref _doctors, value);
        }

        private ObservableCollection<OrthodonticPlan> _orthodonticPlans = new();
        public ObservableCollection<OrthodonticPlan> OrthodonticPlans
        {
            get => _orthodonticPlans;
            set => SetProperty(ref _orthodonticPlans, value);
        }

        private Patient? _selectedPatient;
        public Patient? SelectedPatient
        {
            get => _selectedPatient;
            set
            {
                if (SetProperty(ref _selectedPatient, value))
                {
                    _ = LoadOrthodonticPlansAsync();
                }
            }
        }

        private Doctor? _selectedDoctor;
        public Doctor? SelectedDoctor
        {
            get => _selectedDoctor;
            set => SetProperty(ref _selectedDoctor, value);
        }

        private string _serviceType = string.Empty;
        [Required(ErrorMessage = "نوع الخدمة مطلوب")]
        public string ServiceType
        {
            get => _serviceType;
            set => SetProperty(ref _serviceType, value);
        }

        private string _appointmentType = "استشارة";
        [Required(ErrorMessage = "نوع الموعد مطلوب")]
        public string AppointmentType
        {
            get => _appointmentType;
            set => SetProperty(ref _appointmentType, value);
        }

        private DateTime _appointmentDate;
        [Required(ErrorMessage = "تاريخ الموعد مطلوب")]
        public DateTime AppointmentDate
        {
            get => _appointmentDate;
            set => SetProperty(ref _appointmentDate, value);
        }

        private TimeSpan _appointmentTime;
        [Required(ErrorMessage = "وقت الموعد مطلوب")]
        public TimeSpan AppointmentTime
        {
            get => _appointmentTime;
            set => SetProperty(ref _appointmentTime, value);
        }

        private int _durationMinutes = 30;
        [Range(5, 180, ErrorMessage = "المدة يجب أن تكون بين 5 و 180 دقيقة")]
        public int DurationMinutes
        {
            get => _durationMinutes;
            set => SetProperty(ref _durationMinutes, value);
        }

        private string? _notes;
        public string? Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        private OrthodonticPlan? _selectedOrthodonticPlan;
        public OrthodonticPlan? SelectedOrthodonticPlan
        {
            get => _selectedOrthodonticPlan;
            set => SetProperty(ref _selectedOrthodonticPlan, value);
        }

        public ObservableCollection<string> ServiceTypes { get; } = new(new[] { "استشارة", "متابعة", "إجراء طبي", "أخرى" });
        public ObservableCollection<string> AppointmentTypes { get; } = new(new[] { "استشارة", "متابعة", "إجراء", "طارئ" });

        #endregion

        #region Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        #endregion

        public event Action? RequestClose;
        public event Action<string>? ShowMessage;

        private async Task LoadPatientsAsync()
        {
            try
            {
                var (patients, _, _) = await _patientService.GetPatientsAsync(pageSize: 1000);
                Patients.Clear();
                foreach (var patient in patients)
                {
                    Patients.Add(patient);
                }
            }
            catch (Exception ex)
            {
                ShowMessage?.Invoke($"خطأ في تحميل المرضى: {ex.Message}");
            }
        }

        private async Task LoadDoctorsAsync()
        {
            try
            {
                var doctors = await _doctorService.GetAllDoctorsAsync();
                Doctors.Clear();
                foreach (var doctor in doctors)
                {
                    Doctors.Add(doctor);
                }
            }
            catch (Exception ex)
            {
                ShowMessage?.Invoke($"خطأ في تحميل الأطباء: {ex.Message}");
            }
        }

        private async Task LoadOrthodonticPlansAsync()
        {
            OrthodonticPlans.Clear();
            if (SelectedPatient != null)
            {
                var plans = await _orthodonticPlanService.GetPlansByPatientAsync(SelectedPatient.Id);
                foreach (var plan in plans)
                {
                    OrthodonticPlans.Add(plan);
                }
            }
        }

        private async Task SaveAsync()
        {
            if (SelectedPatient == null || SelectedDoctor == null)
            {
                ShowMessage?.Invoke("يجب اختيار المريض والطبيب.");
                return;
            }

            var dateTime = AppointmentDate.Date + AppointmentTime;
            var isAvailable = await _appointmentService.IsTimeSlotAvailableAsync(SelectedDoctor.Id, dateTime, DurationMinutes, _isEditMode ? AppointmentId : (int?)null);
            if (!isAvailable)
            {
                ShowMessage?.Invoke("الطبيب لديه موعد آخر في هذا الوقت.");
                return;
            }

            var appointment = new Appointment
            {
                Id = AppointmentId,
                PatientId = SelectedPatient.Id,
                DoctorId = SelectedDoctor.Id,
                ServiceType = ServiceType,
                AppointmentType = AppointmentType,
                AppointmentDateTime = dateTime,
                DurationMinutes = DurationMinutes,
                Notes = Notes,
                Status = "مجدول",
                OrthodonticPlanId = SelectedOrthodonticPlan?.Id
            };

            if (_isEditMode)
            {
                await _appointmentService.UpdateAppointmentAsync(appointment);
                ShowMessage?.Invoke("تم تحديث الموعد بنجاح.");
            }
            else
            {
                await _appointmentService.CreateAppointmentAsync(appointment);
                ShowMessage?.Invoke("تم إضافة الموعد بنجاح.");
            }
            RequestClose?.Invoke();
        }

        private void Cancel()
        {
            RequestClose?.Invoke();
        }
    }
}