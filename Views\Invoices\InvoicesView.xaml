<UserControl x:Class="AqlanCenterProApp.Views.Invoices.InvoicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Invoices"
             xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
             xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             Background="#F5F5F5">

    <UserControl.Resources>
        <!-- المحولات -->
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>


    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  PanningMode="VerticalOnly">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط الأدوات -->
            <Border Grid.Row="0"
                    Background="White"
                    CornerRadius="8"
                    Padding="20"
                    Margin="0,0,0,20"
                    Effect="{StaticResource CardShadow}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- أدوات البحث والفلترة -->
                    <StackPanel Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                        <TextBlock Text="البحث:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"
                                   FontWeight="Bold"/>
                        <TextBox Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                                 Width="200"
                                 Height="35"
                                 Padding="10,5"
                                 VerticalAlignment="Center"
                                 Margin="0,0,15,0"
                                 BorderBrush="#DDDDDD"
                                 BorderThickness="1"/>

                        <TextBlock Text="الحالة:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"
                                   FontWeight="Bold"/>
                        <ComboBox ItemsSource="{Binding StatusOptions}"
                                  SelectedItem="{Binding SelectedStatus}"
                                  Width="120"
                                  Height="35"
                                  Padding="10,5"
                                  VerticalAlignment="Center"
                                  Margin="0,0,15,0"
                                  BorderBrush="#DDDDDD"
                                  BorderThickness="1"/>

                        <TextBlock Text="من:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"
                                   FontWeight="Bold"/>
                        <DatePicker SelectedDate="{Binding StartDate}"
                                    Width="120"
                                    Height="35"
                                    VerticalAlignment="Center"
                                    Margin="0,0,15,0"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"/>

                        <TextBlock Text="إلى:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"
                                   FontWeight="Bold"/>
                        <DatePicker SelectedDate="{Binding EndDate}"
                                    Width="120"
                                    Height="35"
                                    VerticalAlignment="Center"
                                    Margin="0,0,15,0"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"/>
                    </StackPanel>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Column="1"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                        <Button Content="➕ فاتورة جديدة"
                                Command="{Binding AddInvoiceCommand}"
                                Style="{StaticResource PrimaryButton}"
                                Margin="0,0,10,0"/>

                        <Button Content="🔍 بحث"
                                Command="{Binding SearchInvoicesCommand}"
                                Style="{StaticResource AccentButton}"
                                Margin="0,0,10,0"/>

                        <Button Content="🔄 تحديث"
                                Command="{Binding RefreshCommand}"
                                Style="{StaticResource AccentButton}"
                                Margin="0,0,10,0"/>

                        <Button Content="🖨️ طباعة"
                                Command="{Binding PrintInvoiceCommand}"
                                Style="{StaticResource AccentButton}"
                                Margin="0,0,10,0"/>

                        <Button Content="📊 تقرير"
                                Command="{Binding GenerateReportCommand}"
                                Style="{StaticResource AccentButton}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- جدول الفواتير -->
            <Border Grid.Row="1"
                    Background="White"
                    CornerRadius="8"
                    Margin="0,0,0,20"
                    Effect="{StaticResource CardShadow}">
                <DataGrid ItemsSource="{Binding Invoices}"
                          SelectedItem="{Binding SelectedInvoice}"
                          Style="{StaticResource ModernDataGrid}"
                          ColumnHeaderStyle="{StaticResource DataGridHeaderStyle}"
                          Margin="20"
                          RowHeight="40">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة"
                                            Binding="{Binding InvoiceNumber}"
                                            Width="120"/>
                        <DataGridTextColumn Header="التاريخ"
                                            Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}"
                                            Width="100"/>
                        <DataGridTextColumn Header="المريض"
                                            Binding="{Binding Patient.FullName}"
                                            Width="200"/>
                        <DataGridTextColumn Header="رقم الملف"
                                            Binding="{Binding Patient.FileNumber}"
                                            Width="100"/>
                        <DataGridTextColumn Header="المجموع الفرعي"
                                            Binding="{Binding SubTotal, StringFormat=N0}"
                                            Width="120"/>
                        <DataGridTextColumn Header="الخصم"
                                            Binding="{Binding DiscountAmount, StringFormat=N0}"
                                            Width="100"/>
                        <DataGridTextColumn Header="الضريبة"
                                            Binding="{Binding TaxAmount, StringFormat=N0}"
                                            Width="100"/>
                        <DataGridTextColumn Header="الإجمالي"
                                            Binding="{Binding TotalAmount, StringFormat=N0}"
                                            Width="120"/>
                        <DataGridTextColumn Header="المدفوع"
                                            Binding="{Binding PaidAmount, StringFormat=N0}"
                                            Width="100"/>
                        <DataGridTextColumn Header="المتبقي"
                                            Binding="{Binding RemainingAmount, StringFormat=N0}"
                                            Width="100"/>
                        <DataGridTextColumn Header="الحالة"
                                            Binding="{Binding InvoiceStatus}"
                                            Width="100"/>
                        <DataGridTextColumn Header="تاريخ الاستحقاق"
                                            Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}"
                                            Width="120"/>
                        <DataGridTextColumn Header="المصدر"
                                            Binding="{Binding IssuedBy}"
                                            Width="120"/>
                    </DataGrid.Columns>

                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding InvoiceStatus}"
                                             Value="متأخرة">
                                    <Setter Property="Background"
                                            Value="#FFEBEE"/>
                                    <Setter Property="Foreground"
                                            Value="#C62828"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding InvoiceStatus}"
                                             Value="مدفوعة">
                                    <Setter Property="Background"
                                            Value="#E8F5E8"/>
                                    <Setter Property="Foreground"
                                            Value="#2E7D32"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>
                </DataGrid>
            </Border>

            <!-- مؤشر التحميل -->
            <Grid Grid.Row="0" Grid.RowSpan="2" 
                  Background="#80000000" 
                  Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="100" Height="10" Margin="0,0,0,10"/>
                    <TextBlock Text="جاري التحميل..." Foreground="White" HorizontalAlignment="Center" FontSize="16"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl> 