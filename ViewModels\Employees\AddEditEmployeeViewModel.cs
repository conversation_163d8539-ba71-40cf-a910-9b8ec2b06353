using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AqlanCenterProApp.Models;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.ViewModels.Base;

namespace AqlanCenterProApp.ViewModels.Employees
{
    public class AddEditEmployeeViewModel : BaseViewModel
    {
        private readonly IEmployeeService _employeeService;
        private readonly bool _isEditMode;
        private Employee _employee;

        public AddEditEmployeeViewModel(IEmployeeService employeeService, Employee? employee = null)
        {
            _employeeService = employeeService;
            _isEditMode = employee != null;
            _employee = employee ?? new Employee();

            // Initialize commands
            SaveCommand = new RelayCommand(async () => await SaveAsync(), CanSave);
            CancelCommand = new RelayCommand(Cancel);

            // Initialize collections
            Departments = new List<string> { "الإدارة", "التمريض", "المحاسبة", "الاستقبال", "الصيانة", "الأمن", "النظافة" };
            Positions = new List<string> { "مدير", "ممرض", "محاسب", "موظف استقبال", "فني صيانة", "حارس", "عامل نظافة" };
            Statuses = new List<string> { "نشط", "موقوف", "مستقيل", "مؤرشف" };

            // Set default values
            if (!_isEditMode)
            {
                Employee.HireDate = DateTime.Today;
                Employee.IsActive = true;
                Employee.Status = "نشط";
            }

            // Subscribe to property changes for validation
            PropertyChanged += OnPropertyChanged;
        }

        public Employee Employee
        {
            get => _employee;
            set
            {
                _employee = value;
                OnPropertyChanged();
            }
        }

        public string WindowTitle => _isEditMode ? "تعديل موظف" : "إضافة موظف جديد";

        public List<string> Departments { get; set; }
        public List<string> Positions { get; set; }
        public List<string> Statuses { get; set; }

        public string TotalSalary => Employee.TotalSalary.ToString("N0") + " ريال يمني";

        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        private bool CanSave()
        {
            return !string.IsNullOrWhiteSpace(Employee.FirstName) &&
                   !string.IsNullOrWhiteSpace(Employee.LastName) &&
                   !string.IsNullOrWhiteSpace(Employee.EmployeeNumber) &&
                   !string.IsNullOrWhiteSpace(Employee.PhoneNumber) &&
                   !string.IsNullOrWhiteSpace(Employee.Department) &&
                   !string.IsNullOrWhiteSpace(Employee.Position);
        }

        private async Task SaveAsync()
        {
            try
            {
                IsLoading = true;

                if (_isEditMode)
                {
                    var success = await _employeeService.UpdateEmployeeAsync(Employee, "System");
                    if (success)
                    {
                        MessageBox.Show("تم تحديث بيانات الموظف بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات الموظف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }
                else
                {
                    var newEmployee = await _employeeService.AddEmployeeAsync(Employee, "System");
                    if (newEmployee != null)
                    {
                        MessageBox.Show("تم إضافة الموظف بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة الموظف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                // Close window
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var window = Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
                    window?.Close();
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void Cancel()
        {
            var result = MessageBox.Show("هل أنت متأكد من إلغاء العملية؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var window = Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
                    window?.Close();
                });
            }
        }

        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Employee))
            {
                // Re-evaluate save command when employee changes
                CommandManager.InvalidateRequerySuggested();
            }

            // Monitor salary-related property changes
            if (e.PropertyName?.StartsWith("Employee.") == true)
            {
                var propertyName = e.PropertyName.Replace("Employee.", "");
                if (propertyName == nameof(Employee.BasicSalary) ||
                    propertyName == nameof(Employee.HousingAllowance) ||
                    propertyName == nameof(Employee.TransportationAllowance) ||
                    propertyName == nameof(Employee.FoodAllowance) ||
                    propertyName == nameof(Employee.OtherAllowances) ||
                    propertyName == nameof(Employee.InsuranceDeduction) ||
                    propertyName == nameof(Employee.TaxDeduction) ||
                    propertyName == nameof(Employee.OtherDeductions))
                {
                    OnPropertyChanged(nameof(TotalSalary));
                }
            }
        }
    }
}